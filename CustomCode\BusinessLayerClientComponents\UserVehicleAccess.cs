﻿using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// UserVehicleAccess Component
	///  
	/// </summary>
    public partial class UserVehicleAccess : IUserVehicleAccess 
    {
		public void Dispose()
		{
		}

        public Task<ComponentResponse<bool>> DeselectAllAsync(Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }

        public Task<ComponentResponse<bool>> SelectAllAsync(Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }
    }
}
