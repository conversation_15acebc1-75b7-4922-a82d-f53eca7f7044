﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ExceptionHandling;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// CustomerAPI Component
	///  
	/// </summary>
    public partial class CustomerAPI : BaseServerComponent, ICustomerAPI
    {
        public CustomerAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
        {
        }

        /// <summary>
        /// SoftDelete Method
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SoftDeleteAsync(Guid customerid, Dictionary<string, object> parameters = null)
        {
            var customer = _serviceProvider.GetService<CustomerDataObject>();
            customer.Id = customerid;

            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer);

            if (customer == null)
            {
                throw new GOServerException($"unknown customer id {customerid}");
            }

            // Check for sites first
            var sites = await customer.LoadSitesAsync();
            if (sites.Any())
            {
                throw new GOServerException("Cannot delete customer: Customer contains sites. Please delete all sites first.");
            }

            // Check for departments
            var departments = await customer.LoadDepartmentItemsAsync();
            if (departments.Any())
            {
                throw new GOServerException("Cannot delete customer: Customer contains departments. Please delete all departments first.");
            }

            customer.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            return new ComponentResponse<bool>(true);
        }
    }
}
