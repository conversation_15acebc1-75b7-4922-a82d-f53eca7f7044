﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class AllVehicleUnlocksViewDataProvider : DataProvider<AllVehicleUnlocksViewDataObject>
    {
        protected readonly IConfiguration _configuration;
        public AllVehicleUnlocksViewDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<AllVehicleUnlocksViewDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetAllVehicleUnlocks", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }
                    if (filter.HasLockoutType)
                    {
                        command.Parameters.Add(new SqlParameter("@LockoutType", SqlDbType.NVarChar) { Value = filterArguments[filter.LockoutTypeParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }

        protected override async Task DoDeleteAsync(AllVehicleUnlocksViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<AllVehicleUnlocksViewDataObject> DoGetAsync(AllVehicleUnlocksViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetAllVehicleUnlocks", connection))
                {
                    AllVehicleUnlocksViewDataObject result = null;

                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@VehicleLockoutId", SqlDbType.UniqueIdentifier) { Value = entity.Id });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                result = _serviceProvider.GetRequiredService<AllVehicleUnlocksViewDataObject>();
                                result.IsNew = false;

                                // IMPORTANT: We must set values on the 'result' object instead of the input 'entity' parameter
                                // because only the 'result' object is tracked by the context and returned from this method.
                                // Setting values on 'entity' would lose the data since that object is not returned or tracked.
                                // This ensures all properties persist when displaying data in forms or popups.
                                
                                // Set all values on the result object
                                result.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                result.VehicleLockoutId = reader.GetGuid(reader.GetOrdinal("VehicleLockoutId"));
                                result.TimezoneAdjustedLockoutDatetime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedLockoutDatetime"));
                                result.TimezoneAdjustedUnlockDatetime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedUnlockDatetime"));
                                result.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));
                                result.UnlockedBy = reader.GetString(reader.GetOrdinal("UnlockedBy"));

                                context.AddObject(result);
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get AllChecklistResultViewDataObject data", "Unable to get AllChecklistResultViewDataObject data", ex);
                    }
                }
            }
        }

        protected override async Task<DataObjectCollection<AllVehicleUnlocksViewDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<AllVehicleUnlocksViewDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetAllVehicleUnlocks", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }
                    if (filter.HasLockoutType)
                    {
                        command.Parameters.Add(new SqlParameter("@LockoutType", SqlDbType.NVarChar) { Value = filterArguments[filter.LockoutTypeParameterNumber] });
                    }

                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<AllVehicleUnlocksViewDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, TimeSlot, NumberOfRedImpacts, and NumberOfAmberImpacts
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.VehicleLockoutId = reader.GetGuid(reader.GetOrdinal("VehicleLockoutId"));
                                    entity.TimezoneAdjustedLockoutDatetime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedLockoutDatetime"));
                                    entity.TimezoneAdjustedUnlockDatetime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedUnlockDatetime"));

                                    var ord = reader.GetOrdinal("DealerId");
                                    entity.DealerId = reader.IsDBNull(ord) ? (Guid?)null : reader.GetGuid(ord);

                                    entity.UnlockedBy = reader.GetString(reader.GetOrdinal("UnlockedBy"));
                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get IncompletedChecklist data", "Unable to get IncompletedChecklist data", ex);
                    }
                }
            }
        }

        protected override async Task<AllVehicleUnlocksViewDataObject> DoSaveAsync(AllVehicleUnlocksViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
