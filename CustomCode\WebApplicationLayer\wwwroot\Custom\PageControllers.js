////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

/**
 * The Custom Folder allow you to set Custom Code to extend your application.
 * Every Custom files you will create should have a name ending by : ".custom.js"
 * Custom Code for Routes should be set in  : 
 * /Custom/Application/Routes.custom.js
 *
 * Custom Code for SourceHandling should be set in :
 * /Custom/Application/SourceHandler.custom.js
 * This file will allow you to load specific files when accessing to a page.
 *
 * Custom Code for Custom Fields Providers should be set in : 
 * /Custom/Model/CustomFields/MyEntityCustomFields.custom.js
 * Note : A stub has been generated once you have create a Custom Field on you entity
 *
 * Custom Code for Entity Factories should be set in :
 * /Custom/Model/DataObjects/MyEntityObject.custom.js
 *
 * To extend the behavior of your pages. Put your code in : 
 * /Custom/Controllers/MyPage.custom.js
 *
 *
 * How to extend Behavior of Validators
 * In each DataObjectValidator, there is a call of the specific CustomValidator : FleetXQ.Web.Model.DataObjects.Validation.MyEntityValidatorCustom
 * In this function, you can define new functions:
 * bool validate(MyEntityDataObject dataobject)
 *	Return wether or not the validation should continue
 * void OnAfterValidate(MyEntityDataObject dataobject)
 *	You can modify the behavior after the validation.
 * void validateMyEntity(MyEntityDataObject dataobject)
 *	Generative Objects didn't provided enough tools to write your Custom Validation Rule ? Just put your Custom Validation here.
 *
 * 
 * To extend the behavior of you ViewModels. Put your code in : 
 * /Custom/ViewModels/(Feature/MyEntity)/(MyEntity/Feature)(Form|Grid|List)ViewModel.custom.js
 * You are free to chose the name of your field.
 * 
 * Rules to merge the content of your custom code can be founded in FleetXQ.Application.Web.csproj.
 * Involved Targets are : Merge_CustomFields / Merge_ViewModels / Merge_Controllers
 *
 **/
(function () {
    FleetXQ.Web.Controllers.BroadcastMessageReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds ? AllowedSiteIds.replace(/[{} ]/g, '').split(',') : [];
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;

            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)';
                configuration.filterParameters = '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
                parameterCount++;
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += '(Driver.Person.SiteId == @' + parameterCount + ' OR Vehicle.SiteId == @' + parameterCount + ')';
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
                parameterCount++;
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            console.log('[Debug] getConfiguration called');
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];

            var filter = self.controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            console.log('[Debug] Filter data:', {
                CustomerId: filter.CustomerId(),
                SiteId: filter.SiteId(),
                DepartmentId: filter.DepartmentId(),
                MultiSearch: filter.MultiSearch(),
                StartTime: filter.StartTime(),
                EndTime: filter.EndTime(),
                Type: filter.Type()
            });

            var parameterCount = 0;
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            console.log('[Debug] Current user CustomerId:', customerId);

            // For non-admin users (with CustomerId), if no customer filter is selected, use default config
            if (filter.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            // Handle Customer filter - for admin users or when specific customer is selected
            if (filter.CustomerId()) {
                configuration.filterPredicate += "(Driver.Person.CustomerId == @" + parameterCount + " OR Vehicle.CustomerId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.CustomerId()
                });
                parameterCount++;
            }

            // Handle Site filter
            if (filter.SiteId()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.SiteId == @" + parameterCount + " OR Vehicle.SiteId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.SiteId()
                });
                parameterCount++;
            }

            // Handle Department filter
            if (filter.DepartmentId()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.DepartmentId == @" + parameterCount + " OR Vehicle.DepartmentId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.DepartmentId()
                });
                parameterCount++;
            }

            // Handle MultiSearch (Person's FullName)
            if (filter.MultiSearch()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.FirstName.Contains(@" + parameterCount + ") || Driver.Person.LastName.Contains(@" + (parameterCount + 1) + "))";
                configuration.filterParameters.push({
                    "TypeName": "System.String",
                    "IsNullable": true,
                    "Value": filter.MultiSearch()
                });
                configuration.filterParameters.push({
                    "TypeName": "System.String",
                    "IsNullable": true,
                    "Value": filter.MultiSearch()
                });
                parameterCount += 2;
            }

            // Handle StartTime filter
            if (filter.StartTime()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "SentTime >= @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.DateTime",
                    "IsNullable": false,
                    "Value": filter.StartTime()
                });
                parameterCount++;
            }

            // Handle EndTime filter
            if (filter.EndTime()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "SentTime <= @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.DateTime",
                    "IsNullable": false,
                    "Value": filter.EndTime()
                });
                parameterCount++;
            }

            // Handle Type filter
            if (filter.Type() !== null) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "Type = @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.Int32",
                    "IsNullable": false,
                    "Value": filter.Type()
                });
                parameterCount++;
            }

            // Convert parameters to string at the end
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            console.log('[Debug] Final configuration:', {
                filterPredicate: configuration.filterPredicate,
                filterParameters: configuration.filterParameters
            });

            return configuration;
        };

        this.initialize = function () {
            console.log('[Debug] Initializing BroadcastMessageReportPageController');
            // Add filterData function to the filter form view model
            self.controller.BroadcastMessageHistoryFilterFormViewModel.filterData = function () {
                try {
                    console.log('[Debug] filterData called');
                    var configuration = self.getConfiguration();

                    // Use the same configuration for both export and grid
                    self.controller.BroadcastMessageHistoryGridViewModel.exportFilterPredicate = configuration.filterPredicate;
                    self.controller.BroadcastMessageHistoryGridViewModel.exportFilterParameters = configuration.filterParameters;

                    // Load the grid data
                    self.controller.BroadcastMessageHistoryGridViewModel.LoadBroadcastMessageHistoryObjectCollection(configuration);
                } catch (e) {
                    console.error("[Error] Error filtering data:", e);
                }
            };

            // Load initial data for customer users
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            console.log('[Debug] Initial load - CustomerId:', customerId);
            if (customerId != null) {
                var configuration = self.getDefaultConfiguration();
                self.controller.BroadcastMessageHistoryGridViewModel.LoadBroadcastMessageHistoryObjectCollection(configuration);
            } else {
                // For admin users, load with empty configuration to see all data
                var configuration = {
                    filterPredicate: '',
                    filterParameters: '[]'
                };
                self.controller.BroadcastMessageHistoryGridViewModel.LoadBroadcastMessageHistoryObjectCollection(configuration);
            }

            self.controller.IsInEditMode = function () {
                return false;
            }
        };
    }
}());
(function () {

    FleetXQ.Web.Controllers.CurrentStatusReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;
        this.IoTHubManagerProxy = new FleetXQ.Web.Model.Components.IoTHubManagerProxy(this.ObjectsDataSet);

        this.getDefaultConfiguration = function (forGrid = false) {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += !forGrid ? 'CustomerId == @' + parameterCount++ :  'Driver.Person.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += !forGrid ? 'SiteId == @' + parameterCount++ : 'Driver.Person.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            configuration.filterPredicate = "CustomerId == @0 && SiteId == @1 && DepartmentId == @2";
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.CustomerId() ? '"' + currentData.CustomerId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.SiteId() ? '"' + currentData.SiteId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.DepartmentId() ? '"' + currentData.DepartmentId() + '"' : 'null') + ' }]';
            return configuration;
        };

        this.LoadCurrentStatusDriverViewGridViewData = function () {
            var configuration = {
            };

            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            var parameterCount = 0;

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration(true);
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection(defaultConfig);
                var vehicleConfiguration = JSON.parse(JSON.stringify(defaultConfig));
                vehicleConfiguration.filterPredicate = vehicleConfiguration.filterPredicate.replace(/Driver\.Person/g, 'Vehicle');
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection(vehicleConfiguration);
                return;
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'Driver.Person.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                // configuration.filterPredicate += 'Driver.Person.PersonAllocationItems.Where(SiteId == @' + parameterCount++ +').Any()';
                configuration.filterPredicate += 'Driver.Person.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'Driver.Person.DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection(configuration);

            //if (currentData.CustomerId() != null) {
            //    var vehicleConfiguration = JSON.parse(JSON.stringify(configuration));
            //    vehicleConfiguration.filterPredicate = vehicleConfiguration.filterPredicate.replace(/Driver\.Person/g, 'Vehicle');
            //    self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection(vehicleConfiguration);
            //    return;
            //}
        };

        this.LoadCurrentStatusVehicleViewGridViewData = function () {
            var configuration = {
            };
        
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
        
            var parameterCount = 0;
        
            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }
        
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
        
            // self.controller.CurrentStatusVehicleViewGridViewModel.filterPredicate = configuration.filterPredicate;
            // self.controller.CurrentStatusVehicleViewGridViewModel.filterParameters =configuration.filterParameters;

            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection(configuration);
        };
        
        
        this.loadReportData = function () {
            var configuration = this.getConfiguration();
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.CurrentVehicleStatusChartViewReportViewModel.LoadCurrentVehicleStatusChartViewObjectCollection(defaultConfig);
                return;
            }

            self.controller.CurrentVehicleStatusChartViewReportViewModel.LoadCurrentVehicleStatusChartViewObjectCollection(configuration);
        };

        /**
         * Helper method to update IoT device connection status
         * This method queries the IoT Hub to get the connection status of all devices
         * based on the current filter criteria (customer, site, department)
         * @returns {Promise} - Promise that resolves when the update is complete
         */
        this.updateIoTDeviceConnectionStatus = async function() {
            // Set busy indicators to show loading state
            self.controller.DashboardFilterFormViewModel.StatusData.IsBusy(true);
            self.controller.CurrentStatusCombinedViewFormViewModel.StatusData.IsBusy(true);
            
            // Create configuration object for the API call
            var configuration = {};
            
            // Get current filter values from the dashboard filter
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            
            // Use filter values if available, otherwise fall back to user claims or empty GUID
            // Priority: Filter value > User claim > Empty GUID
            var customerId = currentData.CustomerId() || 
                self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId || 
                "00000000-0000-0000-0000-000000000000";
                
            var siteId = currentData.SiteId() || "00000000-0000-0000-0000-000000000000";
            var departmentId = currentData.DepartmentId() || "00000000-0000-0000-0000-000000000000";
            
            // Add IDs to configuration object
            configuration.customerId = customerId;
            configuration.siteId = siteId;
            configuration.departmentId = departmentId;
            
            // Success handler - clears busy indicators when complete
            configuration.successHandler = function (result) {
                self.controller.DashboardFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.CurrentStatusCombinedViewFormViewModel.StatusData.IsBusy(false);

                // Then load grid and report data with updated connection status
                self.LoadCurrentStatusVehicleViewGridViewData();
                self.LoadCurrentStatusDriverViewGridViewData();
                self.loadReportData();
            };
            
            // Error handler - shows error popup and clears busy indicators
            configuration.errorHandler = function () {
                self.controller.applicationController.showAlertPopup(
                    self.controller, 
                    "Failed to update vehicle status", 
                    "Error", 
                    null, 
                    self.controller.contextId
                );
                self.controller.DashboardFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.CurrentStatusCombinedViewFormViewModel.StatusData.IsBusy(false);
            }; 
            
            // Call the API to update device connection status
            return self.IoTHubManagerProxy.GetAllDevicesTwinConnection(configuration);
        };

        /**
         * Load initial data when the page loads
         * For users with customer ID, updates IoT status before loading data
         */
        this.loadInitialData = async function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            
            if (customerId != null) {
                self.LoadCurrentStatusDriverViewGridViewData();
                self.loadReportData();
                return;
            }
            if (!GO.Filter.hasUrlFilter(self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.FILTER_NAME, self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel)) {
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection();
            }

            if (!GO.Filter.hasUrlFilter(self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.FILTER_NAME, self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel)) {
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection();
            }
        }

        /**
         * Initialize the controller
         * Sets up event handlers and loads initial data
         */
        this.initialize = async function () {
            // Prevent confirmation dialog when changing page
            // This avoids asking to confirm changing page and lose changes
            // (caused by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            };

            // // Handle page reload to ensure hash is set correctly
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            /**
             * Modified filterData function to update IoT connection status before loading grid data
             * This ensures we have the latest connection status before displaying data
             */
            self.controller.DashboardFilterFormViewModel.filterData = async function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.DashboardFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.DashboardFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                try {
                    // First update IoT device connection status
                    await self.updateIoTDeviceConnectionStatus();
                } catch (error) {
                    console.error("Error in filterData:", error);
                    // Continue with data loading even if IoT status update fails
                    self.LoadCurrentStatusVehicleViewGridViewData();
                    self.LoadCurrentStatusDriverViewGridViewData();
                    self.loadReportData();
                }
            };
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.DashboardtobedeletedPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        // Add data stores
        this.dashboardDriverCardStoreProcedureDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DashboardDriverCardStoreProcedure');
        this.dashboardVehicleCardStoreProcedureDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DashboardVehicleCardStoreProcedure');
        this.driverLicenseExpiryViewDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DriverLicenseExpiryView');
        this.todaysPreopCheckDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'TodaysPreopCheckView');
        this.todaysImpactDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'TodaysImpactView');
        this.vehicleUtilizationDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'VehicleUtilizationLastTwelveHoursView');

        function formatDateForSqlServer(date) {
            // Format date as 'YYYY-MM-DD HH:mm:ss' in local time
            var localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
            return localDate.toISOString().slice(0, 19).replace('T', ' ');
        }

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
            var customerId = userClaims.CustomerId;
            var AllowedSiteIds = userClaims.AllowedSiteIds;

            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds ? AllowedSiteIds.replace(/[{} ]/g, '').split(',') : [];
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;

            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : "' + customerId + '" }';
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            var defaultDate = new Date();  // Get current date
            defaultDate.setHours(0, 0, 0, 0);  // Set time to midnight (12 AM)
            console.log('[Default Config] Using default date:', defaultDate);

            // Add default reference date parameter
            configuration.parameters = JSON.stringify({
                ReferenceDate: {
                    TypeName: "System.DateTime",
                    IsNullable: false,
                    Value: formatDateForSqlServer(defaultDate)
                }
            });

            return configuration;
        };

        this.getConfiguration = function () {
            var currentData = self.controller.MainDashboardFilterFormViewModel.CurrentObject().Data;
            var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
            var customerId = userClaims.CustomerId;

            // If no filter is applied, return default configuration
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            var configuration = {};
            var parameterCount = 0;
            var filterPredicates = [];
            var filterParameters = [];

            var effectiveCustomerId = currentData.CustomerId() || customerId;
            if (effectiveCustomerId != null) {
                filterPredicates.push('CustomerId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": effectiveCustomerId
                });
                parameterCount++;
            }

            if (currentData.SiteId() != null) {
                filterPredicates.push('SiteId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": currentData.SiteId()
                });
                parameterCount++;
            }

            if (currentData.DepartmentId() != null) {
                filterPredicates.push('DepartmentId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": currentData.DepartmentId()
                });
                parameterCount++;
            }

            // Handle the date parameter
            var selectedDate = currentData.Date();
            console.log('[Filter Config] Initial selected date:', selectedDate);

            if (selectedDate) {
                // Ensure we're working with a Date object
                if (typeof selectedDate === 'string') {
                    selectedDate = new Date(selectedDate);
                }
                // Keep the time component as is from the selected date
                console.log('[Filter Config] Using selected date with its time:', selectedDate);
            } else {
                // Use current date at midnight
                selectedDate = new Date();
                selectedDate.setHours(0, 0, 0, 0);  // Set time to midnight (12 AM)
                console.log('[Filter Config] Using current date at midnight:', selectedDate);
            }

            // Add reference date parameter
            filterPredicates.push('ReferenceDate == @' + parameterCount);
            filterParameters.push({
                "TypeName": "System.DateTime",
                "IsNullable": false,
                "Value": formatDateForSqlServer(selectedDate)
            });

            // Combine predicates with AND
            configuration.filterPredicate = filterPredicates.join(' && ');
            configuration.filterParameters = JSON.stringify(filterParameters);

            console.log('[Filter Config] Final configuration:', configuration);
            return configuration;
        };

        this.loadDashboardData = function () {
            // Set all forms to busy state
            self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(true);
            self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(true);
            self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(true);

            var baseConfiguration = {
                contextId: self.controller.contextId,
                errorHandler: function (error) {
                    console.error('Error loading dashboard data:', error);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.ShowError(error);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.ShowError(error);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.ShowError(error);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(false);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(false);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(false);
                }
            };

            var config = self.getConfiguration();
            console.log('[Load Data] Configuration being sent:', config);

            // Driver Card Configuration
            var driverConfig = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received driver data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardDriverCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardDriverCardStoreProcedureFormViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.SetDashboardDriverCardStoreProcedureObject(content);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardDriverCardStoreProcedureFormViewModel.include
            };

            // Vehicle Card Configuration
            var vehicleConfig = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received vehicle data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardVehicleCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardVehicleCardStoreProcedureFormViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.SetDashboardVehicleCardStoreProcedureObject(content);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardVehicleCardStoreProcedureFormViewModel.include
            };

            // Vehicle Card Form1 Configuration
            var vehicleForm1Config = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received vehicle form1 data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardVehicleCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.SetDashboardVehicleCardStoreProcedureObject(content);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.include
            };

            console.log('[Load Data] Final configurations - Driver:', driverConfig, 'Vehicle:', vehicleConfig, 'Vehicle Form1:', vehicleForm1Config);

            // Load all card data
            self.dashboardDriverCardStoreProcedureDataStore.LoadObjectCollection(driverConfig);
            self.dashboardVehicleCardStoreProcedureDataStore.LoadObjectCollection(vehicleConfig);
            self.dashboardVehicleCardStoreProcedureDataStore.LoadObjectCollection(vehicleForm1Config);
        };

        this.loadChartData = function () {
            var config = self.getConfiguration();
            console.log('[loadChartData] Starting with configuration:', config);

            var baseConfiguration = {
                contextId: self.controller.contextId,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                errorHandler: function (error) {
                    console.error('[loadChartData] Error:', error);
                }
            };

            // Load chart data with error handling
            try {
                // Load data using the correct data stores
                console.log('[loadChartData] Loading TodaysPreopCheck data...');
                self.todaysPreopCheckDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] TodaysPreopCheck data received:', data);
                        self.controller.TodaysPreopCheckReportViewModel.OnTodaysPreopCheckViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading VehicleUtilization data...');
                self.vehicleUtilizationDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] VehicleUtilization data received:', data);
                        self.controller.VehicleUtilizationLastTwelveHoursViewReportViewModel.OnVehicleUtilizationLastTwelveHoursViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading TodaysImpact data...');
                self.todaysImpactDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] TodaysImpact data received:', data);
                        self.controller.TodaysImpactViewReportViewModel.OnTodaysImpactViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading DriverLicenseExpiry data...');
                self.driverLicenseExpiryViewDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] DriverLicenseExpiry data received:', data);
                        self.controller.DriverLicenseExpiryViewReportViewModel.OnDriverLicenseExpiryViewObjectCollectionLoaded(data);
                    }
                });
            } catch (error) {
                console.error('[loadChartData] Error loading chart data:', error);
            }
        };

        this.initialize = function () {
            // to avoid having the message asking to confirm changing page and lose changes
            self.controller.IsInEditMode = function () {
                return false;
            };

            // Initialize filter data function
            self.controller.MainDashboardFilterFormViewModel.filterData = function () {
                self.loadDashboardData();
                self.loadChartData();
            };

            // Load initial data with a small delay to ensure filter is initialized
            setTimeout(function() {
                console.log('[Initialize] Loading initial data through filter');
                self.controller.MainDashboardFilterFormViewModel.filterData();
            }, 100);
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.DriverAccessAbuseReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.DriverAccessAbuseFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                var startDate = new Date(currentData.StartDate());
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                var startDate = new Date(currentData.StartDate());
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                var startDate = new Date(currentData.StartDate());
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                configuration.filterPredicate += `StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                configuration.filterPredicate += `EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.DriverAccessAbuseFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);
            self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel.LoadAllDriverAccessAbuseStoreProcedureObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel.LoadAllDriverAccessAbuseStoreProcedureObjectCollection(configuration);
                    return;
                }
				self.controller.AllDriverAccessAbuseStoreProcedureGridViewModel.LoadAllDriverAccessAbuseStoreProcedureObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.DriverAccessAbuseFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.DriverAccessAbuseFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.DriverAccessAbuseFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };


            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.EmailSubscriptionReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.EmailSubscriptionReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.EmailSubscriptionReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);
            self.controller.AllEmailSubscriptionStoreProcedureGridViewModel.LoadAllEmailSubscriptionStoreProcedureObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllEmailSubscriptionStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllEmailSubscriptionStoreProcedureGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllEmailSubscriptionStoreProcedureGridViewModel.LoadAllEmailSubscriptionStoreProcedureObjectCollection(configuration);
                    return;
                }
				self.controller.AllEmailSubscriptionStoreProcedureGridViewModel.LoadAllEmailSubscriptionStoreProcedureObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.EmailSubscriptionReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.EmailSubscriptionReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.EmailSubscriptionReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };


            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.FleetDashboardPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        // Add data stores
        this.dashboardDriverCardStoreProcedureDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DashboardDriverCardStoreProcedure');
        this.dashboardVehicleCardStoreProcedureDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DashboardVehicleCardStoreProcedure');
        this.driverLicenseExpiryViewDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DriverLicenseExpiryView');
        this.todaysPreopCheckDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'TodaysPreopCheckView');
        this.todaysImpactDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'TodaysImpactView');
        this.vehicleUtilizationDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'VehicleUtilizationLastTwelveHoursView');

        function formatDateForSqlServer(date) {
            // Format date as 'YYYY-MM-DD HH:mm:ss' in local time
            var localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
            return localDate.toISOString().slice(0, 19).replace('T', ' ');
        }

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
            var customerId = userClaims.CustomerId;
            var AllowedSiteIds = userClaims.AllowedSiteIds;

            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds ? AllowedSiteIds.replace(/[{} ]/g, '').split(',') : [];
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;

            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : "' + customerId + '" }';
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            var defaultDate = new Date();  // Get current date
            defaultDate.setHours(0, 0, 0, 0);  // Set time to midnight (12 AM)
            console.log('[Default Config] Using default date:', defaultDate);

            // Add default reference date parameter
            configuration.parameters = JSON.stringify({
                ReferenceDate: {
                    TypeName: "System.DateTime",
                    IsNullable: false,
                    Value: formatDateForSqlServer(defaultDate)
                }
            });

            return configuration;
        };

        this.getConfiguration = function () {
            var currentData = self.controller.MainDashboardFilterFormViewModel.CurrentObject().Data;
            var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
            var customerId = userClaims.CustomerId;

            // If no filter is applied, return default configuration
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            var configuration = {};
            var parameterCount = 0;
            var filterPredicates = [];
            var filterParameters = [];

            var effectiveCustomerId = currentData.CustomerId() || customerId;
            if (effectiveCustomerId != null) {
                filterPredicates.push('CustomerId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": effectiveCustomerId
                });
                parameterCount++;
            }

            if (currentData.SiteId() != null) {
                filterPredicates.push('SiteId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": currentData.SiteId()
                });
                parameterCount++;
            }

            if (currentData.DepartmentId() != null) {
                filterPredicates.push('DepartmentId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": currentData.DepartmentId()
                });
                parameterCount++;
            }

            // Handle the date parameter
            var selectedDate = currentData.Date();
            console.log('[Filter Config] Initial selected date:', selectedDate);

            if (selectedDate) {
                // Ensure we're working with a Date object
                if (typeof selectedDate === 'string') {
                    selectedDate = new Date(selectedDate);
                }
                // Keep the time component as is from the selected date
                console.log('[Filter Config] Using selected date with its time:', selectedDate);
            } else {
                // Use current date at midnight
                selectedDate = new Date();
                selectedDate.setHours(0, 0, 0, 0);  // Set time to midnight (12 AM)
                console.log('[Filter Config] Using current date at midnight:', selectedDate);
            }

            // Add reference date parameter
            filterPredicates.push('ReferenceDate == @' + parameterCount);
            filterParameters.push({
                "TypeName": "System.DateTime",
                "IsNullable": false,
                "Value": formatDateForSqlServer(selectedDate)
            });

            // Combine predicates with AND
            configuration.filterPredicate = filterPredicates.join(' && ');
            configuration.filterParameters = JSON.stringify(filterParameters);

            console.log('[Filter Config] Final configuration:', configuration);
            return configuration;
        };

        this.loadDashboardData = function () {
            // Set all forms to busy state
            self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(true);
            self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(true);
            self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(true);

            var baseConfiguration = {
                contextId: self.controller.contextId,
                errorHandler: function (error) {
                    console.error('Error loading dashboard data:', error);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.ShowError(error);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.ShowError(error);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.ShowError(error);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(false);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(false);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(false);
                }
            };

            var config = self.getConfiguration();
            console.log('[Load Data] Configuration being sent:', config);

            // Driver Card Configuration
            var driverConfig = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received driver data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardDriverCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardDriverCardStoreProcedureFormViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.SetDashboardDriverCardStoreProcedureObject(content);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardDriverCardStoreProcedureFormViewModel.include
            };

            // Vehicle Card Configuration
            var vehicleConfig = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received vehicle data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardVehicleCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardVehicleCardStoreProcedureFormViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.SetDashboardVehicleCardStoreProcedureObject(content);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardVehicleCardStoreProcedureFormViewModel.include
            };

            // Vehicle Card Form1 Configuration
            var vehicleForm1Config = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received vehicle form1 data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardVehicleCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.SetDashboardVehicleCardStoreProcedureObject(content);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.include
            };

            console.log('[Load Data] Final configurations - Driver:', driverConfig, 'Vehicle:', vehicleConfig, 'Vehicle Form1:', vehicleForm1Config);

            // Load all card data
            self.dashboardDriverCardStoreProcedureDataStore.LoadObjectCollection(driverConfig);
            self.dashboardVehicleCardStoreProcedureDataStore.LoadObjectCollection(vehicleConfig);
            self.dashboardVehicleCardStoreProcedureDataStore.LoadObjectCollection(vehicleForm1Config);
        };

        this.loadChartData = function () {
            var config = self.getConfiguration();
            console.log('[loadChartData] Starting with configuration:', config);

            var baseConfiguration = {
                contextId: self.controller.contextId,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                errorHandler: function (error) {
                    console.error('[loadChartData] Error:', error);
                }
            };

            // Load chart data with error handling
            try {
                // Load data using the correct data stores
                console.log('[loadChartData] Loading TodaysPreopCheck data...');
                self.todaysPreopCheckDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] TodaysPreopCheck data received:', data);
                        self.controller.TodaysPreopCheckReportViewModel.OnTodaysPreopCheckViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading VehicleUtilization data...');
                self.vehicleUtilizationDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] VehicleUtilization data received:', data);
                        self.controller.VehicleUtilizationLastTwelveHoursViewReportViewModel.OnVehicleUtilizationLastTwelveHoursViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading TodaysImpact data...');
                self.todaysImpactDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] TodaysImpact data received:', data);
                        self.controller.TodaysImpactViewReportViewModel.OnTodaysImpactViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading DriverLicenseExpiry data...');
                self.driverLicenseExpiryViewDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] DriverLicenseExpiry data received:', data);
                        self.controller.DriverLicenseExpiryViewReportViewModel.OnDriverLicenseExpiryViewObjectCollectionLoaded(data);
                    }
                });
            } catch (error) {
                console.error('[loadChartData] Error loading chart data:', error);
            }
        };

        this.initialize = function () {
            // to avoid having the message asking to confirm changing page and lose changes
            self.controller.IsInEditMode = function () {
                return false;
            };

            // Initialize filter data function
            self.controller.MainDashboardFilterFormViewModel.filterData = function () {
                self.loadDashboardData();
                self.loadChartData();
            };

            // Load initial data with a small delay to ensure filter is initialized
            setTimeout(function () {
                console.log('[Initialize] Loading initial data through filter');
                self.controller.MainDashboardFilterFormViewModel.filterData();
            }, 100);
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.GeneralProductivityReportPagePageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getGeneralProductivityConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject().Data;
        
            var parameterIndex = 0; // Start indexing after the initial three parameters.
        
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }
        
            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1;
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate += ` && SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1;
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += ` && DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1;
            }
        
            // Only add dates to the filter if they are actually set
            if (currentData.StartDate() != null && currentData.StartDate() !== "") {
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1;
            }
        
            if (currentData.EndDate() != null && currentData.EndDate() !== "") {
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1;
            }
        
            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);
        
            return configuration;
        };
        

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadGeneralProductivityPerVehicleViewData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection(configuration);
        };

        this.loadGeneralProductivityPerDriverViewData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection(configuration);
        };
        
        // Load unit utilization data (utilized units)
        this.loadUnitUtilisationData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.LoadUnitUtilisationStoreProcedureObjectCollection(configuration);
        };

        // Load unit unutilization data (unutilized units)
        this.loadUnitUnutilisationData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.LoadUnitUnutilisationStoreProcedureObjectCollection(configuration);
        };
        
        this.loadReportData = function () {
            var configuration = this.getGeneralProductivityConfiguration();
            self.controller.LoggedHoursVersusSeatHoursViewReportViewModel.LoadLoggedHoursVersusSeatHoursViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection(defaultConfig);
                
                var defaultConfig2 = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection(defaultConfig2);
                
                // Load unit utilization and unutilization data with default configuration
                var defaultConfig3 = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.LoadUnitUtilisationStoreProcedureObjectCollection(defaultConfig3);
                
                var defaultConfig4 = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.LoadUnitUnutilisationStoreProcedureObjectCollection(defaultConfig4);
                
                return;
            }
            
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection();
            }
            
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection();
            }
            
            // Load unit utilization and unutilization data
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.LoadUnitUtilisationStoreProcedureObjectCollection();
            }
            
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.LoadUnitUnutilisationStoreProcedureObjectCollection();
            }
        };


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter
            self.controller.GeneralProductivityReportFilterFormViewModel.filterData = function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.GeneralProductivityReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }

                // The validation is now done in the Commands.FilterDataCommand
                self.loadGeneralProductivityPerDriverViewData();
                self.loadGeneralProductivityPerVehicleViewData();
                self.loadReportData();
                // Also load unit utilization data when filter is applied
                self.loadUnitUtilisationData();
                self.loadUnitUnutilisationData();

                // save filter data to the GeneralProductivityViewFormViewModel so that it can be used by the "Show Sessions" popup.
                var currentData = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject().Data;

                // Handle StartDate: set or clear it properly
                if (currentData.StartDate() && currentData.StartDate() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate = currentData.StartDate();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.StartDate = currentData.StartDate();
                } else {
                    // Clear the stored StartDate when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.StartDate = null;
                }
                
                // Handle EndDate: set or clear it properly
                if (currentData.EndDate() && currentData.EndDate() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate = currentData.EndDate();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.EndDate = currentData.EndDate();
                } else {
                    // Clear the stored EndDate when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.EndDate = null;
                }
                
                // Handle CustomerId: set or clear it properly
                if (currentData.CustomerId() && currentData.CustomerId() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.CustomerId = currentData.CustomerId();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.CustomerId = currentData.CustomerId();
                } else {
                    // Clear the stored CustomerId when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.CustomerId = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.CustomerId = null;
                }
                
                // Handle SiteId: set or clear it properly
                if (currentData.SiteId() && currentData.SiteId() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.SiteId = currentData.SiteId();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.SiteId = currentData.SiteId();
                } else {
                    // Clear the stored SiteId when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.SiteId = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.SiteId = null;
                }
                
                // Handle DepartmentId: set or clear it properly
                if (currentData.DepartmentId() && currentData.DepartmentId() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.DepartmentId = currentData.DepartmentId();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.DepartmentId = currentData.DepartmentId();
                } else {
                    // Clear the stored DepartmentId when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.DepartmentId = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.DepartmentId = null;
                }
            };

            // Uncomment if you want to load data on page initialization
            // self.loadInitialGridData();
            // self.loadReportData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.ImpactReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.ImpactReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.ImpactLevel() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && ImpactLevel == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Int32", "IsNullable": false, "Value": currentData.ImpactLevel() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.ImpactReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
        
        
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // add the filter for multi search to the configuration
            configuration = this.addMultiSearchFilter(configuration);
            self.controller.ImpactFrequencyPerWeekMonthViewReportViewModel.LoadImpactFrequencyPerWeekMonthViewObjectCollection(configuration);
            self.controller.ImpactFrequencyPerWeekDayViewReportViewModel.LoadImpactFrequencyPerWeekDayViewObjectCollection(configuration);
            self.controller.ImpactFrequencyPerTimeSlotViewReportViewModel.LoadImpactFrequencyPerTimeSlotViewObjectCollection(configuration);

            self.controller.AllImpactsViewGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllImpactsViewGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllImpactsViewGridViewModel.LoadAllImpactsViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllImpactsViewGridViewModel.FILTER_NAME, self.controller.AllImpactsViewGridViewModel)) {
                if (customerId != null) {
                    var configuration = self.getConfiguration();
                    self.controller.AllImpactsViewGridViewModel.LoadAllImpactsViewObjectCollection(configuration);
                    return;
                }
				self.controller.AllImpactsViewGridViewModel.LoadAllImpactsViewObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.ImpactReportFilterFormViewModel.filterData = function () {

                const userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
                const isDealerAdmin = userClaims.role?.includes('DealerAdmin');
                const customerId = self.controller.ImpactReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                
                if (isDealerAdmin && (!customerId || customerId === "")) {
                    // Use ShowError to display validation message
                    self.controller.ImpactReportFilterFormViewModel.ShowError("Please select a customer", "Error");
                    return; // Prevent filtering
                }

                // The validation is now done in the Commands.FilterDataCommand
                self.loadPageData(false);
            };

            // self.loadPageData();
            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.LicenseExpiryReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.LicenseExpiryReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.LicenseType() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += `LicenseType == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Int32", "IsNullable": false, "Value": currentData.LicenseType() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.LicenseExpiryReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            self.controller.AllLicenseExpiryViewGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllLicenseExpiryViewGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllLicenseExpiryViewGridViewModel.LoadAllLicenseExpiryViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllLicenseExpiryViewGridViewModel.FILTER_NAME, self.controller.AllLicenseExpiryViewGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllLicenseExpiryViewGridViewModel.LoadAllLicenseExpiryViewObjectCollection(configuration);
                    return;
                }
				self.controller.AllLicenseExpiryViewGridViewModel.LoadAllLicenseExpiryViewObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.LicenseExpiryReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.LicenseExpiryReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.LicenseExpiryReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };


            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.MachineUnlockReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        // Store the original filterData function
        var originalFilterData = null;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.MachineUnlockReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.Unlock() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && LockoutType == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Int32", "IsNullable": false, "Value": currentData.Unlock() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.MachineUnlockReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            self.controller.AllVehicleUnlocksViewGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllVehicleUnlocksViewGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllVehicleUnlocksViewGridViewModel.LoadAllVehicleUnlocksViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllVehicleUnlocksViewGridViewModel.FILTER_NAME, self.controller.AllVehicleUnlocksViewGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllVehicleUnlocksViewGridViewModel.LoadAllVehicleUnlocksViewObjectCollection(configuration);
                    return;
                }
				self.controller.AllVehicleUnlocksViewGridViewModel.LoadAllVehicleUnlocksViewObjectCollection();
			}
        }

        this.initialize = function () {

            // Override the filterData function to include DealerAdmin validation
            self.controller.MachineUnlockReportFilterFormViewModel.filterData = function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.MachineUnlockReportFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.MachineUnlockReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };

            // Initialize other custom functionality
            var filterViewModel = self.controller.MachineUnlockReportFilterFormViewModel;
            
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.AllVehicleUnlocksViewGridViewModel.filterData = function () {
                self.loadPageData();
            }

            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.OnDemandAuthorisationReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.OnDemandAuthorisationFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.OnDemandAuthorisationFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // add the filter for multi search to the configuration
            configuration = this.addMultiSearchFilter(configuration);

            self.controller.OnDemandAuthorisationStoreProcedureGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.OnDemandAuthorisationStoreProcedureGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.OnDemandAuthorisationStoreProcedureGridViewModel.LoadOnDemandAuthorisationStoreProcedureObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.OnDemandAuthorisationStoreProcedureGridViewModel.FILTER_NAME, self.controller.OnDemandAuthorisationStoreProcedureGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.OnDemandAuthorisationStoreProcedureGridViewModel.LoadOnDemandAuthorisationStoreProcedureObjectCollection(configuration);
                    return;
                }
				self.controller.OnDemandAuthorisationStoreProcedureGridViewModel.LoadOnDemandAuthorisationStoreProcedureObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.OnDemandAuthorisationFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.OnDemandAuthorisationFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.OnDemandAuthorisationFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };
            
            
            // self.loadPageData();
            // self.loadInitialGridData();
        };
    };

})();
(function () {
    FleetXQ.Web.Controllers.PedestrianDetectionReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds ? AllowedSiteIds.replace(/[{} ]/g, '').split(',') : [];
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;

            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)';
                configuration.filterParameters = '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
                parameterCount++;
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += '(Driver.Person.SiteId == @' + parameterCount + ' OR Vehicle.SiteId == @' + parameterCount + ')';
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
                parameterCount++;
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            console.log('[Debug] getConfiguration called');
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];

            var filter = self.controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            console.log('[Debug] Filter data:', {
                CustomerId: filter.CustomerId(),
                SiteId: filter.SiteId(),
                DepartmentId: filter.DepartmentId(),
                MultiSearch: filter.MultiSearch(),
                StartTime: filter.StartTime(),
                EndTime: filter.EndTime()
            });

            var parameterCount = 0;
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            console.log('[Debug] Current user CustomerId:', customerId);

            // For non-admin users (with CustomerId), if no customer filter is selected, use default config
            if (filter.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            // Handle Customer filter - for admin users or when specific customer is selected
            if (filter.CustomerId()) {
                configuration.filterPredicate += "(Driver.Person.CustomerId == @" + parameterCount + " OR Vehicle.CustomerId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.CustomerId()
                });
                parameterCount++;
            }

            // Handle Site filter
            if (filter.SiteId()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.SiteId == @" + parameterCount + " OR Vehicle.SiteId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.SiteId()
                });
                parameterCount++;
            }

            // Handle Department filter
            if (filter.DepartmentId()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.DepartmentId == @" + parameterCount + " OR Vehicle.DepartmentId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.DepartmentId()
                });
                parameterCount++;
            }

            // Handle MultiSearch (Person's FullName)
            if (filter.MultiSearch()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.FirstName.Contains(@" + parameterCount + ") || Driver.Person.LastName.Contains(@" + (parameterCount + 1) + "))";
                configuration.filterParameters.push({
                    "TypeName": "System.String",
                    "IsNullable": true,
                    "Value": filter.MultiSearch()
                });
                configuration.filterParameters.push({
                    "TypeName": "System.String",
                    "IsNullable": true,
                    "Value": filter.MultiSearch()
                });
                parameterCount += 2;
            }

            // Handle StartTime filter
            if (filter.StartTime()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "Date >= @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.DateTime",
                    "IsNullable": false,
                    "Value": filter.StartTime()
                });
                parameterCount++;
            }

            // Handle EndTime filter
            if (filter.EndTime()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "Date <= @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.DateTime",
                    "IsNullable": false,
                    "Value": filter.EndTime()
                });
                parameterCount++;
            }

            // Convert parameters to string at the end
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            console.log('[Debug] Final configuration:', {
                filterPredicate: configuration.filterPredicate,
                filterParameters: configuration.filterParameters
            });

            return configuration;
        };

        this.initialize = function () {
            console.log('[Debug] Initializing PedestrianDetectionReportPageController');
            
            // Override IsInEditMode to always return false (prevent edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            };
            
            // Add filterData function to the filter form view model
            self.controller.PedestrianDetectionHistoryFilterFormViewModel.filterData = function () {
                try {
                    console.log('[Debug] filterData called');
                    var configuration = self.getConfiguration();

                    // Use the same configuration for both export and grid
                    self.controller.PedestrianDetectionHistoryGridViewModel.exportFilterPredicate = configuration.filterPredicate;
                    self.controller.PedestrianDetectionHistoryGridViewModel.exportFilterParameters = configuration.filterParameters;

                    // Load the grid data
                    self.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
                } catch (e) {
                    console.error("[Error] Error filtering data:", e);
                }
            };

            // Load initial data for customer users
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            console.log('[Debug] Initial load - CustomerId:', customerId);
            if (customerId != null) {
                var configuration = self.getDefaultConfiguration();
                self.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
            } else {
                // For admin users, load with empty configuration to see all data
                var configuration = {
                    filterPredicate: '',
                    filterParameters: '[]'
                };
                self.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
            }
        };
    }
}());
(function () {
    // Custom extension for PersonDetailPagePageController
    FleetXQ.Web.Controllers.PersonDetailPagePageControllerCustom = function (baseController) {
        this.baseController = baseController;

        this.initialize = function() {
            
           // Simplified IsInEditMode that just checks display mode
            FleetXQ.Web.Controllers.IsInEditMode = function() {
                var personFormVM = window?.ApplicationController?.viewModel?.pageController?.PersonFormViewModel;
                return personFormVM?.StatusData?.DisplayMode() === 'edit';
            };
        };
    };
})();
(function () {

    FleetXQ.Web.Controllers.PreOpCheckReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;


            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.PreOpReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.ResultType() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && ResultType == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Int32", "IsNullable": false, "Value": currentData.ResultType() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.PreOpReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ?
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{
                            "TypeName": "System.String",
                            "IsNullable": true,
                            "Value": currentData.MultiSearch()
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // add the filter for multi search to the configuration
            configuration = this.addMultiSearchFilter(configuration);
            self.controller.IncompletedChecklistViewReportViewModel.LoadIncompletedChecklistViewObjectCollection(configuration);
            self.controller.ChecklistStatusViewViewModel.LoadChecklistStatusViewObjectCollection(configuration);

            self.controller.AllChecklistResultViewGrid1ViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllChecklistResultViewGrid1ViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllChecklistResultViewGrid1ViewModel.LoadAllChecklistResultViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllChecklistResultViewGrid1ViewModel.FILTER_NAME, self.controller.AllChecklistResultViewGrid1ViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllChecklistResultViewGrid1ViewModel.LoadAllChecklistResultViewObjectCollection(configuration);
                    return;
                }
                self.controller.AllChecklistResultViewGrid1ViewModel.LoadAllChecklistResultViewObjectCollection();
            }
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');

            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // Store original filter function
            var originalFilterData = function () {
                self.loadPageData();
            };

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter
            self.controller.PreOpReportFilterFormViewModel.filterData = function () {
                // Check if user is a DealerAdmin and validate customer selection
                var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
                var isDealerAdmin = userClaims && userClaims.role && userClaims.role.includes('DealerAdmin');
                
                if (isDealerAdmin) {
                    var customerId = self.controller.PreOpReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    
                    if (!customerId || customerId === '') {
                        self.controller.PreOpReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                // If validation passes or not needed, proceed with filtering
                originalFilterData();
            };


            // self.loadPageData();
            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.ProficiencyReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.ProficiencyReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };
        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.ProficiencyReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadPageData = function () {
            var driverConfig = this.getConfiguration();
            // add the filter for multi search to the configuration
            driverConfig = this.addMultiSearchFilter(driverConfig);
            self.controller.ProficiencyCombinedViewFormViewModel.DriverProficiencyViewItemsGridViewModel.exportFilterPredicate = driverConfig.filterPredicate;
            self.controller.ProficiencyCombinedViewFormViewModel.DriverProficiencyViewItemsGridViewModel.exportFilterParameters = driverConfig.filterParameters;
            self.controller.ProficiencyCombinedViewFormViewModel.DriverProficiencyViewItemsGridViewModel.LoadDriverProficiencyViewObjectCollection(driverConfig);
            var vehicleConfig = this.getConfiguration();
            // add the filter for multi search to the configuration
            vehicleConfig = this.addMultiSearchFilter(vehicleConfig);

            self.controller.ProficiencyCombinedViewFormViewModel.VehicleProficiencyViewItemsGridViewModel.exportFilterPredicate = vehicleConfig.filterPredicate;
            self.controller.ProficiencyCombinedViewFormViewModel.VehicleProficiencyViewItemsGridViewModel.exportFilterParameters = vehicleConfig.filterParameters;
            self.controller.ProficiencyCombinedViewFormViewModel.VehicleProficiencyViewItemsGridViewModel.LoadVehicleProficiencyViewObjectCollection(vehicleConfig);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (customerId != null) {
                this.loadPageData();
                return;
            }
            if (!GO.Filter.hasUrlFilter(self.controller.ProficiencyCombinedViewFormViewModel.DriverProficiencyViewItemsGridViewModel.FILTER_NAME, self.controller.ProficiencyCombinedViewFormViewModel.DriverProficiencyViewItemsGridViewModel)) {
                self.controller.ProficiencyCombinedViewFormViewModel.DriverProficiencyViewItemsGridViewModel.LoadDriverProficiencyViewObjectCollection();
			}
            if (!GO.Filter.hasUrlFilter(self.controller.ProficiencyCombinedViewFormViewModel.VehicleProficiencyViewItemsGridViewModel.FILTER_NAME, self.controller.ProficiencyCombinedViewFormViewModel.VehicleProficiencyViewItemsGridViewModel)) {
				self.controller.ProficiencyCombinedViewFormViewModel.VehicleProficiencyViewItemsGridViewModel.LoadVehicleProficiencyViewObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // Store the original filterData function for later use
            var originalFilterData = function () {
                self.loadPageData();
            };

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter
            self.controller.ProficiencyReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.ProficiencyReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.ProficiencyReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                // Call the original filter function
                originalFilterData();
            };

            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.ServiceCheckReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            configuration.filterPredicate = "CustomerId == @0 && SiteId == @1 && DepartmentId == @2";
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.CustomerId() ? '"' + currentData.CustomerId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.SiteId() ? '"' + currentData.SiteId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.DepartmentId() ? '"' + currentData.DepartmentId() + '"' : 'null') + ' }]';
            return configuration;
        };

        this.LoadVehicleGridGridViewData = function () {
            var configuration = {
            };

            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection(defaultConfig);
                return;
            }

            var parameterCount = 0;

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }

            // add filterpredicate for ServiceSettingsId != null
            configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
            configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

            configuration.filterPredicate += 'ServiceSettingsId != null';

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection(configuration);
        };
        

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.FILTER_NAME, self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel)) {
                if (customerId != null) {
                    var defaultConfig = self.getDefaultConfiguration();
                    self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection(defaultConfig);
                    return;
                }
				self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.DashboardFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.DashboardFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.DashboardFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.LoadVehicleGridGridViewData();
            };

            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.SynchronizationStatusReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;
        this.IoTHubManagerProxy = new FleetXQ.Web.Model.Components.IoTHubManagerProxy(this.ObjectsDataSet);

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `Vehicle.Department.Site.CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `Vehicle.Department.SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `Vehicle.DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        /**
         * Helper method to update IoT device last activity time
         * This method queries the IoT Hub to get the last activity time of all devices
         * based on the current filter criteria (customer, site, department)
         * @returns {Promise} - Promise that resolves when the update is complete
         */
        this.updateIoTDeviceLastActivityTime = async function() {
            // Set busy indicators to show loading state
            self.controller.SynchronizationStatusReportFilterFormViewModel.StatusData.IsBusy(true);
            self.controller.AllMessageHistoryStoreProcedureGridViewModel.StatusData.IsBusy(true);
            
            // Create configuration object for the API call
            var configuration = {};
            
            // Get current filter values from the filter form
            var currentData = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject().Data;
            
            // Use filter values if available, otherwise fall back to user claims or empty GUID
            // Priority: Filter value > User claim > Empty GUID
            var customerId = currentData.CustomerId() || 
                self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId || 
                "00000000-0000-0000-0000-000000000000";
                
            var siteId = currentData.SiteId() || "00000000-0000-0000-0000-000000000000";
            var departmentId = currentData.DepartmentId() || "00000000-0000-0000-0000-000000000000";
            
            // Add IDs to configuration object
            configuration.customerId = customerId;
            configuration.siteId = siteId;
            configuration.departmentId = departmentId;
            
            // Success handler - clears busy indicators when complete
            configuration.successHandler = function (result) {
                self.controller.SynchronizationStatusReportFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.AllMessageHistoryStoreProcedureGridViewModel.StatusData.IsBusy(false);

                // Then load grid data with updated activity times
                self.loadPageData();
            };
            
            // Error handler - shows error popup and clears busy indicators
            configuration.errorHandler = function () {
                self.controller.applicationController.showAlertPopup(
                    self.controller, 
                    "Failed to update vehicle last activity time", 
                    "Error", 
                    null, 
                    self.controller.contextId
                );
                self.controller.SynchronizationStatusReportFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.AllMessageHistoryStoreProcedureGridViewModel.StatusData.IsBusy(false);
            }; 
            
            // Call the API to update device last activity time
            return self.IoTHubManagerProxy.GetAllDeviceTwinsLastActiveTime(configuration);
        };

        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            self.controller.AllMessageHistoryStoreProcedureGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllMessageHistoryStoreProcedureGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllMessageHistoryStoreProcedureGridViewModel.LoadAllMessageHistoryStoreProcedureObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllMessageHistoryStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllMessageHistoryStoreProcedureGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllMessageHistoryStoreProcedureGridViewModel.LoadAllMessageHistoryStoreProcedureObjectCollection(configuration);
                    return;
                }
				self.controller.AllMessageHistoryStoreProcedureGridViewModel.LoadAllMessageHistoryStoreProcedureObjectCollection();
			}
        }

        this.initialize = async function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // Modified filterData function to update IoT device last activity time before loading grid data
            self.controller.SynchronizationStatusReportFilterFormViewModel.filterData = async function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.SynchronizationStatusReportFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.SynchronizationStatusReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                try {
                    // First update IoT device last activity time
                    await self.updateIoTDeviceLastActivityTime();
                } catch (error) {
                    console.error("Error in filterData:", error);
                    // Continue with data loading even if IoT update fails
                    self.loadPageData();
                }
            };
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.UserEmailAlertSummaryReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Person.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Person.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                
                configuration.filterPredicate += `Person.CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';

                configuration.filterPredicate += `Person.SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';

                configuration.filterPredicate += `Person.DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);
            self.controller.AlertSubscriptionGridViewModel.LoadAlertSubscriptionObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AlertSubscriptionGridViewModel.FILTER_NAME, self.controller.AlertSubscriptionGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AlertSubscriptionGridViewModel.LoadAlertSubscriptionObjectCollection(configuration);
                    return;
                }
				self.controller.AlertSubscriptionGridViewModel.LoadAlertSubscriptionObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.DashboardFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.DashboardFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.DashboardFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };


            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.UserSummaryReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);
            self.controller.AllUserSummaryStoreProcedureGridViewModel.LoadAllUserSummaryStoreProcedureObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllUserSummaryStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllUserSummaryStoreProcedureGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllUserSummaryStoreProcedureGridViewModel.LoadAllUserSummaryStoreProcedureObjectCollection(configuration);
                    return;
                }
				self.controller.AllUserSummaryStoreProcedureGridViewModel.LoadAllUserSummaryStoreProcedureObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            if (!sessionStorage.getItem('hasReloaded')) {
                // Set the flag before reloading
                sessionStorage.setItem('hasReloaded', 'true');
                
                // Force a reload after a brief delay to ensure hash is set
                window.location.reload();
            } else {
                // Clear the flag for next time
                sessionStorage.removeItem('hasReloaded');
            }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.DashboardFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.DashboardFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.DashboardFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };


            // self.loadInitialGridData();
        };
    };

})();
(function () {
    /**
     * Custom controller for Vehicle Calibration Report Page
     * Extends the base functionality with custom behavior for grid visibility and filtering
     * @param {object} controller - The base controller instance
     */
    FleetXQ.Web.Controllers.VehicleCalibrationReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        /**
         * HACK: This is a temporary solution to hide "vehicle calibration value" grid column from customer users
         * as there is no easy way to hide grid columns in GO at the moment.
         * Controls the visibility of admin and customer grids based on user claims
         * Shows admin grid if user has no CustomerId (is admin)
         * Shows customer grid if user has CustomerId (is customer)
         */
        this.updateGridVisibility = function() {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var isCustomer = customerId != null;
            
            // Control visibility of admin grid (visible when not a customer)
            if (self.controller.AllVehicleCalibrationStoreProcedureGridViewModel) {
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.StatusData.IsVisible(!isCustomer);
            }
            
            // Control visibility of customer grid (visible when is a customer)
            if (self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel) {
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.StatusData.IsVisible(isCustomer);
            }
        };

        /**
         * Gets default configuration for filtering based on user claims
         * Applies CustomerId and SiteId filters if available
         * @returns {object} Configuration object with filter predicates and parameters
         */
        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
            var parameterCount = 0;
        
            // Add CustomerId filter if available
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            // Add SiteId filter if available
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        /**
         * Gets configuration based on current filter form values
         * Falls back to default configuration if no CustomerId is selected for a customer user
         * @returns {object} Configuration object with filter predicates and parameters
         */
        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.AllVehicleCalibrationFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        /**
         * Adds multi-search filter to existing configuration
         * @param {object} configuration - Existing filter configuration
         * @returns {object} Updated configuration with multi-search filter
         */
        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.AllVehicleCalibrationFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        /**
         * Loads page data with current configuration and multi-search filter
         */
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var isCustomer = customerId != null;

            // For Admin User Grid
            if (!isCustomer) {
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.exportFilterPredicate = configuration.filterPredicate;
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.exportFilterParameters = configuration.filterParameters;
                
                // Subscribe to the CollectionLoaded event to trigger sort
                var subscription = self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.Events.CollectionLoaded.subscribe(function() {
                    self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.sortingOptions({
                        columnName: "Vehicle.Module.CalibrationDate",
                        order: "desc"
                    });
                    subscription.dispose(); // Unsubscribe after first load
                });
                
                self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection(configuration);
            }
            // For Customer User Grid
            if (isCustomer) {
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.exportFilterPredicate = configuration.filterPredicate;
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.exportFilterParameters = configuration.filterParameters;
                
                // Subscribe to the CollectionLoaded event to trigger sort
                var subscription = self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.Events.CollectionLoaded.subscribe(function() {
                    self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.sortingOptions({
                        columnName: "Vehicle.Module.CalibrationDate",
                        order: "desc"
                    });
                    subscription.dispose(); // Unsubscribe after first load
                });
                
                self.controller.AllVehicleCalibrationStoreProcedureGridCustomerViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection(configuration);
            }
        };

        /**
         * Loads initial grid data based on user claims
         */
        // this.loadInitialGridData = function () {
        //     var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
        //     if (!GO.Filter.hasUrlFilter(self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.FILTER_NAME, self.controller.AllVehicleCalibrationStoreProcedureGridViewModel)) {
        //         if (customerId != null) {
        //             var configuration = this.getConfiguration();
        //             self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection(configuration);
        //             return;
        //         }
        //         self.controller.AllVehicleCalibrationStoreProcedureGridViewModel.LoadAllVehicleCalibrationStoreProcedureObjectCollection();
        //     }
        // }

        /**
         * Initializes the controller
         * Sets up event handlers, subscriptions, and initial grid visibility
         */
        this.initialize = function () {
            // Disable edit mode confirmation
            self.controller.IsInEditMode = function () {
                return false;
            }

            // Initial visibility update
            // self.updateGridVisibility();

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // Set up filter data callback
            self.controller.AllVehicleCalibrationFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.AllVehicleCalibrationFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.AllVehicleCalibrationFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };

            // self.loadInitialGridData();
        };
    };
})();
(function () {

    FleetXQ.Web.Controllers.VehicleHireDehireReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            var currentData = self.controller.HireDeHireReportFilterFormViewModel.CurrentObject().Data;
            configuration.filterPredicate = "CustomerId == @0 && SiteId == @1 && DepartmentId == @2";
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.CustomerId() ? '"' + currentData.CustomerId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.SiteId() ? '"' + currentData.SiteId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.DepartmentId() ? '"' + currentData.DepartmentId() + '"' : 'null') + ' }]';
            return configuration;
        };

        this.LoadVehicleHireDehireHistory = function () {
            var configuration = {
            };

            var currentData = self.controller.HireDeHireReportFilterFormViewModel.CurrentObject().Data;

            
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.VehicleHireDehireHistoryGridViewModel.LoadVehicleHireDehireHistoryObjectCollection(defaultConfig);
                return;
            }

            var parameterCount = 0;

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            self.controller.VehicleHireDehireHistoryGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.VehicleHireDehireHistoryGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.VehicleHireDehireHistoryGridViewModel.LoadVehicleHireDehireHistoryObjectCollection(configuration);
        };
        

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.VehicleHireDehireHistoryGridViewModel.FILTER_NAME, self.controller.VehicleHireDehireHistoryGridViewModel)) {
                if (customerId != null) {
                    var defaultConfig = self.getDefaultConfiguration();
                    self.controller.VehicleHireDehireHistoryGridViewModel.LoadVehicleHireDehireHistoryObjectCollection(defaultConfig);
                    return;
                }
				self.controller.VehicleHireDehireHistoryGridViewModel.LoadVehicleHireDehireHistoryObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.HireDeHireReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.HireDeHireReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.HireDeHireReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.LoadVehicleHireDehireHistory();
            };

            // self.loadInitialGridData();
        };
    };

})();
(function () {

    FleetXQ.Web.Controllers.VORReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.VORReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.StartDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.EndDate() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };
        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.VORReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadPageData = function () {
            var VORSessionsConfig = this.getConfiguration();
            // add the filter for multi search to the configuration
            VORSessionsConfig = this.addMultiSearchFilter(VORSessionsConfig);

            self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.exportFilterPredicate = VORSessionsConfig.filterPredicate;
            self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.exportFilterParameters = VORSessionsConfig.filterParameters;
            self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.LoadAllVORSessionsPerVehicleStoreProcedureObjectCollection(VORSessionsConfig);

            var VORStatusConfig = this.getConfiguration();
            // add the filter for multi search to the configuration
            VORStatusConfig = this.addMultiSearchFilter(VORStatusConfig);

            self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.exportFilterPredicate = VORStatusConfig.filterPredicate;
            self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.exportFilterParameters = VORStatusConfig.filterParameters;

            // Subscribe to the CollectionLoaded event to trigger sort
            var subscription = self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.Events.CollectionLoaded.subscribe(function() {
                self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.sortingOptions({
                    columnName: "TimezoneAdjustedStartDateTime",
                    order: "desc"
                });
                subscription.dispose(); // Unsubscribe after first load
            });

            self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.LoadAllVORStatusStoreProcedureObjectCollection(VORStatusConfig);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (customerId != null) {
                this.loadPageData();
                return;
            }
            if (!GO.Filter.hasUrlFilter(self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel)) {
                self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.LoadAllVORSessionsPerVehicleStoreProcedureObjectCollection();
			}
            if (!GO.Filter.hasUrlFilter(self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel)) {
				self.controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.LoadAllVORStatusStoreProcedureObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.VORReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.VORReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.VORReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();

                // save filter data to the GeneralProductivityViewFormViewModel so that it can be used by the "Show Sessions" popup.
                var currentData = self.controller.VORReportFilterFormViewModel.CurrentObject().Data;

                if (currentData.StartDate()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.StartDate = currentData.StartDate();
                }
                if (currentData.EndDate()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.EndDate = currentData.EndDate();
                }
                if (currentData.CustomerId()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.CustomerId = currentData.CustomerId();
                }
                if (currentData.SiteId()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.SiteId = currentData.SiteId();
                }
                if (currentData.DepartmentId()) {
                    self.controller.VORReportCombinedViewFormViewModel.AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel.DepartmentId = currentData.DepartmentId();
                }
            };




            // self.loadInitialGridData();
        };
    };

})();
