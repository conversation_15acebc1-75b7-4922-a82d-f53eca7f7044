﻿using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// ReportActions Component
	///  
	/// </summary>
    public partial class ReportActions : IReportActions
    {
		
		public void Dispose()
		{
		}

        public Task<ComponentResponse<bool>> EmailAsync(EmailContainer email, int reportType, System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }

        public Task<ComponentResponse<bool>> PrintAsync(Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }

        public Task<ComponentResponse<ReportSubscriptionContainer>> SubscribeAsync(int reportType, ReportSubscriptionContainer reportSubscription, Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }
    }
}
