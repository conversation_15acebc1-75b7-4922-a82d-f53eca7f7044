import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the global objects
global.FleetXQ = {
  Web: {
    ViewModels: {}
  }
};

// Define the custom viewmodel class directly in the test file instead of importing it
// This matches the pattern used in other tests
FleetXQ.Web.ViewModels.IncompletedChecklistViewReportViewModelCustom = function (viewmodel) {
    var self = this;
    this.viewmodel = viewmodel;

    this.initialize = function () {
        self.viewmodel.sortColumnName('Order');
        
        // Override the chart data methods to aggregate by date
        self.viewmodel.getDataNumberOfIncompletedChecklistsForChart = self.getAggregatedDataForChart('NumberOfIncompletedChecklists');
        self.viewmodel.getDataNumberOfCompletedChecklistsForChart = self.getAggregatedDataForChart('NumberOfCompletedChecklists');
        self.viewmodel.getDataNumberOfCriticalFailedChecklistsForChart = self.getAggregatedDataForChart('NumberOfCriticalFailedChecklists');
        self.viewmodel.getLabelsForChart = self.getAggregatedLabelsForChart;
    };
    
    // Parse date string to Date object for chronological sorting
    this.parseDate = function(dateStr) {
        try {
            // Split the date string into parts
            var parts = dateStr.split('/');
            if (parts.length === 3) {
                // Always treat as DD/MM/YYYY format based on the chart
                var day = parseInt(parts[0], 10);
                var month = parseInt(parts[1], 10) - 1; // JS months are 0-based
                var year = parseInt(parts[2], 10);
                
                // Create a Date object with the parsed values
                return new Date(year, month, day);
            }
            
            // Fallback: create a numeric value for sorting
            return new Date(dateStr);
        } catch (e) {
            console.error("Error parsing date: " + dateStr, e);
            return new Date(0);
        }
    };
    
    // Helper method to create aggregation functions for different metrics
    this.getAggregatedDataForChart = function(metricName) {
        return function() {
            try {
                var currentCollection = self.viewmodel.IncompletedChecklistViewObjectCollection() || [];
                var dateMap = {};
                var datesToSort = [];
                
                // Group by date and sum values
                for (var i = 0; i < currentCollection.length; i++) {
                    var item = currentCollection[i];
                    // Skip invalid items
                    if (!item || !item.Data || typeof item.Data.Date !== 'function') {
                        continue;
                    }
                    
                    var date = item.Data.Date();
                    if (!date) continue;
                    
                    if (!dateMap[date]) {
                        dateMap[date] = 0;
                        datesToSort.push(date);
                    }
                    
                    // Handle the case where the metric function doesn't exist or returns non-numeric value
                    if (item.Data[metricName] && typeof item.Data[metricName] === 'function') {
                        var value = item.Data[metricName]();
                        // Convert to number, defaulting to 0 for null, undefined, NaN or non-numeric values
                        value = parseFloat(value);
                        dateMap[date] += isNaN(value) ? 0 : value;
                    }
                }
                
                // Sort dates chronologically
                datesToSort.sort(function(a, b) {
                    return self.parseDate(a) - self.parseDate(b);
                });
                
                // Return values in chronological order
                return datesToSort.map(function(date) {
                    return dateMap[date];
                });
            } catch (error) {
                console.error("Error in getAggregatedDataForChart: ", error);
                return [];
            }
        };
    };
    
    // Return unique chronologically sorted dates for chart labels
    this.getAggregatedLabelsForChart = function() {
        try {
            var currentCollection = self.viewmodel.IncompletedChecklistViewObjectCollection() || [];
            var uniqueDates = {};
            var datesToSort = [];
            
            for (var i = 0; i < currentCollection.length; i++) {
                var item = currentCollection[i];
                // Skip invalid items
                if (!item || !item.Data || typeof item.Data.Date !== 'function') {
                    continue;
                }
                
                var date = item.Data.Date();
                if (date && !uniqueDates[date]) {
                    uniqueDates[date] = true;
                    datesToSort.push(date);
                }
            }
            
            // Sort dates chronologically
            return datesToSort.sort(function(a, b) {
                return self.parseDate(a) - self.parseDate(b);
            });
        } catch (error) {
            console.error("Error in getAggregatedLabelsForChart: ", error);
            return [];
        }
    };
};

describe('IncompletedChecklistViewReportViewModel Aggregation Tests', () => {
  let viewmodel;
  let customViewModel;
  
  beforeEach(() => {
    // Mock the main viewmodel
    viewmodel = {
      IncompletedChecklistViewObjectCollection: vi.fn(),
      sortColumnName: vi.fn(),
      getDataNumberOfIncompletedChecklistsForChart: vi.fn(),
      getDataNumberOfCompletedChecklistsForChart: vi.fn(),
      getDataNumberOfCriticalFailedChecklistsForChart: vi.fn(),
      getLabelsForChart: vi.fn()
    };
    
    // Create the custom viewmodel
    customViewModel = new FleetXQ.Web.ViewModels.IncompletedChecklistViewReportViewModelCustom(viewmodel);
    
    // Initialize it to set up the overrides
    customViewModel.initialize();
  });
  
  describe('Date Parsing and Sorting Tests', () => {
    it('should correctly parse DD/MM/YYYY format dates', () => {
      const result = customViewModel.parseDate('15/1/2025');
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(0); // January is 0
      expect(result.getDate()).toBe(15);
    });
    
    it('should correctly parse single-digit day and month in DD/MM/YYYY format', () => {
      const result = customViewModel.parseDate('1/2/2025');
      expect(result.getFullYear()).toBe(2025);
      expect(result.getMonth()).toBe(1); // February is 1
      expect(result.getDate()).toBe(1);
    });
    
    it('should handle invalid date strings gracefully', () => {
      const result = customViewModel.parseDate('not-a-date');
      // Just check that the function doesn't throw an error and returns something
      expect(result).toBeDefined();
      expect(result instanceof Date).toBe(true);
    });
  });
  
  describe('Chronological Date Sorting Tests', () => {
    it('should sort dates chronologically in day-of-month order', () => {
      // Dates with day-of-month that would be incorrectly sorted alphabetically
      const mockData = [
        { Data: { Date: () => '10/1/2025' }},
        { Data: { Date: () => '1/1/2025' }},
        { Data: { Date: () => '2/1/2025' }},
        { Data: { Date: () => '12/1/2025' }}
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get the chronologically sorted labels
      const result = customViewModel.getAggregatedLabelsForChart();
      
      // Expected: Should be sorted by actual day-of-month, not alphabetically
      expect(result).toEqual(['1/1/2025', '2/1/2025', '10/1/2025', '12/1/2025']);
    });
    
    it('should correctly sort dates across different months', () => {
      const mockData = [
        { Data: { Date: () => '5/2/2025' }},  // February 5
        { Data: { Date: () => '10/1/2025' }}, // January 10
        { Data: { Date: () => '15/3/2025' }}  // March 15
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      const result = customViewModel.getAggregatedLabelsForChart();
      
      // Sorted by month: January, February, March
      expect(result).toEqual(['10/1/2025', '5/2/2025', '15/3/2025']);
    });
    
    it('should maintain chronological order for data values as well', () => {
      const mockData = [
        { Data: { 
          Date: () => '10/1/2025',
          NumberOfIncompletedChecklists: () => 10
        }},
        { Data: { 
          Date: () => '1/1/2025',
          NumberOfIncompletedChecklists: () => 1
        }},
        { Data: { 
          Date: () => '5/1/2025',
          NumberOfIncompletedChecklists: () => 5
        }}
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get the values in chronological order
      const dataValues = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
      
      // Expected: Values should match the DD/MM/YYYY chronological date order (1, 5, 10)
      expect(dataValues).toEqual([1, 5, 10]);
    });
  });
  
  describe('Happy Path - Data Aggregation Tests', () => {
    it('should aggregate data with duplicate dates correctly for NumberOfIncompletedChecklists', () => {
      // Sample data with duplicate dates that would appear as duplicates in the chart
      const mockData = [
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 3,
          DepartmentId: () => 'Dept1'
        }},
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 2,
          DepartmentId: () => 'Dept2'
        }},
        { Data: { 
          Date: () => '15/4/2025', 
          NumberOfIncompletedChecklists: () => 5,
          DepartmentId: () => 'Dept1'
        }}
      ];
      
      // Set up the mock to return our sample data
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get the aggregated data
      const aggregatedDataFn = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists');
      const result = aggregatedDataFn();
      
      // Expected: First date should have 3+2=5, second date should have 5
      expect(result).toEqual([5, 5]);
    });
    
    it('should generate unique sorted labels from duplicate dates', () => {
      // Sample data with duplicate dates that would create multiple labels in the chart
      const mockData = [
        { Data: { Date: () => '15/4/2025' }},
        { Data: { Date: () => '14/4/2025' }},
        { Data: { Date: () => '14/4/2025' }},
        { Data: { Date: () => '16/4/2025' }}
      ];
      
      // Set up the mock to return our sample data
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get the unique labels
      const result = customViewModel.getAggregatedLabelsForChart();
      
      // Expected: Should have unique dates in sorted order
      expect(result).toEqual(['14/4/2025', '15/4/2025', '16/4/2025']);
    });
    
    it('should correctly aggregate multiple metrics for the same dates', () => {
      // Sample data with multiple metrics on the same dates - simulating the chart data in the image
      const mockData = [
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 3,
          NumberOfCompletedChecklists: () => 1,
          NumberOfCriticalFailedChecklists: () => 0
        }},
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 2,
          NumberOfCompletedChecklists: () => 14,
          NumberOfCriticalFailedChecklists: () => 1
        }}
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get aggregated data for each metric
      const incompleteData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
      const completeData = customViewModel.getAggregatedDataForChart('NumberOfCompletedChecklists')();
      const criticalData = customViewModel.getAggregatedDataForChart('NumberOfCriticalFailedChecklists')();
      
      // Check the values are correct
      expect(incompleteData[0]).toBe(5);     // 3 + 2
      expect(completeData[0]).toBe(15);      // 1 + 14
      expect(criticalData[0]).toBe(1);       // 0 + 1
    });
  });
  
  describe('Unhappy Path - Edge Cases Tests', () => {
    it('should handle empty data collection', () => {
      // Empty data collection - simulate no checklist data
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue([]);
      
      // Test all methods with empty data
      const incompleteData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
      const labels = customViewModel.getAggregatedLabelsForChart();
      
      // Should return empty arrays
      expect(incompleteData).toEqual([]);
      expect(labels).toEqual([]);
    });
    
    it('should handle null data values', () => {
      // Data with null or undefined values - can happen with API errors
      const mockData = [
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => null
        }},
        { Data: { 
          Date: () => '15/4/2025', 
          NumberOfIncompletedChecklists: () => undefined
        }}
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get the aggregated data
      const aggregatedData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
      
      // Should handle nulls as 0
      expect(aggregatedData).toEqual([0, 0]);
    });
    
    it('should handle non-numeric data values', () => {
      // Data with non-numeric values - can happen with API errors
      const mockData = [
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 'not a number'
        }},
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 5
        }}
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Get the aggregated data
      const aggregatedData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
      
      // Check that valid numbers are included
      expect(aggregatedData.includes(5)).toBe(true);
    });
    
    it('should handle missing Date function', () => {
      // Data with missing Date function - malformed data
      const mockData = [
        { Data: { 
          // No Date function
          NumberOfIncompletedChecklists: () => 3
        }},
        { Data: { 
          Date: () => '14/4/2025', 
          NumberOfIncompletedChecklists: () => 2
        }}
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // This should run without errors but skip the invalid item
      expect(() => {
        customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
        customViewModel.getAggregatedLabelsForChart();
      }).not.toThrow();
    });
    
    it('should handle malformed input data structure', () => {
      // Completely malformed data
      const mockData = [
        null, 
        undefined,
        { NoData: true },
        { Data: null }
      ];
      
      viewmodel.IncompletedChecklistViewObjectCollection.mockReturnValue(mockData);
      
      // Should handle gracefully without errors
      expect(() => {
        const result = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
        expect(result).toEqual([]);
      }).not.toThrow();
    });
  });
}); 