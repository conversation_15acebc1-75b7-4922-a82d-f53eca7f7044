import { describe, it, expect, beforeEach, vi } from 'vitest';
import ko from 'knockout';

// Mock the FleetXQ namespace
global.FleetXQ = {
    Web: {
        ViewModels: {
            Filters: {
                PersonFilterViewModelCustom: class {
                    constructor(viewmodel) {
                        this.viewmodel = viewmodel;
                        this.onBeforeInitialize();
                    }

                    onBeforeInitialize() {
                        const self = this;
                        this.subscriptions = [];

                        // Add computed observables for dropdown icon visibility
                        self.IsCustomerDropdownIconVisible = ko.pureComputed(() => true);

                        self.IsSiteDropdownIconVisible = ko.pureComputed(() => {
                            const customerValue = self.viewmodel.filterData.fields.CustomerValue();
                            if (!customerValue) return false;
                            if (customerValue.text === 'Customer') return false;
                            if (!customerValue.value || customerValue.value === 'Customer') return false;
                            return true;
                        });

                        self.IsDepartmentDropdownIconVisible = ko.pureComputed(() => {
                            const siteValue = self.viewmodel.filterData.fields.SiteValue();
                            if (!siteValue) return false;
                            if (siteValue.text === 'Site') return false;
                            if (!siteValue.value || siteValue.value === 'Site') return false;
                            return true;
                        });

                        // Override getSiteCollectionData
                        self.viewmodel.getSiteCollectionData = function() {
                            const customerValue = self.viewmodel.filterData.fields.CustomerValue();
                            if (customerValue && 
                                customerValue.value !== 'All' && 
                                customerValue.value !== 'Customer' &&
                                customerValue.value?.Data?.Id) {
                                const customerId = customerValue.value.Data.Id();
                                const configuration = {
                                    filterPredicate: 'CustomerId == @0',
                                    filterParameters: `[  { "TypeName" : "System.Guid", "Value" : "${customerId}" } ]`,
                                    contextId: self.viewmodel.contextId,
                                    successHandler: self.viewmodel.onGetSiteCollectionDataSuccess,
                                    errorHandler: self.viewmodel.onGetSiteCollectionDataError,
                                    sortOrder: 'ASC',
                                    sortColumn: 'Name'
                                };
                                self.viewmodel.DataStoreSite.LoadObjectCollection(configuration);
                            } else {
                                self.viewmodel.onGetSiteCollectionDataSuccess([]);
                            }
                        };

                        // Override getDepartmentCollectionData
                        self.viewmodel.getDepartmentCollectionData = function() {
                            const siteValue = self.viewmodel.filterData.fields.SiteValue();
                            if (siteValue && 
                                siteValue.value !== 'All' && 
                                siteValue.value !== 'Site' &&
                                siteValue.value?.Data?.Id) {
                                const siteId = siteValue.value.Data.Id();
                                const configuration = {
                                    filterPredicate: 'SiteId == @0',
                                    filterParameters: `[  { "TypeName" : "System.Guid", "Value" : "${siteId}" } ]`,
                                    contextId: self.viewmodel.contextId,
                                    successHandler: self.viewmodel.onGetDepartmentCollectionDataSuccess,
                                    errorHandler: self.viewmodel.onGetDepartmentCollectionDataError,
                                    sortOrder: 'ASC',
                                    sortColumn: 'Name'
                                };
                                self.viewmodel.DataStoreDepartment.LoadObjectCollection(configuration);
                            } else {
                                self.viewmodel.onGetDepartmentCollectionDataSuccess([]);
                            }
                        };

                        // Override release
                        self.viewmodel.release = function() {
                            for (let i = 0; i < self.subscriptions.length; i++) {
                                self.subscriptions[i].dispose();
                            }
                            self.viewmodel.controller.ObjectsDataSet.cleanContext(self.viewmodel.contextId);
                            if (self.viewmodel.$container) {
                                ko.removeNode(self.viewmodel.$container);
                            }
                        };

                        // Subscribe to customer changes
                        self.subscriptions.push(
                            self.viewmodel.filterData.fields.CustomerValue.subscribe(() => {
                                self.viewmodel.getSiteCollectionData();
                            })
                        );

                        // Subscribe to site changes
                        self.subscriptions.push(
                            self.viewmodel.filterData.fields.SiteValue.subscribe(() => {
                                self.viewmodel.getDepartmentCollectionData();
                            })
                        );
                    }
                }
            }
        }
    }
};

describe('PersonFilterViewModel Custom Tests', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Mock the base view model
        viewModel = {
            filterData: {
                fields: {
                    CustomerValue: ko.observable(),
                    SiteValue: ko.observable(),
                    DepartmentValue: ko.observable()
                }
            },
            contextId: 'test-context',
            DataStoreSite: {
                LoadObjectCollection: vi.fn()
            },
            DataStoreDepartment: {
                LoadObjectCollection: vi.fn()
            },
            onGetSiteCollectionDataSuccess: vi.fn(),
            onGetSiteCollectionDataError: vi.fn(),
            onGetDepartmentCollectionDataSuccess: vi.fn(),
            onGetDepartmentCollectionDataError: vi.fn(),
            CustomerValues: ko.observableArray([]),
            controller: {
                ObjectsDataSet: {
                    cleanContext: vi.fn()
                }
            },
            $container: document.createElement('div')
        };

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.Filters.PersonFilterViewModelCustom(viewModel);
    });

    describe('Dropdown Icon Visibility Tests', () => {
        it('should always show Customer dropdown icon', () => {
            expect(customViewModel.IsCustomerDropdownIconVisible()).toBe(true);
        });

        it('should not show Site dropdown icon when CustomerValue is null', () => {
            viewModel.filterData.fields.CustomerValue(null);
            expect(customViewModel.IsSiteDropdownIconVisible()).toBe(false);
        });

        it('should not show Site dropdown icon when CustomerValue is placeholder', () => {
            viewModel.filterData.fields.CustomerValue({ text: 'Customer', value: 'Customer' });
            expect(customViewModel.IsSiteDropdownIconVisible()).toBe(false);
        });

        it('should show Site dropdown icon when CustomerValue is valid', () => {
            viewModel.filterData.fields.CustomerValue({
                text: 'Test Customer',
                value: { Data: { Id: ko.observable('test-id') } }
            });
            expect(customViewModel.IsSiteDropdownIconVisible()).toBe(true);
        });

        it('should not show Department dropdown icon when SiteValue is null', () => {
            viewModel.filterData.fields.SiteValue(null);
            expect(customViewModel.IsDepartmentDropdownIconVisible()).toBe(false);
        });

        it('should not show Department dropdown icon when SiteValue is placeholder', () => {
            viewModel.filterData.fields.SiteValue({ text: 'Site', value: 'Site' });
            expect(customViewModel.IsDepartmentDropdownIconVisible()).toBe(false);
        });

        it('should show Department dropdown icon when SiteValue is valid', () => {
            viewModel.filterData.fields.SiteValue({
                text: 'Test Site',
                value: { Data: { Id: ko.observable('test-id') } }
            });
            expect(customViewModel.IsDepartmentDropdownIconVisible()).toBe(true);
        });
    });

    describe('Filter Data Loading Tests', () => {
        it('should load site data when customer changes to valid value', () => {
            const customerId = 'test-customer-id';
            viewModel.filterData.fields.CustomerValue({
                text: 'Test Customer',
                value: { Data: { Id: ko.observable(customerId) } }
            });

            expect(viewModel.DataStoreSite.LoadObjectCollection).toHaveBeenCalledWith(
                expect.objectContaining({
                    filterPredicate: 'CustomerId == @0',
                    contextId: 'test-context',
                    sortOrder: 'ASC',
                    sortColumn: 'Name'
                })
            );
        });

        it('should clear site data when customer changes to null', () => {
            viewModel.filterData.fields.CustomerValue(null);
            expect(viewModel.onGetSiteCollectionDataSuccess).toHaveBeenCalledWith([]);
        });

        it('should load department data when site changes to valid value', () => {
            const siteId = 'test-site-id';
            viewModel.filterData.fields.SiteValue({
                text: 'Test Site',
                value: { Data: { Id: ko.observable(siteId) } }
            });

            expect(viewModel.DataStoreDepartment.LoadObjectCollection).toHaveBeenCalledWith(
                expect.objectContaining({
                    filterPredicate: 'SiteId == @0',
                    contextId: 'test-context',
                    sortOrder: 'ASC',
                    sortColumn: 'Name'
                })
            );
        });

        it('should clear department data when site changes to null', () => {
            viewModel.filterData.fields.SiteValue(null);
            expect(viewModel.onGetDepartmentCollectionDataSuccess).toHaveBeenCalledWith([]);
        });
    });

    describe('Subscription Management Tests', () => {
        it('should dispose subscriptions when released', () => {
            const disposeSpy = vi.fn();
            customViewModel.subscriptions = [{ dispose: disposeSpy }, { dispose: disposeSpy }];
            
            viewModel.release();
            
            expect(disposeSpy).toHaveBeenCalledTimes(2);
            expect(viewModel.controller.ObjectsDataSet.cleanContext).toHaveBeenCalledWith('test-context');
        });
    });
});
