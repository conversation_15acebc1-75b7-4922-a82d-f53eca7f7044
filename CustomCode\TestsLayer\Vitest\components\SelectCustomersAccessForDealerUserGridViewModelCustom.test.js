import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the FleetXQ global object and necessary sub-objects if they are not already globally available in the test environment.
// This is often needed if the custom script relies on FleetXQ.Web.ViewModels etc.
const mockFleetXQ = {
    Web: {
        ViewModels: {
            SelectCustomersAccessForDealerUserGridViewModelCustom: null // Will be assigned the actual class
        },
        Model: {
            DataObjects: {
                // If GOUserObject is a constructor used internally, mock it if necessary
                // GOUserObject: vi.fn().mockImplementation(() => ({ Data: {} })) 
            }
        }
    }
};
global.FleetXQ = mockFleetXQ;

// The actual script to be tested - ensure this path is correct
// Assuming the compiled/transformed JS for SelectCustomersAccessForDealerUserGridViewModelCustom is accessible.
// If it's part of a larger bundle, this import might need adjustment or pre-compilation step.
// For now, let's assume it attaches itself to global.FleetXQ.Web.ViewModels
// and we can extract it after a simulated load or direct import if possible.

// --- Code from SelectCustomersAccessForDealerUserGridViewModel.custom.js --- 
// Paste the IIFE from your custom JS file here for the test to access it directly.
// This is a common way to test IIFEs that attach to a global namespace.
(function () {
    FleetXQ.Web.ViewModels.SelectCustomersAccessForDealerUserGridViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        var applyDealerFilter = function () {
            var currentDealerId;
            if (self.viewmodel.GOUser && self.viewmodel.GOUser.Data) {
                if (typeof self.viewmodel.GOUser.Data.DealerId === 'function') {
                    currentDealerId = self.viewmodel.GOUser.Data.DealerId();
                } else if (self.viewmodel.GOUser.Data.DealerId !== undefined) {
                    currentDealerId = self.viewmodel.GOUser.Data.DealerId;
                }
            } else {
                return;
            }

            if (currentDealerId) {
                var finalPredicate = self.viewmodel.baseFilterPredicate || "";
                var finalParameters = [];

                if (self.viewmodel.baseFilterParameters && self.viewmodel.baseFilterParameters !== '') {
                    try {
                        finalParameters = JSON.parse(self.viewmodel.baseFilterParameters);
                        if (!Array.isArray(finalParameters)) {
                            finalParameters = [];
                        }
                    } catch (e) {
                        finalParameters = [];
                    }
                }

                var dealerSpecificPredicate = "(DealerId == @" + finalParameters.length + ")";
                var dealerFilterArgument = {
                    TypeName: "System.Guid",
                    Value: currentDealerId
                };
                finalParameters.push(dealerFilterArgument);

                if (finalPredicate !== "") {
                    finalPredicate = finalPredicate + " && " + dealerSpecificPredicate;
                } else {
                    finalPredicate = dealerSpecificPredicate;
                }

                self.viewmodel.filterPredicate = finalPredicate;
                self.viewmodel.filterParameters = JSON.stringify(finalParameters);

                if (typeof self.viewmodel.setGridPageNumber === 'function') {
                    self.viewmodel.setGridPageNumber(0);
                }
                if (typeof self.viewmodel.Rebind === 'function') {
                    self.viewmodel.Rebind(true);
                }
            }
        };

        this.initialize = function () {
            self.viewmodel.applyDealerFilter = applyDealerFilter;
        };

        // Add other functions from the custom file if they need to be tested or are dependencies
        // For example, checkAll, updateCheckStates, toggleChecked if they interact or are complex.
        // For this test, we are focusing on applyDealerFilter and its setup in initialize.

        self.viewmodel.checkAll = function () { };
        self.viewmodel.checkedStates = global.ko ? global.ko.observableArray([]) : { removeAll: vi.fn(), push: vi.fn(), '()': [] }; // Mock Knockout if not present
        self.viewmodel.CustomerObjectCollection = global.ko ? global.ko.observableArray([]) : { '()': [] };
        self.viewmodel.selectedCustomers = global.ko ? global.ko.observableArray([]) : { find: vi.fn(), push: vi.fn(), remove: vi.fn(), '()': [] };

        self.viewmodel.updateCheckStates = function () {
            self.viewmodel.checkedStates.removeAll();
            self.viewmodel.CustomerObjectCollection().forEach(function (item, index) {
                self.viewmodel.checkedStates.push(global.ko ? global.ko.observable(self.viewmodel.selectedCustomers().find(el => el.Data.CustomerId() == item.Data.Id()) != undefined) : { '()': false, notifySubscribers: vi.fn() });
            });
        };

        self.viewmodel.toggleChecked = function (index, event) {
            if (event) event.stopPropagation();
            // Simplified mock for testing applyDealerFilter focus
            // Real implementation is more complex
        };
    };
}());
// --- End of pasted code ---

const SelectCustomersAccessForDealerUserGridViewModelCustom = FleetXQ.Web.ViewModels.SelectCustomersAccessForDealerUserGridViewModelCustom;

describe('SelectCustomersAccessForDealerUserGridViewModelCustom', () => {
    let mockViewmodel;
    let customVMInstance;

    beforeEach(() => {
        // Reset mocks before each test
        mockViewmodel = {
            GOUser: null,
            baseFilterPredicate: null,
            baseFilterParameters: null,
            filterPredicate: null,
            filterParameters: null,
            setGridPageNumber: vi.fn(),
            Rebind: vi.fn(),
            // Mock parts of the viewmodel that are accessed by other functions in the custom class, if any
            // For instance, if updateCheckStates uses CustomerObjectCollection, selectedCustomers:
            CustomerObjectCollection: vi.fn(() => []),
            selectedCustomers: vi.fn(() => []),
            checkedStates: { removeAll: vi.fn(), push: vi.fn(), '()': [] }, // Mock observable array behavior
            contextId: 'test-context' // Example property
        };
        customVMInstance = new SelectCustomersAccessForDealerUserGridViewModelCustom(mockViewmodel);
        customVMInstance.initialize(); // This will expose applyDealerFilter on mockViewmodel
    });

    it('should set filterPredicate and filterParameters when GOUser with DealerId is present', () => {
        const dealerId = 'dealer-guid-123';
        mockViewmodel.GOUser = {
            Data: {
                DealerId: dealerId
            }
        };

        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBe('(DealerId == @0)');
        expect(JSON.parse(mockViewmodel.filterParameters)).toEqual([{
            TypeName: "System.Guid",
            Value: dealerId
        }]);
        expect(mockViewmodel.setGridPageNumber).toHaveBeenCalledWith(0);
        expect(mockViewmodel.Rebind).toHaveBeenCalledWith(true);
    });

    it('should append to existing baseFilterPredicate and baseFilterParameters', () => {
        const dealerId = 'dealer-guid-456';
        mockViewmodel.GOUser = { Data: { DealerId: dealerId } };
        mockViewmodel.baseFilterPredicate = '(SomeOtherField == @0)';
        mockViewmodel.baseFilterParameters = JSON.stringify([{ TypeName: "System.String", Value: "someValue" }]);

        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBe('(SomeOtherField == @0) && (DealerId == @1)');
        expect(JSON.parse(mockViewmodel.filterParameters)).toEqual([
            { TypeName: "System.String", Value: "someValue" },
            { TypeName: "System.Guid", Value: dealerId }
        ]);
        expect(mockViewmodel.setGridPageNumber).toHaveBeenCalledWith(0);
        expect(mockViewmodel.Rebind).toHaveBeenCalledWith(true);
    });

    it('should handle GOUser.Data.DealerId as a function', () => {
        const dealerId = 'dealer-guid-789';
        mockViewmodel.GOUser = {
            Data: {
                DealerId: vi.fn(() => dealerId)
            }
        };

        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBe('(DealerId == @0)');
        expect(JSON.parse(mockViewmodel.filterParameters)).toEqual([{
            TypeName: "System.Guid",
            Value: dealerId
        }]);
        expect(mockViewmodel.GOUser.Data.DealerId).toHaveBeenCalled();
    });

    it('should not set filters or rebind if GOUser is null', () => {
        mockViewmodel.GOUser = null;
        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBeNull();
        expect(mockViewmodel.filterParameters).toBeNull();
        expect(mockViewmodel.setGridPageNumber).not.toHaveBeenCalled();
        expect(mockViewmodel.Rebind).not.toHaveBeenCalled();
    });

    it('should not set filters or rebind if GOUser.Data is null', () => {
        mockViewmodel.GOUser = { Data: null };
        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBeNull();
        expect(mockViewmodel.filterParameters).toBeNull();
        expect(mockViewmodel.setGridPageNumber).not.toHaveBeenCalled();
        expect(mockViewmodel.Rebind).not.toHaveBeenCalled();
    });

    it('should not set filters or rebind if DealerId is missing', () => {
        mockViewmodel.GOUser = { Data: {} }; // DealerId is not present
        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBeNull();
        expect(mockViewmodel.filterParameters).toBeNull();
        expect(mockViewmodel.setGridPageNumber).not.toHaveBeenCalled();
        expect(mockViewmodel.Rebind).not.toHaveBeenCalled();
    });

    it('should correctly parse valid JSON baseFilterParameters', () => {
        const dealerId = 'dealer-guid-abc';
        mockViewmodel.GOUser = { Data: { DealerId: dealerId } };
        mockViewmodel.baseFilterParameters = JSON.stringify([{ TypeName: "System.Int32", Value: 10 }]);

        mockViewmodel.applyDealerFilter();

        expect(JSON.parse(mockViewmodel.filterParameters)).toEqual([
            { TypeName: "System.Int32", Value: 10 },
            { TypeName: "System.Guid", Value: dealerId }
        ]);
    });

    it('should handle invalid JSON in baseFilterParameters gracefully', () => {
        const dealerId = 'dealer-guid-def';
        mockViewmodel.GOUser = { Data: { DealerId: dealerId } };
        mockViewmodel.baseFilterParameters = 'invalid-json-string';

        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBe('(DealerId == @0)'); // Predicate should still form with new param at index 0
        expect(JSON.parse(mockViewmodel.filterParameters)).toEqual([{
            TypeName: "System.Guid",
            Value: dealerId
        }]); // Parameters should only contain the new dealerId param
    });

    it('should handle baseFilterParameters that parse to non-array gracefully', () => {
        const dealerId = 'dealer-guid-ghi';
        mockViewmodel.GOUser = { Data: { DealerId: dealerId } };
        mockViewmodel.baseFilterParameters = JSON.stringify({ not: 'an array' }); // Valid JSON, but not an array

        mockViewmodel.applyDealerFilter();

        expect(mockViewmodel.filterPredicate).toBe('(DealerId == @0)');
        expect(JSON.parse(mockViewmodel.filterParameters)).toEqual([{
            TypeName: "System.Guid",
            Value: dealerId
        }]);
    });

    // Test for initialize to ensure applyDealerFilter is exposed
    it('initialize should expose applyDealerFilter on the viewmodel', () => {
        // initialize is called in beforeEach, so this primarily checks the outcome
        expect(mockViewmodel.applyDealerFilter).toBeDefined();
        expect(typeof mockViewmodel.applyDealerFilter).toBe('function');
    });
});

// Minimalistic Knockout mock if not available globally for tests
if (typeof ko === 'undefined') {
    global.ko = {
        observable: vi.fn((initialValue) => {
            let _value = initialValue;
            const obs = () => _value;
            obs.subscribe = vi.fn();
            obs.notifySubscribers = vi.fn();
            obs.peek = () => _value;
            // Add other methods if your code uses them e.g. obs(newValue) to set
            return obs;
        }),
        observableArray: vi.fn((initialArray) => {
            let _array = initialArray || [];
            const obsArray = () => _array;
            obsArray.push = vi.fn((item) => _array.push(item));
            obsArray.remove = vi.fn((itemOrPredicate) => {
                if (typeof itemOrPredicate === 'function') {
                    _array = _array.filter(item => !itemOrPredicate(item));
                } else {
                    _array = _array.filter(item => item !== itemOrPredicate);
                }
                return []; // Mock remove return value if necessary
            });
            obsArray.removeAll = vi.fn(() => { _array = []; return []; });
            obsArray.subscribe = vi.fn();
            obsArray.notifySubscribers = vi.fn();
            obsArray.peek = () => _array;
            // Add other array methods if used: indexOf, splice, etc.
            return obsArray;
        }),
        isObservable: vi.fn(ob => typeof ob === 'function' && Reflect.has(ob, 'subscribe')),
        // Add computed, etc. if needed by the SUT or its dependencies
    };
}
