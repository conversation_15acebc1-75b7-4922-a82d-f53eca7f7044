import { describe, it, expect, beforeEach, vi } from 'vitest';

// Setup global mocks
global.FleetXQ = {
    Web: {
        ViewModels: {}
    }
};

global.ApplicationController = {
    viewModel: {
        security: {
            currentUserClaims: () => ({
                HasUsersAccess: 'True',
                CanEditUserLicense: 'True',
                CanCreateUserLicense: 'True'
            })
        }
    }
};

global.ko = {
    computed: fn => {
        const computed = fn;
        computed.notifySubscribers = vi.fn();
        // Add dependency tracking
        computed.getDependenciesCount = () => 1;
        computed.peek = () => computed();
        return computed;
    },
    observable: value => {
        const obs = function (newValue) {
            if (arguments.length) {
                obs.value = newValue;
                // Notify all subscribers when value changes
                if (obs.subscriptions) {
                    obs.subscriptions.forEach(callback => callback(newValue));
                }
                obs.notifySubscribers();
            }
            return obs.value;
        };
        obs.value = value;
        obs.subscriptions = [];
        obs.subscribe = function (callback) {
            obs.subscriptions.push(callback);
        };
        obs.notifySubscribers = vi.fn();
        return obs;
    }
};

// Define the ViewModel directly
FleetXQ.Web.ViewModels.DriverLicensesFormViewModelCustom = function (viewModel) {
    var self = this;
    this.viewModel = viewModel;
    this.previousGeneralLicence = null;
    this.isAddingLicense = false;

    function logDebug(action, details) {
        console.log(`[DriverLicenses] ${action}:`, details);
    }

    this.initialize = function () {
        logDebug('Initializing custom ViewModel', 'Start');

        // Store original functions
        var originalCreateNewGeneralLicence = viewModel.createNewGeneralLicence;
        var originalCancelEdit = viewModel.CancelEdit;

        // Override tab visibility functions
        viewModel.StatusData.IsGenericTabVisible = ko.computed(function () {
            return !(viewModel.DriverObject() &&
                viewModel.DriverObject().getLicensesByModel() &&
                viewModel.DriverObject().getLicensesByModel().length > 0);
        });

        viewModel.StatusData.IsByModelTabVisible = ko.computed(function () {
            return !(viewModel.DriverObject() && viewModel.DriverObject().getGeneralLicence() !== null);
        });

        // Override the createNewGeneralLicence function
        viewModel.createNewGeneralLicence = function () {
            self.isAddingLicense = true;
            originalCreateNewGeneralLicence.call(viewModel);
        };

        // Override the CancelEdit function
        viewModel.CancelEdit = function (isCommandCall, isCreatePopup) {
            if (self.isAddingLicense) {
                viewModel.DriverObject().setGeneralLicence(null);
                if (!!viewModel.GeneralLicenceFormViewModel) {
                    viewModel.GeneralLicenceFormViewModel.SetLicenceDetailObject(null);
                }
                self.isAddingLicense = false;
                return false;
            }
            return originalCancelEdit.call(viewModel, isCommandCall, isCreatePopup);
        };

        // Subscribe to changes in the driver object to update tab visibility
        if (viewModel.DriverObject) {
            viewModel.DriverObject.subscribe(function (newValue) {
                if (newValue) {
                    viewModel.StatusData.IsGenericTabVisible.notifySubscribers();
                    viewModel.StatusData.IsByModelTabVisible.notifySubscribers();
                }
            });
        }
    };
};

describe('DriverLicensesFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let mockDriverObject;
    let originalCreateNewGeneralLicence;
    let originalCancelEdit;

    beforeEach(() => {
        // Mock driver object
        mockDriverObject = {
            getGeneralLicence: vi.fn(() => null),
            getLicensesByModel: vi.fn(() => []),
            setGeneralLicence: vi.fn(),
            Data: {
                LicenseMode: ko.observable(0),
                LicenceDetailId: ko.observable(null),
                DenyAccessWhenExpired: ko.observable(false),
                VehicleAccess: ko.observable(1)
            }
        };

        // Create spies for original functions
        originalCreateNewGeneralLicence = vi.fn();
        originalCancelEdit = vi.fn();

        // Mock base viewModel with expanded functionality
        viewModel = {
            DriverObject: ko.observable(mockDriverObject),
            StatusData: {
                DisplayMode: ko.observable('edit'),
                CurrentTabIndex: ko.observable(1),
                IsEmpty: ko.observable(false)
            },
            createNewGeneralLicence: originalCreateNewGeneralLicence,
            CancelEdit: originalCancelEdit,
            GeneralLicenceFormViewModel: {
                SetLicenceDetailObject: vi.fn(),
                StatusData: {
                    IsEmpty: ko.observable(false),
                    DisplayMode: ko.observable('edit')
                }
            },
            contextId: 'test-context',
            include: [],
            GetDriverObject: vi.fn(() => mockDriverObject),
            SetDriverObject: vi.fn(),
            DataStore: {
                SaveObject: vi.fn()
            },
            controller: {
                ObjectsDataSet: {
                    RemoveObject: vi.fn()
                }
            },
            LicensesByModelListViewModel: {
                createNewCommandInitActions: [],
                commands: {
                    removeItemCommand: vi.fn()
                }
            },
            // Add deleteGeneralLicence function
            deleteGeneralLicence: vi.fn()
        };

        // Create custom viewModel
        customViewModel = new FleetXQ.Web.ViewModels.DriverLicensesFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('Tab Visibility', () => {
        it('should show Generic tab when no model licenses exist', () => {
            mockDriverObject.getLicensesByModel.mockReturnValue([]);
            expect(viewModel.StatusData.IsGenericTabVisible()).toBe(true);
        });

        it('should hide Generic tab when model licenses exist', () => {
            mockDriverObject.getLicensesByModel.mockReturnValue([{ id: 1 }]);
            expect(viewModel.StatusData.IsGenericTabVisible()).toBe(false);
        });

        it('should show By Model tab when no general license exists', () => {
            mockDriverObject.getGeneralLicence.mockReturnValue(null);
            expect(viewModel.StatusData.IsByModelTabVisible()).toBe(true);
        });

        it('should hide By Model tab when general license exists', () => {
            mockDriverObject.getGeneralLicence.mockReturnValue({ id: 1 });
            expect(viewModel.StatusData.IsByModelTabVisible()).toBe(false);
        });
    });

    describe('License Management', () => {
        it('should handle new general license creation', () => {
            customViewModel.viewModel.createNewGeneralLicence();
            expect(customViewModel.isAddingLicense).toBe(true);
            expect(originalCreateNewGeneralLicence).toHaveBeenCalled();
        });

        it('should handle cancel during new license creation', () => {
            customViewModel.isAddingLicense = true;
            const result = customViewModel.viewModel.CancelEdit();

            expect(result).toBe(false);
            expect(mockDriverObject.setGeneralLicence).toHaveBeenCalledWith(null);
            expect(viewModel.GeneralLicenceFormViewModel.SetLicenceDetailObject).toHaveBeenCalledWith(null);
            expect(customViewModel.isAddingLicense).toBe(false);
        });

        it('should use original CancelEdit when not adding license', () => {
            customViewModel.isAddingLicense = false;
            customViewModel.viewModel.CancelEdit();
            expect(originalCancelEdit).toHaveBeenCalled();
        });
    });

    describe('Driver Object Changes', () => {
        it('should notify subscribers when driver object changes', () => {
            // Store the original computed functions
            const isGenericTabVisibleFn = viewModel.StatusData.IsGenericTabVisible;
            const isByModelTabVisibleFn = viewModel.StatusData.IsByModelTabVisible;

            // Create spies for notifications
            const genericTabSpy = vi.fn();
            const modelTabSpy = vi.fn();

            // Replace the computed functions with spied versions
            viewModel.StatusData.IsGenericTabVisible = {
                ...isGenericTabVisibleFn,
                notifySubscribers: genericTabSpy
            };

            viewModel.StatusData.IsByModelTabVisible = {
                ...isByModelTabVisibleFn,
                notifySubscribers: modelTabSpy
            };

            // Trigger the change
            const newDriver = { ...mockDriverObject };
            viewModel.DriverObject(newDriver);

            // Verify the spies were called
            expect(genericTabSpy).toHaveBeenCalled();
            expect(modelTabSpy).toHaveBeenCalled();
        });
    });

    // New test cases for license deletion functionality
    describe('License Deletion', () => {
        beforeEach(() => {
            // Setup fake timers
            vi.useFakeTimers();

            // Reset mocks for deletion tests
            viewModel.DataStore.SaveObject.mockReset();
            viewModel.controller.ObjectsDataSet.RemoveObject.mockReset();
            global.ApplicationController.showConfirmPopup = vi.fn((vm, message, title, callback) => callback(true));
            global.ApplicationController.showErrorPopup = vi.fn();

            // Setup deleteGeneralLicence implementation
            viewModel.deleteGeneralLicence = function () {
                if (!viewModel.DriverObject() || !viewModel.DriverObject().getGeneralLicence()) return;

                ApplicationController.showConfirmPopup(viewModel, 'Are you sure you want to delete this general license?', 'Delete License', function (confirmed) {
                    if (!confirmed) return;

                    const driver = viewModel.DriverObject();
                    const license = driver.getGeneralLicence();

                    driver.Data.LicenceDetailId(null);
                    driver.Data.LicenseMode(0);
                    driver.setGeneralLicence(null);

                    if (license) {
                        viewModel.controller.ObjectsDataSet.RemoveObject(license);
                    }

                    if (driver.Data.DenyAccessWhenExpired()) {
                        driver.Data.VehicleAccess(0);
                    }

                    setTimeout(() => {
                        viewModel.DataStore.SaveObject({
                            contextId: viewModel.contextId,
                            include: viewModel.include,
                            objectToSave: driver,
                            successHandler: function (objectSaved) {
                                viewModel.SetDriverObject(objectSaved);
                                viewModel.StatusData.DisplayMode('edit');
                            },
                            errorHandler: function (error) {
                                ApplicationController.showErrorPopup(viewModel, 'Failed to save license changes: ' + error.message);
                            }
                        });
                    }, 100);
                }, viewModel.contextId);
            };

            // Setup removeItemCommand implementation
            viewModel.LicensesByModelListViewModel.commands.removeItemCommand = function (itemToRemove) {
                ApplicationController.showConfirmPopup(viewModel, 'Are you sure you want to delete this license by model?', 'Delete License', function (confirmed) {
                    if (!confirmed) return;

                    const driver = viewModel.DriverObject();
                    if (!driver.getLicensesByModel() || driver.getLicensesByModel().length === 0) {
                        driver.Data.LicenseMode(0);
                    }

                    if (driver.Data.DenyAccessWhenExpired()) {
                        driver.Data.VehicleAccess(0);
                    }

                    setTimeout(() => {
                        viewModel.DataStore.SaveObject({
                            contextId: viewModel.contextId,
                            include: viewModel.include,
                            objectToSave: driver,
                            successHandler: function (objectSaved) {
                                viewModel.SetDriverObject(objectSaved);
                                viewModel.StatusData.DisplayMode('edit');
                            },
                            errorHandler: function (error) {
                                ApplicationController.showErrorPopup(viewModel, 'Failed to save license changes: ' + error.message);
                            }
                        });
                    }, 100);
                }, viewModel.contextId);
            };
        });

        afterEach(() => {
            vi.useRealTimers();
        });

        describe('General License', () => {
            it('should maintain edit mode after deletion', async () => {
                // Setup
                mockDriverObject.getGeneralLicence.mockReturnValue({ id: 1 });
                viewModel.StatusData.DisplayMode('edit');

                // Trigger deletion
                viewModel.deleteGeneralLicence();

                // Fast-forward timers
                vi.runAllTimers();

                // Simulate successful save
                const saveConfig = viewModel.DataStore.SaveObject.mock.calls[0][0];
                saveConfig.successHandler(mockDriverObject);

                // Verify state
                expect(viewModel.StatusData.DisplayMode()).toBe('edit');
            });

            it('should update vehicle access when DenyAccessWhenExpired is true', () => {
                // Setup
                mockDriverObject.getGeneralLicence.mockReturnValue({ id: 1 });
                mockDriverObject.Data.DenyAccessWhenExpired(true);

                // Trigger deletion
                viewModel.deleteGeneralLicence();

                // Verify vehicle access update
                expect(mockDriverObject.Data.VehicleAccess()).toBe(0);
            });
        });

        describe('License By Model', () => {
            it('should switch to generic mode when deleting last model license', async () => {
                // Setup
                mockDriverObject.getLicensesByModel.mockReturnValue([]);
                mockDriverObject.Data.LicenseMode(1);

                // Simulate deletion through list view model
                const removeCommand = viewModel.LicensesByModelListViewModel.commands.removeItemCommand;
                removeCommand({ id: 1 });

                // Fast-forward timers
                vi.runAllTimers();

                // Verify mode switch
                expect(mockDriverObject.Data.LicenseMode()).toBe(0);
            });

            it('should maintain edit mode after model license deletion', async () => {
                // Setup
                viewModel.StatusData.DisplayMode('edit');

                // Simulate deletion
                const removeCommand = viewModel.LicensesByModelListViewModel.commands.removeItemCommand;
                removeCommand({ id: 1 });

                // Fast-forward timers
                vi.runAllTimers();

                // Simulate successful save
                const saveConfig = viewModel.DataStore.SaveObject.mock.calls[0][0];
                saveConfig.successHandler(mockDriverObject);

                // Verify state
                expect(viewModel.StatusData.DisplayMode()).toBe('edit');
            });
        });
    });
}); 