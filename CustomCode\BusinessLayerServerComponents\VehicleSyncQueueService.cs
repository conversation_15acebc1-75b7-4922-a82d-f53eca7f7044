using Azure.Messaging.ServiceBus;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    /// <summary>
    /// Interface for vehicle sync queue service that handles sending vehicle sync messages to Azure Service Bus
    /// </summary>
    public interface IVehicleSyncQueueService
    {
        /// <summary>
        /// Sends a vehicle sync message to the Azure Service Bus queue for asynchronous processing
        /// </summary>
        /// <param name="message">The vehicle sync message containing vehicle sync information</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SendVehicleSyncMessageAsync(VehicleSyncMessage message);
    }

    /// <summary>
    /// Service responsible for queuing vehicle sync requests using Azure Service Bus
    /// Provides asynchronous, reliable message delivery for vehicle sync operations
    /// </summary>
    public class VehicleSyncQueueService : IVehicleSyncQueueService, IAsyncDisposable
    {
        private readonly ServiceBusClient _serviceBusClient;
        private readonly ServiceBusSender _sender;
        private readonly ILogger<VehicleSyncQueueService> _logger;

        /// <summary>
        /// Initializes a new instance of the VehicleSyncQueueService
        /// </summary>
        /// <param name="configuration">Configuration instance to get connection string and queue name</param>
        /// <param name="logger">Logger instance for diagnostics</param>
        public VehicleSyncQueueService(IConfiguration configuration, ILogger<VehicleSyncQueueService> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            var connectionString = configuration.GetConnectionString("ServiceBus");
            var queueName = configuration.GetValue<string>("ServiceBus:VehicleSyncQueue", "vehicle-sync");

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("ServiceBus connection string is not configured");
            }

            try
            {
                _serviceBusClient = new ServiceBusClient(connectionString);
                _sender = _serviceBusClient.CreateSender(queueName);
                _logger.LogInformation("VehicleSyncQueueService initialized successfully for queue: {QueueName}", queueName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize VehicleSyncQueueService for queue: {QueueName}", queueName);
                throw;
            }
        }

        /// <summary>
        /// Sends a vehicle sync message to the queue
        /// </summary>
        public async Task SendVehicleSyncMessageAsync(VehicleSyncMessage message)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            try
            {
                var messageJson = JsonSerializer.Serialize(message);
                var serviceBusMessage = new ServiceBusMessage(messageJson)
                {
                    MessageId = Guid.NewGuid().ToString(),
                    SessionId = message.PersonId.ToString(), // Group by person for ordered processing
                    Subject = "VehicleSync",
                    TimeToLive = TimeSpan.FromMinutes(30), // Longer TTL for sync operations
                    CorrelationId = message.CorrelationId ?? Guid.NewGuid().ToString()
                };

                serviceBusMessage.ApplicationProperties["PersonId"] = message.PersonId.ToString();
                serviceBusMessage.ApplicationProperties["VehicleId"] = message.VehicleId.ToString();
                serviceBusMessage.ApplicationProperties["SyncReason"] = message.SyncReason ?? "Unknown";
                serviceBusMessage.ApplicationProperties["Priority"] = message.Priority;
                serviceBusMessage.ApplicationProperties["InitiatedByUserId"] = message.InitiatedByUserId?.ToString() ?? "";
                serviceBusMessage.ApplicationProperties["VehicleSequence"] = message.VehicleSequence;
                serviceBusMessage.ApplicationProperties["TotalVehicles"] = message.TotalVehicles;

                await _sender.SendMessageAsync(serviceBusMessage);
                _logger.LogInformation("[PERF] Vehicle sync message sent for person {PersonId}, vehicle: {VehicleId} ({VehicleSequence}/{TotalVehicles}), reason: {SyncReason}",
                    message.PersonId, message.VehicleId, message.VehicleSequence, message.TotalVehicles, message.SyncReason);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PERF] Failed to send vehicle sync message for person {PersonId}, vehicle {VehicleId}", message.PersonId, message.VehicleId);
                throw;
            }
        }

        /// <summary>
        /// Disposes the Azure Service Bus resources asynchronously
        /// </summary>
        /// <returns>Task representing the asynchronous disposal operation</returns>
        public async ValueTask DisposeAsync()
        {
            if (_sender != null)
            {
                await _sender.DisposeAsync();
            }

            if (_serviceBusClient != null)
            {
                await _serviceBusClient.DisposeAsync();
            }
        }
    }
}