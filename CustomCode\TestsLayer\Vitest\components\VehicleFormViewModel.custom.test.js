import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehicleFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Mock window.location
        global.window = {
            location: {
                reload: vi.fn(),
                hash: ''
            }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Messages: {
                    confirmDeleteMessage: "Are you sure you want to delete this %ENTITY%?",
                    confirmDeletePopupTitle: "Confirm Delete"
                }
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehicleFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        HasVehiclesAccess: 'True',
                        CanViewVORStatus: 'True',
                        CanEditVehicle: 'True',
                        CanViewChecklist: 'True',
                        CanViewChecklistSetting: 'True',
                        CanViewImpactSetting: 'True',
                        CanViewService: 'True',
                        CanViewFullImpactLockout: 'True'
                    })
                }
            }
        };

        // Create base view model with required properties
        viewModel = {
            CurrentObject: ko.observable({
                Data: {
                    Id: ko.observable('123'),
                    IsNew: ko.observable(false),
                    ChecklistSettings: ko.observable(null)
                }
            }),
            VehicleObject: function () { return viewModel.CurrentObject(); },
            onDeleteSuccess: vi.fn(),
            VehicleOtherSettingsFormViewModel: {
                SavedData: {
                    Data: {
                        VORStatus: ko.observable('oldValue')
                    }
                },
                VehicleOtherSettingsObject: ko.observable({
                    Data: {
                        VORStatus: ko.observable('newValue')
                    }
                })
            },
            StatusData: {
                DisplayMode: ko.observable('view'),
                IsEmpty: ko.observable(false),
                IsUIDirty: ko.observable(false),
                isValid: ko.observable(true),
                errorSummary: ko.observableArray([]),
                IsCHECKLISTTabVisible: ko.observable(true),
                IsIMPACTSETTINGSTabVisible: ko.observable(true),
                IsSERVICETabVisible: ko.observable(true),
                IsMORESETTINGSTabVisible: ko.observable(true)
            },
            controller: {
                applicationController: {
                    showAlertPopup: vi.fn(),
                    customNavigateToVehicleDetail: vi.fn(),
                    getProxyForComponent: vi.fn().mockReturnValue({
                        SoftDelete: vi.fn()
                    }),
                    showConfirmPopup: vi.fn(),
                    closePopup: vi.fn(),
                    setIsBusy: vi.fn()
                },
                ObjectsDataSet: {
                    isContextIdDirty: vi.fn()
                }
            },
            contextId: 'test-context',
            resetValidation: vi.fn(),
            Events: {
                VehicleSaved: ko.observable(false),
                CancelEdit: ko.observable(false),
                VehicleLoaded: ko.observable(false)
            },
            subscriptions: [],
            Commands: {
                IsSendQuestionsToVehicleCommandVisible: ko.observable(false)
            },
            DataStore: {
                CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
            },
            Delete: vi.fn(),
            ShowError: vi.fn()
        };

        // Mock the Events.VehicleSaved.subscribe method
        viewModel.Events.VehicleSaved.subscribe = vi.fn();

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.VehicleFormViewModelCustom(viewModel);
        customViewModel.initialize();

        // Setup fake timers
        vi.useFakeTimers();
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    describe('onAfterSave', () => {
        it('should show VOR status update popup when VORStatus has changed', () => {
            // Call onAfterSave
            customViewModel.onAfterSave();

            // Verify that showAlertPopup was called with correct parameters
            expect(viewModel.controller.applicationController.showAlertPopup).toHaveBeenCalledWith(
                viewModel,
                "The VOR setting will now be synchronized with the vehicle. Once the vehicle receives the VOR mode, the value in the dashboard will reflect the changes.",
                "VOR Status Update",
                null,
                viewModel.contextId
            );
        });

        it('should not show VOR status update popup when VORStatus has not changed', () => {
            // Set same value for both old and new VORStatus
            viewModel.VehicleOtherSettingsFormViewModel.SavedData.Data.VORStatus('sameValue');
            viewModel.VehicleOtherSettingsFormViewModel.VehicleOtherSettingsObject().Data.VORStatus('sameValue');

            // Call onAfterSave
            customViewModel.onAfterSave();

            // Verify that showAlertPopup was not called
            expect(viewModel.controller.applicationController.showAlertPopup).not.toHaveBeenCalled();
        });

        it('should handle navigation after save', () => {
            // Call onAfterSave
            customViewModel.onAfterSave();

            // Verify that VehicleSaved event subscription was set up
            expect(viewModel.Events.VehicleSaved.subscribe).toHaveBeenCalled();
        });

        it('should update vehicle state after save', () => {
            // Call onAfterSave
            customViewModel.onAfterSave();

            // Verify that IsNew was set to false
            expect(viewModel.CurrentObject().Data.IsNew()).toBe(false);

            // Verify that validation was reset
            expect(viewModel.resetValidation).toHaveBeenCalled();
        });
    });

    describe('IsCreateNewCommandVisible', () => {
        it('should always return false', () => {
            expect(customViewModel.IsCreateNewCommandVisible()).toBe(false);
        });
    });

    describe('IsModifyCommandVisible', () => {
        it('should return true when user has edit permissions', () => {
            expect(customViewModel.IsModifyCommandVisible()).toBe(true);
        });

        it('should return false when user does not have edit permissions', () => {
            // Update user claims to remove edit permission
            global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                HasVehiclesAccess: 'True',
                CanViewVORStatus: 'True',
                CanEditVehicle: 'False'
            });

            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });
    });

    describe('Delete functionality', () => {
        beforeEach(() => {
            // Add mock for VehicleAPI proxy
            viewModel.controller.applicationController.getProxyForComponent = vi.fn().mockReturnValue({
                SoftDelete: vi.fn()
            });

            // Add mock for showConfirmPopup
            viewModel.controller.applicationController.showConfirmPopup = vi.fn();

            // Add mock for closePopup
            viewModel.closePopup = vi.fn();

            // Add mock for setIsBusy
            viewModel.setIsBusy = vi.fn();
        });

        it('should show confirmation popup when Delete is called', () => {
            // Call Delete
            viewModel.Delete();

            // Verify that setIsBusy was called
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);

            // Verify that showConfirmPopup was called with correct parameters
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                FleetXQ.Web.Messages.confirmDeleteMessage.replace(/%ENTITY%/g, "Vehicle"),
                FleetXQ.Web.Messages.confirmDeletePopupTitle,
                expect.any(Function),
                viewModel.contextId
            );
        });

        it('should handle successful deletion', () => {
            // Setup mock data
            const mockData = { success: true };

            // Get the onConfirmDelete callback (4th argument)
            viewModel.Delete();
            const onConfirmDelete = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];

            // Call onConfirmDelete with true
            onConfirmDelete(true);

            // Verify that SoftDelete was called with correct configuration
            expect(viewModel.controller.applicationController.getProxyForComponent('VehicleAPI').SoftDelete)
                .toHaveBeenCalledWith(expect.objectContaining({
                    caller: viewModel,
                    contextId: viewModel.contextId,
                    vehicleId: viewModel.CurrentObject().Data.Id()
                }));
        });

        it('should handle deletion cancellation', () => {
            // Get the onConfirmDelete callback (4th argument)
            viewModel.Delete();
            const onConfirmDelete = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];

            // Call onConfirmDelete with false
            onConfirmDelete(false);

            // Verify that setIsBusy was called with false
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);

            // Verify that SoftDelete was not called
            expect(viewModel.controller.applicationController.getProxyForComponent('VehicleAPI').SoftDelete)
                .not.toHaveBeenCalled();
        });

        it('should handle successful deletion callback', () => {
            // Setup mock data
            const mockData = { success: true };

            // Get the onConfirmDelete callback (4th argument)
            viewModel.Delete();
            const onConfirmDelete = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];
            onConfirmDelete(true);

            // Get the configuration object passed to SoftDelete
            const config = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI').SoftDelete.mock.calls[0][0];
            const successHandler = config.successHandler;

            // Call success handler
            successHandler(mockData);

            // Verify that closePopup was called
            expect(viewModel.closePopup).toHaveBeenCalledWith(true);

            // Verify that window.location.hash was updated
            expect(window.location.hash).toBe('!/Vehicles');
        });

        it('should handle deletion error', () => {
            // Setup mock error handler
            const mockError = new Error('Delete failed');
            viewModel.ShowError = vi.fn();

            // Get the onConfirmDelete callback (4th argument)
            viewModel.Delete();
            const onConfirmDelete = viewModel.controller.applicationController.showConfirmPopup.mock.calls[0][3];
            onConfirmDelete(true);

            // Get the configuration object passed to SoftDelete
            const config = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI').SoftDelete.mock.calls[0][0];
            const errorHandler = config.errorHandler;

            // Call error handler
            errorHandler(mockError);

            // Verify that ShowError was called
            expect(viewModel.ShowError).toHaveBeenCalledWith(mockError);
        });
    });
}); 