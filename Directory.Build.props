﻿

<Project>
  <!-- 
    Unified Version Management for GO Application
    
    NOTE: This file manages go-application/ projects only. Framework projects (go-framework/) 
    use Git-based versioning. External packages are managed in the root 
    Directory.Packages.props for the entire solution.
  -->
  <PropertyGroup>
    <!-- Application projects use build number-based versioning generated at generation time -->
    <AssemblyVersion>1.0.6900</AssemblyVersion>
    <FileVersion>1.0.6900</FileVersion>
    <PackageVersion>1.0.6900</PackageVersion>
    <InformationalVersion>1.0.6900</InformationalVersion>
    
    <!-- Standard assembly properties -->
    <Company>Generative Objects</Company>
    <Product>FleetXQ</Product>
    <Copyright>Copyright � Generative Objects 2025</Copyright>
    
    <!-- Common build settings -->
    <TargetFramework>net8.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    
    
    <!-- Repository information -->
    <RepositoryType>git</RepositoryType>
    <RepositoryUrl>https://github.com/generativeobjects/go-modeler</RepositoryUrl>
    
    <!-- Common warning suppressions for generated code -->
    <NoWarn>$(NoWarn);CS1591;CS0618;CS8618;CS8601;CS8602;CS8603;CS8604</NoWarn>
  </PropertyGroup>
  
  <!-- 
    VERSION STRATEGY:
    - GeneratedCode: Uses build number version (1.0.{BuildNumber}) generated at generation time
    - CustomCode: Matches GeneratedCode version for compatibility
    - GenerationModels: Uses build number-based versioning
  -->
  
  <!-- Generated application assemblies - use build number versioning -->
  <PropertyGroup Condition="$(MSBuildProjectDirectory.Contains('GeneratedCode'))">
    <Description>Generated assembly from FleetXQ ($(InformationalVersion))</Description>
  </PropertyGroup>
  
  <!-- Custom code assemblies - use build number versioning -->
  <PropertyGroup Condition="$(MSBuildProjectDirectory.Contains('CustomCode'))">
    <Description>Custom extensions for FleetXQ ($(InformationalVersion))</Description>
  </PropertyGroup>
  
  <!-- Generation models - use build number versioning -->
  <PropertyGroup Condition="$(MSBuildProjectDirectory.Contains('GenerationModels'))">
    <Description>Generation models for FleetXQ ($(InformationalVersion))</Description>
  </PropertyGroup>

  <!-- Debug configuration -->
  <PropertyGroup Condition="'$(Configuration)' == 'Debug'">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
  </PropertyGroup>

  <!-- Release configuration -->
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <DefineConstants>TRACE</DefineConstants>
  </PropertyGroup>

  <!-- Project-specific descriptions -->
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('DataLayer'))">
    <Description>$(Description) - Data access layer with ORM mappings</Description>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('BusinessLayer'))">
    <Description>$(Description) - Business logic and domain services</Description>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('ServiceLayer'))">
    <Description>$(Description) - API and service endpoints</Description>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('WebApplication'))">
    <Description>$(Description) - Web application and UI components</Description>
  </PropertyGroup>
  
  <PropertyGroup Condition="$(MSBuildProjectName.Contains('Test'))">
    <Description>$(Description) - Unit and integration tests</Description>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <!-- Import custom package versions if the file exists -->
  <Import Project="$(MSBuildThisFileDirectory)Directory.Packages.Custom.props" 
          Condition="Exists('$(MSBuildThisFileDirectory)Directory.Packages.Custom.props')" />

</Project> 
