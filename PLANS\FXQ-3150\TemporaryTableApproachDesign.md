# Temporary Table Approach Design Document
## FXQ-3150 Bulk Import Implementation

### Executive Summary

This document outlines the alternative approach to bulk data import that **avoids creating any permanent staging tables** in the database schema. Instead, the solution uses **session-scoped temporary tables** managed within stored procedures, combined with SqlBulkCopy for high-performance data loading.

### Problem Statement

The original bulk import plan included creating permanent staging tables to facilitate high-volume data import. However, to avoid permanent database schema changes and maintain a clean database design, an alternative approach was needed that:

1. Maintains high performance for bulk operations
2. Provides data validation and error handling
3. Supports atomic transaction processing
4. Requires no permanent schema modifications
5. Automatically cleans up temporary resources

### Solution Architecture

#### Core Components

1. **Temporary Table Management**: Stored procedures create and manage session-scoped temporary tables
2. **SqlBulkCopy Integration**: High-performance bulk loading directly into temporary tables
3. **Streaming Data Readers**: Memory-efficient data processing without large object allocation
4. **Validation & Merge Logic**: Comprehensive validation before atomic merge to production tables
5. **Session Tracking**: Import session management for auditing and monitoring

#### Data Flow

```mermaid
graph TD
    A[CSV/Data Source] --> B[Streaming Data Reader]
    B --> C[Initialize Import Session]
    C --> D[Create Temporary Table #ImportDrivers/#ImportVehicles]
    D --> E[SqlBulkCopy to Temp Table]
    E --> F[Validation & Business Rules]
    F --> G{Validation Passed?}
    G -->|Yes| H[MERGE to Production Tables]
    G -->|No| I[Return Validation Errors]
    H --> J[Update Session Status]
    I --> K[Rollback & Cleanup]
    J --> L[Automatic Temp Table Cleanup]
    K --> L
```

### Implementation Details

#### 1. Stored Procedure Architecture

**Session Initialization Procedures:**
- `BeginDriverImport`: Creates temporary table `#ImportDrivers` with optimal indexing
- `BeginVehicleImport`: Creates temporary table `#ImportVehicles` with optimal indexing

**Processing Procedures:**
- `ProcessDriverImport`: Validates and merges driver data from temp to production tables
- `ProcessVehicleImport`: Validates and merges vehicle data from temp to production tables
- `GetImportSessionStatus`: Retrieves import session status and metrics

**Key Features:**
- Session-scoped temporary tables (automatically dropped when connection closes)
- Comprehensive validation with detailed error reporting
- Atomic transactions with rollback capability
- Performance-optimized indexing on temporary tables
- Import session tracking for audit trails

#### 2. C# Implementation

**BulkImportEngine Class:**
- Manages the complete import workflow
- Handles connection lifecycle and session management
- Provides comprehensive error handling and logging
- Supports configurable batch sizes and timeouts

**Streaming Data Readers:**
- `DriverDataReader`: Implements IDataReader for driver data streaming
- `VehicleDataReader`: Implements IDataReader for vehicle data streaming
- `ConfigurableDataReader<T>`: Generic reader for custom entity types
- Memory-efficient processing without large object allocation

#### 3. Performance Characteristics

**Temporary Table Benefits:**
- Session-scoped lifecycle (automatic cleanup)
- Support for custom indexing for merge performance
- Can utilize SqlBulkCopy for high-throughput loading
- Support for large datasets (limited only by tempdb space)
- Isolated per connection/session

**SqlBulkCopy Optimization:**
- Batch size: 10,000 rows (configurable)
- Streaming enabled for memory efficiency
- Column mapping for data type safety
- Transaction-based for atomicity

### Comparison with Alternatives

| Approach | Performance | Memory Usage | Schema Impact | Cleanup | Complexity |
|----------|-------------|--------------|---------------|---------|------------|
| **Temporary Tables** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ✅ None | ✅ Automatic | ⭐⭐⭐ |
| Permanent Staging | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ❌ Required | ❌ Manual | ⭐⭐⭐⭐ |
| Table Variables | ⭐⭐ | ⭐⭐ | ✅ None | ✅ Automatic | ⭐⭐ |
| In-Memory Collections | ⭐⭐⭐ | ⭐ | ✅ None | ✅ Automatic | ⭐⭐ |
| CTE/Direct Insert | ⭐⭐ | ⭐⭐⭐⭐ | ✅ None | ✅ Automatic | ⭐⭐⭐⭐⭐ |

### Usage Examples

#### Basic Driver Import
```csharp
var engine = new BulkImportEngine(configuration, logger);
var drivers = LoadDriversFromCsv("drivers.csv");

var result = await engine.ImportDriversAsync(drivers, allowUpdates: true);
if (result.Success)
{
    Console.WriteLine($"Imported {result.ProcessedRowCount} drivers");
}
else
{
    Console.WriteLine($"Import failed: {result.ErrorMessage}");
}
```

#### Large-Scale Batch Processing
```csharp
var drivers = LoadLargeDriverDataset(); // Could be millions of records
var batches = drivers.Batch(50000); // Process in 50k batches

foreach (var batch in batches)
{
    var result = await engine.ImportDriversAsync(batch);
    // Each batch uses its own session and temporary tables
}
```

#### Streaming from CSV
```csharp
public IEnumerable<DriverImportRow> StreamDriversFromCsv(string filePath)
{
    using var reader = new StreamReader(filePath);
    using var csv = new CsvReader(reader, CultureInfo.InvariantCulture);
    
    foreach (var record in csv.GetRecords<DriverImportRow>())
    {
        yield return record; // Streams data without loading entire file
    }
}
```

### Benefits of This Approach

1. **No Schema Changes**: Zero impact on production database schema
2. **High Performance**: Maintains SqlBulkCopy performance benefits
3. **Memory Efficient**: Streaming processing without large object allocation
4. **Automatic Cleanup**: Temporary tables dropped when session ends
5. **Comprehensive Validation**: Business rule validation before data commitment
6. **Atomic Operations**: Full transaction support with rollback capability
7. **Audit Trail**: Complete session tracking and error reporting
8. **Scalable**: Handles datasets from thousands to millions of records
9. **Flexible**: Easy to extend for new entity types

### Configuration

```json
{
  "BulkImport": {
    "BatchSize": 10000,
    "CommandTimeoutSeconds": 300,
    "BulkCopyTimeoutSeconds": 600
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=...;Database=...;..."
  }
}
```

### Error Handling & Monitoring

- **Validation Errors**: Detailed row-level error reporting with line numbers
- **Session Tracking**: Complete audit trail of import operations
- **Performance Metrics**: Duration, throughput, and row count tracking
- **Structured Logging**: Machine-readable logs for monitoring and alerting
- **Graceful Failures**: Rollback and cleanup on any errors

### Conclusion

The temporary table approach provides an optimal balance of performance, reliability, and maintainability while completely avoiding permanent database schema changes. This solution maintains all the benefits of staging tables while ensuring automatic cleanup and zero schema impact on the production database.

This approach is recommended for the FXQ-3150 bulk import implementation as it meets all requirements while maintaining FleetXQ's architectural principles and database design standards.
