-- =============================================
-- Bulk Import Drivers Using Temporary Tables
-- =============================================

-- Step 1: Initialize Driver Import Session
-- Creates temporary table for bulk data loading
CREATE OR ALTER PROCEDURE [dbo].[BeginDriverImport]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Create session-specific temporary table for drivers
    CREATE TABLE #ImportDrivers (
        ExternalId NVARCHAR(50) NOT NULL,
        FirstName NVARCHAR(100) NOT NULL,
        LastName NVARCHAR(100) NOT NULL,
        Email NVARCHAR(200) NULL,
        PhoneNumber NVARCHAR(50) NULL,
        EmployeeNumber NVARCHAR(50) NULL,
        DepartmentName NVARCHAR(100) NULL,
        SiteName NVARCHAR(100) NULL,
        CustomerName NVARCHAR(100) NULL,
        Active BIT DEFAULT 1,
        -- Add indexes for performance during merge
        INDEX IX_ImportDrivers_ExternalId NONCLUSTERED (ExternalId),
        INDEX IX_ImportDrivers_Lookup NONCLUSTERED (CustomerName, SiteName, DepartmentName)
    );
    
    -- Store session metadata
    INSERT INTO ImportSession (Id, EntityType, Status, StartedAt)
    VALUES (@ImportSessionId, 'Driver', 'InProgress', GETUTCDATE());
    
    SELECT 'Driver import session initialized' AS Message;
END
GO

-- Step 2: Process Driver Import Data
-- Validates and merges data from temporary table to production
CREATE OR ALTER PROCEDURE [dbo].[ProcessDriverImport]
    @ImportSessionId UNIQUEIDENTIFIER,
    @AllowUpdates BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @ErrorCount INT = 0;
    DECLARE @ValidationErrors TABLE (
        RowNumber INT,
        ExternalId NVARCHAR(50),
        ErrorMessage NVARCHAR(500)
    );
    
    -- Check if temp table exists (should be created by BeginDriverImport)
    IF OBJECT_ID('tempdb..#ImportDrivers') IS NULL
    BEGIN
        RAISERROR('Import session not initialized. Call BeginDriverImport first.', 16, 1);
        RETURN;
    END
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Add row numbers for error reporting
        ALTER TABLE #ImportDrivers ADD RowNumber INT IDENTITY(1,1);
        
        -- Validation 1: Check for duplicate ExternalIds in import data
        INSERT INTO @ValidationErrors (RowNumber, ExternalId, ErrorMessage)
        SELECT 
            MIN(RowNumber),
            ExternalId,
            'Duplicate ExternalId in import data: ' + ExternalId
        FROM #ImportDrivers
        GROUP BY ExternalId
        HAVING COUNT(*) > 1;
        
        -- Validation 2: Check for required fields
        INSERT INTO @ValidationErrors (RowNumber, ExternalId, ErrorMessage)
        SELECT RowNumber, ExternalId, 'FirstName is required'
        FROM #ImportDrivers
        WHERE FirstName IS NULL OR LTRIM(RTRIM(FirstName)) = '';
        
        INSERT INTO @ValidationErrors (RowNumber, ExternalId, ErrorMessage)
        SELECT RowNumber, ExternalId, 'LastName is required'
        FROM #ImportDrivers
        WHERE LastName IS NULL OR LTRIM(RTRIM(LastName)) = '';
        
        -- Validation 3: Check for valid Customer/Site/Department combinations
        WITH HierarchyValidation AS (
            SELECT 
                i.RowNumber,
                i.ExternalId,
                i.CustomerName,
                i.SiteName,
                i.DepartmentName,
                c.Id AS CustomerId,
                s.Id AS SiteId,
                d.Id AS DepartmentId
            FROM #ImportDrivers i
            LEFT JOIN dbo.Customer c ON c.CompanyName = i.CustomerName AND c.Active = 1
            LEFT JOIN dbo.Site s ON s.Name = i.SiteName AND s.CustomerId = c.Id AND s.Active = 1
            LEFT JOIN dbo.Department d ON d.Name = i.DepartmentName AND d.SiteId = s.Id AND d.Active = 1
        )
        INSERT INTO @ValidationErrors (RowNumber, ExternalId, ErrorMessage)
        SELECT RowNumber, ExternalId, 
            CASE 
                WHEN CustomerId IS NULL THEN 'Invalid Customer: ' + ISNULL(CustomerName, 'NULL')
                WHEN SiteId IS NULL THEN 'Invalid Site: ' + ISNULL(SiteName, 'NULL') + ' for Customer: ' + CustomerName
                WHEN DepartmentId IS NULL THEN 'Invalid Department: ' + ISNULL(DepartmentName, 'NULL') + ' for Site: ' + SiteName
            END
        FROM HierarchyValidation
        WHERE CustomerId IS NULL OR SiteId IS NULL OR DepartmentId IS NULL;
        
        -- If validation errors exist, return them and rollback
        IF EXISTS (SELECT 1 FROM @ValidationErrors)
        BEGIN
            SELECT * FROM @ValidationErrors ORDER BY RowNumber;
            SET @ErrorCount = @@ROWCOUNT;
            
            UPDATE ImportSession 
            SET Status = 'Failed', 
                EndedAt = GETUTCDATE(),
                ErrorMessage = 'Validation failed with ' + CAST(@ErrorCount AS NVARCHAR(10)) + ' errors'
            WHERE Id = @ImportSessionId;
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Create lookup table for valid hierarchy
        CREATE TABLE #ValidHierarchy (
            ExternalId NVARCHAR(50),
            CustomerId UNIQUEIDENTIFIER,
            SiteId UNIQUEIDENTIFIER,
            DepartmentId UNIQUEIDENTIFIER,
            INDEX IX_ExternalId (ExternalId)
        );
        
        INSERT INTO #ValidHierarchy
        SELECT 
            i.ExternalId,
            c.Id,
            s.Id,
            d.Id
        FROM #ImportDrivers i
        INNER JOIN dbo.Customer c ON c.CompanyName = i.CustomerName AND c.Active = 1
        INNER JOIN dbo.Site s ON s.Name = i.SiteName AND s.CustomerId = c.Id AND s.Active = 1
        INNER JOIN dbo.Department d ON d.Name = i.DepartmentName AND d.SiteId = s.Id AND d.Active = 1;
        
        -- Perform the merge operation
        WITH SourceData AS (
            SELECT 
                NEWID() AS NewId,
                i.ExternalId,
                i.FirstName,
                i.LastName,
                i.Email,
                i.PhoneNumber,
                i.EmployeeNumber,
                i.Active,
                h.CustomerId,
                h.SiteId,
                h.DepartmentId,
                @ImportSessionId AS ImportSessionId,
                GETUTCDATE() AS ImportDate
            FROM #ImportDrivers i
            INNER JOIN #ValidHierarchy h ON i.ExternalId = h.ExternalId
        )
        MERGE dbo.Driver AS Target
        USING SourceData AS Source ON Target.ExternalId = Source.ExternalId
        WHEN NOT MATCHED THEN
            INSERT (
                Id, ExternalId, FirstName, LastName, Email, PhoneNumber, 
                EmployeeNumber, Active, CustomerId, ImportSessionId, CreatedDate
            )
            VALUES (
                Source.NewId, Source.ExternalId, Source.FirstName, Source.LastName, 
                Source.Email, Source.PhoneNumber, Source.EmployeeNumber, Source.Active,
                Source.CustomerId, Source.ImportSessionId, Source.ImportDate
            )
        WHEN MATCHED AND @AllowUpdates = 1 THEN
            UPDATE SET 
                FirstName = Source.FirstName,
                LastName = Source.LastName,
                Email = Source.Email,
                PhoneNumber = Source.PhoneNumber,
                EmployeeNumber = Source.EmployeeNumber,
                Active = Source.Active,
                LastModifiedDate = Source.ImportDate;
        
        SET @ProcessedCount = @@ROWCOUNT;
        
        -- Update import session
        UPDATE ImportSession 
        SET Status = 'Completed', 
            EndedAt = GETUTCDATE(),
            ProcessedCount = @ProcessedCount
        WHERE Id = @ImportSessionId;
        
        COMMIT TRANSACTION;
        
        -- Return success summary
        SELECT 
            @ProcessedCount AS ProcessedRows,
            (SELECT COUNT(*) FROM #ImportDrivers) AS InputRows,
            'Import completed successfully' AS Message;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Log error
        UPDATE ImportSession 
        SET Status = 'Failed', 
            EndedAt = GETUTCDATE(),
            ErrorMessage = ERROR_MESSAGE()
        WHERE Id = @ImportSessionId;
        
        -- Re-raise the error
        THROW;
    END CATCH
END
GO

-- Step 3: Get Import Session Status
CREATE OR ALTER PROCEDURE [dbo].[GetImportSessionStatus]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        Id,
        EntityType,
        Status,
        StartedAt,
        EndedAt,
        ProcessedCount,
        ErrorMessage,
        DATEDIFF(MILLISECOND, StartedAt, ISNULL(EndedAt, GETUTCDATE())) AS DurationMs
    FROM ImportSession
    WHERE Id = @ImportSessionId;
END
GO

-- Supporting table for import session tracking (if not exists)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'ImportSession')
BEGIN
    CREATE TABLE ImportSession (
        Id UNIQUEIDENTIFIER PRIMARY KEY,
        EntityType NVARCHAR(50) NOT NULL,
        Status NVARCHAR(20) NOT NULL, -- InProgress, Completed, Failed
        StartedAt DATETIME NOT NULL,
        EndedAt DATETIME NULL,
        ProcessedCount INT DEFAULT 0,
        ErrorMessage NVARCHAR(MAX) NULL,
        CreatedBy NVARCHAR(100) DEFAULT SYSTEM_USER
    );
END
GO
