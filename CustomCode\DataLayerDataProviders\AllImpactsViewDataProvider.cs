﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.Data.DataProviders.Custom

{
    public class AllImpactsViewDataProvider : DataProvider<AllImpactsViewDataObject>
    {
        protected readonly IConfiguration _configuration;
        public AllImpactsViewDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<AllImpactsViewDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetAllImpacts", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasImpactLevel)
                    {
                        var impactLevel = (int)filterArguments[filter.ImpactLevelParameterNumber];
                        command.Parameters.Add(new SqlParameter("@ImpactLevel", SqlDbType.Int) { Value = impactLevel });
                    }
                    else
                    {
                        // When no filter is selected, set @ImpactLevel to 1 (AmberLevelAndAbove) to exclude blue impacts
                        command.Parameters.Add(new SqlParameter("@ImpactLevel", SqlDbType.Int) { Value = 1 }); // AmberLevelAndAbove
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }

        protected override async Task DoDeleteAsync(AllImpactsViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<AllImpactsViewDataObject> DoGetAsync(AllImpactsViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetAllImpacts", connection))
                {
                    AllImpactsViewDataObject result = null;

                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@ImpactId", SqlDbType.UniqueIdentifier) { Value = entity.Id });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                result = _serviceProvider.GetRequiredService<AllImpactsViewDataObject>();
                                result.IsNew = false;

                                // Assuming the stored procedure returns Id, VehicleId, LoggedHours, and SeatHours
                                result.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                result.ImpactId = reader.GetGuid(reader.GetOrdinal("ImpactId"));
                                result.ImpactType = (ImpactTypeEnum)reader.GetInt32(reader.GetOrdinal("ImpactType"));
                                result.GForceLevel = reader.GetString(reader.GetOrdinal("GForceLevel"));
                                result.ShockValue = reader.GetDouble(reader.GetOrdinal("ShockValue"));
                                result.Threshold = reader.GetDouble(reader.GetOrdinal("Threshold"));
                                result.TimezoneAdjustedImpactDatetime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedImpactDatetime"));
                                result.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));

                                context.AddObject(result);
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get AllImpactsViewDataObject data", "Unable to get AllImpactsViewDataObject data", ex);
                    }
                }
            }
        }

        protected override async Task<DataObjectCollection<AllImpactsViewDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<AllImpactsViewDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetAllImpacts", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasImpactLevel)
                    {
                        var impactLevel = (int)filterArguments[filter.ImpactLevelParameterNumber];
                        command.Parameters.Add(new SqlParameter("@ImpactLevel", SqlDbType.Int) { Value = impactLevel });
                    }
                    else
                    {
                        // When no filter is selected, set @ImpactLevel to 1 (AmberLevelAndAbove) to exclude blue impacts
                        command.Parameters.Add(new SqlParameter("@ImpactLevel", SqlDbType.Int) { Value = 1 }); // AmberLevelAndAbove
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }


                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<AllImpactsViewDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, VehicleId, LoggedHours, and SeatHours
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.ImpactId = reader.GetGuid(reader.GetOrdinal("ImpactId"));
                                    entity.ImpactType = (ImpactTypeEnum)reader.GetInt32(reader.GetOrdinal("ImpactType"));
                                    entity.GForceLevel = reader.GetString(reader.GetOrdinal("GForceLevel"));
                                    entity.ShockValue = reader.GetDouble(reader.GetOrdinal("ShockValue"));
                                    entity.Threshold = reader.GetDouble(reader.GetOrdinal("Threshold"));
                                    entity.TimezoneAdjustedImpactDatetime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedImpactDatetime"));
                                    entity.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));
                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GeneralProductivityPerDriverViewLatest data", "Unable to get GeneralProductivityPerDriverViewLatest data", ex);
                    }
                }
            }
        }
        protected override async Task<AllImpactsViewDataObject> DoSaveAsync(AllImpactsViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
