import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('LoggedHoursVersusSeatHoursViewReportViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/LoggedHoursVersusSeatHoursView/LoggedHoursVersusSeatHoursViewReportViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            LoggedHoursVersusSeatHoursViewObjectCollection: ko.observableArray([]),
            sortColumnName: ko.observable(''),
            getDataHydraulicHoursForChart: vi.fn(),
            getDataLoggedHoursForChart: vi.fn(),
            getDataTractionHoursForChart: vi.fn(),
            getDataSeatHoursForChart: vi.fn(),
            getLabelsForChart: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.LoggedHoursVersusSeatHoursViewReportViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('initialize', () => {
        it('should set initial sort column to Order', () => {
            expect(viewModel.sortColumnName()).toBe('Order');
        });

        it('should override chart data methods with aggregated versions', () => {
            expect(typeof viewModel.getDataHydraulicHoursForChart).toBe('function');
            expect(typeof viewModel.getDataLoggedHoursForChart).toBe('function');
            expect(typeof viewModel.getDataTractionHoursForChart).toBe('function');
            expect(typeof viewModel.getDataSeatHoursForChart).toBe('function');
            expect(typeof viewModel.getLabelsForChart).toBe('function');
        });
    });

    describe('parseDate', () => {
        it('should correctly parse valid date components', () => {
            const date = customViewModel.parseDate(15, 3, 2024);
            expect(date.getDate()).toBe(15);
            expect(date.getMonth()).toBe(2); // 0-based month
            expect(date.getFullYear()).toBe(2024);
        });

        it('should handle invalid date components gracefully', () => {
            const date = customViewModel.parseDate('invalid', 'invalid', 'invalid');
            expect(date.getTime()).toBe(NaN);
        });
    });

    describe('getAggregatedDataForChart', () => {
        it('should aggregate data correctly for hydraulic hours', () => {
            const testData = [
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024',
                        HydraulicHours: () => 5.5
                    }
                },
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024',
                        HydraulicHours: () => 3.5
                    }
                }
            ];
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection(testData);

            const aggregatedData = customViewModel.getAggregatedDataForChart('HydraulicHours')();
            expect(aggregatedData).toEqual([9]); // 5.5 + 3.5 = 9
        });

        it('should handle multiple metrics correctly', () => {
            const testData = [
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024',
                        HydraulicHours: () => 5,
                        LoggedHours: () => 8,
                        TractionHours: () => 3,
                        SeatHours: () => 10
                    }
                }
            ];
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection(testData);

            const hydraulicData = customViewModel.getAggregatedDataForChart('HydraulicHours')();
            const loggedData = customViewModel.getAggregatedDataForChart('LoggedHours')();
            const tractionData = customViewModel.getAggregatedDataForChart('TractionHours')();
            const seatData = customViewModel.getAggregatedDataForChart('SeatHours')();

            expect(hydraulicData).toEqual([5]);
            expect(loggedData).toEqual([8]);
            expect(tractionData).toEqual([3]);
            expect(seatData).toEqual([10]);
        });

        it('should handle empty collection', () => {
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection([]);
            const aggregatedData = customViewModel.getAggregatedDataForChart('HydraulicHours')();
            expect(aggregatedData).toEqual([]);
        });

        it('should handle invalid data gracefully', () => {
            const testData = [
                {
                    Data: {
                        Day: () => null,
                        Month: () => null,
                        Year: () => null,
                        DateDisplay: () => null,
                        HydraulicHours: () => 'invalid'
                    }
                }
            ];
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection(testData);

            const aggregatedData = customViewModel.getAggregatedDataForChart('HydraulicHours')();
            expect(aggregatedData).toEqual([]);
        });
    });

    describe('getAggregatedLabelsForChart', () => {
        it('should return chronologically sorted unique dates', () => {
            const testData = [
                {
                    Data: {
                        Day: () => 2,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '02/01/2024'
                    }
                },
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024'
                    }
                }
            ];
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection(testData);

            const labels = customViewModel.getAggregatedLabelsForChart();
            expect(labels).toEqual(['01/01/2024', '02/01/2024']);
        });

        it('should handle empty collection', () => {
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection([]);
            const labels = customViewModel.getAggregatedLabelsForChart();
            expect(labels).toEqual([]);
        });

        it('should handle invalid data gracefully', () => {
            const testData = [
                {
                    Data: {
                        Day: () => null,
                        Month: () => null,
                        Year: () => null,
                        DateDisplay: () => null
                    }
                }
            ];
            viewModel.LoggedHoursVersusSeatHoursViewObjectCollection(testData);

            const labels = customViewModel.getAggregatedLabelsForChart();
            expect(labels).toEqual([]);
        });
    });
}); 