import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('BroadcastMessageFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/BroadcastMessage/BroadcastMessageFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            customerId: ko.observable(null),
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            closePopup: vi.fn(),
            ShowError: vi.fn(),
            onSendSuccess: vi.fn(),
            BroadcastMessageObject: ko.observable({
                Data: {
                    Message: ko.observable(''),
                    Priority: ko.observable(''),
                    ResponseOptions: ko.observable('')
                }
            }),
            VehicleBroadcastMessageItemsGridViewModel: {
                getSourceCollection: vi.fn().mockReturnValue([])
            },
            controller: {
                applicationController: {
                    getProxyForComponent: vi.fn().mockReturnValue({
                        BroadcastMessage: vi.fn()
                    })
                }
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.BroadcastMessageFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    it('should initialize the Send function', () => {
        // Verify Send function exists
        expect(viewModel.Send).toBeDefined();
        expect(typeof viewModel.Send).toBe('function');
    });

    it('should call BroadcastMessage with correct configuration when Send is called', () => {
        // Setup test data
        const mockMessage = 'Test message';
        const mockPriority = 'High';
        const mockResponseOptions = 'Yes,No';
        const mockVehicleIds = ['vehicle1', 'vehicle2'];

        viewModel.BroadcastMessageObject().Data.Message(mockMessage);
        viewModel.BroadcastMessageObject().Data.Priority(mockPriority);
        viewModel.BroadcastMessageObject().Data.ResponseOptions(mockResponseOptions);
        viewModel.VehicleBroadcastMessageItemsGridViewModel.getSourceCollection.mockReturnValue([
            { Data: { VehicleId: ko.observable('vehicle1') } },
            { Data: { VehicleId: ko.observable('vehicle2') } }
        ]);

        // Call Send function
        viewModel.Send();

        // Verify BroadcastMessage was called with correct configuration
        expect(viewModel.controller.applicationController.getProxyForComponent).toHaveBeenCalledWith('VehicleAPI');
        const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
        expect(vehicleAPI.BroadcastMessage).toHaveBeenCalledWith(expect.objectContaining({
            caller: viewModel,
            contextId: viewModel.contextId,
            successHandler: viewModel.onSendSuccess,
            errorHandler: viewModel.ShowError,
            broadcastMessage: viewModel.BroadcastMessageObject(),
            vehicleIds: ['vehicle1', 'vehicle2']
        }));
    });

    it('should handle empty vehicle collection gracefully', () => {
        // Setup empty vehicle collection
        viewModel.VehicleBroadcastMessageItemsGridViewModel.getSourceCollection.mockReturnValue([]);

        // Call Send function
        viewModel.Send();

        // Verify BroadcastMessage was called with empty vehicleIds array
        const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
        expect(vehicleAPI.BroadcastMessage).toHaveBeenCalledWith(expect.objectContaining({
            vehicleIds: []
        }));
    });

    it('should set isBusy and close popup after sending message', () => {
        // Call Send function
        viewModel.Send();

        // Verify setIsBusy and closePopup were called
        expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
        expect(viewModel.closePopup).toHaveBeenCalledWith(false);
    });

    it('should handle null or undefined vehicle items gracefully', () => {
        // Setup collection with null and undefined items
        viewModel.VehicleBroadcastMessageItemsGridViewModel.getSourceCollection.mockReturnValue([
            null,
            { Data: { VehicleId: ko.observable('vehicle1') } },
            undefined,
            { Data: null },
            { Data: { VehicleId: ko.observable('vehicle2') } }
        ]);

        // Call Send function
        viewModel.Send();

        // Verify BroadcastMessage was called with only valid vehicleIds
        const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
        expect(vehicleAPI.BroadcastMessage).toHaveBeenCalledWith(expect.objectContaining({
            vehicleIds: ['vehicle1', 'vehicle2']
        }));
    });
}); 