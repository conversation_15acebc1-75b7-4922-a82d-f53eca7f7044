﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class GORoleDataProviderExtension : IDataProviderExtension<GORoleDataObject>
    {
        private Dictionary<string, int> _roles = new Dictionary<string, int>
        {
            { "Guest", 0 },
            { "Driver", 1 },
            { "Supervisor", 2 },
            { "SiteAdmin", 3 },
            { "Customer", 4 },
            { "DealerAdmin", 5 },
            { "Administrator", 6 },
        };

        private readonly IDataFacade _dataFacade;
        private readonly IAuthentication _authentication;

        public GORoleDataProviderExtension(IDataFacade dataFacade, IAuthentication authentication)
        {
            _dataFacade = dataFacade;
            _authentication = authentication;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            var claims = await _authentication.GetCurrentUserClaimsAsync();

            if (claims.UserId != null && arg.Result is DataObjectCollection<GORoleDataObject> items)
            {
                var callinguser = await _dataFacade.GOUserDataProvider.GetAsync(new GOUserDataObject(claims.UserId.Value), includes: new List<string> { "UserRoleItems" }, skipSecurity: true);

                var userRoles = _roles.Where(x => callinguser.UserRoleItems.Select(y => y.GORoleName)?.Contains(x.Key) == true);

                var highestRole = userRoles.OrderByDescending(x => x.Value).First();

                var found = false;
                foreach (var role in _roles)
                {
                    if (!found && highestRole.Key == role.Key)
                    {
                        found = true;
                        continue;
                    }

                    if (found)
                    {
                        var userRole = items.FirstOrDefault(x => x.Name == role.Key);

                        if (userRole != null)
                        {
                            items.Remove(userRole);
                        }
                    }
                }
            }
        }
    }
}
