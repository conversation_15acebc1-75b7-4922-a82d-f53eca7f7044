using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class DepartmentAPITest : TestBase
    {
        private IDataFacade _dataFacade;
        private IDepartmentAPI _departmentAPI;
        private readonly string _testDatabaseName = $"DepartmentAPITest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _departmentAPI = _serviceProvider.GetRequiredService<IDepartmentAPI>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);
        }

        [Test]
        public async Task SoftDeleteAsync_UnknownDepartmentId_ThrowsException()
        {
            // Arrange
            var unknownDepartmentId = Guid.NewGuid();

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _departmentAPI.SoftDeleteAsync(unknownDepartmentId));

            Assert.That(exception.Message, Is.EqualTo($"unknow department id {unknownDepartmentId}"));
        }

        [Test]
        public async Task SoftDeleteAsync_DepartmentWithVehicles_ThrowsException()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create test module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID1";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000001" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create test vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = customer.Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle";
            vehicle.SerialNo = "Test Serial No";
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _departmentAPI.SoftDeleteAsync(department.Id));

            Assert.That(exception.Message, Is.EqualTo("Cannot delete department: Department contains vehicles."));
        }

        [Test]
        public async Task SoftDeleteAsync_DepartmentWithUsers_ThrowsException()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test user
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.IsDriver = false;
            person.IsActiveDriver = false;
            person.FirstName = "Test";
            person.LastName = "User";
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _departmentAPI.SoftDeleteAsync(department.Id));

            Assert.That(exception.Message, Is.EqualTo("Cannot delete department: Department contains users."));
        }

        [Test]
        public async Task SoftDeleteAsync_ValidDepartment_SuccessfullySoftDeletes()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Act
            var response = await _departmentAPI.SoftDeleteAsync(department.Id);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Is.True);

            // Verify department was soft deleted
            var deletedDepartment = await _dataFacade.DepartmentDataProvider.GetAsync(department);
            Assert.That(deletedDepartment, Is.Not.Null);
            Assert.That(deletedDepartment.DeletedAtUtc, Is.Not.Null);
        }
    }
}