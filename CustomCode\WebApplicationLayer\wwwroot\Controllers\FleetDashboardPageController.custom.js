﻿(function () {

    FleetXQ.Web.Controllers.FleetDashboardPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        // Add data stores
        this.dashboardDriverCardStoreProcedureDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DashboardDriverCardStoreProcedure');
        this.dashboardVehicleCardStoreProcedureDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DashboardVehicleCardStoreProcedure');
        this.driverLicenseExpiryViewDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'DriverLicenseExpiryView');
        this.todaysPreopCheckDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'TodaysPreopCheckView');
        this.todaysImpactDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'TodaysImpactView');
        this.vehicleUtilizationDataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'VehicleUtilizationLastTwelveHoursView');

        function formatDateForSqlServer(date) {
            // Format date as 'YYYY-MM-DD HH:mm:ss' in local time
            var localDate = new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
            return localDate.toISOString().slice(0, 19).replace('T', ' ');
        }

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
            var customerId = userClaims.CustomerId;
            var AllowedSiteIds = userClaims.AllowedSiteIds;

            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds ? AllowedSiteIds.replace(/[{} ]/g, '').split(',') : [];
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;

            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : "' + customerId + '" }';
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            var defaultDate = new Date();  // Get current date
            defaultDate.setHours(0, 0, 0, 0);  // Set time to midnight (12 AM)
            console.log('[Default Config] Using default date:', defaultDate);

            // Add default reference date parameter
            configuration.parameters = JSON.stringify({
                ReferenceDate: {
                    TypeName: "System.DateTime",
                    IsNullable: false,
                    Value: formatDateForSqlServer(defaultDate)
                }
            });

            return configuration;
        };

        this.getConfiguration = function () {
            var currentData = self.controller.MainDashboardFilterFormViewModel.CurrentObject().Data;
            var userClaims = self.controller.applicationController.viewModel.security.currentUserClaims();
            var customerId = userClaims.CustomerId;

            // If no filter is applied, return default configuration
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            var configuration = {};
            var parameterCount = 0;
            var filterPredicates = [];
            var filterParameters = [];

            var effectiveCustomerId = currentData.CustomerId() || customerId;
            if (effectiveCustomerId != null) {
                filterPredicates.push('CustomerId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": effectiveCustomerId
                });
                parameterCount++;
            }

            if (currentData.SiteId() != null) {
                filterPredicates.push('SiteId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": currentData.SiteId()
                });
                parameterCount++;
            }

            if (currentData.DepartmentId() != null) {
                filterPredicates.push('DepartmentId == @' + parameterCount);
                filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": true,
                    "Value": currentData.DepartmentId()
                });
                parameterCount++;
            }

            // Handle the date parameter
            var selectedDate = currentData.Date();
            console.log('[Filter Config] Initial selected date:', selectedDate);

            if (selectedDate) {
                // Ensure we're working with a Date object
                if (typeof selectedDate === 'string') {
                    selectedDate = new Date(selectedDate);
                }
                // Keep the time component as is from the selected date
                console.log('[Filter Config] Using selected date with its time:', selectedDate);
            } else {
                // Use current date at midnight
                selectedDate = new Date();
                selectedDate.setHours(0, 0, 0, 0);  // Set time to midnight (12 AM)
                console.log('[Filter Config] Using current date at midnight:', selectedDate);
            }

            // Add reference date parameter
            filterPredicates.push('ReferenceDate == @' + parameterCount);
            filterParameters.push({
                "TypeName": "System.DateTime",
                "IsNullable": false,
                "Value": formatDateForSqlServer(selectedDate)
            });

            // Combine predicates with AND
            configuration.filterPredicate = filterPredicates.join(' && ');
            configuration.filterParameters = JSON.stringify(filterParameters);

            console.log('[Filter Config] Final configuration:', configuration);
            return configuration;
        };

        this.loadDashboardData = function () {
            // Set all forms to busy state
            self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(true);
            self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(true);
            self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(true);

            var baseConfiguration = {
                contextId: self.controller.contextId,
                errorHandler: function (error) {
                    console.error('Error loading dashboard data:', error);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.ShowError(error);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.ShowError(error);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.ShowError(error);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(false);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(false);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(false);
                }
            };

            var config = self.getConfiguration();
            console.log('[Load Data] Configuration being sent:', config);

            // Driver Card Configuration
            var driverConfig = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received driver data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardDriverCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardDriverCardStoreProcedureFormViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.SetDashboardDriverCardStoreProcedureObject(content);
                    self.controller.DashboardDriverCardStoreProcedureFormViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardDriverCardStoreProcedureFormViewModel.include
            };

            // Vehicle Card Configuration
            var vehicleConfig = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received vehicle data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardVehicleCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardVehicleCardStoreProcedureFormViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.SetDashboardVehicleCardStoreProcedureObject(content);
                    self.controller.DashboardVehicleCardStoreProcedureFormViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardVehicleCardStoreProcedureFormViewModel.include
            };

            // Vehicle Card Form1 Configuration
            var vehicleForm1Config = {
                ...baseConfiguration,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                successHandler: function (data) {
                    console.log('[Load Data] Received vehicle form1 data:', data);
                    var content = data[0];
                    if (!content) {
                        content = FleetXQ.Web.Model.DataObjects.DashboardVehicleCardStoreProcedureObjectFactory.createNew(
                            self.controller.ObjectsDataSet,
                            self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.contextId
                        );
                    }
                    content.ObjectsDataSet = self.controller.ObjectsDataSet;
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.SetDashboardVehicleCardStoreProcedureObject(content);
                    self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.setIsBusy(false);
                },
                include: self.controller.DashboardVehicleCardStoreProcedureForm1ViewModel.include
            };

            console.log('[Load Data] Final configurations - Driver:', driverConfig, 'Vehicle:', vehicleConfig, 'Vehicle Form1:', vehicleForm1Config);

            // Load all card data
            self.dashboardDriverCardStoreProcedureDataStore.LoadObjectCollection(driverConfig);
            self.dashboardVehicleCardStoreProcedureDataStore.LoadObjectCollection(vehicleConfig);
            self.dashboardVehicleCardStoreProcedureDataStore.LoadObjectCollection(vehicleForm1Config);
        };

        this.loadChartData = function () {
            var config = self.getConfiguration();
            console.log('[loadChartData] Starting with configuration:', config);

            var baseConfiguration = {
                contextId: self.controller.contextId,
                filterPredicate: config.filterPredicate,
                filterParameters: config.filterParameters,
                parameters: config.parameters,
                errorHandler: function (error) {
                    console.error('[loadChartData] Error:', error);
                }
            };

            // Load chart data with error handling
            try {
                // Load data using the correct data stores
                console.log('[loadChartData] Loading TodaysPreopCheck data...');
                self.todaysPreopCheckDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] TodaysPreopCheck data received:', data);
                        self.controller.TodaysPreopCheckReportViewModel.OnTodaysPreopCheckViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading VehicleUtilization data...');
                self.vehicleUtilizationDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] VehicleUtilization data received:', data);
                        self.controller.VehicleUtilizationLastTwelveHoursViewReportViewModel.OnVehicleUtilizationLastTwelveHoursViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading TodaysImpact data...');
                self.todaysImpactDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] TodaysImpact data received:', data);
                        self.controller.TodaysImpactViewReportViewModel.OnTodaysImpactViewObjectCollectionLoaded(data);
                    }
                });

                console.log('[loadChartData] Loading DriverLicenseExpiry data...');
                self.driverLicenseExpiryViewDataStore.LoadObjectCollection({
                    ...baseConfiguration,
                    successHandler: function (data) {
                        console.log('[loadChartData] DriverLicenseExpiry data received:', data);
                        self.controller.DriverLicenseExpiryViewReportViewModel.OnDriverLicenseExpiryViewObjectCollectionLoaded(data);
                    }
                });
            } catch (error) {
                console.error('[loadChartData] Error loading chart data:', error);
            }
        };

        this.initialize = function () {
            // to avoid having the message asking to confirm changing page and lose changes
            self.controller.IsInEditMode = function () {
                return false;
            };

            // Initialize filter data function
            self.controller.MainDashboardFilterFormViewModel.filterData = function () {
                self.loadDashboardData();
                self.loadChartData();
            };

            // Load initial data with a small delay to ensure filter is initialized
            setTimeout(function () {
                console.log('[Initialize] Loading initial data through filter');
                self.controller.MainDashboardFilterFormViewModel.filterData();
            }, 100);
        };
    };

})();
