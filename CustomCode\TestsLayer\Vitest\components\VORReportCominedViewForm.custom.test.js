// Import required testing utilities
import { describe, it, expect, beforeEach, vi, beforeAll } from 'vitest';
import ko from 'knockout';

// Mock FleetXQ global object
beforeAll(() => {
    global.FleetXQ = {
        Web: {
            ViewModels: {
                VORReportCombinedViewFormViewModelCustom: function (viewmodel) {
                    var self = this;
                    this.viewmodel = viewmodel;

                    if (!this.viewmodel.StatusData) {
                        this.viewmodel.StatusData = {};
                    }

                    this.viewmodel.StatusData.messageText = ko.observable("This is the historical data for VOR, including activation details and current status.");

                    var updateMessageBasedOnTab = function (tabIndex) {
                        tabIndex = Number(tabIndex);

                        if (tabIndex === 1) {
                            self.viewmodel.StatusData.messageText("These are the sessions by mastercode drivers while the vehicle has the VOR Activated.");
                        } else {
                            self.viewmodel.StatusData.messageText("This is the historical data for VOR, including activation details and current status.");
                        }
                    };

                    if (this.viewmodel.StatusData.CurrentTabIndex) {
                        this.viewmodel.StatusData.CurrentTabIndex.subscribe(function (newValue) {
                            updateMessageBasedOnTab(newValue);
                        });
                    }

                    this.viewmodel.TabChangedMethod = function () {
                        var currentIndex = self.viewmodel.StatusData.CurrentTabIndex ?
                            self.viewmodel.StatusData.CurrentTabIndex() : 0;
                        updateMessageBasedOnTab(currentIndex);
                    };

                    this.initialize = function () {
                        var currentIndex = this.viewmodel.StatusData.CurrentTabIndex ?
                            this.viewmodel.StatusData.CurrentTabIndex() : 0;
                        updateMessageBasedOnTab(currentIndex);
                        self.viewmodel.Modify();
                    }
                }
            }
        }
    };
});

describe('VORReportCombinedViewFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Create a mock base view model with required properties
        viewModel = {
            StatusData: {
                CurrentTabIndex: ko.observable(0),
                messageText: ko.observable('')
            },
            Modify: vi.fn() // Mock the Modify function
        };

        // Create instance of custom view model
        customViewModel = new FleetXQ.Web.ViewModels.VORReportCombinedViewFormViewModelCustom(viewModel);
    });

    describe('Initialization', () => {
        it('should set default message for initial tab (index 0)', () => {
            customViewModel.initialize();
            expect(viewModel.StatusData.messageText()).toBe('This is the historical data for VOR, including activation details and current status.');
        });

        it('should call viewModel.Modify() during initialization', () => {
            customViewModel.initialize();
            expect(viewModel.Modify).toHaveBeenCalled();
        });
    });

    describe('Tab Change Behavior', () => {
        it('should update message when switching to VOR tab (index 1)', () => {
            viewModel.StatusData.CurrentTabIndex(1);
            expect(viewModel.StatusData.messageText()).toBe('These are the sessions by mastercode drivers while the vehicle has the VOR Activated.');
        });

        it('should update message when switching back to VOR Status tab (index 0)', () => {
            // First switch to tab 1
            viewModel.StatusData.CurrentTabIndex(1);
            // Then switch back to tab 0
            viewModel.StatusData.CurrentTabIndex(0);
            expect(viewModel.StatusData.messageText()).toBe('This is the historical data for VOR, including activation details and current status.');
        });

        it('should handle direct tab change method call', () => {
            // Simulate direct tab change method call
            viewModel.StatusData.CurrentTabIndex(1);
            customViewModel.viewmodel.TabChangedMethod();
            expect(viewModel.StatusData.messageText()).toBe('These are the sessions by mastercode drivers while the vehicle has the VOR Activated.');
        });
    });

    describe('Edge Cases', () => {
        it('should handle undefined CurrentTabIndex gracefully', () => {
            // Remove CurrentTabIndex to simulate edge case
            viewModel.StatusData.CurrentTabIndex = undefined;
            customViewModel.initialize();
            expect(viewModel.StatusData.messageText()).toBe('This is the historical data for VOR, including activation details and current status.');
        });

        it('should handle invalid tab index by defaulting to initial message', () => {
            viewModel.StatusData.CurrentTabIndex(999); // Invalid index
            expect(viewModel.StatusData.messageText()).toBe('This is the historical data for VOR, including activation details and current status.');
        });
    });
});
