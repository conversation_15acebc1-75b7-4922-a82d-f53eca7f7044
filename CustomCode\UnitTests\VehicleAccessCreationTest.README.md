# VehicleAccessCreation Unit Tests

This file documents the comprehensive unit tests created for the `VehicleAccessCreation` component.

## Test File Location
`CustomCode/BusinessLayerServerComponentsTests/FleetXQ.BusinessLayer.Components.Server.Custom.Tests/VehicleAccessCreationTest.cs`

## Test Coverage

The test suite provides comprehensive coverage of the `VehicleAccessCreation.CreateVehicleAccessAsync` method with the following scenarios:

### ✅ Success Scenarios
- **CreateVehicleAccessAsync_WithValidMessage_ShouldReturnSuccessResponse**
  - Tests successful vehicle access creation with valid message
  - Verifies access records are created
  - Confirms database operations are called correctly

- **CreateVehicleAccessAsync_WithDeviceSyncEnabled_ShouldCallDeviceSync**
  - Tests that device synchronization is called when IoTDevice is provided
  - Verifies `IDeviceTwinHandler.SyncDriverToVehicle` is invoked

- **CreateVehicleAccessAsync_WithDeviceSyncFailure_ShouldSucceedWithWarning**
  - Tests that the operation succeeds even if device sync fails
  - Ensures device sync failures don't break the main process

### ❌ Error Scenarios
- **CreateVehicleAccessAsync_WithInvalidJsonMessage_ShouldReturnErrorResponse**
  - Tests handling of malformed JSON messages
  - Verifies no database operations are performed with invalid input

- **CreateVehicleAccessAsync_WithNullMessage_ShouldReturnInvalidFormatResponse**
  - Tests handling of null message content
  - Returns "Invalid message format" error

- **CreateVehicleAccessAsync_WithVehicleNotFound_ShouldReturnVehicleNotFoundResponse**
  - Tests scenario when vehicle doesn't exist in database
  - Returns "Vehicle not found" error

- **CreateVehicleAccessAsync_WithSiteNotFound_ShouldReturnSiteNotFoundResponse**
  - Tests scenario when site or model cannot be loaded
  - Returns "Site or model not found" error

- **CreateVehicleAccessAsync_WithNoDriverPermissions_ShouldReturnNoPermissionsResponse**
  - Tests scenario when no driver permissions are found
  - Returns "No driver permissions found" error

- **CreateVehicleAccessAsync_WithNoSiteAccesses_ShouldReturnNoSiteAccessesResponse**
  - Tests scenario when no site accesses are found
  - Returns "No site accesses found" error

- **CreateVehicleAccessAsync_WithExceptionDuringProcessing_ShouldReturnErrorResponse**
  - Tests exception handling during database operations
  - Returns error message with exception details

## Test Architecture

### Mocking Strategy
The tests use **Moq** framework for mocking dependencies:
- `IServiceProvider` - For dependency injection
- `IConfiguration` - For configuration settings
- `IDataFacade` - For data access operations
- `ILogger<VehicleAccessCreation>` - For logging
- `IDeviceTwinHandler` - For IoT device synchronization
- Various data providers for database operations

### Test Data
- **VehicleAccessCreationMessage**: Creates realistic test messages with GUIDs
- **Mock Data Objects**: Creates mocked vehicles, sites, models, permissions, and accesses
- **Setup Helpers**: Utility methods for configuring complex test scenarios

### Dependencies Tested
- ✅ Vehicle loading and validation
- ✅ Site and model loading
- ✅ Driver permissions retrieval (Level 1 and Level 3)
- ✅ Site access queries
- ✅ Existing access checks (deduplication)
- ✅ Access record creation (Model and Vehicle)
- ✅ IoT device synchronization
- ✅ Exception handling and logging

## Key Testing Patterns

1. **Arrange-Act-Assert**: Clear test structure
2. **Mock isolation**: Each test has clean mock setup
3. **Comprehensive scenarios**: Both success and failure paths
4. **Realistic data**: Uses proper GUIDs and business objects
5. **Dependency verification**: Confirms correct method calls
6. **Error handling**: Tests exception scenarios gracefully

## Running the Tests

To run these specific tests:

```bash
# From the solution root
dotnet test --filter "VehicleAccessCreationTest"

# From the test project directory
dotnet test --filter "CreateVehicleAccessAsync"

# Run with verbose output
dotnet test --filter "VehicleAccessCreationTest" --verbosity normal
```

## Test Benefits

1. **Business Logic Coverage**: Tests the complete vehicle access creation workflow
2. **Error Resilience**: Ensures graceful handling of various failure scenarios
3. **Performance Insights**: Validates that expensive operations are optimized
4. **Regression Prevention**: Catches breaking changes to critical functionality
5. **Documentation**: Serves as living documentation of expected behavior
6. **Maintenance**: Makes refactoring safer with comprehensive test coverage

## Integration with Existing Tests

This test follows the same patterns as other tests in the project:
- Inherits from `TestBase` class
- Uses NUnit framework
- Follows existing naming conventions
- Uses Moq for mocking (consistent with other tests)
- Provides comprehensive helper methods for test setup

The tests complement the existing Azure Function tests by focusing on the core business logic layer rather than the HTTP/Service Bus infrastructure. 