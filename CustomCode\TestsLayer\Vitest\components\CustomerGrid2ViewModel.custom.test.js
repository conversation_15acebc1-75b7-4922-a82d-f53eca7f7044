import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('CustomerGrid2ViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Person/CustomerGrid2ViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            baseFilterPredicate: null,
            baseFilterParameters: null,
            baseFilterParametersCount: 0
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.CustomerGrid2ViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    it('should set base filter to exclude deleted records', () => {
        // Verify that the base filter is set to exclude deleted records
        expect(viewModel.baseFilterPredicate).toBe('DeletedAtUtc == null');
        expect(viewModel.baseFilterParameters).toBeNull();
        expect(viewModel.baseFilterParametersCount).toBe(0);
    });

    it('should have a release function', () => {
        // Verify that the release function exists
        expect(typeof customViewModel.release).toBe('function');
    });
}); 