﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using System;

namespace FleetXQ.BusinessLayer.Components.Server.Custom;

public class VehicleCalibrationReportExportComponentExtension : IImportExportComponentExtension<VehicleCalibrationReportExportSection0Component,
        AllVehicleCalibrationStoreProcedureDataObject>
{
    private readonly IDataFacade dataFacade;
    private readonly IAuthentication authentication;

    public VehicleCalibrationReportExportComponentExtension(IDataFacade dataFacade, IAuthentication authentication)
    {
        this.dataFacade = dataFacade;
        this.authentication = authentication;
    }

    public void Init(IImportExportComponent<AllVehicleCalibrationStoreProcedureDataObject> importExportComponent)
    {
        importExportComponent.OnAfterExportDataRowAsync += ImportExportComponent_OnAfterExportDataRowAsync;
    }

    private async Task ImportExportComponent_OnAfterExportDataRowAsync(OnAfterExportDataRowEventArgs<AllVehicleCalibrationStoreProcedureDataObject> arg)
    {
        if (arg.Entity?.Vehicle?.Module == null)
        {
            return;
        }

        try
        {
            var culture = await GetUserCulture();
            await SetCalibrationDates(arg, culture);
            await SetImpactValues(arg, culture);
        }
        catch (CultureNotFoundException)
        {
            // If the culture is invalid, just return without modifying any values
            return;
        }
    }

    private async Task<CultureInfo> GetUserCulture()
    {
        var userClaims = await authentication.GetCurrentUserClaimsAsync();
        var callingUser = await dataFacade.GOUserDataProvider.GetAsync(
            new GOUserDataObject(userClaims.UserId.Value),
            includes: new List<string> { "Person", "Person.Customer" }
        );

        var preferredLocale = callingUser.PreferredLocale ?? callingUser.Person?.Customer?.PreferredLocale;
        return preferredLocale.HasValue
            ? new CultureInfo(DataUtils.GetLocaleString(preferredLocale.Value))
            : CultureInfo.InvariantCulture;
    }

    private Task SetCalibrationDates(OnAfterExportDataRowEventArgs<AllVehicleCalibrationStoreProcedureDataObject> arg, CultureInfo culture)
    {
        if (arg.Entity.Vehicle.Module.CalibrationDate.HasValue)
        {
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_CALIBRATIONDATE] =
                arg.Entity.Vehicle.Module.CalibrationDate.Value.ToString(culture);
        }

        if (arg.Entity.Vehicle.Module.CalibrationResetDate.HasValue)
        {
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_CALIBRATIONDATE] =
                arg.Entity.Vehicle.Module.CalibrationResetDate.Value.ToString(culture);
        }

        return Task.CompletedTask;
    }

    private Task SetImpactValues(OnAfterExportDataRowEventArgs<AllVehicleCalibrationStoreProcedureDataObject> arg, CultureInfo culture)
    {
        var module = arg.Entity.Vehicle.Module;
        
        // Only set impact values if the module is calibrated
        if (module.CalibrationDate.HasValue)
        {
            // Set Blue Impact (base threshold)
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_BLUEIMPACT] = 
                string.Format(culture, "{0:F1}g", module.BlueImpact);

            // Set Amber Impact (5x base threshold)
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_AMBERIMPACT] = 
                string.Format(culture, "{0:F1}g", module.AmberImpact);

            // Set Red Impact (10x base threshold)
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_REDIMPACT] = 
                string.Format(culture, "{0:F1}g", module.RedImpact);
        }
        else
        {
            // For uncalibrated modules, set all impact values to "Not Calibrated"
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_BLUEIMPACT] = "Not Calibrated";
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_AMBERIMPACT] = "Not Calibrated";
            arg.DataRow[VehicleCalibrationReportExportSection0Component.COL_REDIMPACT] = "Not Calibrated";
        }

        return Task.CompletedTask;
    }
}
