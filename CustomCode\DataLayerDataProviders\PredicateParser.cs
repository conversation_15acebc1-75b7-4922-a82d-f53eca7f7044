﻿using System;
using System.Text.RegularExpressions;

namespace FleetXQ.Data.DataProviders.Custom
{
    internal class FilterDetails
    {
        public bool HasCustomerId { get; set; }
        public int CustomerIdParameterNumber { get; set; } = -1;

        public bool HasSiteId { get; set; }
        public int SiteIdParameterNumber { get; set; } = -1;

        public bool HasDepartmentId { get; set; }
        public int DepartmentIdParameterNumber { get; set; } = -1;

        public bool HasStartDate { get; set; }
        public int StartDateParameterNumber { get; set; } = -1;

        public bool HasEndDate { get; set; }
        public int EndDateParameterNumber { get; set; } = -1;

        public bool HasImpactLevel { get; set; }
        public int ImpactLevelParameterNumber { get; set; } = -1;

        public bool HasResultType { get; set; }
        public int ResultTypeParameterNumber { get; set; } = -1;

        public bool HasMultiSearch { get; set; }
        public int MultiSearchParameterNumber { get; set; } = -1;

        public bool HasVehicleId { get; set; }
        public int VehicleIdParameterNumber { get; set; } = -1;

        public bool HasDriverId { get; set; }
        public int DriverIdParameterNumber { get; set; } = -1;

        public bool HasLicenseType { get; set; }
        public int LicenseTypeParameterNumber { get; set; } = -1;

        public bool HasLockoutType { get; set; }
        public int LockoutTypeParameterNumber { get; set; } = -1;

    }

    internal static class PredicateParser
    {
        public static FilterDetails ParseFilterPredicate(string filterPredicate)
        {
            var details = new FilterDetails();

            if (string.IsNullOrEmpty(filterPredicate))
                return details;

            var matches = Regex.Matches(filterPredicate, @"(\w+) == @(\d+)");

            foreach (Match match in matches)
            {
                if (match.Groups.Count == 3) // Ensure there are two groups captured
                {
                    string filter = match.Groups[1].Value;
                    int paramNumber = int.Parse(match.Groups[2].Value);

                    switch (filter)
                    {
                        case "CustomerId":
                            details.HasCustomerId = true;
                            details.CustomerIdParameterNumber = paramNumber;
                            break;
                        case "SiteId":
                            details.HasSiteId = true;
                            details.SiteIdParameterNumber = paramNumber;
                            break;
                        case "DepartmentId":
                            details.HasDepartmentId = true;
                            details.DepartmentIdParameterNumber = paramNumber;
                            break;
                        case "StartDate":
                            details.HasStartDate = true;
                            details.StartDateParameterNumber = paramNumber;
                            break;
                        case "EndDate":
                            details.HasEndDate = true;
                            details.EndDateParameterNumber = paramNumber;
                            break;
                        case "ImpactLevel":
                            details.HasImpactLevel = true;
                            details.ImpactLevelParameterNumber = paramNumber;
                            break;
                        case "ResultType":
                            details.HasResultType = true;
                            details.ResultTypeParameterNumber = paramNumber;
                            break;
                        case "MultiSearch":
                            details.HasMultiSearch = true;
                            details.MultiSearchParameterNumber = paramNumber;
                            break;
                        case "VehicleId":
                            details.HasVehicleId = true;
                            details.VehicleIdParameterNumber = paramNumber;
                            break;
                        case "DriverId":
                            details.HasDriverId = true;
                            details.DriverIdParameterNumber = paramNumber;
                            break;
                        case "LicenseType":
                            details.HasLicenseType = true;
                            details.LicenseTypeParameterNumber = paramNumber;
                            break;
                        case "LockoutType":
                            details.HasLockoutType = true;
                            details.LockoutTypeParameterNumber = paramNumber;
                            break;
                    }
                }
            }

            return details;
        }
    }
}