using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Threading.Tasks;

// Mock message class for testing
public class UserAccessUpdateMessage
{
    public Guid PersonId { get; set; }
    public Guid CustomerId { get; set; }
    public string? PersonToSiteAccessesJson { get; set; }
    public string? PersonToDepartmentAccessesJson { get; set; }
    public string? PersonToModelAccessesJson { get; set; }
    public string? PersonToVehicleAccessesJson { get; set; }
    public DateTime CreatedAt { get; set; }
    public Guid? InitiatedByUserId { get; set; }
    public string? CorrelationId { get; set; }
    public string Priority { get; set; } = "Normal";
    public int PermissionLevel { get; set; } = 3;
    public bool CascadeAddPermission { get; set; } = true;
}

namespace FleetXQ.BusinessLayer.Components.Server.UnitTests
{
    [TestFixture]
    public class VehicleAccessCreationManageUserAccessTest
    {
        private VehicleAccessCreation _vehicleAccessCreation;
        private IServiceProvider _mockServiceProvider;
        private IConfiguration _mockConfiguration;
        private IDataFacade _mockDataFacade;
        private ILogger<VehicleAccessCreation> _mockLogger;
        private IServiceScopeFactory _mockServiceScopeFactory;

        [SetUp]
        public void Setup()
        {
            // Create substitutes using NSubstitute
            _mockServiceProvider = Substitute.For<IServiceProvider>();
            _mockConfiguration = Substitute.For<IConfiguration>();
            _mockDataFacade = Substitute.For<IDataFacade>();
            _mockLogger = Substitute.For<ILogger<VehicleAccessCreation>>();
            _mockServiceScopeFactory = Substitute.For<IServiceScopeFactory>();

            // Create the component under test
            _vehicleAccessCreation = new VehicleAccessCreation(
                _mockServiceProvider,
                _mockConfiguration,
                _mockDataFacade,
                _mockLogger,
                _mockServiceScopeFactory);
        }

        [Test]
        public async Task ManageUserAccessAsync_WithValidMessage_ShouldReturnSuccessResponse()
        {
            // Arrange
            var testMessage = CreateTestUserAccessUpdateMessage();
            var messageJson = JsonSerializer.Serialize(testMessage);

            // Act & Assert
            try
            {
                var result = await _vehicleAccessCreation.ManageUserAccessAsync(messageJson);

                // Test passes if it doesn't throw an exception during JSON parsing
                Assert.That(result, Is.Not.Null);
                Assert.That(result.Result, Is.Not.Null);
            }
            catch (JsonException jsonEx)
            {
                Assert.Fail($"JSON parsing failed: {jsonEx.Message}");
            }
            catch (Exception ex)
            {
                // Expected for incomplete mock setup, but JSON parsing should work
                Assert.That(ex.Message, Does.Not.Contain("JsonException"));
                Console.WriteLine($"Expected exception due to mocking: {ex.Message}");
            }
        }

        [Test]
        public async Task ManageUserAccessAsync_WithInvalidJsonMessage_ShouldReturnErrorResponse()
        {
            // Arrange
            var invalidMessage = "invalid json {";

            // Act
            var result = await _vehicleAccessCreation.ManageUserAccessAsync(invalidMessage);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Does.Contain("Error processing user access update"));
        }

        [Test]
        public async Task ManageUserAccessAsync_WithNullMessage_ShouldReturnInvalidFormatResponse()
        {
            // Arrange
            var nullMessage = "null";

            // Act
            var result = await _vehicleAccessCreation.ManageUserAccessAsync(nullMessage);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Is.EqualTo("Invalid message format"));
        }

        [Test]
        public async Task ManageUserAccessAsync_WithEmptyMessage_ShouldHandleGracefully()
        {
            // Arrange
            var emptyMessage = "";

            // Act
            var result = await _vehicleAccessCreation.ManageUserAccessAsync(emptyMessage);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Result, Does.Contain("Error processing user access update"));
        }

        [Test]
        public async Task ManageUserAccessAsync_WithMessageMissingPersonId_ShouldHandleGracefully()
        {
            // Arrange
            var messageWithoutPersonId = "{\"CustomerId\":\"" + Guid.NewGuid() + "\",\"PersonToSiteAccessesJson\":\"[]\"}";

            // Act
            var result = await _vehicleAccessCreation.ManageUserAccessAsync(messageWithoutPersonId);

            // Assert
            Assert.That(result, Is.Not.Null);
            // Should either parse with empty GUID or handle gracefully
        }

        [Test]
        public async Task ManageUserAccessAsync_WithValidMessageStructure_ShouldParseCorrectly()
        {
            // Arrange
            var testMessage = new
            {
                PersonId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                PersonToSiteAccessesJson = "[]",
                PersonToDepartmentAccessesJson = "[]",
                PersonToModelAccessesJson = "[]",
                PersonToVehicleAccessesJson = "[]",
                CreatedAt = DateTime.UtcNow,
                InitiatedByUserId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid().ToString(),
                Priority = "Normal"
            };
            var messageJson = JsonSerializer.Serialize(testMessage);

            // Act & Assert
            try
            {
                var result = await _vehicleAccessCreation.ManageUserAccessAsync(messageJson);

                // If we get here, JSON parsing was successful
                Assert.That(result, Is.Not.Null);
            }
            catch (JsonException)
            {
                Assert.Fail("JSON parsing should not fail with valid message structure");
            }
            catch (Exception ex)
            {
                // Expected for mock limitations
                Console.WriteLine($"Expected exception: {ex.Message}");
            }
        }

        [Test]
        public void CreateTestUserAccessUpdateMessage_ShouldCreateValidMessage()
        {
            // Act
            var message = CreateTestUserAccessUpdateMessage();

            // Assert
            Assert.That(message, Is.Not.Null);
            Assert.That(message.PersonId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(message.CustomerId, Is.Not.EqualTo(Guid.Empty));
            Assert.That(message.InitiatedByUserId, Is.Not.Null);
            Assert.That(message.CorrelationId, Is.Not.Null.And.Not.Empty);
            Assert.That(message.Priority, Is.EqualTo("Normal"));
        }

        [Test]
        public void UserAccessUpdateMessage_JsonSerialization_ShouldProduceValidJson()
        {
            // Arrange
            var message = CreateTestUserAccessUpdateMessage();

            // Act
            var json = JsonSerializer.Serialize(message);

            // Assert
            Assert.That(json, Is.Not.Null.And.Not.Empty);
            Assert.That(json, Does.Contain("PersonId"));
            Assert.That(json, Does.Contain("CustomerId"));
            Assert.That(json, Does.Contain("CorrelationId"));

            // Verify it can be deserialized back
            var deserializedMessage = JsonSerializer.Deserialize<UserAccessUpdateMessage>(json);
            Assert.That(deserializedMessage, Is.Not.Null);
            Assert.That(deserializedMessage.PersonId, Is.EqualTo(message.PersonId));
        }

        #region Helper Methods

        private UserAccessUpdateMessage CreateTestUserAccessUpdateMessage()
        {
            return new UserAccessUpdateMessage
            {
                PersonId = Guid.NewGuid(),
                CustomerId = Guid.NewGuid(),
                PersonToSiteAccessesJson = "[]",
                PersonToDepartmentAccessesJson = "[]",
                PersonToModelAccessesJson = "[]",
                PersonToVehicleAccessesJson = "[]",
                CreatedAt = DateTime.UtcNow,
                InitiatedByUserId = Guid.NewGuid(),
                CorrelationId = Guid.NewGuid().ToString(),
                Priority = "Normal"
            };
        }

        #endregion
    }
}