﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;


namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class VehicleOtherSettingsDataProviderExtension : IDataProviderExtension<VehicleOtherSettingsDataObject>
    {

        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDeviceTwinHandler _deviceTwinHandler;
        private readonly IAuthentication _authentication;

        public VehicleOtherSettingsDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade, IDeviceTwinHandler deviceTwinHandler, IAuthentication authentication)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _deviceTwinHandler = deviceTwinHandler;
            _authentication = authentication;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += DataProvider_OnBeforeSave;
        }

        private async Task DataProvider_OnBeforeSave(OnBeforeSaveDataSetEventArgs e)
        {
            var otherSettings = e.Entity as VehicleOtherSettingsDataObject;

            if (otherSettings == null)
            {
                return;
            }

            // Validate FullLockoutTimeout
            if (otherSettings.FullLockoutTimeout.HasValue && otherSettings.FullLockoutTimeout.Value < 0)
            {
                throw new GOServerException("FullLockoutTimeout cannot be negative");
            }

            var vehicle = await otherSettings.LoadVehicleAsync(skipSecurity: true);

            if (vehicle == null)
            {
                return;
            }

            await SaveVORSettingHistory(otherSettings, vehicle);
        }

        private async Task SaveVORSettingHistory(VehicleOtherSettingsDataObject vehicleOtherSettings, VehicleDataObject vehicle)
        {
            var vorStatus = vehicleOtherSettings.VORStatus;

            var currentOtherSettings = await _dataFacade.VehicleOtherSettingsDataProvider.GetAsync(new VehicleOtherSettingsDataObject(vehicleOtherSettings.Id), skipSecurity: true);

            if (vehicleOtherSettings.VORStatus != (currentOtherSettings?.VORStatus ?? false))
            {
                vehicleOtherSettings.VORStatusConfirmed = false;
            }

            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            if (module == null)
            {
                return;
            }

            var claims = await _authentication.GetCurrentUserClaimsAsync();

            if (claims == null || claims.UserId == null)
            {
                return;
            }

            var callinguser = await _dataFacade.GOUserDataProvider.GetAsync(new GOUserDataObject(claims.UserId.Value), skipSecurity: true);

            var person = await callinguser.LoadPersonAsync();
            if (person == null)
            {
                return;
            }

            if (vorStatus == true)
            {
                var vorSetting = (await _dataFacade.VORSettingHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0 and EndDateTime == null", new object[] { vehicle.Id })).SingleOrDefault();
                if (vorSetting == null)
                {
                    vorSetting = _serviceProvider.GetRequiredService<VORSettingHistoryDataObject>();
                    vorSetting.VehicleId = vehicle.Id;
                    vorSetting.StartDateTime = DateTime.UtcNow;
                    vorSetting.EndDateTime = null;
                    vorSetting.Status = VORStatusEnum.SENT;
                    vorSetting.PersonId = person.Id;
                    try
                    {
                        await _dataFacade.VORSettingHistoryDataProvider.SaveAsync(vorSetting);
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Something went wrong during VOR Update");
                    }
                }
                else
                {
                    //throw new Exception("There is an existing pending VOR update, this will be ignored");
                }
            }
            else
            {
                var vorSetting = (await _dataFacade.VORSettingHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0 and EndDateTime == null", new object[] { vehicle.Id })).SingleOrDefault();
                if (vorSetting != null)
                {
                    vorSetting.Status = VORStatusEnum.SENT;
                    vorSetting.PersonId = person.Id;
                    await _dataFacade.VORSettingHistoryDataProvider.SaveAsync(vorSetting);
                }
            }
        }

    }
}