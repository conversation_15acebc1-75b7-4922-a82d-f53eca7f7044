﻿using DocumentFormat.OpenXml.Drawing.Charts;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class ChecklistSettingsDataProviderExtension : IDataProviderExtension<ChecklistSettingsDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IServiceProvider _serviceProvider;


        public ChecklistSettingsDataProviderExtension(IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, IServiceProvider serviceProvider)
        {
            _dataFacade = dataFacade;
            _deviceMessageHandler = deviceMessageHandler;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {

        }
    }
}
