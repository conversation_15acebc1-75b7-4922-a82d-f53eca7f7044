describe("003 - Sites Flow (Firmware Update)", () => {

    beforeEach(() => {
        // Perform the login using the login command
        cy.login();
    });

    it("tests Firmware Update flow", () => {
        // Navigate to the site after login
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(2) span")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the first row link
        cy.get("tr:nth-of-type(1) > td:nth-of-type(1) > a")
            .should('exist')
            .should('be.visible')
            .click();

        // Click on the specific tab
        cy.get("[data-test-id='tab_link_941e8b48-2441-4501-b057-2597f5143ad2'] > a > span:nth-of-type(1)")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the first row in the table
        cy.get("[data-test-id='\\39 41e8b48-2441-4501-b057-2597f5143ad2'] tr:nth-of-type(1) > td:nth-of-type(1)")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the "View Site" button
        cy.get("[data-test-id='d878e927-dec4-4268-8448-e40773bad533']")
            .should('exist')
            .should('be.visible')
            .click();

        // Navigate to the firmware tab
        cy.get("html > body > #form > [data-test-id='main-page-content'] > #popupContainer > #popupContainer0 > [data-test-id='b2fbd9ef-a897-4b83-ada6-a14f5819a621'] > div > div:nth-of-type(1) > [data-test-id='b2fbd9ef-a897-4b83-ada6-a14f5819a621'] > #SiteForm1Data > div:nth-of-type(2) > div > div > [data-test-id='\\39 c819493-0863-40c5-9b8f-087f69d88a43'] [data-test-id='tab_link_3a75cb14-3fbb-417e-a2bc-7ca2245298e7'] > a")
            .eq(0)
            .should('exist')
            .should('be.visible')
            .click();

        // Select the firmware version from the dropdown
        //cy.get("form > div > div > div > div > div > div:nth-of-type(2) > div > div > div:nth-of-type(1) select")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Clear the search command
        //cy.get("[data-test-id='searchCommand']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Clear the command input
        //cy.get("[data-test-id='clearCommand']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Click the third row of the table to select a firmware item
        //cy.get("[data-test-id='\\33 a75cb14-3fbb-417e-a2bc-7ca2245298e7'] tr:nth-of-type(3) > td:nth-of-type(2)")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Click the firmware update button
        //cy.get("[data-test-id='f5d104c2-74fc-4794-85f5-07cecce9d969']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Select the appropriate lookup input
        //cy.get("#popupContainer1 [data-test-id='lookup_input']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Select the first item from the lookup dropdown
        //cy.get("[data-test-id='lookup_item']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();
    });
});
