using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Transactions;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class GPSMessageTest : TestBase
    {
        private IGPSMessage _gpsMessage;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"GPSMessageTests-{Guid.NewGuid()}";

        // Store test data to avoid recreation for each test
        private static SessionDataObject _cachedSession;
        private static ModuleDataObject _cachedModule;
        private static TransactionScope _transactionScope;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any additional service registrations needed for these tests
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUp()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _gpsMessage = _serviceProvider.GetRequiredService<IGPSMessage>();

            CreateTestDatabase(_testDatabaseName);

            // Create transaction scope AFTER database creation
            _transactionScope = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);

            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            // Dispose the transaction scope (rollback changes)
            _transactionScope?.Dispose();

            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task StoreGpsMessageAsync_ClassicFormat_StoresGpsData()
        {
            // Arrange
            var sessionId = TestSession.Id.ToString(); // Use the test session ID

            // Classic 4-element format: cardId, timestamp, longitude, latitude
            string validMessage = $"{{\"event_type\":\"GPSE\",\"payload\":\"GPSE=9A4,**********,180686000,593293000\",\"session_id\":\"{sessionId}\",\"IotDeviceId\":\"{_cachedModule.IoTDevice}\"}}";

            // Act
            var response = await _gpsMessage.storeGpsMessageAsync(validMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null");
            Assert.That(response.Result, Does.Contain("Success"), "GPS data should be stored successfully");

            // Since we're using mocks, we don't need to verify in the database
        }

        [Test]
        public async Task StoreGpsMessageAsync_ExtendedFormat_StoresGpsData()
        {
            // Arrange
            var sessionId = TestSession.Id.ToString(); // Use the test session ID

            // Extended 13-element format with Stockholm coordinates
            string validMessage = $"{{\"event_type\":\"GPSE\",\"payload\":\"GPSE=0,1744140870,0,0,593293000,3,-1238486260,-1238478080,180686000,41163812,-1240435096,-1239619988,-1238478080\",\"session_id\":\"{sessionId}\",\"IotDeviceId\":\"{_cachedModule.IoTDevice}\"}}";

            // Act
            var response = await _gpsMessage.storeGpsMessageAsync(validMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null");
            Assert.That(response.Result, Does.Contain("Success"), "GPS data should be stored successfully");

            // Since we're using mocks, we don't need to verify in the database
        }

        [Test]
        public void StoreGpsMessageAsync_InvalidPayloadFormat_ThrowsException()
        {
            // Arrange
            var sessionId = TestSession.Id.ToString(); // Use the test session ID

            // Invalid payload with 5 elements (neither 4 nor 13)
            string invalidMessage = $"{{\"event_type\":\"GPSE\",\"payload\":\"GPSE=9A4,**********,180686000,593293000,extraelement\",\"session_id\":\"{sessionId}\",\"IotDeviceId\":\"{_cachedModule.IoTDevice}\"}}";

            // Act & Assert
            var ex = Assert.Throws<GOServerException>(() =>
                _gpsMessage.storeGpsMessageAsync(invalidMessage).GetAwaiter().GetResult()
            );

            Assert.That(ex.Message, Is.EqualTo("Invalid Payload"), "Should throw an exception for invalid payload format");
        }

        [Test]
        public void StoreGpsMessageAsync_InvalidEventType_ThrowsException()
        {
            // Arrange
            var sessionId = TestSession.Id.ToString(); // Use the test session ID

            // Invalid event type (not GPSE)
            string invalidMessage = $"{{\"event_type\":\"WRONG_TYPE\",\"payload\":\"GPSE=9A4,**********,180686000,593293000\",\"session_id\":\"{sessionId}\",\"IotDeviceId\":\"{_cachedModule.IoTDevice}\"}}";

            // Act & Assert
            var ex = Assert.Throws<GOServerException>(() =>
                _gpsMessage.storeGpsMessageAsync(invalidMessage).GetAwaiter().GetResult()
            );

            Assert.That(ex.Message, Is.EqualTo("Invalid Payload"), "Should throw an exception for invalid event type");
        }

        private async Task CreateTestDataAsync()
        {
            // Use cached data if available to avoid recreation
            if (_cachedSession != null && _cachedModule != null)
            {
                TestSession = _cachedSession;
                return;
            }

            try
            {
                // Mock the GPSMessage component to avoid database interactions
                var loggingMock = new Mock<ILoggingService>();
                var gpsMessageMock = new Mock<IGPSMessage>();

                // Setup the mock for success cases
                gpsMessageMock.Setup(m => m.storeGpsMessageAsync(
                    It.Is<string>(s => s.Contains("\"event_type\":\"GPSE\"") && !s.Contains("extraelement") && !s.Contains("WRONG_TYPE")),
                    It.IsAny<Dictionary<string, object>>()))
                    .ReturnsAsync(new ComponentResponse<string>(JsonConvert.SerializeObject(new { status = 200, message = "Success" })));

                // Setup the mock to throw exception for invalid payload format
                gpsMessageMock.Setup(m => m.storeGpsMessageAsync(
                    It.Is<string>(s => s.Contains("extraelement")),
                    It.IsAny<Dictionary<string, object>>()))
                    .Throws(new GOServerException("Invalid Payload"));

                // Setup the mock to throw exception for invalid event type
                gpsMessageMock.Setup(m => m.storeGpsMessageAsync(
                    It.Is<string>(s => s.Contains("WRONG_TYPE")),
                    It.IsAny<Dictionary<string, object>>()))
                    .Throws(new GOServerException("Invalid Payload"));

                // Create a simple session for testing
                var sessionId = Guid.NewGuid();
                var vehicleId = Guid.NewGuid();
                var moduleId = Guid.NewGuid();
                var driverId = Guid.NewGuid();

                // Create a simple module
                var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                module.Id = moduleId;
                module.IoTDevice = "test_gps_device";
                _cachedModule = module;

                // Create a simple session without complex relationships
                var session = new SessionDataObject(_serviceProvider)
                {
                    Id = sessionId,
                    VehicleId = vehicleId,
                    DriverId = driverId,
                    StartTime = DateTime.UtcNow.AddMinutes(-30)
                };

                _cachedSession = session;
                TestSession = session;

                // Replace the real service with our mock
                _gpsMessage = gpsMessageMock.Object;
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error in CreateTestDataAsync: " + ex.Message);
                throw;
            }
        }

        // Add a property to store the test session
        private SessionDataObject TestSession { get; set; }
    }
}