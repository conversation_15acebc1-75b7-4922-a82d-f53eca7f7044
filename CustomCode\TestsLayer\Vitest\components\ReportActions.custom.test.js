import { describe, it, expect, beforeEach, vi } from 'vitest'
import fs from 'fs'
import path from 'path'

describe('ReportActionsCustom', () => {
    let reportActions;
    let mockObjectsDataSet;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                Model: {
                    Components: {}
                }
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/Model/Components/ReportActions.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base objects dataset
        mockObjectsDataSet = {};

        // Create the report actions instance
        reportActions = new FleetXQ.Web.Model.Components.ReportActions(mockObjectsDataSet);
    });

    describe('Email', () => {
        it('should call the ReportActionsAPI proxy with correct configuration', () => {
            const mockCaller = {
                popupParameter: '1',
                popupCaller: {
                    exportFilterPredicate: 'testPredicate',
                    exportFilterParameters: { param1: 'value1' }
                },
                setIsBusy: vi.fn(),
                closePopup: vi.fn(),
                ShowError: vi.fn(),
                CurrentObject: vi.fn().mockReturnValue({ email: '<EMAIL>' }),
                controller: {
                    applicationController: {
                        getProxyForComponent: vi.fn().mockReturnValue({
                            Email: vi.fn()
                        })
                    }
                }
            };

            const config = {
                caller: mockCaller,
                contextId: 'testContext',
                successHandler: vi.fn()
            };

            reportActions.Email(config);

            expect(mockCaller.setIsBusy).toHaveBeenCalledWith(true);
            expect(mockCaller.controller.applicationController.getProxyForComponent).toHaveBeenCalledWith('ReportActionsAPI');

            const expectedEmailConfig = {
                caller: mockCaller,
                contextId: 'testContext',
                successHandler: expect.any(Function),
                errorHandler: mockCaller.ShowError,
                reportType: 1,
                email: { email: '<EMAIL>' },
                filterPredicate: 'testPredicate',
                filterParameters: { param1: 'value1' }
            };

            const proxy = mockCaller.controller.applicationController.getProxyForComponent('ReportActionsAPI');
            expect(proxy.Email).toHaveBeenCalledWith(expectedEmailConfig);
        });

        it('should handle success callback correctly', () => {
            const mockCaller = {
                popupParameter: '1',
                popupCaller: {
                    exportFilterPredicate: 'testPredicate',
                    exportFilterParameters: { param1: 'value1' }
                },
                setIsBusy: vi.fn(),
                closePopup: vi.fn(),
                ShowError: vi.fn(),
                CurrentObject: vi.fn().mockReturnValue({ email: '<EMAIL>' }),
                controller: {
                    applicationController: {
                        getProxyForComponent: vi.fn().mockReturnValue({
                            Email: vi.fn()
                        })
                    }
                }
            };

            const successHandler = vi.fn();
            const config = {
                caller: mockCaller,
                contextId: 'testContext',
                successHandler
            };

            reportActions.Email(config);

            // Get the success handler passed to the proxy
            const proxy = mockCaller.controller.applicationController.getProxyForComponent('ReportActionsAPI');
            const successCallback = proxy.Email.mock.calls[0][0].successHandler;

            // Call the success handler
            successCallback();

            expect(successHandler).toHaveBeenCalled();
            expect(mockCaller.setIsBusy).toHaveBeenCalledWith(false);
            expect(mockCaller.closePopup).toHaveBeenCalledWith(false);
        });

        it('should handle error callback correctly', () => {
            const mockCaller = {
                popupParameter: '1',
                popupCaller: {
                    exportFilterPredicate: 'testPredicate',
                    exportFilterParameters: { param1: 'value1' }
                },
                setIsBusy: vi.fn(),
                closePopup: vi.fn(),
                ShowError: vi.fn(),
                CurrentObject: vi.fn().mockReturnValue({ email: '<EMAIL>' }),
                controller: {
                    applicationController: {
                        getProxyForComponent: vi.fn().mockReturnValue({
                            Email: vi.fn()
                        })
                    }
                }
            };

            const config = {
                caller: mockCaller,
                contextId: 'testContext'
            };

            reportActions.Email(config);

            // Get the error handler passed to the proxy
            const proxy = mockCaller.controller.applicationController.getProxyForComponent('ReportActionsAPI');
            const errorHandler = proxy.Email.mock.calls[0][0].errorHandler;

            // Call the error handler
            errorHandler('Test error');

            expect(mockCaller.ShowError).toHaveBeenCalledWith('Test error');
        });
    });
}); 