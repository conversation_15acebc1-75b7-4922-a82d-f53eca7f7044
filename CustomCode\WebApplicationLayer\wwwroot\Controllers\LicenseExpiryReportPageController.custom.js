﻿(function () {

    FleetXQ.Web.Controllers.LicenseExpiryReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.LicenseExpiryReportFilterFormViewModel.CurrentObject().Data;

            var parameterIndex = 0; // Start indexing after the initial three parameters.

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate += `SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += `DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            if (currentData.LicenseType() != null) {
                // Add StartDate Year, Month, and Day predicates and parameters.
                configuration.filterPredicate += `LicenseType == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Int32", "IsNullable": false, "Value": currentData.LicenseType() });
                parameterIndex += 1; // Increment the index for the next potential parameters.
            }

            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            return configuration;
        };

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.LicenseExpiryReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };
     
        this.loadPageData = function () {
            var configuration = this.getConfiguration();
            // Add the MultiSearch filter to the configuration.
            configuration = this.addMultiSearchFilter(configuration);

            self.controller.AllLicenseExpiryViewGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.AllLicenseExpiryViewGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.AllLicenseExpiryViewGridViewModel.LoadAllLicenseExpiryViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.AllLicenseExpiryViewGridViewModel.FILTER_NAME, self.controller.AllLicenseExpiryViewGridViewModel)) {
                if (customerId != null) {
                    var configuration = this.getConfiguration();
                    self.controller.AllLicenseExpiryViewGridViewModel.LoadAllLicenseExpiryViewObjectCollection(configuration);
                    return;
                }
				self.controller.AllLicenseExpiryViewGridViewModel.LoadAllLicenseExpiryViewObjectCollection();
			}
        }

        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.LicenseExpiryReportFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.LicenseExpiryReportFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.LicenseExpiryReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.loadPageData();
            };


            // self.loadInitialGridData();
        };
    };

})();