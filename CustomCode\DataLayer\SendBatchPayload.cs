﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{
    public class SendBatchPayload
    {
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [JsonProperty("msg_id")]
        public string MessageId { get; set; }

        [JsonProperty("payload")]
        public List<String> Payload { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

    }
}
