describe("002.b - Customer Model Form Polarity Test", () => {
    let cypressCompanyName;

    before(() => {
        // Load test data from fixture
        cy.fixture('testData').then((testData) => {
            cypressCompanyName = testData.cypressCompanyName;
        });
    });

    beforeEach(() => {
        // Use the centralized login function from the support file
        cy.login();

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/dealer/list*').as('getDealerList');

        // Intercept the API call for country list before interacting with the second dropdown
        cy.intercept('/dataset/api/country/list*').as('getCountryList');

        // Wait for the page to load by checking a key element
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(2) span").should('exist').should('be.visible');
    });

    it("verifies customer model form polarity enum functionality", () => {

        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(cypressCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // Check if the company is found or not
        cy.get('body').then($body => {
            if ($body.find('.no-data-message > span:visible').length > 0) {
                cy.log('Company not found after creation');
            } else {
                cy.log('Company found after creation');
            }
        });

        // Select the customer
        cy.get(`[data-bind="jqStopBubble: 'a'"] > a`)
            .should('exist')
            .should('be.visible')
            .first()  // Select the first matching element if multiple exist
            .click();

        cy.wait(3000);
        // go to model tab
        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-3"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // Select first model using the table structure
        cy.get('#CustomerFormControl-CustomerForm-CustomerModelItemsGrid')
            .find('.model-tbody-custom tr.pointer')
            .first()
            .find('td')
            .first()
            .click({ force: true });

        // Click edit button
        cy.get("[data-test-id='a8e0d4d5-0568-4e5b-b063-6ceac85af785']")
            .should('be.visible')
            .click({ force: true });

        // Verify polarity dropdown functionality
        cy.get("[data-test-id='edit_739680e2-4d23-4e67-b8de-9d5af566fd15']")
            .should('be.visible')
            .should('not.be.disabled')
            .then($select => {
                // Get all options first
                cy.wrap($select)
                    .find('option')
                    .then($options => {
                        // Verify that there are options available
                        expect($options.length).to.be.greaterThan(0);

                        // Select each option one by one
                        $options.each((index, option) => {
                            cy.get("[data-test-id='edit_739680e2-4d23-4e67-b8de-9d5af566fd15']")
                                .select(option.value)
                                .should('have.value', option.value);
                        });
                    });
            });
    });
}); 