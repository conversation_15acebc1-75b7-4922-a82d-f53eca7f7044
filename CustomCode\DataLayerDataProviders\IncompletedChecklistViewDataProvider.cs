﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class IncompletedChecklistViewDataProvider : DataProvider<IncompletedChecklistViewDataObject>
    {
        protected readonly IConfiguration _configuration;
        public IncompletedChecklistViewDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<IncompletedChecklistViewDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task DoDeleteAsync(IncompletedChecklistViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<IncompletedChecklistViewDataObject> DoGetAsync(IncompletedChecklistViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DataObjectCollection<IncompletedChecklistViewDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<IncompletedChecklistViewDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetChecklistResultByType", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasResultType)
                    {
                        command.Parameters.Add(new SqlParameter("@ResultType", SqlDbType.Int) { Value = filterArguments[filter.ResultTypeParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<IncompletedChecklistViewDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, TimeSlot, NumberOfRedImpacts, and NumberOfAmberImpacts
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.Year = (short)reader.GetInt32(reader.GetOrdinal("Year"));
                                    entity.Month = (short)reader.GetInt32(reader.GetOrdinal("Month"));
                                    entity.Day = (short)reader.GetInt32(reader.GetOrdinal("Day"));
                                    entity.NumberOfIncompletedChecklists = reader.GetInt32(reader.GetOrdinal("NumberOfIncompletedChecklists"));
                                    entity.NumberOfCompletedChecklists = (short)reader.GetInt32(reader.GetOrdinal("NumberOfCompletedChecklists"));
                                    entity.NumberOfCriticalFailedChecklists = (short)reader.GetInt32(reader.GetOrdinal("NumberOfCriticalFailedChecklists"));
                                    entity.Order = reader.GetInt32(reader.GetOrdinal("Order"));
                                    entity.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));

                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get IncompletedChecklist data", "Unable to get IncompletedChecklist data", ex);
                    }
                }
            }
        }

        protected override async Task<IncompletedChecklistViewDataObject> DoSaveAsync(IncompletedChecklistViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
