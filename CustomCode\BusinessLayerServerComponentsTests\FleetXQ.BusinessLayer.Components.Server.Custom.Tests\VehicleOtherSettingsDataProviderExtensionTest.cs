using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class VehicleOtherSettingsDataProviderExtensionTest : TestBase
    {
        private VehicleOtherSettingsDataProviderExtension _extension;
        private IDataFacade _dataFacade;
        private IDeviceTwinHandler _deviceTwinHandler;
        private IAuthentication _authentication;
        private readonly string _testDatabaseName = $"VehicleOtherSettingsDataProviderExtensionTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
            _authentication = _serviceProvider.GetRequiredService<IAuthentication>();
            _extension = new VehicleOtherSettingsDataProviderExtension(_serviceProvider, _dataFacade, _deviceTwinHandler, _authentication);

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task SaveVehicle_VORStatusChanged_SetsVORStatusConfirmedToFalse()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync();
            var settings = await vehicle.LoadVehicleOtherSettingsAsync();
            settings.VORStatus = true;
            settings.VORStatusConfirmed = true;
            vehicle.SetVehicleOtherSettingsValue(settings);
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Change VORStatus
            settings = await vehicle.LoadVehicleOtherSettingsAsync();
            settings.VORStatus = false;
            settings.VORStatusConfirmed = true;
            vehicle.SetVehicleOtherSettingsValue(settings);

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            settings = await vehicle.LoadVehicleOtherSettingsAsync();

            // Assert
            Assert.That(settings.VORStatusConfirmed, Is.False, "VORStatusConfirmed should be set to false when VORStatus changes");
        }

        [Test]
        public async Task SaveVehicle_VORStatusUnchanged_KeepsVORStatusConfirmed()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync();
            var settings = await vehicle.LoadVehicleOtherSettingsAsync();
            settings.VORStatus = true;
            settings.VORStatusConfirmed = true;
            vehicle.SetVehicleOtherSettingsValue(settings);
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Keep same VORStatus
            settings = await vehicle.LoadVehicleOtherSettingsAsync();
            settings.VORStatus = true;
            settings.VORStatusConfirmed = true;
            vehicle.SetVehicleOtherSettingsValue(settings);

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            settings = await vehicle.LoadVehicleOtherSettingsAsync();

            // Assert
            Assert.That(settings.VORStatusConfirmed, Is.True, "VORStatusConfirmed should remain true when VORStatus doesn't change");
        }

        [Test]
        public async Task SaveVehicle_NoCurrentSettings_SetsVORStatusConfirmedToFalse()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync();
            var settings = await vehicle.LoadVehicleOtherSettingsAsync();
            settings.VORStatus = true;
            settings.VORStatusConfirmed = true;
            vehicle.SetVehicleOtherSettingsValue(settings);

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            settings = await vehicle.LoadVehicleOtherSettingsAsync();

            // Assert
            Assert.That(settings.VORStatusConfirmed, Is.False, "VORStatusConfirmed should be set to false when there are no current settings");
        }

        private async Task<VehicleDataObject> CreateTestVehicleAsync()
        {
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            var (module, vehicle) = await TestUtilities.CreateTestModuleAndVehicleAsync(
                _dataFacade,
                _serviceProvider,
                customer.Id,
                department.Id,
                model.Id,
                site.Id,
                "Test Vehicle",
                "Test Serial No",
                "CCID1",
                $"test_{Guid.NewGuid()}");

            return vehicle;
        }

        private async Task CreateTestDataAsync()
        {
            // Create test data similar to DeviceTwinHandlerTest
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = customer.Id;
            site.Name = "Test Site";
            site.TimezoneId = timeZone.Id;
            site.Id = Guid.NewGuid();
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Description";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);
        }
    }
}