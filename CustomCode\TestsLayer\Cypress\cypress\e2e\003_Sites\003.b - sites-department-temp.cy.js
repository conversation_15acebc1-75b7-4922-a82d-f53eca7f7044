describe("003 - Sites Flow (Add Department)", () => {

    let uniqueSiteName;
    let tempCompanyName;
    let uniqueDepartmentName;
   
    before(() => {
        // Load test data from fixture
        cy.fixture('testData').then((testData) => {
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            tempCompanyName = testData.tempCompanyName;
            uniqueDepartmentName = testData.uniqueDepartmentName;
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login();

        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company else if not found continue and create
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(tempCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // CHECK IF THE COMPANY IS FOUND OR NOT
        cy.get('body').then($body => {
            // Check if the no-data-message exists and is visible
            if ($body.find('.no-data-message > span:visible').length > 0) {
                cy.log('Company not found, please customer.cy to create a company');
                cy.fail('Company not found, please customer.cy to create a company');
            }
        });

        // Customer data found, select the customer
        cy.get('td[data-bind="jqStopBubble: \'a\'"]')
        .should('exist')
        .should('be.visible')
        .first()  // Select the first matching element if multiple exist
        .click();

        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-2"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // CHECK IF THE SITE IS FOUND OR NOT
        cy.get('body').then($body => {
            const noSiteMessage = $body.find('#CustomerFormControl-CustomerForm-SitesGrid > [data-bind="css: { hideElt : false }"] > :nth-child(3) > .no-data-message > span');
            
            if (noSiteMessage.is(':visible') && noSiteMessage.text().includes('No Site data')) {
                cy.log('Site not found, please sites-flow.cy to create a site');
                cy.fail('Site not found, please sites-flow.cy to create a site');
            }
        });

        // Sites already exist, click on an existing site
        cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
        .should('exist')
        .should('be.visible')
        .first()
        .click();
        cy.wait(1000);
        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();
        
    });

    it("Should Add Department and verifies the added department in the table", () => {
        cy.get(`#SiteForm1-SiteFormForm-DepartmentItemsGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(1) > .command-button`)
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);

        cy.get(`#DepartmentCreateNewFormData > :nth-child(1) > .basicForm > .edit > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
        .should('exist')
        .should('be.visible')
        .clear()
        .type(uniqueDepartmentName);
        
        cy.get('.save')
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
        
        // Verify the department is added to the table
        cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
        .should('exist')
        .should('be.visible')
        .should('contain', uniqueDepartmentName);
        
        // Log the unique department name for debugging purposes
        cy.log(`Unique Department Name created: ${uniqueDepartmentName}`);
    });

    it("Should Edit Department and verifies the updated department in the table", () => {

        // Click on the department to edit
        cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(500);

        // Open the department edit form
        cy.get('#SiteForm1-SiteFormForm-DepartmentItemsGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(2) > .command-button')        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
        
        // enable the edit mode
        cy.get(`#popupContainer1 > [data-bind="css: { busy: StatusData.IsBusy(), disabled: !StatusData.IsEnabled() || StatusData.IsBusy() }, 'visible': StatusData.IsVisible()"] > .div-height-custom > #Commands > .btn-group > .edit`)
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
        
        cy.get(`#DepartmentFormData > :nth-child(1) > :nth-child(1) > [data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
        .should('exist')
        .should('be.visible')
        .clear()
        .type(uniqueDepartmentName + " - Updated");
        
        cy.get('.save')
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
        
        // Verify the department is updated in the table
        cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
        .should('exist')
        .should('be.visible')
        .should('contain', uniqueDepartmentName + " - Updated");

        cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
        .should('exist')
        .should('be.visible')
        .should('contain', uniqueDepartmentName + " - Updated");

        cy.log(`Department updated: ${uniqueDepartmentName + " - Updated"}`);

        // revert the department name back to the original name
        // Click on the department to edit
        cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(500);

        // Open the department edit form
        cy.get('#SiteForm1-SiteFormForm-DepartmentItemsGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(2) > .command-button')        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
        
        // enable the edit mode
        cy.get(`#popupContainer1 > [data-bind="css: { busy: StatusData.IsBusy(), disabled: !StatusData.IsEnabled() || StatusData.IsBusy() }, 'visible': StatusData.IsVisible()"] > .div-height-custom > #Commands > .btn-group > .edit`)
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
        
        cy.get(`#DepartmentFormData > :nth-child(1) > :nth-child(1) > [data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
        .should('exist')
        .should('be.visible')
        .clear()
        .type(uniqueDepartmentName);
        
        cy.get('.save')
        .should('exist')
        .should('be.visible')
        .click();

        cy.wait(1000);
    });
});
