import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('ProficiencyReportPageControllerCustom', () => {
    let filterFormViewModel;
    let gridViewModel;
    let controller;
    let customController;

    beforeEach(() => {
        // Create mocks
        filterFormViewModel = {
            CurrentObject: vi.fn().mockReturnValue({
                Data: {
                    CustomerId: vi.fn(),
                    SiteIds: vi.fn(),
                    DepartmentId: vi.fn(),
                    DateFrom: vi.fn(),
                    DateTo: vi.fn()
                }
            })
        };

        gridViewModel = {
            configurationOptions: {},
            loadData: vi.fn()
        };

        controller = {
            ProficiencyReportFilterFormViewModel: filterFormViewModel,
            ProficiencyReportGridViewModel: gridViewModel,
            applicationController: {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn().mockReturnValue({
                            role: 'DealerAdmin',
                            customerId: null,
                            customerIds: ['1', '2', '3'],
                            siteIds: ['100', '200', '300']
                        })
                    }
                }
            },
            IsInEditMode: vi.fn(),
            ShowError: vi.fn()
        };

        // Initialize the custom controller
        customController = new FleetXQ.Web.Controllers.ProficiencyReportPageControllerCustom(controller);
        
        // Set up global objects
        global.FleetXQ = {
            Web: {
                Controllers: {
                    ProficiencyReportPageControllerCustom: function(controller) {
                        this.controller = controller;
                        this.initialize = function() {};
                        this.loadPageData = vi.fn();
                    }
                }
            }
        };

        // Call initialize to set up the filterData function
        customController.initialize();
    });

    describe('DealerAdmin validation', () => {
        it('should show error when no customer is selected for a DealerAdmin', () => {
            // Arrange
            filterFormViewModel.CurrentObject().Data.CustomerId.mockReturnValue(null);
            
            // Act
            controller.ProficiencyReportFilterFormViewModel.filterData();
            
            // Assert
            expect(controller.ShowError).toHaveBeenCalledWith('Please select a customer');
            expect(customController.loadPageData).not.toHaveBeenCalled();
        });

        it('should allow filtering when a customer is selected for a DealerAdmin', () => {
            // Arrange
            filterFormViewModel.CurrentObject().Data.CustomerId.mockReturnValue('1');
            
            // Act
            controller.ProficiencyReportFilterFormViewModel.filterData();
            
            // Assert
            expect(controller.ShowError).not.toHaveBeenCalled();
            expect(customController.loadPageData).toHaveBeenCalled();
        });

        it('should allow filtering without a customer selection for non-DealerAdmin roles', () => {
            // Arrange
            controller.applicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: 'Admin',
                customerId: null,
                customerIds: ['1', '2', '3'],
                siteIds: ['100', '200', '300']
            });
            filterFormViewModel.CurrentObject().Data.CustomerId.mockReturnValue(null);
            
            // Act
            controller.ProficiencyReportFilterFormViewModel.filterData();
            
            // Assert
            expect(controller.ShowError).not.toHaveBeenCalled();
            expect(customController.loadPageData).toHaveBeenCalled();
        });
    });

    describe('Configuration generation', () => {
        it('should add customer and site filter based on user role and available IDs', () => {
            // This would test the generateDefaultConfiguration function, but it's not directly tested in this file
            // as it depends on how the configuration is generated in the controller
            expect(true).toBe(true);
        });
    });
}); 