## CSV Import/Export Enhancements – PR Summary

### Overview
Adds a complete CSV round-trip workflow to LocaleTranslator:
- Automatic CSV export after successful translation runs
- Manual CSV export from existing language folders
- CSV import to update JSON locale files (in-place, with backups)

### Changes Made
- `Program.cs`: new `export-csv` command; auto-export after `translate`; consistent DI/logging/path resolution
- `Services/FileProcessingService.cs`: CSV export flattening; CSV import with in-place JSON updates, arrays handling, backups, header validation
- `README-CSV.md`: documented commands, behavior, options, formats

### Features Added
1) Automatic CSV Export During Translation
- Runs after successful `translate`
- Outputs `exports/translations_{targetLanguage}_{yyyyMMdd_HHmmss}.csv`

2) CSV Export from Existing Translations
- `export-csv --export-language <name-or-full-path>`
- Auto-detects sibling source (`english` or `default`) near target

3) CSV Import for Translation Updates
- `import-csv --csv-file <file> --target <dir> [--backup true]`
- Updates only specified keys, preserves existing structure, creates files if missing

### Technical Details
- CLI: System.CommandLine commands `translate`, `export-csv`, `import-csv`
- Export: pairs source/target by relative path, flattens nested JSON to dot/bracket `KeyPath`, UTF-8 BOM, RFC 4180 quoting
- Import: validates headers, groups by `FilePath`, sets values by `KeyPath` (objects/arrays), backups with `.backup_yyyyMMdd_HHmmss`
- Manual export path resolution: folder name or full path; source discovered as sibling `english`/`default`

### Usage Instructions
- Automatic export (after translate):
```bash
dotnet run -- translate \
  --source "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\english" \
  --target "D:\CODEZ\workz\fleetxq\GeneratedCode\WebApplicationLayer\wwwroot\locales\french" \
  --language "French"
```

- Manual export from existing translations:
```bash
dotnet run -- export-csv --export-language "russian"
dotnet run -- export-csv --export-language "D:\\...\\locales\\russian"
```

- Import CSV to update JSON:
```bash
dotnet run -- import-csv --csv-file "./exports/translations_french_20250811_153045.csv" --target "..\\...\\locales\\french" --backup true
```

Options:
- Export: `--export-language` (name or full path), `--verbose`
- Import: `--csv-file`, `--target`, `--backup` (default true), `--verbose`

### CSV File Format
- Encoding: UTF-8 with BOM; RFC 4180 compliant quoting
- Columns: `SourceText`, `TranslatedText`, `SourceLanguage`, `TargetLanguage`, `FilePath`, `KeyPath`
- File name: `translations_{targetLanguage}_{yyyyMMdd_HHmmss}.csv`

### Impact
- Non-breaking: translation flow unchanged; CSV is additive
- Auto-export only on successful translate runs
- Manual export enables audit/review without translating
- Import preserves structure and logs changes; backups by default





