# Vehicle Access Department Change Update - Queue-Based Processing

## Overview

This document outlines the changes made to update the `UpdateVehicleAccessForDepartmentChangeAsync` method to use the same queue-based approach as `SendVehicleAccessCreationMessageAsync`. The implementation now supports both new vehicle creation and department change operations through a unified asynchronous processing system.

## Changes Made

### 1. Extended VehicleAccessCreationMessage

**Files Updated:**
- `CustomCode/DataLayer/VehicleAccessCreationMessage.cs`
- `VehicleAccessProcessor/VehicleAccessCreationMessage.cs`
- `CustomCode/UnitTests/VehicleAccessCreationTest.cs`

**New Properties Added:**
```csharp
// Properties for department change processing
public bool IsDepartmentChange { get; set; } = false;
public Guid? OldDepartmentId { get; set; }
public Guid? OldSiteId { get; set; }
```

**Purpose:** These properties enable the queue message to carry information about department changes, including the old department/site IDs needed for cleanup operations.

### 2. Updated VehicleDataProviderExtension

**File:** `CustomCode/DataLayerDataProviderExtensions/VehicleDataProviderExtension.cs`

**Key Changes:**
- Replaced the synchronous `UpdateVehicleAccessForDepartmentChangeAsync` method with a queue-based approach
- Method now creates a `VehicleAccessCreationMessage` with department change information and sends it to the queue
- Removed complex synchronous processing logic (moved to VehicleAccessCreation component)

**Before (Synchronous):**
```csharp
// Complex synchronous processing:
// - Get driver permissions
// - Mark existing access for deletion
// - Check for other vehicles in old department
// - Clean up model access if needed
// - Create new access records
// - Sync IoT device
```

**After (Asynchronous Queue-Based):**
```csharp
// Simple queue message creation and sending:
var accessCreationMessage = new VehicleAccessCreationMessage
{
    VehicleId = vehicle.Id,
    CustomerId = vehicle.CustomerId,
    ModelId = vehicle.ModelId,
    DepartmentId = vehicle.DepartmentId,
    SiteId = vehicle.SiteId,
    IsNewVehicle = false,
    IsDepartmentChange = true,
    OldDepartmentId = _oldDepartmentId,
    OldSiteId = _oldSiteId,
    IoTDevice = (await vehicle.LoadModuleAsync(skipSecurity: true))?.IoTDevice
};

await _vehicleAccessQueueService.SendVehicleAccessCreationMessageAsync(accessCreationMessage);
```

### 3. Enhanced VehicleAccessCreation Component

**File:** `CustomCode/BusinessLayerServerComponents/VehicleAccessCreation.cs`

**Key Enhancements:**

#### A. Process Type Detection
```csharp
var processType = vehicleAccessMessage.IsDepartmentChange ? "department change" : 
                 vehicleAccessMessage.IsNewVehicle ? "new vehicle" : "vehicle access update";
```

#### B. Department Change Cleanup Processing
Added `ProcessDepartmentChangeCleanupAsync` method that:
- Marks existing vehicle access records for deletion
- Checks if other vehicles exist with the same model in the old department
- Cleans up model access records if no other vehicles remain
- Provides detailed performance logging

#### C. Integrated Processing Flow
```csharp
// Handle department change cleanup if this is a department change operation
if (vehicleAccessMessage.IsDepartmentChange && vehicleAccessMessage.OldDepartmentId.HasValue)
{
    var cleanupStart = Stopwatch.StartNew();
    await ProcessDepartmentChangeCleanupAsync(vehicle, model, vehicleAccessMessage.OldDepartmentId.Value, driverPermissions);
    cleanupStart.Stop();
    _logger.LogDebug("[PERF] Department change cleanup completed in {ElapsedMs}ms", cleanupStart.ElapsedMilliseconds);
}
```

### 4. Updated VehicleAccessQueueService

**File:** `CustomCode/BusinessLayerServerComponents/VehicleAccessQueueService.cs`

**Enhancements:**
- Added new application properties for Service Bus messages
- Enhanced logging to show operation types
- Added conditional properties for old department/site IDs

**New Application Properties:**
```csharp
serviceBusMessage.ApplicationProperties["IsDepartmentChange"] = message.IsDepartmentChange;

if (message.OldDepartmentId.HasValue)
{
    serviceBusMessage.ApplicationProperties["OldDepartmentId"] = message.OldDepartmentId.Value.ToString();
}

if (message.OldSiteId.HasValue)
{
    serviceBusMessage.ApplicationProperties["OldSiteId"] = message.OldSiteId.Value.ToString();
}
```

### 5. Updated VehicleAccessProcessor Components

**Files Updated:**
- `VehicleAccessProcessor/VehicleAccessCreationService.cs`

**Changes:**
- Added new message properties to API request data
- Enhanced logging to show operation types (new vehicle, department change, update)
- Updated error handling to reflect operation type

## Compatibility and Reusability

### ✅ Compatible with New Vehicles
- `IsNewVehicle = true, IsDepartmentChange = false`: Creates access for new vehicles
- Existing new vehicle flow remains unchanged

### ✅ Compatible with Department Changes  
- `IsNewVehicle = false, IsDepartmentChange = true`: Handles department changes
- Includes cleanup of old access records
- Processes new department access creation

### ✅ Extensible for Future Use Cases
- `IsNewVehicle = false, IsDepartmentChange = false`: Generic vehicle access updates
- Framework ready for additional vehicle access scenarios

### ✅ Same Resource Utilization
- Uses existing queue infrastructure
- Leverages existing VehicleAccessCreation component
- Maintains same Service Bus configuration
- No additional infrastructure required

## Benefits of the Updated Approach

### 1. **Asynchronous Processing**
- Department changes no longer block the main data provider extension
- Better scalability and responsiveness
- Consistent with new vehicle creation pattern

### 2. **Unified Processing Pipeline**
- Both new vehicles and department changes use the same queue
- Single component handles all vehicle access operations
- Consistent error handling and retry logic

### 3. **Enhanced Monitoring**
- Detailed performance logging for all operation types
- Clear distinction between operation types in logs
- Service Bus message properties for filtering and monitoring

### 4. **Improved Reliability**
- Queue-based processing provides retry capabilities
- Dead letter handling for failed operations
- Transactional consistency through message processing

### 5. **Maintainability**
- Centralized business logic in VehicleAccessCreation component
- Simplified data provider extension
- Clear separation of concerns

## Processing Flow

### New Vehicle Creation
```
Vehicle Created → VehicleDataProviderExtension → Queue Message → VehicleAccessCreation Component → Access Records Created → IoT Sync
```

### Department Change
```
Department Changed → VehicleDataProviderExtension → Queue Message → VehicleAccessCreation Component → Cleanup Old Access → Create New Access → IoT Sync
```

### Generic Updates
```
Vehicle Updated → VehicleDataProviderExtension → Queue Message → VehicleAccessCreation Component → Update Access Records → IoT Sync
```

## Testing Considerations

### Unit Tests
- Update existing tests to include new message properties
- Test department change scenarios
- Verify queue message creation for different operation types

### Integration Tests
- Test end-to-end department change processing
- Verify cleanup of old access records
- Confirm creation of new access records
- Test IoT device synchronization

### Performance Tests
- Measure processing time for department changes
- Compare with previous synchronous approach
- Monitor queue depth under load

## Monitoring and Troubleshooting

### Key Metrics
- **Processing Time**: Department change vs new vehicle creation
- **Success Rate**: By operation type (new, change, update)
- **Queue Depth**: Monitor for backlog buildup
- **Error Rate**: Failed department change operations

### Log Analysis
```
# Search for department change operations
[PERF] Processing department change for VehicleId

# Monitor performance
[PERF] Department change cleanup completed in {ElapsedMs}ms

# Check for errors
[PERF] Vehicle access department change FAILED
```

### Service Bus Properties for Filtering
```json
{
  "IsDepartmentChange": true,
  "OldDepartmentId": "guid",
  "VehicleId": "guid"
}
```

## Conclusion

The updated implementation successfully transforms the synchronous department change processing into an asynchronous, queue-based system that:

1. **Maintains Compatibility**: Works with both new vehicles and department changes
2. **Reuses Infrastructure**: Leverages existing queue and processing components  
3. **Improves Performance**: Non-blocking operations with better scalability
4. **Enhances Monitoring**: Detailed logging and Service Bus properties for observability
5. **Ensures Reliability**: Queue-based retry and error handling mechanisms

The system is now consistent, maintainable, and ready for future enhancements while preserving all existing functionality. 