describe("005c - Users Supervisor Access", () => {
    let testFirstName;
    let testLastName;
    let uniqueSiteName;
    let cypressCompanyName;
    let uniqueDepartmentName;
    before(() => {
        cy.fixture('testData').then((testData) => {
            testFirstName = testData.cypressFirstName;
            testLastName = testData.cypressLastName;
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            cypressCompanyName = testData.cypressCompanyName;
            uniqueDepartmentName = testData.uniqueDepartmentName;
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login()

        // Navigate to the users section
        cy.get(`[data-bind="'enable' : navigation.isUserManagementEnabled(), 'visible' : navigation.isUserManagementVisible()"] > .nav-link`).click();
        cy.wait(1000);

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/customer/list*').as('getCustomerList');

        // Search for the user
        cy.get('input.filterTextInputCustom.form-control')
            .should('exist')
            .should('be.visible')
            .click({ force: true })
            .clear({ force: true })
            .type(testFirstName, { force: true });


        // Click on the search button
        cy.get('.btn-primary')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // select the first user
        cy.get(`[data-bind="jqStopBubble: 'a'"] > a`)
            .first()
            .should('be.visible')
            .click();

        cy.wait(3000);


    });

    it('should enable the supervisor access', () => {
        // ENABLE EDIT MODE
        cy.get('#PersonFormControlCommands > .btn-group > .btn').click();

        // make sure that supervisor is enabled
        // enable the supervisor access and make sure yes button is selected
        // Select "Yes" for Supervisor role
        cy.get('input.btn-check[name="Supervisor-edit-yesno"][value="true"]')
            .should('exist')
            .click({ force: true });

        // move to supervisor tab
        cy.get('[data-id="PersonFormControl-PersonForm-tabs-4"]')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // Update Supervisor authorization

        // Ensure Can Unlock Vehicle is checked
        cy.get(`[data-bind="'visible':SupervisorAuthorizationFormViewModel.StatusData.DisplayMode() == 'edit' && SupervisorAuthorizationFormViewModel.StatusData.IsCanUnlockVehicleVisible()"] > .form-field-control-container > .checkbox-field > .form-check-input`)
            .should('be.visible')
            .then($checkbox => {
                if (!$checkbox.prop('checked')) {
                    cy.wrap($checkbox).click({ force: true });
                }
            });

        cy.get(`[data-bind="'visible':SupervisorAuthorizationFormViewModel.StatusData.DisplayMode() == 'edit' && SupervisorAuthorizationFormViewModel.StatusData.IsNormalDriverAccessVisible()"] > .form-field-control-container > .checkbox-field > .form-check-input`)
            .should('be.visible')
            .click({ force: true });

        cy.get(`[data-bind="'visible':SupervisorAuthorizationFormViewModel.StatusData.DisplayMode() == 'edit' && SupervisorAuthorizationFormViewModel.StatusData.IsVORActivateDeactivateVisible()"] > .form-field-control-container > .checkbox-field > .form-check-input`)
            .should('be.visible')
            .click({ force: true });

        cy.wait(1000);

        // save the changes
        cy.get('.save').click();

        cy.wait(1000);

        // Enable Supervisor vehicle access
        cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-SupervisorVehicleAccessFormForm > :nth-child(1) > #PersonFormControl-PersonForm-SupervisorVehicleAccessFormFormCommands > .nav > .edit`)
            .should('be.visible')
            .click({ force: true });

        cy.wait(500);

        // select all sites
        cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-SupervisorVehicleAccessFormForm > :nth-child(1) > #PersonFormControl-PersonForm-SupervisorVehicleAccessFormFormCommands > .nav > .selectall`)
            .should('be.visible')
            .click({ force: true });

        cy.wait(500);

        // save the changes
        cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-SupervisorVehicleAccessFormForm > :nth-child(1) > #PersonFormControl-PersonForm-SupervisorVehicleAccessFormFormCommands > .nav > .save`)
            .should('be.visible')
            .click({ force: true });

        cy.wait(1000);
    });

});