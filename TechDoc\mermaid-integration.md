This guide provides a comprehensive, step-by-step approach to rendering Mermaid diagrams from dedicated .mermaid files within your VuePress project. This method leverages a custom plugin to seamlessly integrate the Mermaid file content into your markdown rendering process.

Prerequisites
A working VuePress project with version ^1.9.10.

A package manager like npm or yarn.

Step 1: Install Required Dependencies
You'll need to install the core mermaid library for rendering diagrams and fs-extra to handle file system operations within the custom plugin.

Bash

npm install mermaid fs-extra
# or
yarn add mermaid fs-extra

Step 2: Create a Custom Plugin
This plugin will extend VuePress's markdown configuration to recognize and process a special syntax in your markdown files.

Create a new JavaScript file for your plugin. The location of this file can be flexible, but a common practice is to place it in a .vuepress/plugins directory.

Add the following code to your plugin file:

JavaScript

const fs = require('fs-extra');
const path = require('path');

module.exports = (options, ctx) => {
  return {
    name: 'vuepress-plugin-mermaid',
    extendMarkdown: md => {
      const defaultRender = md.renderer.rules.fence;

      md.renderer.rules.fence = (tokens, idx, options, env, self) => {
        const token = tokens[idx];
        const info = token.info.trim();

        if (info.startsWith('mermaid-file=')) {
          const filePath = info.split('=')[1].trim();
          // Resolves the file path relative to the VuePress source directory
          const fullPath = path.resolve(ctx.sourceDir, filePath);
          
          try {
            const fileContent = fs.readFileSync(fullPath, 'utf-8');
            const newTokens = [{
              ...token,
              info: 'mermaid',
              content: fileContent,
            }];
            return defaultRender(newTokens, 0, options, env, self);
          } catch (error) {
            console.error(`[mermaid-plugin] Error reading file: ${fullPath}`, error);
            return `<div class="mermaid-error">Error: Could not read Mermaid file at "${filePath}".</div>`;
          }
        }
        return defaultRender(tokens, idx, options, env, self);
      };
    },
  };
};
This plugin works by:

Overriding the default markdown code block (fence) renderer.

Checking if the code block's language tag starts with mermaid-file=.

If the tag matches, it reads the content of the specified file.

It then creates a new code block token with the language mermaid and the file's content, allowing VuePress to render it as a standard Mermaid diagram.

Includes a simple fallback for when the file cannot be found.

Step 3: Configure VuePress to Use the Plugin
Open your .vuepress/config.js file and add the plugin to the plugins array. If your plugin file is located at .vuepress/plugins/my-mermaid-plugin/index.js, the path will be require('./plugins/my-mermaid-plugin').

JavaScript

// .vuepress/config.js

module.exports = {
  title: 'My VuePress Site',
  description: 'A site with Mermaid diagrams!',
  
  plugins: [
    // ... other plugins you may have
    [require('./path/to/your/mermaid-plugin')]
  ]
};
Step 4: Create a Mermaid File
Create a file with the .mermaid extension containing your diagram code. For example, flow-chart.mermaid.

Code snippet

graph TD;
    A[Start] --> B{Is it raining?};
    B -- Yes --> C[Get an umbrella];
    B -- No --> D[Go outside];
    C --> E[End];
    D --> E[End];