IF NOT EXISTS (SELECT 1 FROM [dbo].[<PERSON><PERSON><PERSON>] WHERE [Name] = '388(388C0)')
BEGIN
    INSERT INTO [dbo].[Can<PERSON><PERSON>] ([VehicleSerial], [Id], [CRC], [Name], [Description])
        VALUES (NULL, 'B344E8BD-9CEB-45E9-984C-CA201C212E10', '21D3E387', '388(388C0)', NULL)

        -- Insert CANATT=1,SEAT,3,0,1,1
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANATT=1,SEAT,3,0,1,1' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [<PERSON><PERSON><PERSON>], [<PERSON>ruleId])
                VALUES (NEWID(), 'CANATT=1,SEAT,3,0,1,1', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANPGN=3,0,7,0,91,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANPGN=3,0,7,0,91,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANPGN=3,0,7,0,91,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANLIN2=1,5A8,628,4001200500000000,4B01200500000000,FFFFFFFF00000000,1,27,=,1
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANLIN2=1,5A8,628,4001200500000000,4B01200500000000,FFFFFFFF00000000,1,27,=,1' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANLIN2=1,5A8,628,4001200500000000,4B01200500000000,FFFFFFFF00000000,1,27,=,1', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANLIN2=2,5A8,628,4001200200000000,4B01200200000000,FFFFFFFF00000000,20,20,>,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANLIN2=2,5A8,628,4001200200000000,4B01200200000000,FFFFFFFF00000000,20,20,>,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANLIN2=2,5A8,628,4001200200000000,4B01200200000000,FFFFFFFF00000000,20,20,>,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANPGN=1,0,7,0,92,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANPGN=1,0,7,0,92,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANPGN=1,0,7,0,92,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANATT=2,TRACK,3,0,2,2
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANATT=2,TRACK,3,0,2,2' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANATT=2,TRACK,3,0,2,2', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANATT=5,BACD1,1,5,0,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANATT=5,BACD1,1,5,0,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANATT=5,BACD1,1,5,0,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANSPN=5,3,1,6163,10,18,-,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANSPN=5,3,1,6163,10,18,-,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANSPN=5,3,1,6163,10,18,-,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANBYD=1,628,40012005,1,27,=,1
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANBYD=1,628,40012005,1,27,=,1' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANBYD=1,628,40012005,1,27,=,1', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANBYD=2,628,40012002,20,20,>,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANBYD=2,628,40012002,20,20,>,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANBYD=2,628,40012002,20,20,>,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANCFG=2,500000,0,1
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANCFG=2,500000,0,1' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANCFG=2,500000,0,1', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANLIN=1,1C0,0,8,0,>,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANLIN=1,1C0,0,8,0,>,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANLIN=1,1C0,0,8,0,>,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANSPN=3,1,2,615f,1,1d,>,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANSPN=3,1,2,615f,1,1d,>,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANSPN=3,1,2,615f,1,1d,>,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANPGN=2,0,7,0,94,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANPGN=2,0,7,0,94,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANPGN=2,0,7,0,94,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANSPN=2,2,1,6134,10,18,>,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANSPN=2,2,1,6134,10,18,>,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANSPN=2,2,1,6134,10,18,>,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANATT=4,HRSS0,1,4,0,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANATT=4,HRSS0,1,4,0,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANATT=4,HRSS0,1,4,0,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANSPN=4,2,2,6114,20,18,-,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANSPN=4,2,2,6114,20,18,-,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANSPN=4,2,2,6114,20,18,-,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANSPN=1,1,1,615c,1,1d,>,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANSPN=1,1,1,615c,1,1d,>,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANSPN=1,1,1,615c,1,1d,>,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANLIN=2,1AC,0,8,8,-,0
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANLIN=2,1AC,0,8,8,-,0' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANLIN=2,1AC,0,8,8,-,0', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END

        -- Insert CANATT=3,HYDR,3,0,4,4
        IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANATT=3,HYDR,3,0,4,4' AND [CanruleId] = 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        BEGIN
            INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
                VALUES (NEWID(), 'CANATT=3,HYDR,3,0,4,4', 'B344E8BD-9CEB-45E9-984C-CA201C212E10')
        END
END