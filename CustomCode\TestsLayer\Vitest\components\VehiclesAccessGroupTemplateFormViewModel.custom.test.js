import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the global FleetXQ structure
if (!global.FleetXQ) {
    global.FleetXQ = {
        Web: {
            ViewModels: {}
        }
    };
}

// Mock implementation for the test
// Assuming VehiclesAccessGroupTemplateFormViewModelCustom is loaded or accessible
// Similar to other test files, if IIFE loading is an issue, direct mocking or refactor is needed.
const MockVehiclesAccessGroupTemplateFormViewModelCustom = function (viewmodel) {
    this.viewmodel = viewmodel;
    var self = this; // Mimic the 'self' pattern

    this.selectAllEditCommand = function () {
        self.viewmodel.CurrentObject().Data.CanEditVehicle(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklist(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklistSetting(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleImpactSetting(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleService(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingFullLockout(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingVorStatus(true);
        self.viewmodel.CurrentObject().Data.CanEditRAModuleSwap(true); // Added for RA Module Swap
    };

    this.deselectAllEditCommand = function () {
        self.viewmodel.CurrentObject().Data.CanEditVehicle(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklist(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklistSetting(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleImpactSetting(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleService(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingFullLockout(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingVorStatus(false);
        self.viewmodel.CurrentObject().Data.CanEditRAModuleSwap(false); // Added for RA Module Swap
    };

    // Visibility function using CurrentObject as per correction for this VM
    this.IsCanEditRAModuleSwapVisible = function () {
        return self.viewmodel.CurrentObject().Data.HasVehiclesAccess();
    };

    // Assuming IsHTMLField_2fef... is also needed and uses CurrentObject here
    this.IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible = function () {
        return self.viewmodel.CurrentObject().Data.HasVehiclesAccess();
    };
};

describe('VehiclesAccessGroupTemplateFormViewModelCustom', () => {
    let mockTemplateViewModel;
    let customTemplateViewModel;

    beforeEach(() => {
        mockTemplateViewModel = {
            CurrentObject: vi.fn().mockReturnValue({
                Data: {
                    CanEditVehicle: vi.fn(),
                    CanEditVehicleChecklist: vi.fn(),
                    CanEditVehicleChecklistSetting: vi.fn(),
                    CanEditVehicleImpactSetting: vi.fn(),
                    CanEditVehicleService: vi.fn(),
                    CanEditVehicleOtherSettingFullLockout: vi.fn(),
                    CanEditVehicleOtherSettingVorStatus: vi.fn(),
                    CanEditRAModuleSwap: vi.fn(),
                    HasVehiclesAccess: vi.fn() // For visibility checks
                }
            })
            // Note: If AccessGroupObject() was indeed used for visibility in the final code for this VM,
            // that would need to be mocked here instead/aswell, similar to AccessGroupForm2ViewModel.custom.test.js
            // But we corrected it to use CurrentObject().Data.HasVehiclesAccess for this VM.
        };
        customTemplateViewModel = new MockVehiclesAccessGroupTemplateFormViewModelCustom(mockTemplateViewModel);
        // Replace with actual class: 
        // customTemplateViewModel = new FleetXQ.Web.ViewModels.VehiclesAccessGroupTemplateFormViewModelCustom(mockTemplateViewModel);
    });

    describe('selectAllEditCommand', () => {
        it('should set CanEditRAModuleSwap to true on the template data', () => {
            customTemplateViewModel.selectAllEditCommand();
            expect(mockTemplateViewModel.CurrentObject().Data.CanEditRAModuleSwap).toHaveBeenCalledWith(true);
        });
    });

    describe('deselectAllEditCommand', () => {
        it('should set CanEditRAModuleSwap to false on the template data', () => {
            customTemplateViewModel.deselectAllEditCommand();
            expect(mockTemplateViewModel.CurrentObject().Data.CanEditRAModuleSwap).toHaveBeenCalledWith(false);
        });
    });

    describe('IsCanEditRAModuleSwapVisible', () => {
        it('should return true if template HasVehiclesAccess is true', () => {
            mockTemplateViewModel.CurrentObject().Data.HasVehiclesAccess.mockReturnValue(true);
            expect(customTemplateViewModel.IsCanEditRAModuleSwapVisible()).toBe(true);
        });

        it('should return false if template HasVehiclesAccess is false', () => {
            mockTemplateViewModel.CurrentObject().Data.HasVehiclesAccess.mockReturnValue(false);
            expect(customTemplateViewModel.IsCanEditRAModuleSwapVisible()).toBe(false);
        });
    });

    describe('IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible', () => {
        it('should return true if template HasVehiclesAccess is true', () => {
            mockTemplateViewModel.CurrentObject().Data.HasVehiclesAccess.mockReturnValue(true);
            expect(customTemplateViewModel.IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible()).toBe(true);
        });

        it('should return false if template HasVehiclesAccess is false', () => {
            mockTemplateViewModel.CurrentObject().Data.HasVehiclesAccess.mockReturnValue(false);
            expect(customTemplateViewModel.IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible()).toBe(false);
        });
    });
}); 