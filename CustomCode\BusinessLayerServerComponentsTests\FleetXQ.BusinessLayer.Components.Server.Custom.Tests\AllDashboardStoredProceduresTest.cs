using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using FleetXQ.BusinessLayer.Components.Server.Extensions;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ORMSupportClasses;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    public class AllDashboardStoredProceduresTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"AllDashboardStoredProceduresTest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _siteId;
        private Guid _departmentId;
        private Guid _modelId;
        private Guid _driverId;
        private Guid _vehicleId;
        private Guid _timezoneId;
        private DateTime _referenceDate;

        private static readonly DateTime[] TestDates = new[]
        {
            new DateTime(1753, 1, 1),    // SQL Server minimum date
            new DateTime(2010, 1, 1),    // Past date
            new DateTime(2020, 6, 15),   // Mid-past date
            DateTime.UtcNow,             // Current date
            new DateTime(2027, 12, 31),  // Future date
            DateTime.UtcNow.Date,        // Start of current day
            DateTime.UtcNow.Date.AddDays(1).AddSeconds(-1), // End of current day
            DateTime.UtcNow.AddHours(-12), // 12 hours ago
            DateTime.UtcNow.AddHours(-24), // 24 hours ago
            DateTime.UtcNow.AddHours(-72), // 72 hours ago
            new DateTime(9999, 12, 31)   // SQL Server maximum date
        };

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
            _referenceDate = DateTime.UtcNow;
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create base test data (similar to AllChecklistResultViewDataProviderTest)
            await CreateBaseEntitiesAsync(); // Country, Region, Dealer, Customer, Timezone
            await CreateSiteAndDepartmentAsync();
            await CreateModelAsync();
            await CreateDriverAsync();
            await CreateVehicleWithModuleAsync();
            await CreateTestSessionsAsync();
            await CreateTestImpactsAsync();
            await CreateTestPreOpChecksAsync();
        }

        private async Task CreateBaseEntitiesAsync()
        {
            // Create Country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            await _dataFacade.CountryDataProvider.SaveAsync(country);

            // Create Region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create Dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test Dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create Customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            // Create Timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);
            _timezoneId = timeZone.Id;
        }

        private async Task CreateSiteAndDepartmentAsync()
        {
            // Create Site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = _customerId;
            site.Name = "Test Site";
            site.Id = Guid.NewGuid();
            site.TimezoneId = _timezoneId;
            _siteId = site.Id;
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create Department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            department.CustomerId = _customerId;
            _departmentId = department.Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);
        }

        private async Task CreateModelAsync()
        {
            // Create Region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Id = Guid.NewGuid();
            region.Name = "Test Region";
            await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create Dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Id = Guid.NewGuid();
            dealer.Name = "Test Dealer";
            dealer.Description = "Test Dealer Description";
            dealer.RegionId = region.Id;  // Link Dealer to Region
            await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create Model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.Type = ((ModelTypesEnum[])Enum.GetValues(typeof(ModelTypesEnum))).First(x => x.ToString() == "Electric");
            model.DealerId = dealer.Id;  // Link Model to Dealer
            _modelId = model.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);
        }

        private async Task CreateDriverAsync()
        {
            // Create Person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = _customerId;
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.IsDriver = true;
            person.IsActiveDriver = true;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Create Card
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.FacilityCode = "123";
            card.CardNumber = "123456";
            card.Active = true;
            card.KeypadReader = ((KeypadReaderEnum[])Enum.GetValues(typeof(KeypadReaderEnum))).First(x => x.ToString() == "Rosslare");
            card.Type = CardTypeEnum.CardID;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            // Update Driver with Card
            var driver = person.Driver;
            driver.CardDetailsId = card.Id;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);
            _driverId = driver.Id;
        }

        private async Task CreateVehicleWithModuleAsync()
        {
            // Create Module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID" + Guid.NewGuid().ToString().Substring(0, 4);
            module.FSSSBase = 150000;
            module.FSSXMulti = 1;
            module.IoTDevice = "TEST-IOT-" + Guid.NewGuid().ToString().Substring(0, 4);
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create Vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = _customerId;
            vehicle.SiteId = _siteId;
            vehicle.DepartmentId = _departmentId;
            vehicle.ModelId = _modelId;
            vehicle.IDLETimer = 300;
            vehicle.OnHire = true;
            vehicle.ImpactLockout = true;
            vehicle.HireNo = "TEST-HIRE-001";
            vehicle.SerialNo = "TEST-SERIAL-001";
            vehicle.ModuleId1 = module.Id;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            _vehicleId = vehicle.Id;
        }

        private async Task CreateTestSessionsAsync()
        {
            // Create sessions in the last 12 hours
            var now = DateTime.UtcNow;
            // Round to the nearest hour to ensure consistent hour boundaries
            now = new DateTime(now.Year, now.Month, now.Day, now.Hour, 0, 0, DateTimeKind.Utc);

            // Create a session every 2 hours for the last 12 hours
            for (int i = 0; i <= 12; i += 2)
            {
                var startTime = now.AddHours(-i);
                var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                session.Id = Guid.NewGuid();
                session.VehicleId = _vehicleId;
                session.DriverId = _driverId;
                session.StartTime = startTime;
                session.EndTime = startTime.AddMinutes(30); // 30-minute sessions
                await _dataFacade.SessionDataProvider.SaveAsync(session);
            }
        }

        private async Task CreateTestImpactsAsync()
        {
            // Create impacts in the last 24 hours
            var now = DateTime.UtcNow;
            var impactTimes = new[]
            {
                now.AddHours(-2),  // Recent impact
                now.AddHours(-8),  // Mid-day impact
                now.AddHours(-16), // Earlier impact
                now.AddHours(-23)  // Almost 24 hours old
            };

            foreach (var impactTime in impactTimes)
            {
                // Create a session first
                var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                session.Id = Guid.NewGuid();
                session.VehicleId = _vehicleId;
                session.DriverId = _driverId;
                session.StartTime = impactTime.AddMinutes(-5);
                session.EndTime = impactTime.AddMinutes(5);
                session = await _dataFacade.SessionDataProvider.SaveAsync(session);

                // Then create the impact with the session ID
                var impact = _serviceProvider.GetRequiredService<ImpactDataObject>();
                impact.Id = Guid.NewGuid();
                impact.SessionId = session.Id;
                impact.ImpactDateTime = impactTime;
                impact.ShockValue = 100;    // High impact value
                impact.Threshold = 50;      // Impact threshold
                await _dataFacade.ImpactDataProvider.SaveAsync(impact);
            }
        }

        private async Task CreateTestPreOpChecksAsync()
        {
            // Create pre-op checks in the last 24 hours
            var now = DateTime.UtcNow;
            var checkTimes = new[]
            {
                now.AddHours(-1),  // Recent check
                now.AddHours(-6),  // Mid-day check
                now.AddHours(-12), // Earlier check
                now.AddHours(-23)  // Almost 24 hours old
            };

            foreach (var checkTime in checkTimes)
            {
                // Create a session first
                var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                session.Id = Guid.NewGuid();
                session.VehicleId = _vehicleId;
                session.DriverId = _driverId;
                session.StartTime = checkTime.AddMinutes(-5);
                session.EndTime = checkTime.AddMinutes(5);
                session = await _dataFacade.SessionDataProvider.SaveAsync(session);

                // Then create the checklist with the session ID
                var check = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
                check.Id = Guid.NewGuid();
                check.SessionId1 = session.Id;
                check.StartTime = checkTime;
                check.EndTime = checkTime.AddMinutes(5);
                check.Comment = "Complete";  // Status stored in comment
                await _dataFacade.ChecklistResultDataProvider.SaveAsync(check);
            }
        }

        #region GetDashboardDriverCard Tests

        [Test]
        public async Task GetDashboardDriverCard_WithDifferentFilters_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with Customer only
                var resultCustomer = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                // Test with Customer + Site
                var resultCustomerSite = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                // Test with Customer + Site + Department
                var resultAll = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                // Assert all results
                Assert.Multiple(() =>
                {
                    // Customer only
                    Assert.That(resultCustomer, Is.Not.Null, $"Customer result should not be null for date {testDate}");
                    Assert.That(resultCustomer.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomer.Any())
                    {
                        var card = resultCustomer.First();
                        Assert.That(card.DriversCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.NonActiveDriversCountLastWeek, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.NoLicenseDriversCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.ExpiredLicensesCount, Is.GreaterThanOrEqualTo(0));
                    }

                    // Customer + Site
                    Assert.That(resultCustomerSite, Is.Not.Null, $"Customer+Site result should not be null for date {testDate}");
                    Assert.That(resultCustomerSite.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomerSite.Any())
                    {
                        var card = resultCustomerSite.First();
                        Assert.That(card.DriversCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.NonActiveDriversCountLastWeek, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.NoLicenseDriversCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.ExpiredLicensesCount, Is.GreaterThanOrEqualTo(0));
                    }

                    // All filters
                    Assert.That(resultAll, Is.Not.Null, $"Full filter result should not be null for date {testDate}");
                    Assert.That(resultAll.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultAll.Any())
                    {
                        var card = resultAll.First();
                        Assert.That(card.DriversCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.NonActiveDriversCountLastWeek, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.NoLicenseDriversCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.ExpiredLicensesCount, Is.GreaterThanOrEqualTo(0));
                    }
                });
            }
        }

        [Test]
        public async Task GetDashboardDriverCard_WithCustomerAndDate_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with customer + date
                var result = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                Assert.That(result, Is.Not.Null);
                // Add specific assertions based on expected data for this combination
            }
        }

        [Test]
        public async Task GetDashboardDriverCard_WithCustomerSiteAndDate_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with customer + site + date
                var result = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                Assert.That(result, Is.Not.Null);
                // Add specific assertions based on expected data for this combination
            }
        }

        [Test]
        public async Task GetDashboardDriverCard_WithCustomerSiteDepartmentAndDate_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with customer + site + department + date
                var result = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                Assert.That(result, Is.Not.Null);
                // Add specific assertions based on expected data for this combination
            }
        }

        #endregion

        #region GetDashboardVehicleCard Tests

        [Test]
        public async Task GetDashboardVehicleCard_WithDifferentFilters_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with Customer only
                var resultCustomer = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                // Test with Customer + Site
                var resultCustomerSite = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                // Test with Customer + Site + Department
                var resultAll = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                // Assert all results
                Assert.Multiple(() =>
                {
                    // Customer only
                    Assert.That(resultCustomer, Is.Not.Null, $"Customer result should not be null for date {testDate}");
                    Assert.That(resultCustomer.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomer.Any())
                    {
                        var card = resultCustomer.First();
                        Assert.That(card.VehiclesCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.SessionCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.InactiveVehicleCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.InactiveVehicleCountLastSeventyTwoHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.VehicleVORModeCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.RedImpactCountToday, Is.GreaterThanOrEqualTo(0));
                    }

                    // Customer + Site
                    Assert.That(resultCustomerSite, Is.Not.Null, $"Customer+Site result should not be null for date {testDate}");
                    Assert.That(resultCustomerSite.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomerSite.Any())
                    {
                        var card = resultCustomerSite.First();
                        Assert.That(card.VehiclesCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.SessionCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.InactiveVehicleCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.InactiveVehicleCountLastSeventyTwoHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.VehicleVORModeCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.RedImpactCountToday, Is.GreaterThanOrEqualTo(0));
                    }

                    // All filters
                    Assert.That(resultAll, Is.Not.Null, $"Full filter result should not be null for date {testDate}");
                    Assert.That(resultAll.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultAll.Any())
                    {
                        var card = resultAll.First();
                        Assert.That(card.VehiclesCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.SessionCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.InactiveVehicleCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.InactiveVehicleCountLastSeventyTwoHours, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.VehicleVORModeCount, Is.GreaterThanOrEqualTo(0));
                        Assert.That(card.RedImpactCountToday, Is.GreaterThanOrEqualTo(0));
                    }
                });
            }
        }

        [Test]
        public async Task GetDashboardVehicleCard_WithCustomerAndDate_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                var result = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                Assert.That(result, Is.Not.Null);
                // Add specific assertions based on expected data for this combination
            }
        }

        [Test]
        public async Task GetDashboardVehicleCard_WithCustomerSiteAndDate_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                var result = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                Assert.That(result, Is.Not.Null);
                // Add specific assertions based on expected data for this combination
            }
        }

        [Test]
        public async Task GetDashboardVehicleCard_WithCustomerSiteDepartmentAndDate_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                var result = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                Assert.That(result, Is.Not.Null);
                // Add specific assertions based on expected data for this combination
            }
        }

        [Test]
        public async Task GetDashboardVehicleCard_WithHighImpactThresholds_ShouldCalculateRedImpactsCorrectly()
        {
            var testDate = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc); // Use fixed date for predictable results
            
            // Create a session
            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = _vehicleId;
            session.DriverId = _driverId;
            session.StartTime = testDate.AddMinutes(-30);
            session.EndTime = testDate;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            // Create impacts with different thresholds
            var amberImpact = _serviceProvider.GetRequiredService<ImpactDataObject>();
            amberImpact.Id = Guid.NewGuid();
            amberImpact.SessionId = session.Id;
            amberImpact.ImpactDateTime = testDate.AddMinutes(-15);
            amberImpact.ShockValue = 300;
            amberImpact.Threshold = 50; // This will be amber (300 > 50 but < 50 * 10)
            await _dataFacade.ImpactDataProvider.SaveAsync(amberImpact);

            var redImpact = _serviceProvider.GetRequiredService<ImpactDataObject>();
            redImpact.Id = Guid.NewGuid();
            redImpact.SessionId = session.Id;
            redImpact.ImpactDateTime = testDate.AddMinutes(-10);
            redImpact.ShockValue = 1000;
            redImpact.Threshold = 50; // This will count as red (1000 > 50 * 10)
            await _dataFacade.ImpactDataProvider.SaveAsync(redImpact);

            // Get results using our test date
            var result = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, testDate }
            );

            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.GreaterThan(0));
            var card = result.First();
            
            // Should only count the red impact
            Assert.That(card.RedImpactCountToday, Is.EqualTo(1));
        }

        #endregion

        #region GetDriverLicenseExpiry Tests

        [Test]
        public async Task GetDriverLicenseExpiry_WithDifferentFilters_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with Customer only
                var resultCustomer = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                // Test with Customer + Site
                var resultCustomerSite = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                // Test with Customer + Site + Department
                var resultAll = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                // Assert all results
                Assert.Multiple(() =>
                {
                    // Customer only
                    Assert.That(resultCustomer, Is.Not.Null, $"Customer result should not be null for date {testDate}");
                    Assert.That(resultCustomer.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomer.Any())
                    {
                        var expiry = resultCustomer.First();
                        Assert.That(expiry.TimePeriod, Is.Not.Null);
                        Assert.That(expiry.ExpiredLicenseCount, Is.GreaterThanOrEqualTo(0));
                    }

                    // Customer + Site
                    Assert.That(resultCustomerSite, Is.Not.Null, $"Customer+Site result should not be null for date {testDate}");
                    Assert.That(resultCustomerSite.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomerSite.Any())
                    {
                        var expiry = resultCustomerSite.First();
                        Assert.That(expiry.TimePeriod, Is.Not.Null);
                        Assert.That(expiry.ExpiredLicenseCount, Is.GreaterThanOrEqualTo(0));
                    }

                    // All filters
                    Assert.That(resultAll, Is.Not.Null, $"Full filter result should not be null for date {testDate}");
                    Assert.That(resultAll.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultAll.Any())
                    {
                        var expiry = resultAll.First();
                        Assert.That(expiry.TimePeriod, Is.Not.Null);
                        Assert.That(expiry.ExpiredLicenseCount, Is.GreaterThanOrEqualTo(0));
                    }
                });
            }
        }

        #endregion

        #region GetTodaysPreopCheck Tests

        [Test]
        public async Task GetTodaysPreopCheck_WithDifferentFilters_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with Customer only
                var resultCustomer = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                // Test with Customer + Site
                var resultCustomerSite = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                // Test with Customer + Site + Department
                var resultAll = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                // Assert all results
                Assert.Multiple(() =>
                {
                    // Customer only
                    Assert.That(resultCustomer, Is.Not.Null, $"Customer result should not be null for date {testDate}");
                    Assert.That(resultCustomer.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomer.Any())
                    {
                        var check = resultCustomer.First();
                        Assert.That(check.Status, Is.GreaterThanOrEqualTo(0));
                        Assert.That(check.Percentage, Is.GreaterThanOrEqualTo(0));
                    }

                    // Customer + Site
                    Assert.That(resultCustomerSite, Is.Not.Null, $"Customer+Site result should not be null for date {testDate}");
                    Assert.That(resultCustomerSite.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomerSite.Any())
                    {
                        var check = resultCustomerSite.First();
                        Assert.That(check.Status, Is.GreaterThanOrEqualTo(0));
                        Assert.That(check.Percentage, Is.GreaterThanOrEqualTo(0));
                    }

                    // All filters
                    Assert.That(resultAll, Is.Not.Null, $"Full filter result should not be null for date {testDate}");
                    Assert.That(resultAll.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultAll.Any())
                    {
                        var check = resultAll.First();
                        Assert.That(check.Status, Is.GreaterThanOrEqualTo(0));
                        Assert.That(check.Percentage, Is.GreaterThanOrEqualTo(0));
                    }
                });
            }
        }

        [Test]
        public async Task GetTodaysPreopCheck_WithAllFilterCombinations_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test customer + date
                var result1 = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );
                Assert.That(result1, Is.Not.Null);

                // Test customer + site + date
                var result2 = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );
                Assert.That(result2, Is.Not.Null);

                // Test customer + site + department + date
                var result3 = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );
                Assert.That(result3, Is.Not.Null);
            }
        }

        #endregion

        #region GetTodaysImpact Tests

        [Test]
        public async Task GetTodaysImpact_WithDifferentFilters_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with Customer only
                var resultCustomer = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                // Test with Customer + Site
                var resultCustomerSite = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                // Test with Customer + Site + Department
                var resultAll = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                // Assert all results
                Assert.Multiple(() =>
                {
                    // Customer only
                    Assert.That(resultCustomer, Is.Not.Null, $"Customer result should not be null for date {testDate}");
                    Assert.That(resultCustomer.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomer.Any())
                    {
                        var impact = resultCustomer.First();
                        Assert.That(impact.ImpactType, Is.GreaterThanOrEqualTo(0));
                        Assert.That(impact.ImpactLevel, Is.Not.Null);
                        Assert.That(impact.NumberOfImpacts, Is.GreaterThanOrEqualTo(0));
                    }

                    // Customer + Site
                    Assert.That(resultCustomerSite, Is.Not.Null, $"Customer+Site result should not be null for date {testDate}");
                    Assert.That(resultCustomerSite.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomerSite.Any())
                    {
                        var impact = resultCustomerSite.First();
                        Assert.That(impact.ImpactType, Is.GreaterThanOrEqualTo(0));
                        Assert.That(impact.ImpactLevel, Is.Not.Null);
                        Assert.That(impact.NumberOfImpacts, Is.GreaterThanOrEqualTo(0));
                    }

                    // All filters
                    Assert.That(resultAll, Is.Not.Null, $"Full filter result should not be null for date {testDate}");
                    Assert.That(resultAll.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultAll.Any())
                    {
                        var impact = resultAll.First();
                        Assert.That(impact.ImpactType, Is.GreaterThanOrEqualTo(0));
                        Assert.That(impact.ImpactLevel, Is.Not.Null);
                        Assert.That(impact.NumberOfImpacts, Is.GreaterThanOrEqualTo(0));
                    }
                });
            }
        }

        [Test]
        public async Task GetTodaysImpact_WithAllFilterCombinations_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test customer + date
                var result1 = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );
                Assert.That(result1, Is.Not.Null);

                // Test customer + site + date
                var result2 = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );
                Assert.That(result2, Is.Not.Null);

                // Test customer + site + department + date
                var result3 = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );
                Assert.That(result3, Is.Not.Null);
            }
        }

        #endregion

        #region GetVehicleUtilizationLastTwelveHours Tests

        [Test]
        public async Task GetVehicleUtilizationLastTwelveHours_WithDifferentFilters_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test with Customer only
                var resultCustomer = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );

                // Test with Customer + Site
                var resultCustomerSite = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );

                // Test with Customer + Site + Department
                var resultAll = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );

                // Assert all results
                Assert.Multiple(() =>
                {
                    // Customer only
                    Assert.That(resultCustomer, Is.Not.Null, $"Customer result should not be null for date {testDate}");
                    Assert.That(resultCustomer.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomer.Any())
                    {
                        var utilization = resultCustomer.First();
                        Assert.That(utilization.TimePeriod, Is.Not.Null);
                        Assert.That(utilization.NumberOfSessions, Is.GreaterThanOrEqualTo(0));
                    }

                    // Customer + Site
                    Assert.That(resultCustomerSite, Is.Not.Null, $"Customer+Site result should not be null for date {testDate}");
                    Assert.That(resultCustomerSite.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultCustomerSite.Any())
                    {
                        var utilization = resultCustomerSite.First();
                        Assert.That(utilization.TimePeriod, Is.Not.Null);
                        Assert.That(utilization.NumberOfSessions, Is.GreaterThanOrEqualTo(0));
                    }

                    // All filters
                    Assert.That(resultAll, Is.Not.Null, $"Full filter result should not be null for date {testDate}");
                    Assert.That(resultAll.Count, Is.GreaterThanOrEqualTo(0));
                    if (resultAll.Any())
                    {
                        var utilization = resultAll.First();
                        Assert.That(utilization.TimePeriod, Is.Not.Null);
                        Assert.That(utilization.NumberOfSessions, Is.GreaterThanOrEqualTo(0));
                    }
                });
            }
        }

        [Test]
        public async Task GetVehicleUtilization_WithAllFilterCombinations_ShouldReturnCorrectData()
        {
            foreach (var testDate in TestDates)
            {
                // Test customer + date
                var result1 = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, testDate }
                );
                Assert.That(result1, Is.Not.Null);

                // Test customer + site + date
                var result2 = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && ReferenceDate == @2",
                    filterArguments: new object[] { _customerId, _siteId, testDate }
                );
                Assert.That(result2, Is.Not.Null);

                // Test customer + site + department + date
                var result3 = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                    filterArguments: new object[] { _customerId, _siteId, _departmentId, testDate }
                );
                Assert.That(result3, Is.Not.Null);
            }
        }

        #endregion

        #region Timezone Handling Tests

        [Test]
        public async Task GetDashboardDriverCard_WithTimezoneHandling_ShouldAdjustDatesCorrectly()
        {
            // Test with current UTC time
            var utcNow = DateTime.UtcNow;
            var resultUtc = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, utcNow }
            );

            // Test with timezone-adjusted time (UTC + 10 hours for AEST)
            var timezoneAdjustedTime = utcNow.AddHours(10);
            var resultAdjusted = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, timezoneAdjustedTime }
            );

            Assert.Multiple(() =>
            {
                Assert.That(resultUtc, Is.Not.Null);
                Assert.That(resultAdjusted, Is.Not.Null);
                
                // Both should return the same data since the stored procedure now handles timezone internally
                if (resultUtc.Any() && resultAdjusted.Any())
                {
                    var utcCard = resultUtc.First();
                    var adjustedCard = resultAdjusted.First();
                    
                    // Driver counts should be the same
                    Assert.That(utcCard.DriversCount, Is.EqualTo(adjustedCard.DriversCount));
                    Assert.That(utcCard.ExpiredLicensesCount, Is.EqualTo(adjustedCard.ExpiredLicensesCount));
                }
            });
        }

        [Test]
        public async Task GetTodaysImpact_WithTimezoneHandling_ShouldAdjustImpactDatesCorrectly()
        {
            // Test with UTC reference time
            var utcReference = DateTime.UtcNow;
            var resultUtc = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, utcReference }
            );

            // Test with timezone-adjusted reference time
            var timezoneAdjustedReference = utcReference.AddHours(10); // AEST timezone
            var resultAdjusted = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, timezoneAdjustedReference }
            );

            Assert.Multiple(() =>
            {
                Assert.That(resultUtc, Is.Not.Null);
                Assert.That(resultAdjusted, Is.Not.Null);
                
                // Both should return the same data since timezone is handled internally
                if (resultUtc.Any() && resultAdjusted.Any())
                {
                    var utcRedImpacts = resultUtc.Where(r => r.ImpactLevel == "Red").Sum(r => (int)r.NumberOfImpacts);
                    var adjustedRedImpacts = resultAdjusted.Where(r => r.ImpactLevel == "Red").Sum(r => (int)r.NumberOfImpacts);
                    
                    Assert.That(utcRedImpacts, Is.EqualTo(adjustedRedImpacts), 
                        "Red impact counts should be the same regardless of reference timezone");
                }
            });
        }

        #endregion

        #region Edge Case Tests

        [Test]
        public async Task AllStoredProcedures_WithNullFilters_ShouldReturnData()
        {
            // Test all stored procedures with null filters
            var date = DateTime.UtcNow;
            
            var driverCard = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "ReferenceDate == @0",
                filterArguments: new object[] { date }
            );

            var vehicleCard = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "ReferenceDate == @0",
                filterArguments: new object[] { date }
            );

            var licenseExpiry = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                filterPredicate: "ReferenceDate == @0",
                filterArguments: new object[] { date }
            );

            var preopCheck = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                filterPredicate: "ReferenceDate == @0",
                filterArguments: new object[] { date }
            );

            var impact = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                filterPredicate: "ReferenceDate == @0",
                filterArguments: new object[] { date }
            );

            var utilization = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                filterPredicate: "ReferenceDate == @0",
                filterArguments: new object[] { date }
            );

            Assert.Multiple(() =>
            {
                Assert.That(driverCard, Is.Not.Null);
                Assert.That(vehicleCard, Is.Not.Null);
                Assert.That(licenseExpiry, Is.Not.Null);
                Assert.That(preopCheck, Is.Not.Null);
                Assert.That(impact, Is.Not.Null);
                Assert.That(utilization, Is.Not.Null);
            });
        }

        [Test]
        public async Task AllStoredProcedures_WithInvalidCustomerId_ShouldReturnEmptyResults()
        {
            var invalidCustomerId = Guid.NewGuid();
            var date = DateTime.UtcNow;

            var driverCard = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { invalidCustomerId, date }
            );

            var vehicleCard = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { invalidCustomerId, date }
            );

            Assert.Multiple(() =>
            {
                Assert.That(driverCard, Is.Empty);
                Assert.That(vehicleCard, Is.Empty);
            });
        }

        #endregion

        #region Data Consistency Tests

        [Test]
        public async Task DashboardDriverCard_DataConsistencyAcrossTimeRanges_ShouldBeValid()
        {
            var now = DateTime.UtcNow;
            var yesterday = now.AddDays(-1);
            var lastWeek = now.AddDays(-7);

            var resultNow = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, now }
            );

            var resultYesterday = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, yesterday }
            );

            var resultLastWeek = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, lastWeek }
            );

            Assert.Multiple(() =>
            {
                // Total drivers should remain relatively consistent
                if (resultNow.Any() && resultYesterday.Any() && resultLastWeek.Any())
                {
                    var driversNow = resultNow.First().DriversCount;
                    var driversYesterday = resultYesterday.First().DriversCount;
                    var driversLastWeek = resultLastWeek.First().DriversCount;

                    // Driver counts shouldn't vary drastically
                    Assert.That(Math.Abs((decimal)(driversNow - driversYesterday)), Is.LessThanOrEqualTo((decimal)(driversNow * 0.1)), 
                        "Driver count shouldn't change more than 10% in a day");
                    Assert.That(Math.Abs((decimal)(driversNow - driversLastWeek)), Is.LessThanOrEqualTo((decimal)(driversNow * 0.2)), 
                        "Driver count shouldn't change more than 20% in a week");
                }
            });
        }

        [Test]
        public async Task VehicleUtilization_ConsecutiveHours_ShouldBeConsistent()
        {
            var result = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0",
                filterArguments: new object[] { _customerId }
            );

            Assert.Multiple(() =>
            {
                // We should have exactly 12 hours of data
                Assert.That(result.Count, Is.EqualTo(12), "Should have data for all 12 hours");

                if (result.Count > 1)
                {
                    // Sort by hour for consistent checking
                    var orderedResults = result.OrderBy(r => int.Parse(r.TimePeriod.Split(':')[0])).ToList();

                    // Log what we got for debugging
                    Console.WriteLine("Hours returned:");
                    foreach (var r in orderedResults)
                    {
                        Console.WriteLine($"Hour: {r.TimePeriod}, Sessions: {r.NumberOfSessions}");
                    }

                    // Verify each hour has a sensible number of sessions
                    foreach (var r in orderedResults)
                    {
                        Assert.That(r.NumberOfSessions, Is.GreaterThanOrEqualTo(0), 
                            $"Hour {r.TimePeriod} should have zero or more sessions");
                    }
                }
            });
        }

        [Test]
        public async Task TodaysImpact_SeverityLevels_ShouldBeConsistent()
        {
            var now = DateTime.UtcNow;
            
            var result = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, now }
            );

            Assert.Multiple(() =>
            {
                if (result.Any())
                {
                    var amberImpacts = result.Where(r => r.ImpactLevel == "Amber").Sum(r => (int)r.NumberOfImpacts);
                    var redImpacts = result.Where(r => r.ImpactLevel == "Red").Sum(r => (int)r.NumberOfImpacts);

                    // Typically there should be more amber impacts than red
                    Assert.That(amberImpacts, Is.GreaterThanOrEqualTo(redImpacts), 
                        "There should typically be more amber impacts than red impacts");

                    // Red impacts shouldn't be excessive
                    Assert.That(redImpacts, Is.LessThanOrEqualTo(amberImpacts * 2), 
                        "Red impacts shouldn't be more than double the amber impacts");
                }
            });
        }

        [Test]
        public async Task PreopCheck_StatusDistribution_ShouldBeValid()
        {
            var now = DateTime.UtcNow;
            
            var result = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, now }
            );

            Assert.Multiple(() =>
            {
                if (result.Any())
                {
                    var totalPercentage = result.Sum(r => r.Percentage);
                    Assert.That(Math.Abs(totalPercentage - 100m), Is.LessThanOrEqualTo(0.01m), 
                        "Total percentage across all statuses should be approximately 100%");

                    // Check that critical checks (Status 2) percentage is reasonable
                    var criticalPercentage = result
                        .Where(r => r.Status == DashboardChecklistViewStatusEnum.CriticalFailed)
                        .Sum(r => r.Percentage);
                    Assert.That(criticalPercentage, Is.LessThanOrEqualTo(20m), 
                        "Critical checks should not exceed 20% of total checks");
                }
            });
        }

        [Test]
        public async Task DriverLicenseExpiry_TimePeriodDistribution_ShouldBeValid()
        {
            var now = DateTime.UtcNow;
            
            var result = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                filterArguments: new object[] { _customerId, now }
            );

            Assert.Multiple(() =>
            {
                if (result.Any())
                {
                    var overdue = result.Where(r => r.TimePeriod == "Overdue").Sum(r => r.ExpiredLicenseCount);
                    var lessThanOneMonth = result.Where(r => r.TimePeriod == "< 1 month").Sum(r => r.ExpiredLicenseCount);
                    var lessThanThreeMonths = result.Where(r => r.TimePeriod == "< 3 months").Sum(r => r.ExpiredLicenseCount);

                    // Overdue licenses should be less than upcoming expirations
                    Assert.That(overdue, Is.LessThanOrEqualTo(lessThanOneMonth + lessThanThreeMonths), 
                        "Overdue licenses should not exceed upcoming expirations");

                    // Three month window should have more than one month window
                    Assert.That(lessThanThreeMonths, Is.GreaterThanOrEqualTo(lessThanOneMonth), 
                        "3-month window should have more expiring licenses than 1-month window");
                }
            });
        }

        #endregion

        [Test]
        public async Task GetDashboardDriverCard_ShouldReturnCorrectData()
        {
            // Arrange
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId },
                { "ReferenceDate", _referenceDate }
            };

            // Act
            var result = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                filterArguments: new object[] { _customerId, _siteId, _departmentId, _referenceDate }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.GreaterThan(0));
            var card = result.First();
            Assert.Multiple(() =>
            {
                Assert.That(card.DriversCount, Is.GreaterThan(0));
                Assert.That(card.NonActiveDriversCountLastWeek, Is.GreaterThanOrEqualTo(0));
                Assert.That(card.NoLicenseDriversCount, Is.GreaterThanOrEqualTo(0));
                Assert.That(card.ExpiredLicensesCount, Is.GreaterThanOrEqualTo(0));
            });
        }

        [Test]
        public async Task GetDashboardVehicleCard_ShouldReturnCorrectData()
        {
            // Arrange
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId },
                { "ReferenceDate", _referenceDate }
            };

            // Act
            var result = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                filterArguments: new object[] { _customerId, _siteId, _departmentId, _referenceDate }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.GreaterThan(0));
            var card = result.First();
            Assert.Multiple(() =>
            {
                Assert.That(card.VehiclesCount, Is.GreaterThan(0));
                Assert.That(card.SessionCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                Assert.That(card.InactiveVehicleCountLastTwentyFourHours, Is.GreaterThanOrEqualTo(0));
                Assert.That(card.InactiveVehicleCountLastSeventyTwoHours, Is.GreaterThanOrEqualTo(0));
            });
        }

        [Test]
        public async Task GetDriverLicenseExpiry_ShouldReturnCorrectData()
        {
            // Arrange
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId }
            };

            // Act
            var result = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2",
                filterArguments: new object[] { _customerId, _siteId, _departmentId }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            // Add more specific assertions based on the expected data structure
        }

        [Test]
        public async Task GetTodaysPreopCheck_ShouldReturnCorrectData()
        {
            // Arrange
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId },
                { "ReferenceDate", _referenceDate }
            };

            // Act
            var result = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                filterArguments: new object[] { _customerId, _siteId, _departmentId, _referenceDate }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.GreaterThan(0));
            var check = result.First();
            Assert.Multiple(() =>
            {
                Assert.That(check.Status, Is.Not.Null);
                Assert.That(check.Percentage, Is.GreaterThanOrEqualTo(0));
            });
        }

        [Test]
        public async Task GetTodaysImpact_ShouldReturnCorrectData()
        {
            // Arrange
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId },
                { "ReferenceDate", _referenceDate }
            };

            // Act
            var result = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                filterArguments: new object[] { _customerId, _siteId, _departmentId, _referenceDate }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.GreaterThan(0));
            var impact = result.First();
            Assert.Multiple(() =>
            {
                Assert.That(impact.ImpactType, Is.Not.Null);
                Assert.That(impact.ImpactLevel, Is.Not.Null);
                Assert.That(impact.NumberOfImpacts, Is.GreaterThanOrEqualTo(0));
            });
        }

        [Test]
        public async Task GetVehicleUtilizationLastTwelveHours_ShouldReturnCorrectData()
        {
            // Arrange
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId },
                { "ReferenceDate", _referenceDate }
            };

            // Act
            var result = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                filterArguments: new object[] { _customerId, _siteId, _departmentId, _referenceDate }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.GreaterThan(0));
            var utilization = result.First();
            Assert.Multiple(() =>
            {
                Assert.That(utilization.TimePeriod, Is.Not.Null);
                Assert.That(utilization.NumberOfSessions, Is.GreaterThanOrEqualTo(0));
            });
        }

        [Test]
        public async Task GetVehicleUtilizationLastTwelveHours_ShouldRespectReferenceDate()
        {
            // Arrange
            var pastDate = _referenceDate.AddDays(-1);
            var parameters = new Dictionary<string, object>
            {
                { "CustomerId", _customerId },
                { "SiteId", _siteId },
                { "DepartmentId", _departmentId },
                { "ReferenceDate", pastDate }
            };

            // Act
            var result = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                filterPredicate: "CustomerId == @0 && SiteId == @1 && DepartmentId == @2 && ReferenceDate == @3",
                filterArguments: new object[] { _customerId, _siteId, _departmentId, pastDate }
            );

            // Assert
            Assert.That(result, Is.Not.Null);
            // Verify that we get different results when using a past reference date
            // The exact assertions will depend on your test data setup
        }

        [Test]
        public async Task AllStoredProcedures_WithTimezoneHandling_ShouldReturnConsistentResults()
        {
            // Test all stored procedures with different timezone offsets
            var testOffsets = new[] { -12, -8, -5, 0, 5, 8, 12 }; // Various timezone offsets
            var referenceTime = DateTime.UtcNow;

            foreach (var offset in testOffsets)
            {
                var adjustedTime = referenceTime.AddHours(offset);
                
                // Test all stored procedures with the adjusted time
                var driverCard = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedTime }
                );

                var vehicleCard = await _dataFacade.DashboardVehicleCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedTime }
                );

                var licenseExpiry = await _dataFacade.DriverLicenseExpiryViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedTime }
                );

                var preopCheck = await _dataFacade.TodaysPreopCheckViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedTime }
                );

                var impact = await _dataFacade.TodaysImpactViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedTime }
                );

                var utilization = await _dataFacade.VehicleUtilizationLastTwelveHoursViewDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedTime }
                );

                Assert.Multiple(() =>
                {
                    Assert.That(driverCard, Is.Not.Null, $"Driver card should not be null for offset {offset}");
                    Assert.That(vehicleCard, Is.Not.Null, $"Vehicle card should not be null for offset {offset}");
                    Assert.That(licenseExpiry, Is.Not.Null, $"License expiry should not be null for offset {offset}");
                    Assert.That(preopCheck, Is.Not.Null, $"Preop check should not be null for offset {offset}");
                    Assert.That(impact, Is.Not.Null, $"Impact should not be null for offset {offset}");
                    Assert.That(utilization, Is.Not.Null, $"Utilization should not be null for offset {offset}");
                });
            }
        }

        [Test]
        public async Task StoredProcedures_WithSameDateDifferentTimezones_ShouldReturnSameData()
        {
            // Test that the same date in different timezones returns the same data
            var baseDate = DateTime.UtcNow.Date; // Start of day UTC
            var timezoneOffsets = new[] { -12, -8, -5, 0, 5, 8, 12 };
            
            var results = new List<object>();
            
            foreach (var offset in timezoneOffsets)
            {
                var adjustedDate = baseDate.AddHours(offset);
                
                var driverCard = await _dataFacade.DashboardDriverCardStoreProcedureDataProvider.GetCollectionAsync(
                    filterPredicate: "CustomerId == @0 && ReferenceDate == @1",
                    filterArguments: new object[] { _customerId, adjustedDate }
                );
                
                results.Add(driverCard);
            }
            
            // All results should be the same since they represent the same day
            Assert.That(results.All(r => r != null), "All results should not be null");
            
            // Verify that the first result is consistent with others
            var firstResult = results.First() as DataObjectCollection<DashboardDriverCardStoreProcedureDataObject>;
            if (firstResult != null && firstResult.Any())
            {
                var firstDriverCount = firstResult.First().DriversCount;
                
                foreach (var result in results.Skip(1))
                {
                    var resultCollection = result as DataObjectCollection<DashboardDriverCardStoreProcedureDataObject>;
                    if (resultCollection != null && resultCollection.Any())
                    {
                        Assert.That(resultCollection.First().DriversCount, Is.EqualTo(firstDriverCount),
                            "Driver counts should be the same for the same date across different timezones");
                    }
                }
            }
        }
    }
}
