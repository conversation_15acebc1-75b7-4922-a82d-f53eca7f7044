﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Data.DataObjects.Custom;
using Newtonsoft.Json;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Azure.Devices;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// RegisterModuleAPI Component
	///  
	/// </summary>
    public partial class RegisterModuleAPI : BaseServerComponent, IRegisterModuleAPI
    {
        private readonly IModuleValidator _moduleValidator;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IIoTHubManager _ioTHubManager;
        private readonly ILoggingService _logger;
        public RegisterModuleAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IModuleValidator moduleValidator, IDeviceMessageHandler deviceMessageHandler, IIoTHubManager ioTHubManager, ILoggingService logger) : base(serviceProvider, configuration, dataFacade)
        {
            _moduleValidator = moduleValidator;
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;
            _ioTHubManager = ioTHubManager;
        }

        /// <summary>
        /// RegisterModule Method
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> RegisterModuleAsync(String Message, Dictionary<string, object> parameters = null)
        {
            PayloadDataObject payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            if (payloadObject == null)
            {
                _logger.LogError(new GOServerException("Invalid message"));
                throw new Exception("Invalid message");
            }
            if (payloadObject.IoTDeviceId == null)
            {
                _logger.LogError(new GOServerException("Invalid IoTDeviceId"));
                throw new Exception("Invalid IoTDeviceId");
            }

            // var module = await _moduleValidator.EnforceModuleForVehicleAsync(payloadObject.IoTDeviceId);
            // if(module == null)
            // {
            //     throw new Exception("Failed to save IoTDevice");
            // }

            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { payloadObject.IoTDeviceId })).SingleOrDefault();
            if (module == null)
            {
                _logger.LogError(new GOServerException("Module not found"));
                module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                module.Id = Guid.NewGuid();
                module.IoTDevice = payloadObject.IoTDeviceId;
                module.BlueImpact = 0;
                module.AmberImpact = 0;
                module.RedImpact = 0;
                module.FSSSBase = 0;
                module.FSSXMulti = 0;
                module = await _dataFacade.ModuleDataProvider.SaveAsync(module);
            }

            // check again if modules was saved
            module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { payloadObject.IoTDeviceId })).SingleOrDefault();
            if (module == null)
            {
                _logger.LogError(new GOServerException("Failed to save module"));
                throw new Exception("Failed to save module");
            }

            var vehicle = await module.LoadVehicleAsync();
            if (vehicle == null)
            {
                _logger.LogError(new GOServerException("Module is not assigned to a vehicle"));
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success, but module is not assigned to a vehicle")));
            }
            // // sync drivers to vehicle
            // await _ioTHubManager.SyncDriversToVehicleAsync(payloadObject.IoTDeviceId);
            //sync vehicle to IoT Hub
            await _ioTHubManager.SyncVehicleSettingsAsync(payloadObject.IoTDeviceId);
            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }
    }
}
