import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

describe('BroadcastMessageReportPageController', () => {
    let controller;
    let customController;

    beforeEach(() => {
        // Mock FleetXQ global object
        global.FleetXQ = {
            Web: {
                Controllers: {
                    BroadcastMessageReportPageControllerCustom: function(ctrl) {
                        this.controller = ctrl;

                        this.getDefaultConfiguration = function() {
                            var configuration = {};
                            configuration.filterPredicate = "Driver.Person.CustomerId == @0";
                            configuration.filterParameters = JSON.stringify([{
                                "TypeName": "System.Guid",
                                "IsNullable": false,
                                "Value": this.controller.applicationController.viewModel.security.currentUserClaims().CustomerId
                            }]);
                            return configuration;
                        };

                        this.getConfiguration = function() {
                            var configuration = {};
                            configuration.filterPredicate = "";
                            configuration.filterParameters = [];

                            var filter = this.controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
                            var parameterCount = 0;

                            var customerId = this.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
                            if (filter.CustomerId() == null && customerId != null) {
                                return this.getDefaultConfiguration();
                            }

                            if (filter.CustomerId()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "Driver.Person.CustomerId == @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.Guid",
                                    "IsNullable": false,
                                    "Value": filter.CustomerId()
                                });
                                parameterCount++;
                            }

                            if (filter.SiteId()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "Driver.Person.SiteId == @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.Guid",
                                    "IsNullable": false,
                                    "Value": filter.SiteId()
                                });
                                parameterCount++;
                            }

                            if (filter.DepartmentId()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "Driver.Person.DepartmentId == @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.Guid",
                                    "IsNullable": false,
                                    "Value": filter.DepartmentId()
                                });
                                parameterCount++;
                            }

                            if (filter.MultiSearch()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "(Driver.Person.FirstName.Contains(@" + parameterCount + ") || Driver.Person.LastName.Contains(@" + (parameterCount + 1) + "))";
                                configuration.filterParameters.push({
                                    "TypeName": "System.String",
                                    "IsNullable": true,
                                    "Value": filter.MultiSearch()
                                });
                                configuration.filterParameters.push({
                                    "TypeName": "System.String",
                                    "IsNullable": true,
                                    "Value": filter.MultiSearch()
                                });
                                parameterCount += 2;
                            }

                            if (filter.StartTime()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "SentTime >= @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.DateTime",
                                    "IsNullable": false,
                                    "Value": filter.StartTime()
                                });
                                parameterCount++;
                            }

                            if (filter.EndTime()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "SentTime <= @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.DateTime",
                                    "IsNullable": false,
                                    "Value": filter.EndTime()
                                });
                                parameterCount++;
                            }

                            if (filter.Type() !== null) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "Type = @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.Int32",
                                    "IsNullable": false,
                                    "Value": filter.Type()
                                });
                                parameterCount++;
                            }

                            if (configuration.filterParameters) {
                                configuration.filterParameters = JSON.stringify(configuration.filterParameters);
                            }

                            return configuration;
                        };

                        this.initialize = function() {
                            this.controller.BroadcastMessageHistoryFilterFormViewModel.filterData = function() {
                                try {
                                    var configuration = this.getConfiguration();
                                    this.controller.BroadcastMessageHistoryGridViewModel.LoadBroadcastMessageHistoryObjectCollection(configuration);
                                } catch (e) {
                                    console.error("Error filtering data:", e);
                                }
                            }.bind(this);

                            var customerId = this.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
                            if (customerId != null) {
                                var configuration = this.getDefaultConfiguration();
                                this.controller.BroadcastMessageHistoryGridViewModel.LoadBroadcastMessageHistoryObjectCollection(configuration);
                            }
                        };
                    }
                }
            }
        };

        // Mock the base controller
        controller = {
            applicationController: {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn().mockReturnValue({
                            CustomerId: 'test-customer-id'
                        })
                    }
                }
            },
            BroadcastMessageHistoryFilterFormViewModel: {
                BroadcastMessageHistoryFilterObject: vi.fn().mockReturnValue({
                    Data: {
                        CustomerId: vi.fn(),
                        SiteId: vi.fn(),
                        DepartmentId: vi.fn(),
                        MultiSearch: vi.fn(),
                        StartTime: vi.fn(),
                        EndTime: vi.fn(),
                        Type: vi.fn()
                    }
                })
            },
            BroadcastMessageHistoryGridViewModel: {
                LoadBroadcastMessageHistoryObjectCollection: vi.fn()
            }
        };

        // Create instance of custom controller
        customController = new FleetXQ.Web.Controllers.BroadcastMessageReportPageControllerCustom(controller);
    });

    afterEach(() => {
        vi.clearAllMocks();
        delete global.FleetXQ;
    });

    describe('getDefaultConfiguration', () => {
        it('should return configuration with customer filter', () => {
            const config = customController.getDefaultConfiguration();
            
            expect(config.filterPredicate).toBe('Driver.Person.CustomerId == @0');
            expect(config.filterParameters).toBe(JSON.stringify([{
                "TypeName": "System.Guid",
                "IsNullable": false,
                "Value": "test-customer-id"
            }]));
        });
    });

    describe('getConfiguration', () => {
        beforeEach(() => {
            // Reset all filter values to null by default
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue(null);
            filter.SiteId.mockReturnValue(null);
            filter.DepartmentId.mockReturnValue(null);
            filter.MultiSearch.mockReturnValue(null);
            filter.StartTime.mockReturnValue(null);
            filter.EndTime.mockReturnValue(null);
            filter.Type.mockReturnValue(null);
        });

        it('should return default configuration for customer user with no filter', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue(null);
            
            const config = customController.getConfiguration();
            expect(config.filterPredicate).toBe('Driver.Person.CustomerId == @0');
        });

        it('should build filter predicate with customer ID', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('specific-customer-id');
            
            const config = customController.getConfiguration();
            expect(config.filterPredicate).toBe('Driver.Person.CustomerId == @0');
            expect(JSON.parse(config.filterParameters)[0].Value).toBe('specific-customer-id');
        });

        it('should build filter predicate with site ID', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.SiteId.mockReturnValue('site-id');
            
            const config = customController.getConfiguration();
            const expectedPredicate = 'Driver.Person.CustomerId == @0 && Driver.Person.SiteId == @1';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe('site-id');
        });

        it('should build filter predicate with department ID', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.DepartmentId.mockReturnValue('dept-id');
            
            const config = customController.getConfiguration();
            const expectedPredicate = 'Driver.Person.CustomerId == @0 && Driver.Person.DepartmentId == @1';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe('dept-id');
        });

        it('should build filter predicate with multi-search', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.MultiSearch.mockReturnValue('John');
            
            const config = customController.getConfiguration();
            const expectedPredicate = 'Driver.Person.CustomerId == @0 && (Driver.Person.FirstName.Contains(@1) || Driver.Person.LastName.Contains(@2))';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe('John');
            expect(params[2].Value).toBe('John');
        });

        it('should build filter predicate with time range', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.StartTime.mockReturnValue(new Date('2024-01-01'));
            filter.EndTime.mockReturnValue(new Date('2024-01-02'));
            
            const config = customController.getConfiguration();
            const expectedPredicate = 'Driver.Person.CustomerId == @0 && SentTime >= @1 && SentTime <= @2';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toMatch(/2024-01-01/);
            expect(params[2].Value).toMatch(/2024-01-02/);
        });

        it('should build filter predicate with message type', () => {
            const filter = controller.BroadcastMessageHistoryFilterFormViewModel.BroadcastMessageHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.Type.mockReturnValue(1);
            
            const config = customController.getConfiguration();
            const expectedPredicate = 'Driver.Person.CustomerId == @0 && Type = @1';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe(1);
        });
    });

    describe('initialize', () => {
        it('should add filterData function to filter form view model', () => {
            customController.initialize();
            expect(typeof controller.BroadcastMessageHistoryFilterFormViewModel.filterData).toBe('function');
        });

        it('should load initial data for customer users', () => {
            customController.initialize();
            expect(controller.BroadcastMessageHistoryGridViewModel.LoadBroadcastMessageHistoryObjectCollection)
                .toHaveBeenCalledWith(expect.objectContaining({
                    filterPredicate: 'Driver.Person.CustomerId == @0'
                }));
        });
    });
}); 