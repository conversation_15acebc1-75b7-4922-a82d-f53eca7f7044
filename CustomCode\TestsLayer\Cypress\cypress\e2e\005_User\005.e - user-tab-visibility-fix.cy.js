describe('User Tab Visibility Fix', () => {

    const userId = 'cabc8e16-2c1f-4134-b9c6-32551d9f0785'

    beforeEach(() => {
        // Login first
        cy.login()
      
        // After login, visit the person page
        cy.visit(`/#!/UserManagement/UserDetail/${userId}`)
        
        // Wait for page to load
        cy.wait(2000)
    })

    
    it('should verify only the correct tabs are visible', () => {
        // First check the tabs in view mode
        cy.get('[data-id="PersonFormControl-PersonForm-tabs-1"]').should('be.visible')
        
        // Click Modify button to enter edit mode using the correct selector and force option
        cy.get('[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]')
          .click({ force: true })
        cy.wait(1000) // Wait for edit mode to activate
        
        // Test 1: Set Supervisor to No using data-test-id instead of ID
        cy.get('input[name="Supervisor-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the tab is hidden using jQuery :visible selector
        cy.get('body').then($body => {
            const supervisorTabVisible = $body.find('[data-test-id="tab_link_736f47d4-4967-49b0-b674-8fd1b91e32c3"]:visible').length > 0;
            expect(supervisorTabVisible).to.be.false;
        });
        
        // Test 2: Set Supervisor to Yes using data-test-id instead of ID
        cy.get('input[name="Supervisor-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the tab is visible using jQuery :visible selector
        cy.get('body').then($body => {
            const supervisorTabVisible = $body.find('[data-test-id="tab_link_736f47d4-4967-49b0-b674-8fd1b91e32c3"]:visible').length > 0;
            expect(supervisorTabVisible).to.be.true;
        });
        
        // Test 3: Set Website Access to No using data-test-id instead of ID
        cy.get('input[name="WebSiteAccess-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the tab is hidden using jQuery :visible selector
        cy.get('body').then($body => {
            const websiteTabVisible = $body.find('[data-test-id="tab_link_a263931f-dbde-4b47-b166-a587b9663b26"]:visible').length > 0;
            expect(websiteTabVisible).to.be.false;
        });
        
        // Test 4: Set Website Access to Yes using data-test-id instead of ID
        cy.get('input[name="WebSiteAccess-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the tab is visible using jQuery :visible selector
        cy.get('body').then($body => {
            const websiteTabVisible = $body.find('[data-test-id="tab_link_a263931f-dbde-4b47-b166-a587b9663b26"]:visible').length > 0;
            expect(websiteTabVisible).to.be.true;
        });
        
        // Test 5: Test Vehicle tab visibility - first set IsDriver to No
        cy.get('input[name="IsDriver-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the Vehicle tab is hidden
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.false;
        });
        
        // Test 6: Set IsDriver to Yes but IsActiveDriver to No
        cy.get('input[name="IsDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
            
        cy.get('input[name="IsActiveDriver-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the Vehicle tab is still hidden (should be because IsActiveDriver is No)
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.false;
        });
        
        // Test 7: Set both IsDriver and IsActiveDriver to Yes
        cy.get('input[name="IsDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
            
        cy.get('input[name="IsActiveDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
        
        // Wait for UI to update
        cy.wait(500)
        
        // Check if the Vehicle tab is now visible
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.true;
        });
        
        // Cancel the edit to avoid saving changes
        cy.get('button:contains("Cancel")').click({ force: true })
    })
    
    it('should verify tab visibility persists after save', () => {
        // Click Modify button to enter edit mode using the correct selector and force option
        cy.get('[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]')
          .click({ force: true })
        cy.wait(1000)
        
        // Set all options to Yes
        cy.get('input[name="Supervisor-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
        
        cy.get('input[name="WebSiteAccess-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
            
        cy.get('input[name="IsDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
            
        cy.get('input[name="IsActiveDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
            
        // Save the changes
        cy.get('[data-test-id="bdf5d451-849f-4118-8b6f-55a4dde083ea"]').click({ force: true })
        cy.wait(2000) // Wait for save to complete
        
        // Verify all tabs remain visible after save
        cy.get('[data-test-id="tab_link_736f47d4-4967-49b0-b674-8fd1b91e32c3"]').should('be.visible') // Supervisor tab
        cy.get('[data-test-id="tab_link_a263931f-dbde-4b47-b166-a587b9663b26"]').should('be.visible') // Website tab
        cy.get('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]').should('be.visible') // Vehicle tab
        
        // Click Modify button again to enter edit mode
        cy.get('[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]')
          .click({ force: true })
        cy.wait(1000)
        
        // Set all options to No
        cy.get('input[name="Supervisor-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
            
        cy.get('input[name="WebSiteAccess-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
            
        cy.get('input[name="IsDriver-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
            
        // Save the changes
        cy.get('[data-test-id="bdf5d451-849f-4118-8b6f-55a4dde083ea"]').click({ force: true })
        cy.wait(2000)
        
        // Verify all tabs are hidden after save
        cy.get('body').then($body => {
            const supervisorTabVisible = $body.find('[data-test-id="tab_link_736f47d4-4967-49b0-b674-8fd1b91e32c3"]:visible').length > 0;
            expect(supervisorTabVisible).to.be.false;
        });
        
        cy.get('body').then($body => {
            const websiteTabVisible = $body.find('[data-test-id="tab_link_a263931f-dbde-4b47-b166-a587b9663b26"]:visible').length > 0;
            expect(websiteTabVisible).to.be.false;
        });
        
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.false;
        });
    })
    
    it('should verify IsActiveDriver affects Vehicle tab visibility', () => {
        // Click Modify button to enter edit mode
        cy.get('[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]')
          .click({ force: true })
        cy.wait(1000)
        
        // First ensure IsDriver is Yes
        cy.get('input[name="IsDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
        
        // Test with IsActiveDriver = Yes
        cy.get('input[name="IsActiveDriver-edit-yesno"][value="true"]')
            .click({ force: true })
            .should('be.checked')
            
        // Wait for UI to update
        cy.wait(500)
        
        // Vehicle tab should be visible
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.true;
        });
        
        // Now set IsActiveDriver to No
        cy.get('input[name="IsActiveDriver-edit-yesno"][value="false"]')
            .click({ force: true })
            .should('be.checked')
            
        // Wait for UI to update
        cy.wait(500)
        
        // Vehicle tab should be hidden
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.false;
        });
        
        // Save the changes to verify persistence
        cy.get('[data-test-id="bdf5d451-849f-4118-8b6f-55a4dde083ea"]').click({ force: true })
        cy.wait(2000)
        
        // Vehicle tab should still be hidden after save
        cy.get('body').then($body => {
            const vehicleTabVisible = $body.find('[data-test-id="tab_link_edde3b5d-054c-4de4-bcc8-99f39f3c13ee"]:visible').length > 0;
            expect(vehicleTabVisible).to.be.false;
        });
    })
})