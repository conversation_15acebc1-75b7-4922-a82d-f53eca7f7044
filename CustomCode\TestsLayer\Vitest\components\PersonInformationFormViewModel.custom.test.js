import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('PersonInformationFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Person/PersonInformationFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ko.postbox
        ko.postbox = {
            subscribe: vi.fn()
        };

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        CustomerId: '123',
                        SiteId: '456',
                        DepartmentId: '789',
                        role: ['Administrator']
                    })
                }
            }
        };

        // Create base view model with required properties
        viewModel = {
            PersonObject: ko.observable({
                Data: {
                    IsNew: ko.observable(true),
                    CustomerId: ko.observable(),
                    SiteId: ko.observable(),
                    DepartmentId: ko.observable()
                },
                StatusData: {
                    isCustomerValid: ko.observable(true),
                    isSiteValid: ko.observable(true),
                    isDepartmentValid: ko.observable(true)
                }
            }),
            StatusData: {
                IsUIDirty: ko.observable(false),
                isValid: ko.observable(true),
                errorSummary: ko.observableArray([])
            },
            controller: {
                ObjectsDataSet: {
                    isContextIdDirty: vi.fn()
                }
            },
            contextId: 'test-context',
            rebindLookups: vi.fn(),
            subscriptions: [],
            CustomerContextId: 'customer-context',
            SiteContextId: 'site-context',
            DepartmentContextId: 'department-context',
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            DataStoreSite: {
                LoadObject: vi.fn()
            },
            DataStoreDepartment: {
                LoadObject: vi.fn()
            },
            Customer_CompanyName: ko.observable(),
            Customer_lookupItem: ko.observable(),
            Site_Name: ko.observable(),
            Site_lookupItem: ko.observable(),
            Department_Name: ko.observable(),
            Department_lookupItem: ko.observable(),
            selectiveLoadDataForSite: vi.fn(),
            selectiveLoadDataForDepartment: vi.fn(),
            onPersonObjectChanged: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.PersonInformationFormViewModelCustom(viewModel);
        customViewModel.initialize();

        // Setup fake timers
        vi.useFakeTimers();

        // Clear sessionStorage data
        sessionStorageData = {};
    });

    afterEach(() => {
        vi.useRealTimers();
        sessionStorageData = {};
    });

    it('should call loadDefaultCustomer when onPersonObjectChanged is triggered for new person', () => {
        // Setup spy on loadDefaultCustomer
        const loadDefaultCustomerSpy = vi.spyOn(customViewModel, 'loadDefaultCustomer');

        // Setup filter values in sessionStorage
        const filterData = {
            customerId: '456',
            siteId: '789',
            departmentId: '012'
        };
        sessionStorage.setItem('personFilterValues', JSON.stringify(filterData));

        // Trigger onPersonObjectChanged
        viewModel.onPersonObjectChanged();

        // Use vi.advanceTimersByTime to handle the setTimeout
        vi.advanceTimersByTime(500);

        // Assert loadDefaultCustomer was called with correct parameters
        expect(loadDefaultCustomerSpy).toHaveBeenCalledWith('456', expect.any(Function));
    });

    it('should not call loadDefaultCustomer when person is not new', () => {
        // Setup person as not new
        viewModel.PersonObject().Data.IsNew(false);

        // Setup spy on loadDefaultCustomer
        const loadDefaultCustomerSpy = vi.spyOn(customViewModel, 'loadDefaultCustomer');

        // Trigger onPersonObjectChanged
        viewModel.onPersonObjectChanged();

        // Use vi.advanceTimersByTime to handle the setTimeout
        vi.advanceTimersByTime(500);

        // Assert loadDefaultCustomer was not called
        expect(loadDefaultCustomerSpy).not.toHaveBeenCalled();
    });

    it('should call rebindLookups when onPersonObjectChanged is triggered', () => {
        // Trigger onPersonObjectChanged
        viewModel.onPersonObjectChanged();

        // Assert rebindLookups was called
        expect(viewModel.rebindLookups).toHaveBeenCalled();
    });

    it('should load customer data successfully when loadDefaultCustomer is called', () => {
        const mockCustomer = {
            Data: {
                CompanyName: ko.observable('Test Company')
            }
        };

        // Setup success handler to simulate API response
        viewModel.DataStoreCustomer.LoadObject.mockImplementation(({ successHandler }) => {
            successHandler(mockCustomer);
        });

        // Call loadDefaultCustomer
        customViewModel.loadDefaultCustomer('123', vi.fn());

        // Assert customer data was set correctly
        expect(viewModel.PersonObject().Data.CustomerId()).toBe('123');
        expect(viewModel.Customer_CompanyName()).toBe('Test Company');
        expect(viewModel.Customer_lookupItem()).toEqual({
            label: 'Test Company',
            value: mockCustomer,
            selectable: true
        });
    });
    
    it('should allow site and department to be editable when in edit mode', () => {
        // Setup person as not new (edit mode)
        viewModel.PersonObject().Data.IsNew(false);
        
        // Check if there are explicit read-only flags for site and department
        // If these functions don't exist, it means the fields are always editable
        expect(viewModel.StatusData.IsSiteReadOnly).toBeUndefined();
        expect(viewModel.StatusData.IsDepartmentReadOnly).toBeUndefined();
        
        // In contrast, customer should be read-only when not in new mode
        expect(viewModel.StatusData.IsCustomerReadOnly()).toBe(true);
        
        // Test that person info can be modified
        const newSiteId = 'site-999';
        const newDepartmentId = 'dept-999';
        
        viewModel.PersonObject().Data.SiteId(newSiteId);
        viewModel.PersonObject().Data.DepartmentId(newDepartmentId);
        
        expect(viewModel.PersonObject().Data.SiteId()).toBe(newSiteId);
        expect(viewModel.PersonObject().Data.DepartmentId()).toBe(newDepartmentId);
    });
}); 