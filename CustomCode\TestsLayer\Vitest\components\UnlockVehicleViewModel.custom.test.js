import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('UnlockVehicleViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let originalOnUnlockVehicleSuccess;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/UnlockVehicleViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        originalOnUnlockVehicleSuccess = vi.fn();
        viewModel = {
            onUnlockVehicleSuccess: originalOnUnlockVehicleSuccess,
            closePopup: vi.fn(),
            StatusData: {
                isPopup: ko.observable(true)
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.UnlockVehicleViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('onUnlockVehicleSuccess', () => {
        it('should call the original onUnlockVehicleSuccess and closePopup', () => {
            const testData = { id: 123 };

            // Call the overridden function
            viewModel.onUnlockVehicleSuccess(testData);

            // Verify that the original function was called with the correct data
            expect(originalOnUnlockVehicleSuccess).toHaveBeenCalledWith(testData);

            // Verify that closePopup was called with false
            expect(viewModel.closePopup).toHaveBeenCalledWith(false);
        });

        it('should maintain the original function context', () => {
            const testData = { id: 123 };
            const originalThis = { someProperty: 'value' };

            // Call the overridden function with a specific this context
            viewModel.onUnlockVehicleSuccess.call(originalThis, testData);

            // Verify that the original function was called with the correct this context
            expect(originalOnUnlockVehicleSuccess).toHaveBeenCalledWith(testData);
        });
    });
}); 