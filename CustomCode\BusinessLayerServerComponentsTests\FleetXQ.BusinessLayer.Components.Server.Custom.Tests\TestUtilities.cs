using System;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    public static class TestUtilities
    {
        public static async Task<ModuleDataObject> CreateTestModuleAsync(
            IDataFacade dataFacade,
            IServiceProvider serviceProvider,
            string ccid = "CCID1",
            string iotDevice = null,
            bool isAllocatedToVehicle = true)
        {
            var module = serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = ccid;

            // Set FSSSBASE random from 100000 to 200000 in increment of 10000
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = iotDevice ?? $"test_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";
            module.IsAllocatedToVehicle = isAllocatedToVehicle;

            return await dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);
        }

        public static async Task<VehicleDataObject> CreateTestVehicleAsync(
            IDataFacade dataFacade,
            IServiceProvider serviceProvider,
            Guid customerId,
            Guid departmentId,
            Guid modelId,
            Guid siteId,
            string hireNo,
            string serialNo,
            ModuleDataObject module = null)
        {
            var vehicle = serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = customerId;
            vehicle.DepartmentId = departmentId;
            vehicle.ModelId = modelId;
            vehicle.SiteId = siteId;
            vehicle.HireNo = hireNo;
            vehicle.SerialNo = serialNo;
            vehicle.IDLETimer = 300;
            vehicle.OnHire = true;
            vehicle.ImpactLockout = true;

            if (module != null)
            {
                vehicle.ModuleId1 = module.Id;
            }

            return await dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
        }

        public static async Task<(ModuleDataObject Module, VehicleDataObject Vehicle)> CreateTestModuleAndVehicleAsync(
            IDataFacade dataFacade,
            IServiceProvider serviceProvider,
            Guid customerId,
            Guid departmentId,
            Guid modelId,
            Guid siteId,
            string hireNo,
            string serialNo,
            string ccid = "CCID1",
            string iotDevice = null)
        {
            var module = await CreateTestModuleAsync(dataFacade, serviceProvider, ccid, iotDevice);
            var vehicle = await CreateTestVehicleAsync(
                dataFacade,
                serviceProvider,
                customerId,
                departmentId,
                modelId,
                siteId,
                hireNo,
                serialNo,
                module);

            return (module, vehicle);
        }
    }
}