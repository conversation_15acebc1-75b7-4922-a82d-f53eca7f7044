# Vehicle Access System - Technical Implementation Guide

## Overview

This document provides detailed technical implementation guidance for the Vehicle Access Management System, focusing on the custom code components, architectural patterns, and implementation details that extend the generated framework.

## Table of Contents

- [Custom Code Architecture](#custom-code-architecture)
- [ViewModels Implementation](#viewmodels-implementation)
- [HTML Template System](#html-template-system)
- [Server-Side Implementation](#server-side-implementation)
- [Data Consistency Mechanisms](#data-consistency-mechanisms)
- [Integration Patterns](#integration-patterns)

## Custom Code Architecture

### Component Overview

The Vehicle Access System consists of multiple layers of custom code that extend the generated framework:

#### Frontend Custom Components
- **2 Form ViewModels**: `PersonVehicleAccessFormViewModel.custom.js`, `SupervisorVehicleAccessFormViewModel.custom.js`
- **8 List ViewModels**: Custom pagination and filter integration for Normal and Master access lists
- **8 Filter Templates**: Custom HTML templates for proper context binding
- **Server Integration**: Azure Queue message handling with cascade preferences

#### Backend Custom Components
- **VehicleAccessUtilities.cs**: Core server-side cascade logic and data consistency
- **UserAccessUpdateMessage**: Extended with cascade permission properties
- **Queue Integration**: Background processing with cascade decision handling

### File Structure

```
CustomCode/
├── WebApplicationLayer/wwwroot/
│   ├── ViewModels/
│   │   ├── Person/
│   │   │   ├── PersonVehicleAccessFormViewModel.custom.js
│   │   │   └── SupervisorVehicleAccessFormViewModel.custom.js
│   │   ├── PersonToSiteVehicleNormalAccessView/
│   │   │   └── PersonToSiteVehicleNormalAccessViewListViewModel.custom.js
│   │   ├── PersonToDepartmentVehicleNormalAccessView/
│   │   │   └── PersonToDepartmentVehicleNormalAccessViewListViewModel.custom.js
│   │   ├── PersonToModelVehicleNormalAccessView/
│   │   │   └── PersonToModelVehicleNormalAccessViewListViewModel.custom.js
│   │   ├── PersonToPerVehicleNormalAccessView/
│   │   │   └── PersonToPerVehicleNormalAccessViewListViewModel.custom.js
│   │   ├── PersonToSiteVehicleMasterAccessView/
│   │   │   └── PersonToSiteVehicleMasterAccessViewListViewModel.custom.js
│   │   ├── PersonToDepartmentVehicleMasterAccessView/
│   │   │   └── PersonToDepartmentVehicleMasterAccessViewListViewModel.custom.js
│   │   ├── PersonToModelVehicleMasterAccessView/
│   │   │   └── PersonToModelVehicleMasterAccessViewListViewModel.custom.js
│   │   └── PersonToPerVehicleMasterAccessView/
│   │       └── PersonToPerVehicleMasterAccessViewListViewModel.custom.js
│   └── Views/PartialViews/
│       ├── PersonToSiteVehicleNormalAccessView/Filters/
│       ├── PersonToDepartmentVehicleNormalAccessView/Filters/
│       ├── PersonToModelVehicleNormalAccessView/Filters/
│       ├── PersonToPerVehicleNormalAccessView/Filters/
│       ├── PersonToSiteVehicleMasterAccessView/Filters/
│       ├── PersonToDepartmentVehicleMasterAccessView/Filters/
│       ├── PersonToModelVehicleMasterAccessView/Filters/
│       └── PersonToPerVehicleMasterAccessView/Filters/
└── BusinessLayerServerComponents/
    └── VehicleAccessUtilities.cs
```

## ViewModels Implementation

### Form-Level ViewModels

#### PersonVehicleAccessFormViewModel.custom.js & SupervisorVehicleAccessFormViewModel.custom.js

These ViewModels provide the main form-level functionality:

##### Authorization and Visibility Control
```javascript
this.IsModifyCommandVisible = function () {
    return (self.viewmodel.StatusData.DisplayMode() == 'view' && 
            !self.viewmodel.StatusData.IsEmpty() && 
            self.viewmodel.DataStore && 
            self.viewmodel.DataStore.CheckAuthorizationForEntityAndMethod('save'))
        && (ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.HAS_USERS_ACCESS) &&
            ApplicationController.viewModel.viewModelCustom.hasAccess(ApplicationController.viewModel.viewModelCustom.AccessRules.CAN_EDIT_VEHICLE_ACCESS));
}
```

##### Cascade Permission Detection
```javascript
// Helper method to check if any access is being added (HasAccess changed from false to true)
this.hasAccessBeingAdded = function (accesses) {
    if (!accesses || accesses.length === 0) return false;

    return accesses.some(function (access) {
        // Check if HasAccess is true and the object is dirty (indicating a change)
        return access.Data.HasAccess() && access.Data.IsDirty();
    });
};
```

##### Save Operation with Cascade Integration
The `PerformSave` method handles cascade prompts and queue message creation:

```javascript
this.PerformSave = function (cascadeAddPermission) {
    // ... data collection logic ...
    
    // Create update messages for queue processing
    var updateMessages = [];
    
    // Add cascade preference to each message
    updateMessages.forEach(function(message) {
        message.CascadeAddPermission = cascadeAddPermission || false;
    });
    
    // Send to queue for background processing
    self.viewmodel.DataStore.Save(/* parameters */);
};
```

### List-Level ViewModels

Each of the 8 list ViewModels (4 Normal + 4 Master access) implements identical patterns:

#### Pagination Implementation

##### Core Pagination Properties
```javascript
// Pagination properties
this.viewModel.totalPageNumber = ko.observable(0);
this.viewModel.totalCollection = ko.observable(0);
this.viewModel.ignorePageChange = false;
this.viewModel.pageNumber = ko.observable(0); // 0 based
this.viewModel.pageSize = 15;
```

##### Enhanced Data Protection System
```javascript
// Add computed observables for control state management
self.viewModel.StatusData.IsFilterEnabled = ko.computed(function () {
    return self.viewModel.StatusData.DisplayMode() !== 'edit';
});

self.viewModel.StatusData.IsPaginationEnabled = ko.computed(function () {
    return self.viewModel.StatusData.DisplayMode() !== 'edit';
});

// Expose properties at root level for template binding access
self.viewModel.IsFilterEnabled = self.viewModel.StatusData.IsFilterEnabled;
self.viewModel.IsPaginationEnabled = self.viewModel.StatusData.IsPaginationEnabled;
```

##### Simplified Pagination Protection
```javascript
paginationClickHandler: function (data, e) {
    e.preventDefault();
    e.stopPropagation();
    
    // Don't do anything if pagination is disabled (edit mode)
    if (!self.viewModel.StatusData.IsPaginationEnabled()) {
        return; // Simply ignore the click
    }
    
    // Normal pagination logic
    var targetPage = parseInt(e.currentTarget.getAttribute('data-pagenumber'));
    if (targetPage > -1 && targetPage < self.viewModel.totalPageNumber()) {
        self.viewModel.pageNumber(targetPage);
    }
}
```

#### Filter Integration

##### Filter ViewModel Creation
```javascript
// Create filter viewmodel (like PersonGrid does)
this.viewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel = new FleetXQ.Web.ViewModels.Filters.PersonToSiteVehicleNormalAccessViewFilterViewModel(
    self.viewModel.controller, null, null, self.viewModel.contextId
);
```

##### Filter Command Integration
```javascript
// Expose filter properties at the list viewmodel root
if (!self.viewModel.commands) {
    self.viewModel.commands = {};
}

// Add filter commands to the existing commands object
if (self.viewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.commands) {
    self.viewModel.commands.searchCommand = self.viewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.commands.searchCommand;
    self.viewModel.commands.clearCommand = self.viewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.commands.clearCommand;
}
```

#### Data Loading Architecture

##### Count-then-Load Pattern
```javascript
// Override the original load method to add pagination
self.viewModel.LoadPersonToSiteVehicleNormalAccessViewObjectCollection = function (configuration) {
    self.viewModel.StatusData.IsBusy(true);

    if (!configuration) {
        configuration = {};
    }

    configuration.successHandler = self.OnPersonToSiteVehicleNormalAccessViewObjectCollectionCounted;
    configuration.contextId = self.viewModel.contextId;

    // First count total records
    self.viewModel.DataStore.CountObjects(configuration);
};

this.OnPersonToSiteVehicleNormalAccessViewObjectCollectionCounted = function (count, configuration) {
    self.viewModel.totalCollection(count);
    self.viewModel.totalPageNumber(Math.ceil(count / self.viewModel.pageSize));
    
    // Then load paged data
    self.LoadPagedPersonToSiteVehicleNormalAccessViewObjectCollection(configuration);
};
```

## HTML Template System

### Filter Template Architecture

Each filter template follows a standardized pattern for context isolation:

#### Template Structure
```html
<!--
// This is Custom Code - [Access Type] [Entity] Access Filter
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter "[Filter Name]"-->
<div>
    <div id="{VIEWNAME}-Filter" class="[FilterClassName]" data-test-id="[TestId]">
        <form data-bind="submit: [FormContext].[ListContext].[FilterContext].commands.searchCommand">
            <div class="uiSearchContainer" style="margin-top: 8px;">
                <div class="filterFieldSetContent">
                    <div class="row g-2 align-items-end">
                        <!-- Field implementations -->
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter-->
```

#### Context Binding Patterns

##### Normal Access Context
```html
<!-- Form submission -->
<form data-bind="submit: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.commands.searchCommand">

<!-- Field binding -->
<input data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.filterData.fields.SiteName" />

<!-- Visibility binding -->
<div data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.statusData.isSiteNameVisible">
```

##### Supervisor Access Context
```html
<!-- Form submission -->
<form data-bind="submit: SupervisorVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.commands.searchCommand">

<!-- Field binding -->
<input data-bind="value: SupervisorVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.filterData.fields.SiteName" />
```

#### Modern UI Implementation

##### Horizontal Layout Structure
```html
<div class="uiSearchContainer" style="margin-top: 8px;">
    <div class="filterFieldSetContent">
        <div class="row g-2 align-items-end">
            <!-- Text Input Field -->
            <div class="col-auto" data-bind="visible: [VisibilityBinding]">
                <div class="d-flex align-items-center">
                    <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                        <span data-bind="i18n: '[I18nKey]'">[DefaultLabel]</span>
                    </label>
                    <input type="text" 
                           class="form-control form-control-sm" 
                           style="min-width: 150px; padding-left: 12px;"
                           data-bind="value: [ValueBinding], enable: [ViewModelPath].IsFilterEnabled" />
                </div>
            </div>
            
            <!-- Search Buttons with Data Protection -->
            <div class="col-auto">
                <div class="btn-group" role="group">
                    <button type="submit" class="btn btn-primary btn-sm"
                            data-bind="click: [SearchCommandBinding], enable: [ViewModelPath].IsFilterEnabled">SEARCH</button>
                    <button type="button" class="btn btn-outline-secondary btn-sm"
                            data-bind="click: [ClearCommandBinding], enable: [ViewModelPath].IsFilterEnabled">CLEAR</button>
                </div>
            </div>
        </div>
    </div>
</div>
```

### Enhanced Data Protection Implementation

#### Control State Management
The system implements comprehensive data protection through computed observables that automatically disable controls in edit mode:

```javascript
// ViewModel implementation - enables/disables based on DisplayMode
self.viewModel.StatusData.IsFilterEnabled = ko.computed(function () {
    return self.viewModel.StatusData.DisplayMode() !== 'edit';
});
```

#### Template Binding Patterns
All filter controls use consistent `enable` binding for unified behavior:

```html
<!-- Input fields disabled in edit mode -->
<input data-bind="value: FieldBinding, enable: ViewModelPath.IsFilterEnabled" />

<!-- Dropdown selects disabled in edit mode -->
<select data-bind="value: FieldBinding, options: OptionsBinding, enable: ViewModelPath.IsFilterEnabled"></select>

<!-- Buttons disabled in edit mode -->
<button data-bind="click: CommandBinding, enable: ViewModelPath.IsFilterEnabled">ACTION</button>
```

#### Context-Specific Examples

##### Normal Access Example
```html
<!-- Site Normal Access Filter Template -->
<input type="text" class="form-control form-control-sm"
       data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.filterData.fields.SiteName, 
                  enable: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled" />

<button type="submit" class="btn btn-primary btn-sm"
        data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.commands.searchCommand,
                   enable: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled">SEARCH</button>
```

##### Master Access Example
```html
<!-- Site Master Access Filter Template -->
<input type="text" class="form-control form-control-sm"
       data-bind="value: SupervisorVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.filterData.fields.SiteName,
                  enable: SupervisorVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled" />

<button type="submit" class="btn btn-primary btn-sm"
        data-bind="click: SupervisorVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.PersonToSiteVehicleNormalAccessViewFilterViewModel.commands.searchCommand,
                   enable: SupervisorVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled">SEARCH</button>
```

#### Advantages of This Approach
- **Zero Confirmation Dialogs**: No workflow interruptions
- **Universal UI Pattern**: Standard disabled control behavior
- **Immediate Visual Feedback**: Grayed out controls signal unavailability
- **Automatic State Management**: No manual tracking required
- **Consistent Implementation**: Same pattern across all 8 access lists

### Enhanced Bulk Operations Implementation

#### SelectAllAndSave / DeselectAllAndSave System

The bulk operations system provides enterprise-grade functionality for managing vehicle access permissions at scale through sophisticated server-side processing and intuitive client-side interfaces.

#### Frontend Implementation

##### Button Visibility Control
```javascript
// Bulk operation buttons only visible in edit mode
self.viewmodel.Commands.IsSelectAllSaveCommandVisible = ko.pureComputed(function () {
    return self.viewmodel.StatusData.DisplayMode() == 'edit';
});

self.viewmodel.Commands.IsDeselectAllSaveCommandVisible = ko.pureComputed(function () {
    return self.viewmodel.StatusData.DisplayMode() == 'edit';
});
```

##### SelectAllAndSave Frontend Logic
```javascript
// Override SelectAllSave method with three-option confirmation
self.viewmodel.SelectAllSave = function () {
    var currentTab = self.viewmodel.StatusData.CurrentTabIndex();
    var tabName = currentTab == 1 ? "sites" : (currentTab == 2 ? "departments" : 
                 (currentTab == 3 ? "models" : "vehicles"));

    var combinedMessage = "Are you sure you want to select ALL " + tabName + " and save?\n\n" +
        "This will grant access to all " + tabName + " in the current tab.\n\n" +
        "Do you want to automatically add access to all related child items?";

    self.viewmodel.controller.applicationController.showThreeOptionConfirmPopup(
        self.viewmodel,
        combinedMessage,
        "Confirm Select All and Save",
        function (userChoice) {
            if (userChoice === 'yes') {
                // Cascade save
                var configuration = {
                    caller: self.viewmodel,
                    contextId: self.viewmodel.contextId,
                    successHandler: self.onSelectAllSaveSuccess,
                    errorHandler: self.onSelectAllSaveError,
                    tabIndex: self.viewmodel.StatusData.CurrentTabIndex() - 1,
                    cascadeSave: true,
                    permissionLevel: 3, // Normal access
                    personId: self.viewmodel.PersonObject().Data.Id()
                };
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController
                    .getProxyForComponent("VehicleAccessUtilities")
                    .SelectAllAndSave(configuration);
            } else if (userChoice === 'no') {
                // No cascade save
                var configuration = {
                    // ... same configuration with cascadeSave: false
                };
                // ... call SelectAllAndSave
            }
            // userChoice === 'cancel' = do nothing
        },
        self.viewmodel.contextId
    );
};
```

##### DeselectAllAndSave Frontend Logic
```javascript
// Override DeselectAllSave method with simple confirmation
self.viewmodel.DeselectAllSave = function () {
    var currentTab = self.viewmodel.StatusData.CurrentTabIndex();
    var tabName = currentTab == 1 ? "sites" : (currentTab == 2 ? "departments" : 
                 (currentTab == 3 ? "models" : "vehicles"));

    self.viewmodel.controller.applicationController.showConfirmPopup(
        self.viewmodel,
        "Are you sure you want to deselect ALL " + tabName + " and save?",
        "Confirm Deselect All and Save",
        function (confirmed) {
            if (confirmed) {
                var configuration = {
                    caller: self.viewmodel,
                    contextId: self.viewmodel.contextId,
                    successHandler: self.onDeselectAllSaveSuccess,
                    errorHandler: self.onDeselectAllSaveError,
                    tabIndex: self.viewmodel.StatusData.CurrentTabIndex() - 1,
                    permissionLevel: 3, // Normal access
                    personId: self.viewmodel.PersonObject().Data.Id()
                };
                self.viewmodel.setIsBusy(true);
                self.viewmodel.controller.applicationController
                    .getProxyForComponent("VehicleAccessUtilities")
                    .DeselectAllAndSave(configuration);
            }
        },
        self.viewmodel.contextId
    );
};
```

#### Backend Implementation

##### SelectAllAndSave Server Method
```csharp
public async Task<ComponentResponse<bool>> SelectAllAndSaveAsync(
    int tabIndex, bool cascadeSave, int permissionLevel, Guid personId, 
    Dictionary<string, object> parameters = null)
{
    try
    {
        var person = await GetPersonAsync(personId);
        var card = person.Driver.Card;

        // Get permission ID for the specified level
        var permission = (await _dataFacade.PermissionDataProvider
            .GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel }, 
                               skipSecurity: true)).SingleOrDefault();
        
        if (permission == null)
        {
            _logger?.LogWarning($"[SelectAllAndSaveAsync] No permission found for level {permissionLevel}");
            return new ComponentResponse<bool>(false);
        }

        // Calculate complete access collections based on tab index
        var (siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses) = 
            await CalculateCompleteAccessesForSelectAllAsync(
                tabIndex, card, person, permissionLevel, permission.Id, cascadeSave);

        // Use the existing internal method to process all access updates
        var result = await UpdateAccessesForPersonInternalAsync(
            siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses,
            personId, cascadeSave,
            new Dictionary<string, object> { { "permissionLevel", permissionLevel } });

        _logger?.LogInformation($"[SelectAllAndSaveAsync] Successfully selected all for tab {tabIndex}, person {personId}");
        return result;
    }
    catch (Exception ex)
    {
        _logger?.LogError(ex, $"[SelectAllAndSaveAsync] Failed to select all for tab {tabIndex}: {ex.Message}");
        throw;
    }
}
```

##### Tab-Based Access Calculation
```csharp
private async Task<(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> siteAccesses,
                   DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses,
                   DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
                   DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)>
CalculateCompleteAccessesForSelectAllAsync(int tabIndex, CardDataObject card, PersonDataObject person, 
                                         int permissionLevel, Guid permissionId, bool cascadeSave)
{
    // Initialize collections
    var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
    var siteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
    var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
    var modelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
    var vehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

    switch (tabIndex)
    {
        case 0: // Sites - Select all sites and optionally cascade
            await CalculateAllSiteAccessesAsync(card, person, permissionLevel, permissionId, 
                                              siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses, cascadeSave);
            break;
            
        case 1: // Departments - Select all departments from existing sites and optionally cascade
            await CalculateAllDepartmentAccessesAsync(card, person, permissionLevel, permissionId, 
                                                     departmentAccesses, modelAccesses, vehicleAccesses, cascadeSave);
            break;
            
        case 2: // Models - Select all models from existing departments and optionally cascade
            await CalculateAllModelAccessesAsync(card, person, permissionLevel, permissionId, 
                                                modelAccesses, vehicleAccesses, cascadeSave);
            break;
            
        case 3: // Vehicles - Select all vehicles from existing models
            await CalculateAllVehicleAccessesAsync(card, person, permissionLevel, permissionId, vehicleAccesses);
            break;
    }

    return (siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses);
}
```

##### Site Access Calculation Example
```csharp
private async Task CalculateAllSiteAccessesAsync(CardDataObject card, PersonDataObject person, 
    int permissionLevel, Guid permissionId,
    DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> siteAccesses,
    DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses,
    DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
    DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses,
    bool cascadeSave)
{
    // Get all available sites for the customer
    var customerId = person.CustomerId;
    var allSites = await _dataFacade.SiteDataProvider.GetCollectionAsync(
        null, "CustomerId == @0", new object[] { customerId }, skipSecurity: true);
    
    var existingSiteIds = card.SiteVehicleNormalCardAccessItems
        .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
        .Select(access => access.SiteId)
        .ToHashSet();

    // Add all sites that don't already have access
    foreach (var site in allSites.Where(s => !existingSiteIds.Contains(s.Id)))
    {
        var siteAccess = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
        siteAccess.SiteId = site.Id;
        siteAccess.PersonId = person.Id;
        siteAccess.PermissionId = permissionId;
        siteAccess.HasAccess = true;
        siteAccesses.Add(siteAccess);
    }

    // If cascading is enabled, add departments, models, and vehicles
    if (cascadeSave)
    {
        await CascadeToChildEntitiesAsync(card, person, permissionLevel, permissionId, 
                                        departmentAccesses, modelAccesses, vehicleAccesses);
    }
}
```

#### Integration with Existing Systems

##### Queue Processing Integration
```csharp
// Bulk operations integrate with existing UserAccessUpdateMessage queue system
var messages = new List<UserAccessUpdateMessage>();
foreach (var vehicleAccess in vehicleAccesses)
{
    var message = new UserAccessUpdateMessage
    {
        PersonId = personId,
        VehicleId = vehicleAccess.VehicleId,
        PermissionId = vehicleAccess.PermissionId,
        HasAccess = vehicleAccess.HasAccess,
        CascadeAddPermission = cascadeSave
    };
    messages.Add(message);
}

// Process through background queue for real-time vehicle synchronization
await _vehicleAccessUtilities.UpdateAccessesForPersonAsync(personId, messages, cascadeSave);
```

##### Error Handling and Logging
```csharp
// Comprehensive error handling with Application Insights integration
try
{
    var result = await SelectAllAndSaveAsync(tabIndex, cascadeSave, permissionLevel, personId);
    _logger?.LogInformation($"[BulkOperation] SelectAll completed for {result.Data} items");
    return result;
}
catch (Exception ex)
{
    _logger?.LogError(ex, $"[BulkOperation] SelectAll failed for person {personId}, tab {tabIndex}");
    throw new ComponentException($"Bulk operation failed: {ex.Message}", ex);
}
```

#### Performance Considerations

##### Atomic Transaction Management
- **Single Transaction**: All permission changes processed in one database transaction
- **Rollback Capability**: Failed operations automatically rollback all changes
- **Consistency Guarantee**: Either all permissions are granted/removed or none are

##### Memory Optimization
- **Lazy Loading**: Entities loaded on-demand during calculation
- **Collection Reuse**: Efficient reuse of data object collections
- **Batch Processing**: Large operations processed in manageable chunks

##### Background Processing
- **Queue Integration**: Large vehicle synchronization handled asynchronously  
- **Progress Tracking**: Real-time status updates for long-running operations
- **Resource Management**: Intelligent resource allocation for bulk operations

## Server-Side Implementation

### VehicleAccessUtilities.cs

#### Cascade Permission Implementation

##### Core Cascade Logic
```csharp
public async Task<bool> UpdateAccessesForPersonAsync(
    Guid personId, 
    List<UserAccessUpdateMessage> messages, 
    bool cascadeAddPermission = false)
{
    // Process cascade decisions through background queue
    foreach (var message in messages)
    {
        message.CascadeAddPermission = cascadeAddPermission;
        await _queueService.EnqueueAsync(message);
    }
}
```

##### Site Access Cascade Implementation
```csharp
// Process site additions with cascading
foreach (var siteAccess in sitesToAdd)
{
    var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
    card.SiteVehicleNormalCardAccessItems.Add(newAccess);

    // CASCADE: Add access to all departments, models, and vehicles in this site (only if cascading is enabled)
    if (cascadeAddPermission)
    {
        var vehicleAccesses = await AddAccessForDepartmentsOfSiteAsync(
            card,
            siteAccess.SiteId,
            siteAccess.PermissionId);

        if (vehicleAccesses.Any())
        {
            updatedPerVehicleAccesses.AddRange(vehicleAccesses);
        }
    }
}
```

##### Department Access Cascade Implementation
```csharp
// CASCADE: Add access to all models and vehicles in this department (only if cascading is enabled)
if (cascadeAddPermission)
{
    var department = await GetDepartmentAsync(deptAccess.DepartmentId);

    await AddAccessForModelsOfDepartmentAsync(
        card,
        department,
        deptAccess.PermissionId);

    var vehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(
        card,
        department,
        deptAccess.PermissionId);

    if (vehicleAccesses.Any())
    {
        updatedPerVehicleAccesses.AddRange(vehicleAccesses);
    }
}
```

##### Model Access Cascade Implementation
```csharp
// CASCADE: Add access to all vehicles of this model (only if cascading is enabled)
if (cascadeAddPermission)
{
    _logger?.LogInformation($"[CASCADE] Adding model access for ModelId: {modelAccess.ModelId} - cascading to vehicles");
    var vehicleAccesses = await AddAccessForVehiclesOfModelAsync(
        card,
        modelAccess.ModelId,
        modelAccess.PermissionId);

    _logger?.LogInformation($"[CASCADE] Model access cascade added {vehicleAccesses.Count()} vehicle accesses for ModelId: {modelAccess.ModelId}");
}
```

#### Cascade Vehicle Addition Logic
```csharp
private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
{
    var departments = (await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true)).Select(a => a.DepartmentId).ToList();
    _logger?.LogInformation($"[CASCADE] Found {departments.Count} departments with access for model cascade");

    // Enhanced query with soft-delete filtering
    var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, 
        $"ModelId == @0 and @1.Contains(outerIt.DepartmentId) and DeletedAtUtc == null", 
        new object[] { modelId, departments }, 
        skipSecurity: true);
    _logger?.LogInformation($"[CASCADE] Found {vehicles.Count()} active vehicles for ModelId: {modelId}");

    var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

    foreach (var vehicle in vehicles)
    {
        var hasAccess = card.PerVehicleNormalCardAccessItems.Where(a => a.VehicleId == vehicle.Id && a.PermissionId == permissionId).Any();
        
        if (!hasAccess)
        {
            var perVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            perVehicleAccess.VehicleId = vehicle.Id;
            perVehicleAccess.PermissionId = permissionId;

            card.PerVehicleNormalCardAccessItems.Add(perVehicleAccess);
            perVehicleAccessList.Add(perVehicleAccess);
            _logger?.LogInformation($"[CASCADE] Added vehicle access for VehicleId: {vehicle.Id}");
        }
    }

    return perVehicleAccessList;
}
```

## Data Consistency Mechanisms

### Enhanced Vehicle Filtering and Scoping

#### Soft-Delete Filtering Implementation
The system now consistently filters out soft-deleted vehicles across all operations:

```csharp
// Department vehicle loading with soft-delete filtering
private async System.Threading.Tasks.Task AddAccessForModelsOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
{
    var modelIds = (await department.LoadVehiclesAsync(skipSecurity: true))
        .Where(v => v.DeletedAtUtc == null)  // Filter out soft-deleted vehicles
        .Select(v => v.ModelId)
        .ToHashSet();
    // ... rest of method
}

// All vehicles of department with filtering
private async System.Threading.Tasks.Task AddAccessForAllVehiclesOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
{
    var vehicles = await department.LoadVehiclesAsync(skipSecurity: true);

    // Filter out soft deleted vehicles
    var activeVehicles = vehicles.Where(v => v.DeletedAtUtc == null).ToList();

    // Create all access items at once using LINQ
    var perVehicleAccessList = activeVehicles
        .Select(vehicle =>
        {
            var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            access.VehicleId = vehicle.Id;
            access.PermissionId = permissionId;
            return access;
        })
        .ToList();
    // ... rest of method
}
```

#### Department-Scoped Model Access
Enhanced model access calculations are now properly scoped to departments:

```csharp
// Helper method with department scoping
private async Task CalculateAllAccessesForModelAsync(Guid modelId, Guid departmentId, PersonDataObject person, int permissionLevel, Guid permissionId,
    DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
{
    // Get vehicles for this specific model and department combination
    var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
        null, 
        "ModelId == @0 and DepartmentId == @1 and DeletedAtUtc == null", 
        new object[] { modelId, departmentId }, 
        skipSecurity: true);

    // Track existing vehicle IDs to avoid duplicates
    var existingVehicleIds = vehicleAccesses
        .Select(access => access.VehicleId)
        .ToHashSet();

    foreach (var vehicle in vehicles)
    {
        // Skip if vehicle already has access
        if (existingVehicleIds.Contains(vehicle.Id))
            continue;

        var vehicleAccess = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
        vehicleAccess.VehicleId = vehicle.Id;
        vehicleAccess.HireNo = vehicle.HireNo;
        vehicleAccess.PermissionId = permissionId;
        vehicleAccess.PersonId = person.Id;
        vehicleAccess.HasAccess = true;
        vehicleAccess.IsNew = false;

        vehicleAccesses.Add(vehicleAccess);
        existingVehicleIds.Add(vehicle.Id);
    }
}
```

#### Enhanced Vehicle Access Calculation
Improved logic for calculating vehicle accesses with department filtering:

```csharp
// Vehicle access calculation with department filtering
private async Task CalculateVehicleAccessesFromModelsAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
    DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
{
    var existingVehicleIds = vehicleAccesses
        .Select(access => access.VehicleId)
        .ToHashSet();

    // Get all model IDs from ModelVehicleNormalCardAccessItems
    var modelIds = card.ModelVehicleNormalCardAccessItems
        .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
        .Select(access => access.ModelId)
        .ToHashSet();

    // Get all department IDs from DepartmentVehicleNormalCardAccessItems
    var departmentIds = card.DepartmentVehicleNormalCardAccessItems
        .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
        .Select(access => access.DepartmentId)
        .ToHashSet();

    // Get all vehicles under the departments with soft-delete filtering
    var vehiclesUnderDepartments = new List<VehicleDataObject>();
    if (departmentIds.Any())
    {
        var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
            null, 
            "@0.Contains(outerIt.DepartmentId) and DeletedAtUtc == null", 
            new object[] { departmentIds.ToList() }, 
            skipSecurity: true);
        vehiclesUnderDepartments.AddRange(vehicles);
    }

    // For each vehicle, check if its model is in the modelIds
    foreach (var vehicle in vehiclesUnderDepartments)
    {
        // Skip if vehicle already has access
        if (existingVehicleIds.Contains(vehicle.Id))
            continue;

        // Check if the vehicle's model is in the allowed model IDs
        if (modelIds.Contains(vehicle.ModelId))
        {
            var vehicleAccess = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
            vehicleAccess.VehicleId = vehicle.Id;
            vehicleAccess.HireNo = vehicle.HireNo;
            vehicleAccess.PermissionId = permissionId;
            vehicleAccess.PersonId = person.Id;
            vehicleAccess.HasAccess = true;
            vehicleAccess.IsNew = false;

            vehicleAccesses.Add(vehicleAccess);
            existingVehicleIds.Add(vehicle.Id);
        }
    }
}
```

#### SQL View Updates
Database views now include soft-delete filtering:

```sql
-- Updated SQL view with soft-delete filtering
CROSS JOIN Permission perm
INNER JOIN DepartmentVehicleNormalCardAccess da ON da.CardId = c.Id AND da.DepartmentId = dpt.Id AND da.PermissionId = perm.Id
INNER JOIN ModelVehicleNormalCardAccess ma on ma.CardId = c.Id AND ma.ModelId = v.ModelId AND ma.DepartmentId = dpt.Id AND ma.PermissionId = perm.Id
LEFT JOIN PerVehicleNormalCardAccess va on va.CardId = c.Id AND va.VehicleId = v.Id AND va.PermissionId = perm.Id
WHERE v.DeletedAtUtc IS NULL  -- Exclude soft-deleted vehicles at database level
```

#### Performance and Data Integrity Benefits
- **Consistent Filtering**: All operations respect vehicle deletion status
- **Improved Performance**: Smaller datasets reduce query time and memory usage
- **Data Integrity**: Access permissions only apply to active, usable vehicles
- **Department Scoping**: Model operations properly respect organizational boundaries
- **Duplicate Prevention**: Enhanced tracking prevents access conflicts

### Dummy Object Strategy

The system uses a workaround to handle limitations with the GO framework's component operations that don't work properly with null objects or empty collections. This approach maintains data consistency when saving partial data from paginated views:

#### Client-Side Dummy Object Creation
```javascript
// Client: Empty collections → Dummy objects with marker
// Workaround for GO framework limitation with null/empty collections
var dummyPersonId = '00000000-0000-0000-0000-000000000001';
if (accesses.length === 0) {
    var dummyObject = new AccessViewObject();
    dummyObject.Data.IsDirty(true);
    dummyObject.Data.PersonId(dummyPersonId); // Server filtering marker
    return [dummyObject];
}
```

**Note**: This dummy object strategy is a temporary workaround for GO framework limitations and should be removed when the framework is fixed to properly handle null objects and empty collections.

#### Server-Side Dummy Object Filtering
```csharp
// HACK: Filter out dummy objects (marker PersonId = 00000000-0000-0000-0000-000000000001) before processing
// See https://github.com/generative-objects-org/go-meta-lowcode/issues/351
// TODO: Remove this hack when MapDataSetToJSON can handle empty datasets properly
var dummyPersonId = new Guid("00000000-0000-0000-0000-000000000001");
var filteredModelAccesses = personToModelAccesses?.Where(obj => obj.PersonId != dummyPersonId).ToList();
var (modelsToAdd, modelsToRemove) = filteredModelAccesses?.Any() == true
    ? await CategorizeAccessUpdates(new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>(filteredModelAccesses), existingModelAccesses, person, globalPermissionContext)
    : (new List<PersonToModelVehicleNormalAccessViewDataObject>(), new List<PersonToModelVehicleNormalAccessViewDataObject>());
```

### Access Update Categorization

#### Change Detection Logic
```csharp
private async Task<(List<PersonToSiteVehicleNormalAccessViewDataObject> ToAdd,
                    List<PersonToSiteVehicleNormalAccessViewDataObject> ToRemove)>
CategorizeAccessUpdates(
    DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updates,
    Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingAccesses,
    PersonDataObject person)
{
    var toAdd = new List<PersonToSiteVehicleNormalAccessViewDataObject>();
    var toRemove = new List<PersonToSiteVehicleNormalAccessViewDataObject>();

    // Handle null or empty updates collection (for non-current tabs)
    if (updates?.Any() != true)
    {
        _logger?.LogInformation($"[PERF] Site access - No updates provided, returning empty lists");
        return (toAdd, toRemove);
    }

    // Create a set of keys from updates for quick lookup
    var updateKeys = new HashSet<(Guid SiteId, Guid PermissionId)>();
    foreach (var update in updates)
    {
        var key = (update.SiteId, update.PermissionId);
        updateKeys.Add(key);

        var hasExisting = existingAccesses.ContainsKey(key);

        if (update.HasAccess && !hasExisting)
        {
            toAdd.Add(update);
        }
        else if (!update.HasAccess && hasExisting)
        {
            toRemove.Add(update);
        }
    }
    
    return (toAdd, toRemove);
}
```

## Integration Patterns

### Queue Message Integration

#### UserAccessUpdateMessage Extensions
```csharp
// Extended UserAccessUpdateMessage in both CustomCode and FleetXQFunctionService
public class UserAccessUpdateMessage
{
    // ... existing properties ...
    public bool CascadeAddPermission { get; set; } = false;
}
```

#### Queue Processing with Cascade
```csharp
// In VehicleAccessCreation.cs
public async Task ProcessAsync(UserAccessUpdateMessage message)
{
    // Extract cascade preference from queue message
    bool cascadeAddPermission = message.CascadeAddPermission;
    
    // Process with cascade preference
    await _vehicleAccessUtilities.UpdateAccessesForPersonAsync(
        message.PersonId,
        messages,
        cascadeAddPermission);
}
```

### Framework Integration Points

#### Generated Code Override Patterns
1. **ViewModel Extensions**: Custom ViewModels extend generated ones through naming convention
2. **Template Overrides**: Custom templates override generated ones through file path matching
3. **Method Overriding**: Custom ViewModels override specific methods while preserving framework integration

#### Data Binding Context Resolution
1. **Full Path Specification**: All bindings use complete paths to avoid context ambiguity
2. **Context Isolation**: Form contexts completely separated between Normal and Supervisor access
3. **Filter Integration**: Filter ViewModels properly integrated with list ViewModels for command execution

This technical implementation provides a robust, scalable foundation for vehicle access management while maintaining compatibility with the generated framework and ensuring data consistency across complex user interactions.