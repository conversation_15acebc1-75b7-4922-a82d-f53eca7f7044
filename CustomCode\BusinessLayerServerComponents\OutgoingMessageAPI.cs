﻿using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using Newtonsoft.Json;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using DocumentFormat.OpenXml.EMMA;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// OutgoingMessageAPI Component
	///  
	/// </summary>
    public partial class OutgoingMessageAPI : BaseServerComponent, IOutgoingMessageAPI 
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IServiceProvider _serviceProvider;
		private readonly IAuthentication _authentication;

        public OutgoingMessageAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, IAuthentication authentication) : base(serviceProvider, configuration, dataFacade)
		{
			_dataFacade = dataFacade;
			_deviceMessageHandler = deviceMessageHandler;
			_serviceProvider = serviceProvider;
			_authentication = authentication;
		}

        /// <summary>
        /// ProcessOutgoingMessage Method
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> ProcessOutgoingMessageAsync(System.String Payload, Dictionary<string, object> parameters = null)
        {
			// TODO: This is a custom component - Implementation should be provided
			SyncDataObject syncDataObject = JsonConvert.DeserializeObject<SyncDataObject>(Payload);

			if (syncDataObject != null)
			{
				SyncPayloadDataObjects payloadDataObjects = JsonConvert.DeserializeObject<SyncPayloadDataObjects>(syncDataObject.Payload);

                if (syncDataObject.EventType == "SYNC_DRIVER_PER_VEHICLE") // TODO: Convert these into enums
                {
                    return await SyncDriversPerVehicleAsync(payloadDataObjects);
                }
                else if (syncDataObject.EventType == "SYNC_DRIVER_PER_DEPARTMENT")
                {
                    DepartmentDataObject department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadDataObjects.DepartmentId })).SingleOrDefault();
                    return await SyncDriversPerDepartmentAsync(department);
                }
                else if (syncDataObject.EventType == "SYNC_DRIVER_PER_SITE")
                {
                    SiteDataObject site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadDataObjects.SiteId })).SingleOrDefault();
                    return await SyncDriversPerSiteAsync(site);
                }
                else if (syncDataObject.EventType == "SYNC_DRIVER_PER_MODEL")
                {
                    ModelDataObject model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadDataObjects.ModelId })).SingleOrDefault();
                    return await SyncDriversPerModelAsync(model);
                }
                else if(syncDataObject.EventType == "SYNC_VEHICLE_ACCESS")
                {
                    VehicleDataObject vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadDataObjects.VehicleId })).SingleOrDefault();
                    return await SyncVehicleAccessAsync(vehicle);
                }
                else
                {
                    throw new GOServerException($"Invalid payload {Payload}");
                }
			} else
			{
                throw new GOServerException($"Invalid payload {Payload}");
            }
		}

        //Sync methods here
        // Sync drivers per vehicle
        async System.Threading.Tasks.Task<ComponentResponse<string>> SyncDriversPerVehicleAsync(SyncPayloadDataObjects payloadData, Dictionary<string, object> parameters = null)
		{
			var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadData.VehicleId })).SingleOrDefault();
			if (vehicle == null)
			{
                throw new GOServerException($"unknow vehicle id {payloadData.VehicleId}");
            }
			var vehicleAccessList = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0", new object[] { vehicle.Id })).ToList();
            var cmdList = new List<string>();
            foreach (var access in vehicleAccessList)
			{
				String cmd = $"IDAUTH={(await access.LoadCardAsync()).Weigand}";
				cmdList.Add(cmd);
			}

            SendBatchPayload payloadToSend = new();
            payloadToSend.MessageId = Guid.NewGuid().ToString();
            payloadToSend.SessionId = Guid.NewGuid().ToString();
            payloadToSend.Type = "CMD";
            payloadToSend.Payload = cmdList;

            var module = await vehicle.LoadModuleAsync();
            if (module != null)
            {
				// Save message to database history table
				var message = _serviceProvider.GetService<MessageHistoryDataObject>();
				message.Vehicle = vehicle;

				var user = await _authentication.GetCurrentUserClaimsAsync();

                //message.WebsiteUserId = user.UserId.GetValueOrDefault();
				// message.Message = JsonConvert.SerializeObject(payloadToSend);
				message.MessageStatus = MessageStatusEnum.PENDING;
				message.SentTimestamp = DateTime.UtcNow;
                await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);

                // send the questions to the module
                _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payloadToSend));
            }
            return new ComponentResponse<string>(default(System.String));
        }
        // Sync drivers per department
        async System.Threading.Tasks.Task<ComponentResponse<string>> SyncDriversPerDepartmentAsync(DepartmentDataObject department, Dictionary<string, object> parameters = null)
		{
            // list of DepartmentVehicleNormalCardAccessContainerDataObject
            var departmentVehicleNormalCardAccessContainerList = (await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "DepartmentId == @0", new object[] { department.Id })).ToList();
			var cmdList = new List<string>();
            foreach (var departmentVehicleNormalCardAccessContainer in departmentVehicleNormalCardAccessContainerList)
            {
                var card = await departmentVehicleNormalCardAccessContainer.LoadCardAsync();
                String cmd = $"IDAUTH={card.Weigand}";
                cmdList.Add(cmd);
                
            }

            SendBatchPayload payloadToSend = new();
            payloadToSend.MessageId = Guid.NewGuid().ToString();
            payloadToSend.Type = "CMD";
            payloadToSend.Payload = cmdList;
            // fetch all vehicles for the department and send the command to each vehicle
            var vehicles = await department.LoadVehiclesAsync();
			foreach (var vehicle in vehicles)
			{
                var module = await vehicle.LoadModuleAsync();
                if (module != null)
                {
                    // Save message to database history table
                    var message = _serviceProvider.GetService<MessageHistoryDataObject>();
                    message.Vehicle = vehicle;

                    var user = await _authentication.GetCurrentUserClaimsAsync();

                    //message.WebsiteUserId = user.UserId.GetValueOrDefault();
                    // message.Message = JsonConvert.SerializeObject(payloadToSend);
                    message.MessageStatus = MessageStatusEnum.PENDING;
                    message.SentTimestamp = DateTime.UtcNow;
                    await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);


                    // send the questions to the module
                    _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payloadToSend));
                }
            }
            return new ComponentResponse<string>(default(System.String));
        }
        // Sync drivers per Site
        async System.Threading.Tasks.Task<ComponentResponse<string>> SyncDriversPerSiteAsync(SiteDataObject site, Dictionary<string, object> parameters = null)
		{
            var SiteVehicleNormalCardAccessContainerList = (await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { site.Id })).ToList();
            var cmdList = new List<string>();
            foreach (var SiteVehicleNormalCardAccessContainer in SiteVehicleNormalCardAccessContainerList)
            {
                var card = await SiteVehicleNormalCardAccessContainer.LoadCardAsync();
                String cmd = $"IDAUTH={card.Weigand}";
                cmdList.Add(cmd);

            }

            SendBatchPayload payloadToSend = new();
            payloadToSend.MessageId = Guid.NewGuid().ToString();
            payloadToSend.Type = "CMD";
            payloadToSend.Payload = cmdList;

            var departments = await site.LoadDepartmentItemsAsync();
            var vehicleTasks = departments.Select(async x => await x.LoadVehiclesAsync());
            var vehiclesArrays = await System.Threading.Tasks.Task.WhenAll(vehicleTasks);
            var vehicles = vehiclesArrays.SelectMany(x => x).ToList();

            foreach (var vehicle in vehicles)
            {
                var module = await vehicle.LoadModuleAsync();
                if (module != null)
                {
                    // Save message to database history table
                    var message = _serviceProvider.GetService<MessageHistoryDataObject>();
                    message.Vehicle = vehicle;

                    var user = await _authentication.GetCurrentUserClaimsAsync();

                    //message.WebsiteUserId = user.UserId.GetValueOrDefault();
                    // message.Message = JsonConvert.SerializeObject(payloadToSend);
                    message.MessageStatus = MessageStatusEnum.PENDING;
                    message.SentTimestamp = DateTime.UtcNow;
                    await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);

                    // send the questions to the module
                    _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payloadToSend));
                }
            }


            return new ComponentResponse<string>(default(System.String));
        }
        // Sync drivers per Model
        async System.Threading.Tasks.Task<ComponentResponse<string>> SyncDriversPerModelAsync(ModelDataObject model, Dictionary<string, object> parameters = null)
		{
            // Fetch the list of cards access for the model
            var ModelVehicleNormalCardAccessContainerList = (await _dataFacade.ModelVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "ModelId == @0", new object[] { model.Id })).ToList();
            var cmdList = new List<string>();
            foreach (var ModelVehicleNormalCardAccessContainer in ModelVehicleNormalCardAccessContainerList)
            {
                var card = await ModelVehicleNormalCardAccessContainer.LoadCardAsync();
                String cmd = $"IDAUTH={card.Weigand}";
                cmdList.Add(cmd);

            }

            // Populate the payload
            SendBatchPayload payloadToSend = new();
            payloadToSend.MessageId = Guid.NewGuid().ToString();
            payloadToSend.Type = "CMD";
            payloadToSend.Payload = cmdList;

            // TODO: need to check if models are assigned to sites or departments or else it will send to all vehicles with the same model globally
            // send the command to all vehicles with the same model on the same site/departments
            var vehicles = await model.LoadVehiclesAsync();
            foreach (var vehicle in vehicles)
            {
                var module = await vehicle.LoadModuleAsync();
                if (module != null)
                {
                    // Save message to database history table
                    var message = _serviceProvider.GetService<MessageHistoryDataObject>();
                    message.Vehicle = vehicle;

                    var user = await _authentication.GetCurrentUserClaimsAsync();

                    //message.WebsiteUserId = user.UserId.GetValueOrDefault();
                    // message.Message = JsonConvert.SerializeObject(payloadToSend);
                    message.MessageStatus = MessageStatusEnum.PENDING;
                    message.SentTimestamp = DateTime.UtcNow;
                    await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);

                    // send the questions to the module
                    _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payloadToSend));
                }
            }

            return new ComponentResponse<string>(default(System.String));
        }

        // sync vehicle access
        // This sync all the access for the vehicle
        async System.Threading.Tasks.Task<ComponentResponse<string>> SyncVehicleAccessAsync(VehicleDataObject vehicle, Dictionary<string, object> parameters = null)
        {
            var vehicleAccessList = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0", new object[] { vehicle.Id })).ToList();
            var ModelVehicleNormalCardAccessContainerList = (await _dataFacade.ModelVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "ModelId == @0", new object[] { vehicle.ModelId })).ToList();
            var SiteVehicleNormalCardAccessContainerList = (await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { (await vehicle.LoadDepartmentAsync()).SiteId })).ToList();
            var DepartmentVehicleNormalCardAccessContainerList = (await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "DepartmentId == @0", new object[] { vehicle.DepartmentId })).ToList();

            var cmdList = new List<string>();
            cmdList.Add($"IDBATCH");
            // Merge all the access list and remove duplicates
            foreach (var access in vehicleAccessList)
            {
                String cmd = $"IDAUTH={(await access.LoadCardAsync()).Weigand}";
                // check if the card is already in the list
                if (!cmdList.Contains(cmd))
                {
                    cmdList.Add(cmd);
                }
            }
            foreach (var access in ModelVehicleNormalCardAccessContainerList)
            {
                String cmd = $"IDAUTH={(await access.LoadCardAsync()).Weigand}";
                // check if the card is already in the list
                if (!cmdList.Contains(cmd))
                {
                    cmdList.Add(cmd);
                }
            }
            foreach (var access in SiteVehicleNormalCardAccessContainerList)
            {
                String cmd = $"IDAUTH={(await access.LoadCardAsync()).Weigand}";
                // check if the card is already in the list
                if (!cmdList.Contains(cmd))
                {
                    cmdList.Add(cmd);
                }
            }
            foreach (var access in DepartmentVehicleNormalCardAccessContainerList)
            {
                String cmd = $"IDAUTH={(await access.LoadCardAsync()).Weigand}";
                // check if the card is already in the list
                if (!cmdList.Contains(cmd))
                {
                    cmdList.Add(cmd);
                }
            }
            cmdList.Add($"IDSAVE");

            SendBatchPayload payloadToSend = new();
            payloadToSend.MessageId = Guid.NewGuid().ToString();
            payloadToSend.SessionId = Guid.NewGuid().ToString();
            payloadToSend.Type = "CMD";
            payloadToSend.Payload = cmdList;

            var module = await vehicle.LoadModuleAsync();
            if (module != null)
            {
                // Save message to database history table
                var message = _serviceProvider.GetService<MessageHistoryDataObject>();
                message.Vehicle = vehicle;

                var user = await _authentication.GetCurrentUserClaimsAsync();

                //message.WebsiteUserId = user.UserId.GetValueOrDefault();
                // message.Message = JsonConvert.SerializeObject(payloadToSend);
                message.MessageStatus = MessageStatusEnum.PENDING;
                message.SentTimestamp = DateTime.UtcNow;
                await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);

                // send the questions to the module
                _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payloadToSend));
            }
            return new ComponentResponse<string>(default(System.String));
        }

        // save/update driver access message
        async System.Threading.Tasks.Task<ComponentResponse<string>> SaveDriverAccessMessageAsync(SyncDataObject dataObject, String accessType, Dictionary<string, object> parameters = null)
        {
            SyncPayloadDataObjects payloadData = JsonConvert.DeserializeObject<SyncPayloadDataObjects>(dataObject.Payload);

            if (dataObject.EventType == "SAVE_DRIVER_NORMAL_ACCESS")
            {
               var cardDetails = (await _dataFacade.CardDataProvider.GetCollectionAsync(null, "DriverId == @0", new object[] { payloadData.Driver })).SingleOrDefault();
                var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadData.VehicleId })).SingleOrDefault();
                if (cardDetails == null)
                {
                    if(accessType == "PerVehicleNormalDriverAccess")
                    {
                        var vehicleAccess = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 AND CardId == @1", new object[] { vehicle.Id, cardDetails.Id })).SingleOrDefault();
                        var cmdList = new List<string>();
                        cmdList.Add($"IDAUTH={cardDetails.Weigand}");

                        SendBatchPayload payloadToSend = new();
                        payloadToSend.MessageId = Guid.NewGuid().ToString();
                        payloadToSend.SessionId = Guid.NewGuid().ToString();
                        payloadToSend.Type = "CMD";
                        payloadToSend.Payload = cmdList;

                        var module = await vehicle.LoadModuleAsync();
                        if (module != null)
                        {
                            // Save message to database history table
                            var message = _serviceProvider.GetService<MessageHistoryDataObject>();
                            message.Vehicle = vehicle;

                            var user = await _authentication.GetCurrentUserClaimsAsync();

                            //message.WebsiteUserId = user.UserId.GetValueOrDefault();
                            // message.Message = JsonConvert.SerializeObject(payloadToSend);
                            message.MessageStatus = MessageStatusEnum.PENDING;
                            message.SentTimestamp = DateTime.UtcNow;
                            await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);


                            // send the message to the module
                            _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payloadToSend));
                        }

                    }
                }
            }
            return new ComponentResponse<string>(default(System.String));
        }

        //Sync questions to vehicle
        async System.Threading.Tasks.Task<ComponentResponse<string>> SyncQuestionsToVehicleAsync(SyncDataObject dataObject, Dictionary<string, object> parameters = null)
        {
            SyncPayloadDataObjects payloadData = JsonConvert.DeserializeObject<SyncPayloadDataObjects>(dataObject.Payload);

            if (dataObject.EventType == "SYNC_QUESTIONS_TO_VEHICLE")
            {
                var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { payloadData.VehicleId })).SingleOrDefault();
                if (vehicle == null)
                {
                    throw new GOServerException($"unknow vehicle id {payloadData.VehicleId}");
                }

                var department = await vehicle.LoadDepartmentAsync();
                var vehList = await department.LoadVehiclesAsync();
                var preopChecklist = (await (await vehicle.LoadDepartmentAsync()).LoadSiteChecklistItemsAsync()).SingleOrDefault();
                if (preopChecklist == null)
                {
                    return new ComponentResponse<string>(new GOServerException($"No checklist items found for vehicle {vehicle.Id}").ToString());
                }

                var questions = new List<string>();

                int index = 1;

                foreach (var question in await preopChecklist.LoadPreOperationalChecklistsAsync())
                {
                    int type = question.Critical ? (question.ExpectedAnswer ? 1 : 2) : 0;

                    string questionString = $"OPCHK={question.Order},{question.Id},{type},0,{question.Question}";
                    questions.Add(questionString);

                    index++;
                }
                SendBatchPayload payload = new();
                payload.MessageId = Guid.NewGuid().ToString();
                payload.SessionId = Guid.NewGuid().ToString();
                payload.Type = "CMD";
                payload.Payload = questions;

                foreach (var veh in vehList)
                {
                    var module = await veh.LoadModuleAsync();
                    if (module != null)
                    {
                        // Save message to database history table
                        var message = _serviceProvider.GetService<MessageHistoryDataObject>();
                        message.Vehicle = veh;

                        var user = await _authentication.GetCurrentUserClaimsAsync();

                        //message.WebsiteUserId = user.UserId.GetValueOrDefault();
                        // message.Message = JsonConvert.SerializeObject(payload);
                        message.MessageStatus = MessageStatusEnum.PENDING;
                        message.SentTimestamp = DateTime.UtcNow;
                        await _dataFacade.MessageHistoryDataProvider.SaveAsync(message);

                        // send the questions to the module
                        _deviceMessageHandler.SendCloudToDeviceMessageAsync(module.IoTDevice, JsonConvert.SerializeObject(payload));
                    }
                }

            }
            return new ComponentResponse<string>(default(System.String));
        }

    }
}
