# FXQ-3103: Date Filter Fix Implementation Plan
## Pre-Op Check and Machine Unlock Report Date Filter Issues After Vehicle Transfer

### Problem Summary
Date filters in Pre-Op Check and Machine Unlock reports exhibit erratic and systematic behavior after performing a vehicle transfer between customers in the system. This results in loss of visibility of critical safety historical data and compromises complete report functionality.

### Root Cause Analysis
The issue stems from improper handling of vehicle hire/dehire history in date filtering logic, particularly when vehicles are transferred between customers. The current implementation does not correctly account for the temporal ownership of vehicles during date range queries.

---

## Phase 1: Analysis and Investigation (Estimated: 2 hours)

### Phase 1.1: Database Schema Analysis
- [ ] **Task 1.1.1**: Analyze VehicleHireDehireHistory table structure and relationships
  - **Deliverable**: Documentation of table schema and foreign key relationships
  - **Dependencies**: None

- [ ] **Task 1.1.2**: Examine current date filtering logic in GetTodaysPreopCheck stored procedure
  - **Deliverable**: Detailed analysis of current date range implementation
  - **Dependencies**: Task 1.1.1

- [ ] **Task 1.1.3**: Examine current date filtering logic in GetAllVehicleUnlocks stored procedure
  - **Deliverable**: Detailed analysis of current date range implementation
  - **Dependencies**: Task 1.1.1

- [ ] **Task 1.1.4**: Identify specific date filter failure patterns in stored procedures
  - **Deliverable**: List of specific issues in date range calculations
  - **Dependencies**: Tasks 1.1.2, 1.1.3

### Phase 1.2: Frontend Filter Analysis
- [ ] **Task 1.2.1**: Analyze PreOpCheckReportPageController date filter implementation
  - **Deliverable**: Documentation of current frontend date handling
  - **Dependencies**: None

- [ ] **Task 1.2.2**: Analyze MachineUnlockReportPageController date filter implementation
  - **Deliverable**: Documentation of current frontend date handling
  - **Dependencies**: None

---

## Phase 2: Core Database Fixes (Estimated: 3 hours)

### Phase 2.1: Fix GetTodaysPreopCheck Stored Procedure
- [ ] **Task 2.1.1**: Update date range logic to properly handle vehicle transfer scenarios
  - **Deliverable**: Modified stored procedure with corrected date filtering
  - **Dependencies**: Phase 1 complete

- [ ] **Task 2.1.2**: Implement proper timezone handling for transferred vehicles
  - **Deliverable**: Enhanced timezone calculation logic
  - **Dependencies**: Task 2.1.1
  - **Time**: 30 minutes

- [ ] **Task 2.1.3**: Add vehicle hire/dehire history validation in date queries
  - **Deliverable**: Updated query logic with proper historical ownership
  - **Dependencies**: Task 2.1.2

### Phase 2.2: Fix GetAllVehicleUnlocks Stored Procedure
- [ ] **Task 2.2.1**: Update date range logic to properly handle vehicle transfer scenarios
  - **Deliverable**: Modified stored procedure with corrected date filtering
  - **Dependencies**: Phase 1 complete

- [ ] **Task 2.2.2**: Implement proper timezone handling for transferred vehicles
  - **Deliverable**: Enhanced timezone calculation logic
  - **Dependencies**: Task 2.2.1

- [ ] **Task 2.2.3**: Add vehicle hire/dehire history validation in date queries
  - **Deliverable**: Updated query logic with proper historical ownership
  - **Dependencies**: Task 2.2.2

### Phase 2.3: Database Testing Infrastructure
- [ ] **Task 2.3.1**: Create test data scenarios for vehicle transfer date filtering
  - **Deliverable**: SQL scripts with test scenarios
  - **Dependencies**: Phase 2.1, 2.2 complete

---

## Phase 3: Frontend Filter Improvements (Estimated: 2 hours)

### Phase 3.1: Fix Pre-Op Check Report Frontend
- [ ] **Task 3.1.1**: Update PreOpCheckReportPageController date parameter handling
  - **Deliverable**: Enhanced date parameter validation and formatting
  - **Dependencies**: Phase 2 complete

- [ ] **Task 3.1.2**: Implement proper date range validation in frontend
  - **Deliverable**: Client-side date validation logic
  - **Dependencies**: Task 3.1.1

- [ ] **Task 3.1.3**: Add error handling for invalid date ranges
  - **Deliverable**: User-friendly error messages for date issues
  - **Dependencies**: Task 3.1.2

### Phase 3.2: Fix Machine Unlock Report Frontend
- [ ] **Task 3.2.1**: Update MachineUnlockReportPageController date parameter handling
  - **Deliverable**: Enhanced date parameter validation and formatting
  - **Dependencies**: Phase 2 complete

- [ ] **Task 3.2.2**: Implement proper date range validation in frontend
  - **Deliverable**: Client-side date validation logic
  - **Dependencies**: Task 3.2.1

- [ ] **Task 3.2.3**: Add error handling for invalid date ranges
  - **Deliverable**: User-friendly error messages for date issues
  - **Dependencies**: Task 3.2.2

---

## Phase 4: Data Layer Integration (Estimated: 1.5 hours)

### Phase 4.1: Update Data Providers
- [ ] **Task 4.1.1**: Update TodaysPreopCheckViewDataProvider to handle new date logic
  - **Deliverable**: Enhanced data provider with improved date handling
  - **Dependencies**: Phase 2, 3 complete

- [ ] **Task 4.1.2**: Update AllVehicleUnlocksViewDataProvider to handle new date logic
  - **Deliverable**: Enhanced data provider with improved date handling
  - **Dependencies**: Phase 2, 3 complete

### Phase 4.2: Add Logging and Monitoring
- [ ] **Task 4.2.1**: Implement comprehensive logging for date filter operations
  - **Deliverable**: Enhanced logging for debugging date filter issues
  - **Dependencies**: Phase 4.1 complete

---

## Phase 5: Testing and Validation (Estimated: 2.5 hours)

### Phase 5.1: Unit Testing
- [ ] **Task 5.1.1**: Create unit tests for updated stored procedures
  - **Deliverable**: Comprehensive unit test suite
  - **Dependencies**: Phase 4 complete

- [ ] **Task 5.1.2**: Create unit tests for updated data providers
  - **Deliverable**: Data provider unit tests
  - **Dependencies**: Task 5.1.1

- [ ] **Task 5.1.3**: Create unit tests for frontend date handling
  - **Deliverable**: Frontend unit tests
  - **Dependencies**: Task 5.1.2

### Phase 5.2: Integration Testing
- [ ] **Task 5.2.1**: Test Pre-Op Check report with vehicle transfer scenarios
  - **Deliverable**: Integration test results for Pre-Op Check
  - **Dependencies**: Phase 5.1 complete

- [ ] **Task 5.2.2**: Test Machine Unlock report with vehicle transfer scenarios
  - **Deliverable**: Integration test results for Machine Unlock
  - **Dependencies**: Task 5.2.1

### Phase 5.3: Performance Testing
- [ ] **Task 5.3.1**: Performance testing of updated stored procedures
  - **Deliverable**: Performance benchmarks and optimization recommendations
  - **Dependencies**: Phase 5.2 complete

---

## Phase 6: Documentation and Deployment (Estimated: 1 hour)

### Phase 6.1: Documentation
- [ ] **Task 6.1.1**: Update technical documentation for date filtering
  - **Deliverable**: Updated technical documentation
  - **Dependencies**: Phase 5 complete

- [ ] **Task 6.1.2**: Create user documentation for date filter usage
  - **Deliverable**: User guide for date filtering
  - **Dependencies**: Task 6.1.1

### Phase 6.2: Deployment Preparation
- [ ] **Task 6.2.1**: Create deployment scripts for database changes
  - **Deliverable**: Database migration scripts
  - **Dependencies**: Phase 6.1 complete

---

## Success Criteria

### Functional Requirements
- [ ] All date filters in Pre-Op Check report work correctly after vehicle transfers
- [ ] All date filters in Machine Unlock report work correctly after vehicle transfers
- [ ] "Today to Today" filter shows current day data accurately
- [ ] Custom date ranges include all available historical information
- [ ] No data loss occurs during vehicle transfer operations

### Performance Requirements
- [ ] Date filter queries execute within acceptable time limits (< 5 seconds)
- [ ] No significant performance degradation compared to current implementation
- [ ] Proper indexing is in place for date range queries

### Quality Requirements
- [ ] All unit tests pass
- [ ] All integration tests pass
- [ ] No regression in existing functionality
- [ ] Comprehensive error handling implemented
- [ ] Proper logging for debugging and monitoring

---

## Risk Mitigation

### Technical Risks
- **Risk**: Database performance degradation with new date logic
  - **Mitigation**: Implement proper indexing and query optimization
- **Risk**: Breaking changes to existing functionality
  - **Mitigation**: Comprehensive testing and gradual rollout

### Business Risks
- **Risk**: Extended downtime during deployment
  - **Mitigation**: Implement zero-downtime deployment strategy
- **Risk**: Data integrity issues during migration
  - **Mitigation**: Thorough backup and rollback procedures

---

## Estimated Timeline
- **Total Estimated Time**: 12 hours
- **Phase 1**: 2 hours (Analysis)
- **Phase 2**: 3 hours (Database fixes)
- **Phase 3**: 2 hours (Frontend improvements)
- **Phase 4**: 1.5 hours (Data layer integration)
- **Phase 5**: 2.5 hours (Testing)
- **Phase 6**: 1 hour (Documentation and deployment)

---

## Dependencies
- Access to development environment
- Database schema documentation
- Test data with vehicle transfer scenarios
- Deployment environment access

---

## Notes
- This implementation plan assumes the root cause is in the date filtering logic and vehicle hire/dehire history handling
- Additional investigation may be required during Phase 1 to confirm the exact cause
- The plan includes comprehensive testing to ensure no regression in existing functionality
- All changes should be thoroughly documented for future maintenance 