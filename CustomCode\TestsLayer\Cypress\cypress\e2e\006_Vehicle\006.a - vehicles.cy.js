describe("006a - Vehicles", () => {
    let uniqueSiteName;
    let cypressCompanyName;
    let uniqueDepartmentName;
    let uniqueVehicleId;
    let uniqueSerialNumber;
    let uniqueDeviceId;

    before(() => {
        cy.fixture('testData').then((testData) => {
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            cypressCompanyName = testData.cypressCompanyName;
            uniqueDepartmentName = testData.uniqueDepartmentName;
            uniqueVehicleId = testData.uniqueVehicleId + Math.random().toString(36).substring(2, 15);
            uniqueSerialNumber = testData.uniqueSerialNumber + Math.random().toString(36).substring(2, 15);
            uniqueDeviceId = testData.uniqueDeviceId + Math.random().toString(36).substring(2, 15);
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login()

        // Navigate to the vehicles section
        cy.get(`[data-bind="'enable' : navigation.isVehiclesEnabled(), 'visible' : navigation.isVehiclesVisible()"] > .nav-link`).click();
        cy.wait(1000);

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/customer/list*').as('getCustomerList');
    });

    it('should create a new vehicle', () => {
        // Click on the new vehicle button
        cy.get(`.topGridCommands > :nth-child(1) > .command-button`)
            .should('be.visible')
            .click();

        cy.wait(3000);

        cy.wait('@getCustomerList').then((interception) => {
            cy.log('Customer list API completed:', interception);
        });
        // Fill in the form fields using reliable selectors
        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsCustomerVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(cypressCompanyName, { force: true });

        cy.wait(1000);
        // select the first option 
        cy.get('[data-test-id="lookup_wrapper"] [data-test-id="lookup_item"]')
            .first()
            .should('be.visible')
            .click();


        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsSiteVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueSiteName, { force: true });

        // Wait for dropdown to appear and be ready
        cy.wait(1000);

        // Force Cypress to find the site dropdown item, even if it's detached from DOM
        cy.get('body').then($body => {
            // Use jQuery to find the dropdown item containing the site name text
            const $siteOption = $body.find(`[data-test-id="lookup_item"]:contains("${uniqueSiteName}")`);

            if ($siteOption.length) {
                // Use cy.wrap to get a Cypress-wrapped element and click it with force option
                cy.wrap($siteOption).first().click({ force: true });
            } else {
                // If we can't find the exact match, fall back to clicking the first item
                cy.get('[data-test-id="lookup_item"]')
                    .first()
                    .click({ force: true });
            }
        });

        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsDepartmentVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueDepartmentName, { force: true });

        // Wait for dropdown to appear and be ready
        cy.wait(1000);

        // Force Cypress to find the department dropdown item, even if it's detached from DOM
        cy.get('body').then($body => {
            // Use jQuery to find the dropdown item containing the department name text
            const $deptOption = $body.find(`[data-test-id="lookup_item"]:contains("${uniqueDepartmentName}")`);

            if ($deptOption.length) {
                // Use cy.wrap to get a Cypress-wrapped element and click it with force option
                cy.wrap($deptOption).first().click({ force: true });
            } else {
                // If we can't find the exact match, fall back to clicking the first item
                cy.get('[data-test-id="lookup_item"]')
                    .first()
                    .click({ force: true });
            }
        });

        // Wait for UI to update after selection
        cy.wait(1000);

        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsModelVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type('Order', { force: true });

        cy.wait(1000);

        // Force Cypress to find the model dropdown item, even if it's detached from DOM
        cy.get('body').then($body => {
            // Use jQuery to find the dropdown item containing the model name text
            const $modelOption = $body.find(`[data-test-id="lookup_item"]:contains("Order")`);

            if ($modelOption.length) {
                // Use cy.wrap to get a Cypress-wrapped element and click it with force option
                cy.wrap($modelOption).first().click({ force: true });
            } else {
                // If we can't find the exact match, fall back to clicking the first item
                cy.get('[data-test-id="lookup_item"]')
                    .first()
                    .click({ force: true });
            }
        });

        // ENTER VEHICLE ID
        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsHireNoVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueVehicleId, { force: true });

        // ENTER SERIAL NUMBER
        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsSerialNoVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueSerialNumber, { force: true });

        // ENTER DEVICE ID
        cy.get(`[data-bind="'visible':VehicleInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && VehicleInformationFormFormViewModel.StatusData.IsModule1Visible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueDeviceId, { force: true });

        // SAVE THE VEHICLE
        cy.get('.save')
            .should('be.visible')
            .click();

        // Wait for the save to complete
        cy.wait(1000);

        // Verify the vehicle was created successfully

    });
});
