﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{
    public class SyncPayloadDataObjects 
    {

        [JsonProperty("vehicle", NullValueHandling = NullValueHandling.Ignore)]
        public Guid VehicleId { get; set; }

        [JsonProperty("department", NullValueHandling = NullValueHandling.Ignore)]
        public Guid DepartmentId { get; set; }

        [JsonProperty("site", NullValueHandling = NullValueHandling.Ignore)]
        public Guid SiteId { get; set; }

        [JsonProperty("model", NullValueHandling = NullValueHandling.Ignore)]
        public Guid ModelId { get; set; }

        [JsonProperty("driver", NullValueHandling = NullValueHandling.Ignore)]
        public Guid Driver { get; set; }


        [JsonProperty("website_user", NullValueHandling = NullValueHandling.Ignore)]
        public Guid WebsiteUserId { get; set; }
    }
}
