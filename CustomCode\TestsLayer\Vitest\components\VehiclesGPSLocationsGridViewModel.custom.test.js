import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

// Mock Knockout
vi.mock('knockout', () => ({
    default: {
        observable: vi.fn((initialValue) => {
            let value = initialValue;
            const subscribers = [];
            const obs = function (newValue) {
                if (arguments.length > 0) {
                    value = newValue;
                    subscribers.forEach(callback => callback(value));
                }
                return value;
            };
            obs.isObservable = true;
            obs.subscribe = vi.fn((callback) => {
                subscribers.push(callback);
                return {
                    dispose: () => {
                        const index = subscribers.indexOf(callback);
                        if (index > -1) {
                            subscribers.splice(index, 1);
                        }
                    }
                };
            });
            return obs;
        }),
        observableArray: vi.fn((initialValue = []) => {
            let value = initialValue;
            const subscribers = [];
            const obs = function (newValue) {
                if (arguments.length > 0) {
                    value = newValue;
                    subscribers.forEach(callback => callback(value));
                }
                return value;
            };
            obs.isObservable = true;
            obs.subscribe = vi.fn((callback) => {
                subscribers.push(callback);
                return {
                    dispose: () => {
                        const index = subscribers.indexOf(callback);
                        if (index > -1) {
                            subscribers.splice(index, 1);
                        }
                    }
                };
            });
            return obs;
        }),
        isObservable: vi.fn((obj) => obj && obj.isObservable === true)
    }
}));

describe('VehiclesGPSLocationsGridViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehiclesGPSLocationsGridViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            VehicleObjectCollection: ko.observableArray([]),
            Events: {
                VehicleCollectionLoaded: ko.observable(false)
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.VehiclesGPSLocationsGridViewModelCustom(viewModel);
        customViewModel.initialize();

        // Trigger VehicleCollectionLoaded to ensure all subscriptions are processed
        viewModel.Events.VehicleCollectionLoaded(!viewModel.Events.VehicleCollectionLoaded());
    });

    describe('initialize', () => {
        it('should initialize mapPoints as an observable array', () => {
            expect(typeof viewModel.mapPoints).toBe('function');
            expect(Array.isArray(viewModel.mapPoints())).toBe(true);
            expect(viewModel.mapPoints()).toEqual([]);
        });

        it('should set up mapProvider with correct OpenStreetMap configuration', () => {
            expect(viewModel.mapProvider).toBeDefined();
            expect(viewModel.mapProvider.url).toBe('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png');
            expect(viewModel.mapProvider.attribution).toContain('OpenStreetMap');
        });
    });

    describe('VehicleCollectionLoaded event handling', () => {
        it('should update map points when valid GPS data is available', () => {
            const testData = [{
                Data: {
                    IsMarkedForDeletion: () => false,
                    HireNo: () => 'TEST123',
                    GPSDateTimeDisplay: () => '2024-01-01 12:00',
                    VehicleLastGPSLocationView: () => ({
                        Data: {
                            Latitude: () => 51.5074,
                            Longitude: () => -0.1278
                        }
                    }),
                    Model: () => ({
                        Data: {
                            Name: () => 'TestModel'
                        }
                    }),
                    Department: () => ({
                        Data: {
                            Name: () => 'TestDepartment'
                        }
                    })
                }
            }];

            viewModel.VehicleObjectCollection(testData);
            viewModel.Events.VehicleCollectionLoaded(!viewModel.Events.VehicleCollectionLoaded());

            const mapPoints = viewModel.mapPoints();
            expect(mapPoints).toHaveLength(1);
            expect(mapPoints[0]).toEqual(expect.objectContaining({
                lat: 51.5074,
                lng: -0.1278,
                icon: expect.objectContaining({
                    url: 'styles/images/forklift.png',
                    size: [70, 70],
                    anchor: [35, 35]
                })
            }));
            expect(mapPoints[0].info).toContain('TEST123');
            expect(mapPoints[0].info).toContain('TestModel');
            expect(mapPoints[0].info).toContain('TestDepartment');
        });

        it('should handle vehicles with missing GPS data', () => {
            const testData = [{
                Data: {
                    IsMarkedForDeletion: () => false,
                    VehicleLastGPSLocationView: () => null
                }
            }];

            viewModel.VehicleObjectCollection(testData);
            viewModel.Events.VehicleCollectionLoaded(!viewModel.Events.VehicleCollectionLoaded());

            expect(viewModel.mapPoints()).toHaveLength(0);
        });

        it('should handle vehicles marked for deletion', () => {
            const testData = [{
                Data: {
                    IsMarkedForDeletion: () => true,
                    VehicleLastGPSLocationView: () => ({
                        Data: {
                            Latitude: () => 51.5074,
                            Longitude: () => -0.1278
                        }
                    })
                }
            }];

            viewModel.VehicleObjectCollection(testData);
            viewModel.Events.VehicleCollectionLoaded(!viewModel.Events.VehicleCollectionLoaded());

            expect(viewModel.mapPoints()).toHaveLength(0);
        });

        it('should handle vehicles with invalid GPS coordinates', () => {
            const testData = [{
                Data: {
                    IsMarkedForDeletion: () => false,
                    VehicleLastGPSLocationView: () => ({
                        Data: {
                            Latitude: () => null,
                            Longitude: () => null
                        }
                    })
                }
            }];

            viewModel.VehicleObjectCollection(testData);
            viewModel.Events.VehicleCollectionLoaded(!viewModel.Events.VehicleCollectionLoaded());

            expect(viewModel.mapPoints()).toHaveLength(0);
        });

        it('should handle multiple vehicles with valid GPS data', () => {
            const testData = [
                {
                    Data: {
                        IsMarkedForDeletion: () => false,
                        HireNo: () => 'TEST1',
                        GPSDateTimeDisplay: () => '2024-01-01 12:00',
                        VehicleLastGPSLocationView: () => ({
                            Data: {
                                Latitude: () => 51.5074,
                                Longitude: () => -0.1278
                            }
                        }),
                        Model: () => ({
                            Data: {
                                Name: () => 'Model1'
                            }
                        }),
                        Department: () => ({
                            Data: {
                                Name: () => 'Dept1'
                            }
                        })
                    }
                },
                {
                    Data: {
                        IsMarkedForDeletion: () => false,
                        HireNo: () => 'TEST2',
                        GPSDateTimeDisplay: () => '2024-01-01 12:00',
                        VehicleLastGPSLocationView: () => ({
                            Data: {
                                Latitude: () => 51.5075,
                                Longitude: () => -0.1279
                            }
                        }),
                        Model: () => ({
                            Data: {
                                Name: () => 'Model2'
                            }
                        }),
                        Department: () => ({
                            Data: {
                                Name: () => 'Dept2'
                            }
                        })
                    }
                }
            ];

            viewModel.VehicleObjectCollection(testData);
            viewModel.Events.VehicleCollectionLoaded(!viewModel.Events.VehicleCollectionLoaded());

            const mapPoints = viewModel.mapPoints();
            expect(mapPoints).toHaveLength(2);
            expect(mapPoints[0].info).toContain('TEST1');
            expect(mapPoints[1].info).toContain('TEST2');
        });
    });
}); 