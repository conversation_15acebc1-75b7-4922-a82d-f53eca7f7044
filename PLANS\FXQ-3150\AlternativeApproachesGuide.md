# Alternative Approaches to Staging Tables - Comprehensive Guide
## Avoiding Permanent Database Schema Changes

### Overview

This guide provides detailed alternative approaches to using permanent staging tables for bulk data import operations. All approaches avoid creating permanent database tables while maintaining high performance and data integrity.

---

## Approach 1: Session-Scoped Temporary Tables (RECOMMENDED)

### Description
Use SQL Server temporary tables (`#tablename`) that are automatically scoped to the database session and cleaned up when the session ends.

### Implementation
```sql
-- Create temporary table within stored procedure
CREATE TABLE #ImportData (
    Id UNIQUEIDENTIFIER DEFAULT NEWID(),
    ExternalId NVARCHAR(50) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    -- Add indexes for merge performance
    INDEX IX_ExternalId NONCLUSTERED (ExternalId)
);

-- Use SqlBulkCopy to populate
-- Then merge to production tables
MERGE dbo.Production AS Target
USING #ImportData AS Source ON Target.ExternalId = Source.ExternalId
WHEN NOT MATCHED THEN INSERT...
WHEN MATCHED THEN UPDATE...
```

### Advantages
- ✅ No permanent schema changes
- ✅ Supports SqlBulkCopy for high performance
- ✅ Automatic cleanup when session ends
- ✅ Can create custom indexes for merge performance
- ✅ Handles large datasets (limited by tempdb)
- ✅ Full transaction support

### Use Cases
- Large bulk imports (10K+ records)
- Complex data validation before merge
- When SqlBulkCopy performance is required
- Multi-step processing workflows

---

## Approach 2: Table Variables with Memory Optimization

### Description
Use table variables (`@table`) for smaller datasets that can fit in memory efficiently.

### Implementation
```sql
DECLARE @ImportData TABLE (
    Id UNIQUEIDENTIFIER DEFAULT NEWID(),
    ExternalId NVARCHAR(50) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    INDEX IX_ExternalId NONCLUSTERED (ExternalId)
);

-- Populate via INSERT statements or Table-Valued Parameters
INSERT INTO @ImportData (ExternalId, Name)
VALUES ('EXT001', 'John Doe'), ('EXT002', 'Jane Smith');

-- Merge to production
MERGE dbo.Production AS Target
USING @ImportData AS Source ON Target.ExternalId = Source.ExternalId
-- ... merge logic
```

### C# Implementation with Table-Valued Parameters
```csharp
public async Task ImportUsingTableVariableAsync(IEnumerable<ImportRow> data)
{
    var dataTable = new DataTable();
    dataTable.Columns.Add("ExternalId", typeof(string));
    dataTable.Columns.Add("Name", typeof(string));
    
    foreach (var row in data)
    {
        dataTable.Rows.Add(row.ExternalId, row.Name);
    }
    
    using var connection = new SqlConnection(_connectionString);
    using var command = new SqlCommand("ProcessImportData", connection);
    command.CommandType = CommandType.StoredProcedure;
    command.Parameters.AddWithValue("@ImportData", dataTable);
    
    await connection.OpenAsync();
    await command.ExecuteNonQueryAsync();
}
```

### Advantages
- ✅ No permanent schema changes
- ✅ Automatic cleanup
- ✅ Good for smaller datasets (<10K records)
- ✅ Can be passed to stored procedures
- ✅ Memory-efficient for small data

### Limitations
- ❌ Cannot use SqlBulkCopy directly
- ❌ Size limitations (memory-based)
- ❌ Less suitable for very large datasets

---

## Approach 3: Common Table Expressions (CTEs) with VALUES

### Description
Use CTEs for immediate processing of small to medium datasets without any temporary storage.

### Implementation
```sql
WITH ImportSource AS (
    SELECT * FROM (VALUES
        ('EXT001', 'John', 'Doe', 'Customer1'),
        ('EXT002', 'Jane', 'Smith', 'Customer1'),
        ('EXT003', 'Bob', 'Johnson', 'Customer2')
    ) AS Source(ExternalId, FirstName, LastName, CustomerName)
),
ValidatedData AS (
    SELECT 
        NEWID() AS Id,
        s.ExternalId,
        s.FirstName,
        s.LastName,
        c.Id AS CustomerId
    FROM ImportSource s
    INNER JOIN dbo.Customer c ON c.CompanyName = s.CustomerName
    WHERE s.FirstName IS NOT NULL AND s.LastName IS NOT NULL
)
MERGE dbo.Driver AS Target
USING ValidatedData AS Source ON Target.ExternalId = Source.ExternalId
WHEN NOT MATCHED THEN
    INSERT (Id, ExternalId, FirstName, LastName, CustomerId)
    VALUES (Source.Id, Source.ExternalId, Source.FirstName, Source.LastName, Source.CustomerId)
WHEN MATCHED THEN
    UPDATE SET FirstName = Source.FirstName, LastName = Source.LastName;
```

### C# Dynamic Query Builder
```csharp
public async Task ImportUsingCTEAsync(IEnumerable<ImportRow> data)
{
    var valuesList = data.Select(row => 
        $"('{row.ExternalId}', '{row.FirstName}', '{row.LastName}', '{row.CustomerName}')"
    );
    
    var valuesClause = string.Join(",\n        ", valuesList);
    
    var sql = $@"
        WITH ImportSource AS (
            SELECT * FROM (VALUES
                {valuesClause}
            ) AS Source(ExternalId, FirstName, LastName, CustomerName)
        ),
        ValidatedData AS (
            SELECT 
                NEWID() AS Id,
                s.ExternalId,
                s.FirstName,
                s.LastName,
                c.Id AS CustomerId
            FROM ImportSource s
            INNER JOIN dbo.Customer c ON c.CompanyName = s.CustomerName
        )
        MERGE dbo.Driver AS Target
        USING ValidatedData AS Source ON Target.ExternalId = Source.ExternalId
        WHEN NOT MATCHED THEN INSERT...
        WHEN MATCHED THEN UPDATE...
    ";
    
    using var connection = new SqlConnection(_connectionString);
    await connection.OpenAsync();
    await connection.ExecuteAsync(sql);
}
```

### Advantages
- ✅ No temporary storage required
- ✅ Single atomic operation
- ✅ Good for immediate processing
- ✅ No cleanup needed

### Limitations
- ❌ Limited by SQL statement size
- ❌ Not suitable for large datasets
- ❌ Less flexibility for complex validation

---

## Approach 4: In-Memory Collections with Batched Processing

### Description
Process data in application memory using collections, then batch insert/update operations.

### Implementation
```csharp
public class BatchedImportService
{
    private readonly string _connectionString;
    private const int BATCH_SIZE = 1000;
    
    public async Task ImportDriversBatchedAsync(IEnumerable<DriverImportRow> drivers)
    {
        var batches = drivers.Batch(BATCH_SIZE);
        
        foreach (var batch in batches)
        {
            await ProcessBatchAsync(batch);
        }
    }
    
    private async Task ProcessBatchAsync(IEnumerable<DriverImportRow> batch)
    {
        using var connection = new SqlConnection(_connectionString);
        await connection.OpenAsync();
        
        using var transaction = connection.BeginTransaction();
        try
        {
            // Process each item in the batch
            foreach (var driver in batch)
            {
                await UpsertDriverAsync(connection, transaction, driver);
            }
            
            transaction.Commit();
        }
        catch
        {
            transaction.Rollback();
            throw;
        }
    }
    
    private async Task UpsertDriverAsync(
        SqlConnection connection, 
        SqlTransaction transaction, 
        DriverImportRow driver)
    {
        var sql = @"
            MERGE dbo.Driver AS Target
            USING (VALUES (@ExternalId, @FirstName, @LastName)) AS Source(ExternalId, FirstName, LastName)
            ON Target.ExternalId = Source.ExternalId
            WHEN NOT MATCHED THEN
                INSERT (Id, ExternalId, FirstName, LastName)
                VALUES (NEWID(), Source.ExternalId, Source.FirstName, Source.LastName)
            WHEN MATCHED THEN
                UPDATE SET FirstName = Source.FirstName, LastName = Source.LastName;
        ";
        
        await connection.ExecuteAsync(sql, driver, transaction);
    }
}
```

### Advantages
- ✅ Full control over processing logic
- ✅ Good error handling and retry capability
- ✅ Memory efficient with streaming
- ✅ Flexible validation rules

### Limitations
- ❌ Slower than bulk operations
- ❌ More complex error handling
- ❌ Higher database connection overhead

---

## Approach 5: File-Based Processing with BULK INSERT

### Description
Use SQL Server's built-in file processing capabilities for CSV/text files.

### Implementation
```sql
-- Create temporary table
CREATE TABLE #FileImport (
    LineData NVARCHAR(MAX)
);

-- Bulk insert file data
BULK INSERT #FileImport
FROM 'C:\Import\drivers.csv'
WITH (
    FIELDTERMINATOR = ',',
    ROWTERMINATOR = '\n',
    FIRSTROW = 2 -- Skip header
);

-- Parse and process
WITH ParsedData AS (
    SELECT 
        TRIM(PARSENAME(REPLACE(LineData, ',', '.'), 4)) AS ExternalId,
        TRIM(PARSENAME(REPLACE(LineData, ',', '.'), 3)) AS FirstName,
        TRIM(PARSENAME(REPLACE(LineData, ',', '.'), 2)) AS LastName
    FROM #FileImport
    WHERE LineData IS NOT NULL
)
MERGE dbo.Driver AS Target
USING ParsedData AS Source ON Target.ExternalId = Source.ExternalId
-- ... merge logic
```

### Advantages
- ✅ Direct file processing
- ✅ Good performance for large files
- ✅ No application memory usage
- ✅ Built-in SQL Server functionality

### Limitations
- ❌ Requires file system access
- ❌ Limited parsing flexibility
- ❌ Security considerations for file access

---

## Performance Comparison

| Approach | Dataset Size | Performance | Memory Usage | Complexity | Schema Impact |
|----------|--------------|-------------|--------------|------------|---------------|
| **Temporary Tables** | Large (1M+) | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ✅ None |
| **Table Variables** | Medium (10K) | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | ✅ None |
| **CTEs** | Small (1K) | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ✅ None |
| **Batched Processing** | Any | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ✅ None |
| **File Processing** | Large (1M+) | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ✅ None |

---

## Recommendations by Use Case

### Large-Scale Bulk Import (100K+ records)
**Recommended**: Temporary Tables with SqlBulkCopy
- Highest performance
- Comprehensive validation
- Automatic cleanup

### Medium-Scale Import (1K-10K records)
**Recommended**: Table Variables with Table-Valued Parameters
- Good performance
- Simple implementation
- Memory efficient

### Real-time/Small Imports (<1K records)
**Recommended**: CTEs or Batched Processing
- Immediate processing
- Simple atomic operations
- Low overhead

### File-Based Import
**Recommended**: Temporary Tables + File Processing
- Direct file access
- High performance
- Good for scheduled imports

---

## Best Practices

1. **Always use transactions** for data integrity
2. **Implement comprehensive validation** before committing
3. **Use appropriate batch sizes** (typically 1K-10K records)
4. **Monitor tempdb usage** for large temporary table operations
5. **Implement proper error handling** and rollback procedures
6. **Log import sessions** for audit trails
7. **Test with realistic data volumes** before production deployment
8. **Consider connection pooling** for multiple batch operations

---

## Conclusion

The temporary table approach (Approach 1) provides the best balance of performance, reliability, and maintainability for most bulk import scenarios while completely avoiding permanent database schema changes. It should be the default choice for the FXQ-3150 implementation, with other approaches used for specific edge cases or requirements.
