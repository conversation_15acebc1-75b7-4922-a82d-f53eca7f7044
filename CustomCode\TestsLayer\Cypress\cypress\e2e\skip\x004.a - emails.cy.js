describe("004 - Emails Flow", () => {

    beforeEach(() => {
        // Perform the login using the login command
        cy.login();

        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/emailgroups/list*').as('getEmailGroups');
    });

    it("tests Emails Flow", () => {

        // Navigate to the site after login
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(2) span")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the first row link
        cy.get("tr:nth-of-type(1) > td:nth-of-type(1) > a")
            .should('exist')
            .should('be.visible')
            .click();

        // Click on the Emails tab
        cy.get("[data-test-id='tab_link_1de6fc96-1527-45d5-a4f7-70f7417bb564'] > a > span:nth-of-type(1)")
            .should('exist')
            .should('be.visible')
            .click();

        // Edit the first email group
        cy.get("[data-test-id='\\38 c941a81-bf45-4300-bb60-e0bcdd7c02e8']")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the edit button for the email group
        cy.get("[data-test-id='edit_6c6d9eb6-749a-4b6b-9810-2b9e2aa7a75e']")
            .should('exist')
            .should('be.visible')
            .click()
            .clear()
            .type("New Email Group");

        // Edit the "Test EMail Group" field
        cy.get("[data-test-id='edit_71b14fa4-2181-47eb-9ed9-c5d484ebb5d9']")
            .should('exist')
            .should('be.visible')
            .clear()
            .type("Test EMail Group");

        // Save the email group changes
        cy.get("[data-test-id='\\36 1c1f8f5-3422-45cc-8343-d952cbfd2d65']")
            .should('exist')
            .should('be.visible')
            .click();

        // Wait for the email groups API call to complete
        cy.wait('@getEmailGroups').then((interception) => {
            cy.log('Email groups API call completed:', interception);

            // Click to view the updated group
            //cy.get("[data-test-id='\\31 de6fc96-1527-45d5-a4f7-70f7417bb564'] tr:nth-of-type(2) > td")
            //    .should('exist')
            //    .should('be.visible')
            //    .click();
        });

        //// Click to edit the new email group
        //cy.get("[data-test-id='aeea1399-beab-4dcd-941b-b2955f15a65e']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Edit the email group name (Updated)
        //cy.get("[data-test-id='edit_3f7246e3-909b-46cf-8759-b18dd88b67d9']")
        //    .should('exist')
        //    .should('be.visible')
        //    .clear()
        //    .type("New Email Group (Updated)");

        //// Confirm and apply the changes
        //cy.get("[data-test-id='\\32 25d0c8d-d565-447d-bef5-87c47827c0db'] div.gridCommandContainer > div")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        // Save the changes
        //cy.get("[data-test-id='\\37 67db5fd-b7f1-47da-ad4b-17fa42b9d04d']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Open the lookup input and select the fourth item
        //cy.get("#popupContainer [data-test-id='lookup_input']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //cy.get("li:nth-of-type(4) > [data-test-id='lookup_item']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //// Final save and close the overlay
        //cy.get("[data-test-id='fb67f2b8-53cf-436e-ad4c-a06194e5beaf']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //cy.get("[data-test-id='edit_3f7246e3-909b-46cf-8759-b18dd88b67d9']")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //cy.get("#Commands > div")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();

        //cy.get("a.overlayClose > svg")
        //    .should('exist')
        //    .should('be.visible')
        //    .click();
    });
});
