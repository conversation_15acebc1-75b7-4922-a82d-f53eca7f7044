using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class ReportActionsAPITest : TestBase
    {
        private IReportActionsAPI _reportActionsAPI;
        private IDataFacade _dataFacade;
        private IAuthentication _authentication;
        private readonly string _testDatabaseName = $"ReportActionsAPITest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            _authentication = Substitute.For<IAuthentication>();
            services.AddTransient<IAuthentication>(sp => _authentication);
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _reportActionsAPI = _serviceProvider.GetRequiredService<IReportActionsAPI>();

            CreateTestDatabase(_testDatabaseName);

            // Run the seed script after database creation
            string sqlFileRootPath = "../../../../../../Scripts/OneOffScripts";
            string seedReportTypesPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "SeedReportTypes.sql"));
            if (!File.Exists(seedReportTypesPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedReportTypesPath}\"");
            }
            _databaseFactory.RunSqlScript(_testDatabaseName, File.ReadAllText(seedReportTypesPath));

            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            // Create region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            // Create timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            // Create 3 sites for the customer
            for (int i = 0; i < 3; i++)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.Id = Guid.NewGuid();
                site.Name = $"Test Site {i + 1}";
                site.CustomerId = customer.Id;
                site.TimezoneId = timeZone.Id;
                site.Active = true;
                site.UnlockSetting = true;
                await _dataFacade.SiteDataProvider.SaveAsync(site);
            }

            // Create a department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customer.Id })).First().Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create a GOUser
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "password123";
            await _dataFacade.GOUserDataProvider.SaveAsync(goUser);

            // Create a person with GOUser
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.FirstName = "Test";
            person.LastName = "Person";
            person.SiteId = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customer.Id })).First().Id;
            person.DepartmentId = department.Id;
            person.CustomerId = customer.Id;
            person.IsActiveDriver = true;
            person.IsDriver = true;
            person.NormalDriverAccess = true;
            person.VehicleAccess = true;
            person.GOUserId = goUser.Id;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Setup authentication mock to return the user claims
            var userClaims = new UserClaims { UserId = goUser.Id };
            _authentication.GetCurrentUserClaimsAsync().Returns(userClaims);
        }

        [Test]
        public async Task SaveAsync_WithPerson_SetsPersonId()
        {
            // Arrange
            var reportType = 10; // Current Status Report from seed script
            var reportSubscription = _serviceProvider.GetRequiredService<ReportSubscriptionDataObject>();
            reportSubscription.Id = Guid.NewGuid();
            reportSubscription.Subject = "Test Report Subscription";
            reportSubscription.StartDate = DateTime.Today;
            reportSubscription.ReportTypeId = (await _dataFacade.ReportTypeDataProvider.GetCollectionAsync(null, "ReportType == @0", new object[] { reportType })).First().Id;
            reportSubscription.Frequency = 0; // Daily
            reportSubscription.ReportGenerationTime = HourOfDayEnum.Eight; // 8 AM
            var parameters = new Dictionary<string, object>();

            // Get the person and GOUser from test data
            var person = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(person, Is.Not.Null, "Test data setup failed: No person found");
            Assert.That(person.GOUserId, Is.Not.EqualTo(Guid.Empty), "Person should have a GOUserId");

            // Act
            var response = await _reportActionsAPI.SaveAsync(reportSubscription, reportType, parameters);

            // Assert
            Assert.That(response.Result.PersonId, Is.EqualTo(person.Id), "PersonId should be set to the current user's person ID");
            Assert.That(response.Result.GOUserId, Is.EqualTo(person.GOUserId), "GOUserId should be set to the current user's ID");
        }
    }
} 