import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('PreOpCheckReportPageController.custom.js', () => {
    let mockController, mockPreOpReportFilterFormViewModel, mockCurrentObject, mockData;
    let mockApplicationController, mockViewModel, mockSecurity, mockCurrentUserClaims;
    let customControllerInstance;

    beforeEach(() => {
        // Mock the data
        mockData = {
            CustomerId: vi.fn().mockReturnValue(null),
            StartDate: vi.fn().mockReturnValue(null),
            EndDate: vi.fn().mockReturnValue(null),
            ResultType: vi.fn().mockReturnValue(null),
            MultiSearch: vi.fn().mockReturnValue(null)
        };

        // Mock CurrentObject
        mockCurrentObject = {
            Data: mockData
        };

        // Mock the PreOpReportFilterFormViewModel
        mockPreOpReportFilterFormViewModel = {
            CurrentObject: vi.fn().mockReturnValue(mockCurrentObject),
            filterData: vi.fn()
        };

        // Mock the currentUserClaims
        mockCurrentUserClaims = {
            CustomerId: null,
            AllowedSiteIds: '{}',
            role: null
        };

        // Mock the security
        mockSecurity = {
            currentUserClaims: vi.fn().mockReturnValue(mockCurrentUserClaims)
        };

        // Mock the viewModel
        mockViewModel = {
            security: mockSecurity
        };

        // Mock the applicationController
        mockApplicationController = {
            viewModel: mockViewModel
        };

        // Mock the ShowError function
        const mockShowError = vi.fn();

        // Mock the controller
        mockController = {
            PreOpReportFilterFormViewModel: mockPreOpReportFilterFormViewModel,
            applicationController: mockApplicationController,
            ShowError: mockShowError,
            IncompletedChecklistViewReportViewModel: {
                LoadIncompletedChecklistViewObjectCollection: vi.fn()
            },
            ChecklistStatusViewViewModel: {
                LoadChecklistStatusViewObjectCollection: vi.fn()
            },
            AllChecklistResultViewGrid1ViewModel: {
                exportFilterPredicate: null,
                exportFilterParameters: null,
                LoadAllChecklistResultViewObjectCollection: vi.fn(),
                FILTER_NAME: 'testFilterName'
            }
        };

        // Create a global mock for sessionStorage
        global.sessionStorage = {
            getItem: vi.fn().mockReturnValue(null),
            setItem: vi.fn(),
            removeItem: vi.fn()
        };

        // Create a global mock for window.location
        global.window = {
            location: {
                reload: vi.fn()
            }
        };

        // Create a global namespace for FleetXQ
        global.FleetXQ = {
            Web: {
                Controllers: {}
            }
        };

        // Create mock for GO.Filter
        global.GO = {
            Filter: {
                hasUrlFilter: vi.fn().mockReturnValue(false)
            }
        };

        // Import the controller
        require('../../../WebApplicationLayer/wwwroot/Controllers/PreopCheckReportPageController.custom.js');
        
        // Create an instance
        customControllerInstance = new FleetXQ.Web.Controllers.PreOpCheckReportPageControllerCustom(mockController);
        
        // Call initialize
        customControllerInstance.initialize();
    });

    describe('DealerAdmin validation', () => {
        it('should allow filtering when user is DealerAdmin and customer is selected', () => {
            // Set up the test
            mockCurrentUserClaims.role = 'DealerAdmin';
            mockData.CustomerId.mockReturnValue('test-customer-id');
            
            // Trigger filterData
            mockController.PreOpReportFilterFormViewModel.filterData();
            
            // Expect the data to load because customer is selected
            expect(mockController.IncompletedChecklistViewReportViewModel.LoadIncompletedChecklistViewObjectCollection).toHaveBeenCalled();
            expect(mockController.ShowError).not.toHaveBeenCalled();
        });

        it('should show error when user is DealerAdmin and no customer is selected', () => {
            // Set up the test
            mockCurrentUserClaims.role = 'DealerAdmin';
            mockData.CustomerId.mockReturnValue(null);
            
            // Trigger filterData
            mockController.PreOpReportFilterFormViewModel.filterData();
            
            // Expect error to be shown and data not to load
            expect(mockController.ShowError).toHaveBeenCalledWith('Please select a customer');
            expect(mockController.IncompletedChecklistViewReportViewModel.LoadIncompletedChecklistViewObjectCollection).not.toHaveBeenCalled();
        });

        it('should allow filtering when user is DealerAdmin and customer is empty string', () => {
            // Set up the test
            mockCurrentUserClaims.role = 'DealerAdmin';
            mockData.CustomerId.mockReturnValue('');
            
            // Trigger filterData
            mockController.PreOpReportFilterFormViewModel.filterData();
            
            // Expect error to be shown and data not to load
            expect(mockController.ShowError).toHaveBeenCalledWith('Please select a customer');
            expect(mockController.IncompletedChecklistViewReportViewModel.LoadIncompletedChecklistViewObjectCollection).not.toHaveBeenCalled();
        });

        it('should allow filtering when user is not DealerAdmin', () => {
            // Set up the test
            mockCurrentUserClaims.role = 'SomeOtherRole';
            mockData.CustomerId.mockReturnValue(null);
            
            // Trigger filterData
            mockController.PreOpReportFilterFormViewModel.filterData();
            
            // Expect the data to load regardless of customer selection
            expect(mockController.IncompletedChecklistViewReportViewModel.LoadIncompletedChecklistViewObjectCollection).toHaveBeenCalled();
            expect(mockController.ShowError).not.toHaveBeenCalled();
        });
    });

    describe('Configuration generation', () => {
        it('should generate proper configuration when data is provided', () => {
            // Set up test data
            mockData.CustomerId.mockReturnValue('test-customer-id');
            mockData.StartDate.mockReturnValue(new Date('2023-01-01'));
            mockData.EndDate.mockReturnValue(new Date('2023-01-31'));
            mockData.ResultType.mockReturnValue(1);
            mockData.MultiSearch.mockReturnValue('search term');
            
            // Generate configuration
            const config = customControllerInstance.getConfiguration();
            
            // Parse parameters
            const params = JSON.parse(config.filterParameters);
            
            // Verify configuration
            expect(config.filterPredicate).toContain('CustomerId == @0');
            expect(config.filterPredicate).toContain('StartDate == @1');
            expect(config.filterPredicate).toContain('EndDate == @2');
            expect(config.filterPredicate).toContain('ResultType == @3');
            expect(params.length).toBe(4);
            expect(params[0].Value).toBe('test-customer-id');
            
            // Test multi-search addition
            const configWithMultiSearch = customControllerInstance.addMultiSearchFilter(config);
            const paramsWithSearch = JSON.parse(configWithMultiSearch.filterParameters);
            
            expect(configWithMultiSearch.filterPredicate).toContain('MultiSearch == @4');
            expect(paramsWithSearch[4].Value).toBe('search term');
        });
    });
}); 