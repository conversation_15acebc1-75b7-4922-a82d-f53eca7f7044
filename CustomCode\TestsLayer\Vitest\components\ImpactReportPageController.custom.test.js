import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import fs from 'fs';
import path from 'path';

// Mock the global objects and namespaces needed
global.FleetXQ = {
  Web: {
    Controllers: {}
  }
};

global.GO = {
  Filter: {
    hasUrlFilter: vi.fn().mockReturnValue(false)
  }
};

// Read and evaluate the actual controller file
const controllerFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/Controllers/ImpactReportPageController.custom.js');
const controllerFileContent = fs.readFileSync(controllerFilePath, 'utf8');
eval(controllerFileContent);

describe('ImpactReportPageController', () => {
  let controller;
  let mockApplicationController;
  let mockViewModelSecurity;
  let mockDataObjects;
  let mockReportFilterFormViewModel;
  let customController;
  let originalFilterDataCommand;
  let mockShowError;
  
  beforeEach(() => {
    // Setup mocks
    mockDataObjects = {
      CustomerId: vi.fn(),
      SiteId: vi.fn(),
      DepartmentId: vi.fn(),
      StartDate: vi.fn(),
      EndDate: vi.fn(),
      ImpactLevel: vi.fn(),
      MultiSearch: vi.fn().mockReturnValue(null)
    };
    
    // Mock ShowError function to track calls
    mockShowError = vi.fn();
    
    // Create original FilterDataCommand to track if it was called
    originalFilterDataCommand = vi.fn();
    
    mockReportFilterFormViewModel = {
      CurrentObject: vi.fn().mockReturnValue({
        Data: mockDataObjects
      }),
      filterData: null,
      ShowError: mockShowError,
      Commands: {
        FilterDataCommand: originalFilterDataCommand
      }
    };
    
    mockViewModelSecurity = {
      currentUserClaims: vi.fn().mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: null
      })
    };
    
    mockApplicationController = {
      viewModel: {
        security: mockViewModelSecurity
      }
    };
    
    controller = {
      applicationController: mockApplicationController,
      ImpactReportFilterFormViewModel: mockReportFilterFormViewModel,
      ImpactFrequencyPerWeekMonthViewReportViewModel: {
        LoadImpactFrequencyPerWeekMonthViewObjectCollection: vi.fn()
      },
      ImpactFrequencyPerWeekDayViewReportViewModel: {
        LoadImpactFrequencyPerWeekDayViewObjectCollection: vi.fn()
      },
      ImpactFrequencyPerTimeSlotViewReportViewModel: {
        LoadImpactFrequencyPerTimeSlotViewObjectCollection: vi.fn()
      },
      AllImpactsViewGridViewModel: {
        LoadAllImpactsViewObjectCollection: vi.fn(),
        exportFilterPredicate: null,
        exportFilterParameters: null
      }
    };
    
    // Create instance of the controller we're testing
    customController = new FleetXQ.Web.Controllers.ImpactReportPageControllerCustom(controller);
    
    // Mock sessionStorage
    global.sessionStorage = {
      getItem: vi.fn().mockReturnValue('true'),
      setItem: vi.fn(),
      removeItem: vi.fn()
    };
    
    // Mock window.location
    global.window = {
      location: {
        reload: vi.fn()
      }
    };
    
    // Initialize the controller
    customController.initialize();
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  // HAPPY PATH TESTS
  
  describe('Happy Path Tests', () => {
    it('should correctly generate configuration with customerId and dates', () => {
      // Setup test data
      const customerId = '12345678-1234-1234-1234-123456789012';
      const startDate = '2023-01-01T00:00:00';
      const endDate = '2023-01-31T00:00:00';
      
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.SiteId.mockReturnValue(null);
      mockDataObjects.DepartmentId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(startDate);
      mockDataObjects.EndDate.mockReturnValue(endDate);
      mockDataObjects.ImpactLevel.mockReturnValue(null);
      
      // Generate configuration
      const config = customController.getConfiguration();
      
      // Check if configuration contains correct parameters
      expect(config.filterPredicate).toContain('CustomerId ==');
      expect(config.filterPredicate).toContain('StartDate ==');
      expect(config.filterPredicate).toContain('EndDate ==');
      
      // Verify parameter values
      const params = JSON.parse(config.filterParameters);
      expect(params[0].Value).toBe(customerId);
      expect(params[1].Value).toBe(startDate);
      expect(params[2].Value).toBe(endDate);
    });
    
    it('should correctly handle filter with only customerId', () => {
      // Setup test data
      const customerId = '12345678-1234-1234-1234-123456789012';
      
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.SiteId.mockReturnValue(null);
      mockDataObjects.DepartmentId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(null);
      mockDataObjects.EndDate.mockReturnValue(null);
      mockDataObjects.ImpactLevel.mockReturnValue(null);
      
      // Generate configuration
      const config = customController.getConfiguration();
      
      // Check if configuration contains only customerId
      expect(config.filterPredicate).toBe('CustomerId == @0');
      
      // Verify parameter values
      const params = JSON.parse(config.filterParameters);
      expect(params.length).toBe(1);
      expect(params[0].Value).toBe(customerId);
    });
  });
  
  // DEALERADMIN CUSTOMER VALIDATION TESTS
  describe('DealerAdmin Customer Validation', () => {
    // HAPPY PATH - DealerAdmin with customer selected
    it('should allow filtering when DealerAdmin has selected a customer', () => {
      // Setup test data
      const customerId = '12345678-1234-1234-1234-123456789012';
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      
      // Set user as DealerAdmin
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['DealerAdmin']
      });
      
      // Call the filter command directly
      controller.ImpactReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that:
      // 1. ShowError was not called (no error shown)
      expect(mockShowError).not.toHaveBeenCalled();
      
      // 2. Original FilterDataCommand was called (filter continues)
      expect(originalFilterDataCommand).toHaveBeenCalled();
    });

    // UNHAPPY PATH - DealerAdmin with no customer selected
    it('should block filtering and show error when DealerAdmin has not selected a customer', () => {
      // Setup test data - no customer selected
      mockDataObjects.CustomerId.mockReturnValue(null);
      
      // Set user as DealerAdmin
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['DealerAdmin']
      });
      
      // Call the filter command directly
      controller.ImpactReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that:
      // 1. ShowError was called with correct error message
      expect(mockShowError).toHaveBeenCalledWith("Please select a customer", "Error");
      
      // 2. Original FilterDataCommand was NOT called (filter stopped)
      expect(originalFilterDataCommand).not.toHaveBeenCalled();
    });

    // HAPPY PATH - Other roles don't need customer selected
    it('should allow filtering for non-DealerAdmin roles even without customer selected', () => {
      // Setup test data - no customer selected
      mockDataObjects.CustomerId.mockReturnValue(null);
      
      // Set user with a different role
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['StandardUser']
      });
      
      // Call the filter command directly
      controller.ImpactReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that:
      // 1. ShowError was not called (no error shown)
      expect(mockShowError).not.toHaveBeenCalled();
      
      // 2. Original FilterDataCommand was called (filter continues)
      expect(originalFilterDataCommand).toHaveBeenCalled();
    });

    // EDGE CASE - Empty string for customerId should be treated as not selected
    it('should block filtering when DealerAdmin has empty string for customerId', () => {
      // Setup test data - customer is empty string
      mockDataObjects.CustomerId.mockReturnValue("");
      
      // Set user as DealerAdmin
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['DealerAdmin']
      });
      
      // Call the filter command directly
      controller.ImpactReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that error was shown and filter was blocked
      expect(mockShowError).toHaveBeenCalled();
      expect(originalFilterDataCommand).not.toHaveBeenCalled();
    });
  });
  
  // CONFIGURATION TESTS
  describe('getConfiguration', () => {
    it('should include all filter parameters when they exist', () => {
      // Setup
      const customerId = '12345678-1234-1234-1234-123456789012';
      const siteId = '87654321-4321-4321-4321-210987654321';
      const departmentId = '11111111-2222-3333-4444-555555555555';
      const startDate = '2023-01-01T00:00:00';
      const endDate = '2023-01-31T00:00:00';
      const impactLevel = 3;
      
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.SiteId.mockReturnValue(siteId);
      mockDataObjects.DepartmentId.mockReturnValue(departmentId);
      mockDataObjects.StartDate.mockReturnValue(startDate);
      mockDataObjects.EndDate.mockReturnValue(endDate);
      mockDataObjects.ImpactLevel.mockReturnValue(impactLevel);
      
      // Execute
      const config = customController.getConfiguration();
      
      // Verify
      expect(config.filterPredicate).toContain('CustomerId == @0');
      expect(config.filterPredicate).toContain('SiteId == @1');
      expect(config.filterPredicate).toContain('DepartmentId == @2');
      expect(config.filterPredicate).toContain('StartDate == @3');
      expect(config.filterPredicate).toContain('EndDate == @4');
      expect(config.filterPredicate).toContain('ImpactLevel == @5');
      
      const params = JSON.parse(config.filterParameters);
      expect(params.length).toBe(6);
      expect(params[0].Value).toBe(customerId);
      expect(params[1].Value).toBe(siteId);
      expect(params[2].Value).toBe(departmentId);
      expect(params[3].Value).toBe(startDate);
      expect(params[4].Value).toBe(endDate);
      expect(params[5].Value).toBe(impactLevel);
    });
  });
}); 