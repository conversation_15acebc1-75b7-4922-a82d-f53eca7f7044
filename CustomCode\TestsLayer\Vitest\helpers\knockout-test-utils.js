import ko from 'knockout'

/**
 * Creates a test container and applies a Knockout viewmodel to it
 * @param {Object} viewModel - The Knockout viewmodel to apply
 * @param {string} template - The HTML template to render
 * @returns {HTMLElement} The container element
 */
export function applyBindings(viewModel, template) {
  const container = document.createElement('div')
  container.innerHTML = template
  document.body.appendChild(container)
  ko.applyBindings(viewModel, container)
  return container
}

/**
 * Cleans up Knockout bindings
 * @param {HTMLElement} element - The element to clean up
 */
export function cleanupBindings(element) {
  ko.cleanNode(element)
  if (element.parentNode) {
    element.parentNode.removeChild(element)
  }
}