# Running VehicleAccessProcessor Locally

## Prerequisites

### 1. Install Required Tools

```bash
# Install .NET 7 SDK (if not already installed)
# Download from: https://dotnet.microsoft.com/download/dotnet/7.0

# Install Azure Functions Core Tools v4
npm install -g azure-functions-core-tools@4 --unsafe-perm true

# Install Azurite (local storage emulator)
npm install -g azurite

# Verify installations
func --version
azurite --version
dotnet --version
```

### 2. Configure Local Settings

Update `local.settings.json` with your actual connection strings:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "ServiceBusConnection": "Endpoint=sb://dev-eastus-fxqevents.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=y0gZy0BqnXHYBsUmkaS5csmxKyWppP+1h+ASbMMy43Q=",
    "VehicleAccessQueue": "vehicle-access-creation",
    "MainConnectionString": "data source=(local); initial catalog=fxq_us;integrated security=SSPI; MultipleActiveResultSets=True",
    "SecretKey": "development-secret-key-for-functions",
    "SessionTokenTimeout": "60",
    "ShowExceptionDetails": "true"
  }
}
```

## 🚀 **Running Locally**

### Step 1: Start Storage Emulator

```bash
# Start Azurite in a separate terminal
azurite --silent --location ./data --debug ./debug.log
```

### Step 2: Build the Function

```bash
# Navigate to the function directory
cd VehicleAccessProcessor

# Restore dependencies and build
dotnet restore
dotnet build
```

### Step 3: Start the Function Host

```bash
# Start the Azure Functions runtime
func start
```

You should see output like:

```
Azure Functions Core Tools
Core Tools Version:       4.0.5030 Commit hash: N/A  (64-bit)
Function Runtime Version: 4.21.3.20404

[2024-01-XX XX:XX:XX] Host initialized (XXXms)
[2024-01-XX XX:XX:XX] Host started (XXXms)
[2024-01-XX XX:XX:XX] Job host started

Functions:
        VehicleAccessProcessor: serviceBusTrigger
```

## 🧪 **Testing Locally**

### Option 1: Send Test Message via Code

Create a test console app to send messages:

```csharp
using Azure.Messaging.ServiceBus;
using System.Text.Json;

var connectionString = "your-service-bus-connection-string";
var queueName = "vehicle-access-creation";

await using var client = new ServiceBusClient(connectionString);
await using ServiceBusSender sender = client.CreateSender(queueName);

var testMessage = new
{
    VehicleId = Guid.NewGuid(),
    CustomerId = Guid.NewGuid(),
    ModelId = Guid.NewGuid(),
    DepartmentId = Guid.NewGuid(),
    SiteId = Guid.NewGuid(),
    IsNewVehicle = true,
    IoTDevice = "test-device-001",
    CreatedAt = DateTime.UtcNow,
    RetryCount = 0,
    MaxRetries = 3
};

var messageBody = JsonSerializer.Serialize(testMessage);
var serviceBusMessage = new ServiceBusMessage(messageBody)
{
    SessionId = testMessage.VehicleId.ToString(),
    MessageId = Guid.NewGuid().ToString()
};

await sender.SendMessageAsync(serviceBusMessage);
Console.WriteLine($"Sent test message for vehicle: {testMessage.VehicleId}");
```

### Option 2: Use Service Bus Explorer

1. Install Service Bus Explorer
2. Connect to your Service Bus namespace
3. Navigate to the `vehicle-access-creation` queue
4. Send a test message with this JSON payload:

```json
{
  "VehicleId": "123e4567-e89b-12d3-a456-************",
  "CustomerId": "123e4567-e89b-12d3-a456-************",
  "ModelId": "123e4567-e89b-12d3-a456-************",
  "DepartmentId": "123e4567-e89b-12d3-a456-************",
  "SiteId": "123e4567-e89b-12d3-a456-************",
  "IsNewVehicle": true,
  "IoTDevice": "test-device",
  "CreatedAt": "2024-01-15T10:30:00Z",
  "RetryCount": 0,
  "MaxRetries": 3
}
```

### Option 3: Trigger from Main Application

1. Run your main FleetXQ application locally
2. Create a new vehicle through the UI
3. Watch the Function logs for processing

## 📊 **Monitoring Local Execution**

### Function Logs

You'll see detailed logs in the Function host output:

```
[2024-01-XX XX:XX:XX] Executing 'VehicleAccessProcessor' (Reason='', Id=xxx-xxx-xxx)
[2024-01-XX XX:XX:XX] [PERF] Processing vehicle access creation message: xxx-xxx-xxx
[2024-01-XX XX:XX:XX] [PERF] Starting vehicle access creation for vehicle xxx-xxx-xxx
[2024-01-XX XX:XX:XX] [PERF] Access creation completed for vehicle xxx-xxx-xxx in XXXms
[2024-01-XX XX:XX:XX] Executed 'VehicleAccessProcessor' (Succeeded, Id=xxx-xxx-xxx, Duration=XXXms)
```

### Database Verification

Check that access records were created:

```sql
-- Check model access records
SELECT COUNT(*) FROM ModelVehicleNormalCardAccess 
WHERE ModelId = 'your-test-model-id' AND DepartmentId = 'your-test-department-id'

-- Check vehicle access records  
SELECT COUNT(*) FROM PerVehicleNormalCardAccess 
WHERE VehicleId = 'your-test-vehicle-id'
```

## 🐛 **Troubleshooting**

### Common Issues

**1. Function won't start**
```bash
# Check .NET version
dotnet --version

# Clean and rebuild
dotnet clean
dotnet build
```

**2. Service Bus connection issues**
```bash
# Test connection string format
# Should start with: Endpoint=sb://...
```

**3. Database connection issues**
```bash
# Test database connectivity
sqlcmd -S "(local)" -d "fxq_us" -E -Q "SELECT 1"
```

**4. Storage emulator issues**
```bash
# Clear storage emulator data
azurite --clean
```

### Enable Detailed Logging

Add to `host.json`:

```json
{
  "version": "2.0",
  "logging": {
    "logLevel": {
      "default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Azure.WebJobs": "Information",
      "Microsoft.Azure.WebJobs.ServiceBus": "Debug"
    }
  }
}
```

## 🔄 **Development Workflow**

1. **Make code changes** in VehicleAccessCreationService.cs or VehicleAccessProcessorFunction.cs
2. **Build**: `dotnet build`
3. **Restart function host**: `Ctrl+C` then `func start`
4. **Test**: Send test message
5. **Monitor**: Watch logs and database

## 📝 **Performance Testing**

To test performance improvements locally:

1. Create multiple test vehicles simultaneously
2. Monitor processing times in logs
3. Compare with synchronous processing times
4. Verify all access records are created correctly

## 🚀 **Next Steps**

Once local testing is successful:
1. Deploy to Azure Function App
2. Update production configuration
3. Monitor production performance
4. Set up alerts and monitoring 