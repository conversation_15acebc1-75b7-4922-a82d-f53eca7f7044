@echo off
setlocal enabledelayedexpansion

:: FleetXQ Targeted Build Script
:: This script performs a clean build of specific projects in the correct order
:: Usage: build-targeted.cmd [run] [release]
::   run     - Start the web application after building
::   release - Build in Release configuration (default: Debug)

set "RUN_APP=false"
set "CONFIGURATION=Debug"

:: Parse command line arguments
:parse_args
if "%~1"=="" goto :args_done
if /i "%~1"=="run" set "RUN_APP=true"
if /i "%~1"=="release" set "CONFIGURATION=Release"
shift
goto :parse_args
:args_done

echo ========================================
echo FleetXQ Targeted Build Script
if "%RUN_APP%"=="true" (
    echo ^(Build and Run Mode^)
) else (
    echo ^(Build Only Mode^)
)
echo Configuration: %CONFIGURATION%
echo ========================================
echo.

:: Set error handling
set "SCRIPT_ERROR=0"

:: Define paths
set "SOLUTION_FILE=FleetXQ.sln"
set "CONSTRUCT_VIEWS_PROJECT=GeneratedCode\ConstructViews\FleetXQ.ConstructViews.csproj"
set "WEB_APP_PROJECT=GeneratedCode\WebApplicationLayer\FleetXQ.Application.Web.csproj"

:: Check if dotnet CLI is available
echo [1/5] Checking .NET CLI availability...
dotnet --version >nul 2>&1
if !ERRORLEVEL! neq 0 (
    echo ERROR: .NET CLI is not available. Please install .NET SDK.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo .NET CLI is available.
echo.

:: Step 1: Clean the solution
echo [2/5] Cleaning solution...
dotnet clean "%SOLUTION_FILE%" --configuration %CONFIGURATION% --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to clean solution.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo Solution cleaned successfully.
echo.

:: Step 2: Restore packages for the solution
echo [3/5] Restoring NuGet packages...
dotnet restore "%SOLUTION_FILE%" --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to restore NuGet packages.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo NuGet packages restored successfully.
echo.

:: Step 3: Build ConstructViews project first
echo [4/5] Building FleetXQ.ConstructViews project...
dotnet build "%CONSTRUCT_VIEWS_PROJECT%" --configuration %CONFIGURATION% --no-restore --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to build FleetXQ.ConstructViews project.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo FleetXQ.ConstructViews project built successfully.
echo.

:: Step 4: Build Web Application project
echo [5/5] Building FleetXQ.Application.Web project...
dotnet build "%WEB_APP_PROJECT%" --configuration %CONFIGURATION% --no-restore --verbosity minimal
if !ERRORLEVEL! neq 0 (
    echo ERROR: Failed to build FleetXQ.Application.Web project.
    set "SCRIPT_ERROR=1"
    goto :error_exit
)
echo FleetXQ.Application.Web project built successfully.
echo.

:: Success
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Built projects:
echo   1. %CONSTRUCT_VIEWS_PROJECT%
echo   2. %WEB_APP_PROJECT%
echo.

:: Step 5: Run the web application if requested
if "%RUN_APP%"=="true" (
    echo [6/6] Starting web application...
    echo Web application will be available at:
    echo   - https://localhost:5001 ^(HTTPS^)
    echo   - http://localhost:5000 ^(HTTP^)
    echo.
    echo Press Ctrl+C to stop the application
    echo.

    pushd "GeneratedCode\WebApplicationLayer"
    dotnet run --configuration %CONFIGURATION% --urls "https://localhost:5001;http://localhost:5000" --no-build
    popd
)

goto :end

:error_exit
echo.
echo ========================================
echo BUILD FAILED!
echo ========================================
echo Please check the error messages above and fix any issues.
echo.

:end
exit /b %SCRIPT_ERROR%
