import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('QuestionListViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/PreOperationalChecklist/QuestionListViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            commands: {
                isRemoveItemCommandVisible: ko.observable(true)
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.QuestionListViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('initialize', () => {
        it('should set isRemoveItemCommandVisible to false', () => {
            // Verify that the computed property returns false
            expect(viewModel.commands.isRemoveItemCommandVisible()).toBe(false);
        });

        it('should override the property with a computed that always returns false', () => {
            // Verify that the property returns false even if we try to change it
            viewModel.commands.isRemoveItemCommandVisible(true);
            expect(viewModel.commands.isRemoveItemCommandVisible()).toBe(false);

            viewModel.commands.isRemoveItemCommandVisible(false);
            expect(viewModel.commands.isRemoveItemCommandVisible()).toBe(false);
        });
    });
}); 