-- ===================================================================
-- One-off script: Populate VehicleLockout.VehicleId from related Session
-- Rationale: Column VehicleId was added to dbo.VehicleLockout; backfill existing rows
-- Author: AI Assistant
-- Date: 2025-08-09
-- ===================================================================

SET NOCOUNT ON;

BEGIN TRY
    BEGIN TRANSACTION;

    -- Pre-update diagnostics
    PRINT 'Counting VehicleLockout rows with NULL VehicleId and non-NULL SessionId...';
    SELECT 
        TotalVehicleLockouts = COUNT(1),
        NullVehicleIdCount = SUM(CASE WHEN vl.VehicleId IS NULL THEN 1 ELSE 0 END),
        NonNullSessionIdCount = SUM(CASE WHEN vl.SessionId IS NOT NULL THEN 1 ELSE 0 END)
    FROM dbo.VehicleLockout vl WITH (NOLOCK);

    PRINT 'Counting candidates where Session.VehicleId is available...';
    SELECT 
        CandidatesToUpdate = COUNT(1)
    FROM dbo.VehicleLockout vl WITH (NOLOCK)
    INNER JOIN dbo.[Session] s WITH (NOLOCK) ON s.Id = vl.SessionId
    WHERE vl.VehicleId IS NULL
      AND s.VehicleId IS NOT NULL;

    -- Perform the update
    PRINT 'Updating VehicleLockout.VehicleId from Session.VehicleId...';
    UPDATE vl
    SET vl.VehicleId = s.VehicleId
    FROM dbo.VehicleLockout vl
    INNER JOIN dbo.[Session] s ON s.Id = vl.SessionId
    WHERE vl.VehicleId IS NULL
      AND s.VehicleId IS NOT NULL;

    PRINT 'Update complete. Verifying remaining NULL VehicleId rows...';
    SELECT 
        RemainingNullVehicleId = COUNT(1)
    FROM dbo.VehicleLockout vl WITH (NOLOCK)
    WHERE vl.VehicleId IS NULL;

    -- Optional: sample of any rows still NULL after update (diagnostics)
    SELECT TOP 50 
        vl.Id AS VehicleLockoutId,
        vl.SessionId,
        s.VehicleId AS SessionVehicleId
    FROM dbo.VehicleLockout vl WITH (NOLOCK)
    LEFT JOIN dbo.[Session] s WITH (NOLOCK) ON s.Id = vl.SessionId
    WHERE vl.VehicleId IS NULL
    ORDER BY vl.LockoutTime DESC;

    COMMIT TRANSACTION;
END TRY
BEGIN CATCH
    IF XACT_STATE() <> 0 ROLLBACK TRANSACTION;

    DECLARE @ErrMsg NVARCHAR(4000) = ERROR_MESSAGE();
    DECLARE @ErrSev INT = ERROR_SEVERITY();
    DECLARE @ErrState INT = ERROR_STATE();
    RAISERROR(@ErrMsg, @ErrSev, @ErrState);
END CATCH;

-- Final verification
PRINT 'Final verification of VehicleLockout VehicleId population:';
SELECT 
    TotalVehicleLockouts = COUNT(1),
    NullVehicleIdCount = SUM(CASE WHEN vl.VehicleId IS NULL THEN 1 ELSE 0 END)
FROM dbo.VehicleLockout vl WITH (NOLOCK);


