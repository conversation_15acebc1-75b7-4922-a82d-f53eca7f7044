﻿
<Project>
  <!-- Auto-Publishing Target for Framework Projects -->
  <Target Name="AutoPublishToLocalFeed" AfterTargets="Pack">
    <PropertyGroup>
      <LocalNuGetFeed Condition="'$(LocalNuGetFeed)' == ''">C:\dev\nuget-local</LocalNuGetFeed>
      <PackageOutputPath Condition="'$(PackageOutputPath)' == ''">$(MSBuildProjectDirectory)\bin\$(Configuration)\</PackageOutputPath>
    </PropertyGroup>
    
    <!-- Only include the package from this specific build (current package only) -->
    <!-- This prevents pushing all historical packages and improves performance -->
    <ItemGroup>
      <CurrentPackageFiles Include="$(PackageOutputPath)$(PackageId).$(PackageVersion).nupkg" 
                           Condition="Exists('$(PackageOutputPath)$(PackageId).$(PackageVersion).nupkg')" />
      <CurrentSymbolFiles Include="$(PackageOutputPath)$(PackageId).$(PackageVersion).snupkg" 
                          Condition="Exists('$(PackageOutputPath)$(PackageId).$(PackageVersion).snupkg')" />
    </ItemGroup>
    
    <!-- Ensure local feed directory exists -->
    <MakeDir Directories="$(LocalNuGetFeed)" Condition="!Exists('$(LocalNuGetFeed)')" />
    
    <!-- Auto-publish Debug builds to local feed (current package only for performance) -->
    <Exec Command="dotnet nuget push &quot;%(CurrentPackageFiles.Identity)&quot; --source &quot;$(LocalNuGetFeed)&quot; --skip-duplicate" 
          ContinueOnError="true"
          Condition="'$(Configuration)' == 'Debug' AND @(CurrentPackageFiles->Count()) > 0 AND '$(PublishToLocalFeed)' != 'false'" />
          
    <!-- Copy symbol packages directly to local feed (folder feeds don't support proper symbol package pushing) -->
    <Copy SourceFiles="@(CurrentSymbolFiles)" 
          DestinationFolder="$(LocalNuGetFeed)" 
          SkipUnchangedFiles="true"
          ContinueOnError="true"
          Condition="'$(Configuration)' == 'Debug' AND @(CurrentSymbolFiles->Count()) > 0 AND '$(PublishToLocalFeed)' != 'false'" />
  </Target>
  
  <!-- Optional: Clean old packages to save disk space -->
  <Target Name="CleanOldPackages" BeforeTargets="Pack" Condition="'$(CleanOldPackages)' == 'true'">
    <PropertyGroup>
      <PackageOutputPath Condition="'$(PackageOutputPath)' == ''">$(MSBuildProjectDirectory)\bin\$(Configuration)\</PackageOutputPath>
      <MaxPackagesToKeep Condition="'$(MaxPackagesToKeep)' == ''">3</MaxPackagesToKeep>
    </PropertyGroup>
    
    <!-- Clean old packages using PowerShell (keep only recent ones) -->
    <Exec Command="powershell -ExecutionPolicy Bypass -Command &quot;&amp; { try { Get-ChildItem '$(PackageOutputPath)' -Filter '*.nupkg' -ErrorAction SilentlyContinue | Sort-Object CreationTime -Descending | Select-Object -Skip $(MaxPackagesToKeep) | Remove-Item -Force -ErrorAction SilentlyContinue } catch { Write-Host 'Package cleanup skipped' } }&quot;"
          ContinueOnError="true" />
          
    <Exec Command="powershell -ExecutionPolicy Bypass -Command &quot;&amp; { try { Get-ChildItem '$(PackageOutputPath)' -Filter '*.snupkg' -ErrorAction SilentlyContinue | Sort-Object CreationTime -Descending | Select-Object -Skip $(MaxPackagesToKeep) | Remove-Item -Force -ErrorAction SilentlyContinue } catch { Write-Host 'Symbol package cleanup skipped' } }&quot;"
          ContinueOnError="true" />
  </Target>
</Project> 
