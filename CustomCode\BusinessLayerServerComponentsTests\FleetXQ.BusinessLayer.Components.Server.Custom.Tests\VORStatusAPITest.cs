using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.Data.DataProvidersExtensions.Custom;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class VORStatusAPITest : TestBase
    {
        private IVORStatusAPI _vorStatusAPI;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"VORStatusAPITest-{Guid.NewGuid()}";
        private ILoggingService _logger;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            services.AddScoped<VORStatusAPI>();
            services.AddScoped<IVORStatusAPI, VORStatusAPI>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _vorStatusAPI = _serviceProvider.GetRequiredService<IVORStatusAPI>();
            _logger = _serviceProvider.GetRequiredService<ILoggingService>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task ProcessConvorMessageAsync_ValidMessage_UpdatesVORStatus()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).First();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).First();
            var model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null)).First();

            var (module, vehicle) = await TestUtilities.CreateTestModuleAndVehicleAsync(
                _dataFacade,
                _serviceProvider,
                customer.Id,
                department.Id,
                model.Id,
                site.Id,
                "Test Vehicle",
                "Test Serial No",
                "CCID1",
                $"test_{Guid.NewGuid()}");

            // Create a person and card for testing
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.IsDriver = true;
            person.IsActiveDriver = true;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.FacilityCode = "123";
            card.CardNumber = "456789";
            card.Active = true;
            card.KeypadReader = KeypadReaderEnum.Rosslare;
            card.Type = CardTypeEnum.CardID;
            card.Weigand = "123456";
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var driver = person.Driver;
            driver.CardDetailsId = card.Id;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Get master permission and create access
            var permissionMaster = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master })).SingleOrDefault();
            var perVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            perVehicleAccess.Id = Guid.NewGuid();
            perVehicleAccess.CardId = card.Id;
            perVehicleAccess.VehicleId = vehicle.Id;
            perVehicleAccess.PermissionId = permissionMaster.Id;
            await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(perVehicleAccess);

            // Create a valid CONVOR message
            var message = new
            {
                event_type = "CONVOR",
                payload = $"CONVOR={card.Weigand},66D0068C,1",
                session_id = "00000000-0000-0000-0000-**********",
                IoTDeviceId = module.IoTDevice
            };

            // Act
            var response = await _vorStatusAPI.ProcessConvorMessageAsync(JsonConvert.SerializeObject(message), new Dictionary<string, object>());

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<dynamic>(response.Result);
            Assert.That((int)responseObj.status, Is.EqualTo(200));
            Assert.That((string)responseObj.detail, Is.EqualTo("Success"));

            // Verify VOR status was updated
            var updatedVehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicle.Id })).SingleOrDefault();
            var otherSettings = await updatedVehicle.LoadVehicleOtherSettingsAsync();
            Assert.That(otherSettings.VORStatus, Is.True);

            // Verify VOR setting history was created with correct person
            var vorSettings = await _dataFacade.VORSettingHistoryDataProvider.GetCollectionAsync(
                null,
                "VehicleId == @0",
                new object[] { vehicle.Id }
            );
            Assert.That(vorSettings, Is.Not.Null);
            Assert.That(vorSettings.Count(), Is.EqualTo(1));
            var vorSetting = vorSettings.First();
            Assert.That(vorSetting.Status, Is.EqualTo(VORStatusEnum.VOREnabled));
            Assert.That(vorSetting.PersonId, Is.EqualTo(person.Id));
            Assert.That(vorSetting.EndDateTime, Is.Null);
        }

        [Test]
        public async Task ProcessConvorMessageAsync_InvalidIoTDeviceId_ThrowsException()
        {
            // Arrange
            var message = new
            {
                event_type = "CONVOR",
                payload = "CONVOR=123,66D0068C,1",
                session_id = "00000000-0000-0000-0000-**********",
                IoTDeviceId = "invalid_device_id"
            };

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () =>
                await _vorStatusAPI.ProcessConvorMessageAsync(JsonConvert.SerializeObject(message), new Dictionary<string, object>()));
            Assert.That(ex.Message, Is.EqualTo("Invalid IoTDeviceId"));
        }

        [Test]
        public async Task ProcessConvorMessageAsync_InvalidPayload_ThrowsException()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).First();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).First();
            var model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null)).First();

            var (module, _) = await TestUtilities.CreateTestModuleAndVehicleAsync(
                _dataFacade,
                _serviceProvider,
                customer.Id,
                department.Id,
                model.Id,
                site.Id,
                "Test Vehicle",
                "Test Serial No",
                "CCID1",
                $"test_{Guid.NewGuid()}");

            var message = new
            {
                event_type = "INVALID",
                payload = "INVALID_PAYLOAD",
                session_id = "00000000-0000-0000-0000-**********",
                IoTDeviceId = module.IoTDevice
            };

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () =>
                await _vorStatusAPI.ProcessConvorMessageAsync(JsonConvert.SerializeObject(message), new Dictionary<string, object>()));
            Assert.That(ex.Message, Is.EqualTo("Invalid Payload"));
        }

        private async Task CreateTestDataAsync()
        {
            // Create test data similar to DeviceTwinHandlerTest
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = customer.Id;
            site.Name = "Test Site";
            site.TimezoneId = timeZone.Id;
            site.Id = Guid.NewGuid();
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Description";
            model.DealerId = dealer.Id;
            model.Type = ModelTypesEnum.Electric;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);
        }
    }
} 