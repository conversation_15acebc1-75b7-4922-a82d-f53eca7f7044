import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

describe('PedestrianDetectionReportPageController', () => {
    let controller;
    let customController;

    beforeEach(() => {
        // Mock FleetXQ global object
        global.FleetXQ = {
            Web: {
                Controllers: {
                    PedestrianDetectionReportPageControllerCustom: function(ctrl) {
                        this.controller = ctrl;

                        this.getDefaultConfiguration = function() {
                            var configuration = {};
                            configuration.filterPredicate = "(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)";
                            configuration.filterParameters = JSON.stringify([{
                                "TypeName": "System.Guid",
                                "IsNullable": false,
                                "Value": this.controller.applicationController.viewModel.security.currentUserClaims().CustomerId
                            }]);
                            return configuration;
                        };

                        this.getConfiguration = function() {
                            var configuration = {};
                            configuration.filterPredicate = "";
                            configuration.filterParameters = [];

                            var filter = this.controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
                            var parameterCount = 0;

                            var customerId = this.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
                            if (filter.CustomerId() == null && customerId != null) {
                                return this.getDefaultConfiguration();
                            }

                            if (filter.CustomerId()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "(Driver.Person.CustomerId == @" + parameterCount + " OR Vehicle.CustomerId == @" + parameterCount + ")";
                                configuration.filterParameters.push({
                                    "TypeName": "System.Guid",
                                    "IsNullable": false,
                                    "Value": filter.CustomerId()
                                });
                                parameterCount++;
                            }

                            if (filter.SiteId()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "(Driver.Person.SiteId == @" + parameterCount + " OR Vehicle.SiteId == @" + parameterCount + ")";
                                configuration.filterParameters.push({
                                    "TypeName": "System.Guid",
                                    "IsNullable": false,
                                    "Value": filter.SiteId()
                                });
                                parameterCount++;
                            }

                            if (filter.DepartmentId()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "(Driver.Person.DepartmentId == @" + parameterCount + " OR Vehicle.DepartmentId == @" + parameterCount + ")";
                                configuration.filterParameters.push({
                                    "TypeName": "System.Guid",
                                    "IsNullable": false,
                                    "Value": filter.DepartmentId()
                                });
                                parameterCount++;
                            }

                            if (filter.MultiSearch()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "(Driver.Person.FirstName.Contains(@" + parameterCount + ") || Driver.Person.LastName.Contains(@" + (parameterCount + 1) + "))";
                                configuration.filterParameters.push({
                                    "TypeName": "System.String",
                                    "IsNullable": true,
                                    "Value": filter.MultiSearch()
                                });
                                configuration.filterParameters.push({
                                    "TypeName": "System.String",
                                    "IsNullable": true,
                                    "Value": filter.MultiSearch()
                                });
                                parameterCount += 2;
                            }

                            if (filter.StartTime()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "Date >= @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.DateTime",
                                    "IsNullable": false,
                                    "Value": filter.StartTime()
                                });
                                parameterCount++;
                            }

                            if (filter.EndTime()) {
                                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                                configuration.filterPredicate += "Date <= @" + parameterCount;
                                configuration.filterParameters.push({
                                    "TypeName": "System.DateTime",
                                    "IsNullable": false,
                                    "Value": filter.EndTime()
                                });
                                parameterCount++;
                            }

                            if (configuration.filterParameters) {
                                configuration.filterParameters = JSON.stringify(configuration.filterParameters);
                            }

                            return configuration;
                        };

                        this.initialize = function() {
                            // Override IsInEditMode to always return false (prevent edit mode)
                            this.controller.IsInEditMode = function () {
                                return false;
                            };

                            this.controller.PedestrianDetectionHistoryFilterFormViewModel.filterData = function() {
                                try {
                                    var configuration = this.getConfiguration();
                                    this.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
                                } catch (e) {
                                    console.error("Error filtering data:", e);
                                }
                            }.bind(this);

                            var customerId = this.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
                            if (customerId != null) {
                                var configuration = this.getDefaultConfiguration();
                                this.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
                            }
                        };
                    }
                }
            }
        };

        // Mock the base controller
        controller = {
            applicationController: {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn().mockReturnValue({
                            CustomerId: 'test-customer-id'
                        })
                    }
                }
            },
            PedestrianDetectionHistoryFilterFormViewModel: {
                PedestrianDetectionHistoryFilterObject: vi.fn().mockReturnValue({
                    Data: {
                        CustomerId: vi.fn(),
                        SiteId: vi.fn(),
                        DepartmentId: vi.fn(),
                        MultiSearch: vi.fn(),
                        StartTime: vi.fn(),
                        EndTime: vi.fn()
                    }
                })
            },
            PedestrianDetectionHistoryGridViewModel: {
                LoadPedestrianDetectionHistoryObjectCollection: vi.fn()
            }
        };

        // Create instance of custom controller
        customController = new FleetXQ.Web.Controllers.PedestrianDetectionReportPageControllerCustom(controller);
    });

    afterEach(() => {
        vi.clearAllMocks();
        delete global.FleetXQ;
    });

    describe('getDefaultConfiguration', () => {
        it('should return configuration with customer filter', () => {
            const config = customController.getDefaultConfiguration();
            
            expect(config.filterPredicate).toBe('(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)');
            expect(config.filterParameters).toBe(JSON.stringify([{
                "TypeName": "System.Guid",
                "IsNullable": false,
                "Value": "test-customer-id"
            }]));
        });
    });

    describe('getConfiguration', () => {
        beforeEach(() => {
            // Reset all filter values to null by default
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue(null);
            filter.SiteId.mockReturnValue(null);
            filter.DepartmentId.mockReturnValue(null);
            filter.MultiSearch.mockReturnValue(null);
            filter.StartTime.mockReturnValue(null);
            filter.EndTime.mockReturnValue(null);
        });

        it('should return default configuration for customer user with no filter', () => {
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue(null);
            
            const config = customController.getConfiguration();
            expect(config.filterPredicate).toBe('(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)');
        });

        it('should build filter predicate with customer ID', () => {
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('specific-customer-id');
            
            const config = customController.getConfiguration();
            expect(config.filterPredicate).toBe('(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)');
            expect(JSON.parse(config.filterParameters)[0].Value).toBe('specific-customer-id');
        });

        it('should build filter predicate with site ID', () => {
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.SiteId.mockReturnValue('site-id');
            
            const config = customController.getConfiguration();
            const expectedPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0) && (Driver.Person.SiteId == @1 OR Vehicle.SiteId == @1)';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe('site-id');
        });

        it('should build filter predicate with department ID', () => {
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.DepartmentId.mockReturnValue('dept-id');
            
            const config = customController.getConfiguration();
            const expectedPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0) && (Driver.Person.DepartmentId == @1 OR Vehicle.DepartmentId == @1)';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe('dept-id');
        });

        it('should build filter predicate with multi-search', () => {
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.MultiSearch.mockReturnValue('John');
            
            const config = customController.getConfiguration();
            const expectedPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0) && (Driver.Person.FirstName.Contains(@1) || Driver.Person.LastName.Contains(@2))';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toBe('John');
            expect(params[2].Value).toBe('John');
        });

        it('should build filter predicate with time range', () => {
            const filter = controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            filter.CustomerId.mockReturnValue('customer-id');
            filter.StartTime.mockReturnValue(new Date('2024-01-01'));
            filter.EndTime.mockReturnValue(new Date('2024-01-02'));
            
            const config = customController.getConfiguration();
            const expectedPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0) && Date >= @1 && Date <= @2';
            expect(config.filterPredicate).toBe(expectedPredicate);
            
            const params = JSON.parse(config.filterParameters);
            expect(params[0].Value).toBe('customer-id');
            expect(params[1].Value).toMatch(/2024-01-01/);
            expect(params[2].Value).toMatch(/2024-01-02/);
        });
    });

    describe('initialize', () => {
        it('should override IsInEditMode to return false', () => {
            customController.initialize();
            expect(controller.IsInEditMode()).toBe(false);
        });

        it('should add filterData function to filter form view model', () => {
            customController.initialize();
            expect(typeof controller.PedestrianDetectionHistoryFilterFormViewModel.filterData).toBe('function');
        });

        it('should load initial data for customer users', () => {
            customController.initialize();
            expect(controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection)
                .toHaveBeenCalledWith(expect.objectContaining({
                    filterPredicate: '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)'
                }));
        });
    });
});
