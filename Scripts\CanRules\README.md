# CAN Rules SQL Generator

This solution helps you generate SQL INSERT statements for CAN rules based on your CSV data dump from the database.

## What's Included

1. **`canrule_template.sql`** - Updated template file showing the nested structure
2. **`generate_canrules_simple.ps1`** - PowerShell script to process your CSV and generate complete SQL
3. **`canrules.csv`** - Your complete CSV file with CAN rules data
4. **`canrules_generated.sql`** - Generated SQL output file
5. This README file

## How to Use

### Step 1: Prepare Your CSV File
1. Your complete CSV data should be saved as `canrules.csv` in this directory
2. Make sure the CSV has these columns: `can_cd,can_mod,can_rules,crc`
3. Current CSV contains **2,915 records** with **124 unique vehicle models**

### Step 2: Run the PowerShell Script
```powershell
# Navigate to the Scripts/CanRules directory
cd "Scripts/CanRules"

# Run the script with default parameters
.\generate_canrules_simple.ps1

# Or specify custom file names
.\generate_canrules_simple.ps1 -CsvPath "your_data.csv" -OutputPath "your_output.sql"
```

### Step 3: Review and Execute the Generated SQL
1. The script will create `canrules_generated.sql`
2. Review the generated SQL file (currently **~648 KB** with **1,537 rules**)
3. Execute it against your SQL Server database
4. The script includes logging that will print which vehicle models already exist

## Key Features

✅ **Nested Structure**: CanruleDetails inserts are nested inside main Canrule IF NOT EXISTS blocks
✅ **Duplicate Removal**: Automatically removes duplicate records (**1,378 duplicates removed** from current data)
✅ **Dynamic GUIDs**: Generates unique GUIDs for each vehicle model
✅ **NULL Handling**: Converts `\N` values to SQL NULL
✅ **Safe Inserts**: Uses `IF NOT EXISTS` to prevent duplicate insertions
✅ **Logging**: Prints messages showing which vehicle models already exist in database
✅ **Progress Tracking**: Shows detailed breakdown of all vehicle models processed
✅ **Comprehensive Reporting**: Includes verification queries and execution summary

## Database Schema

The script generates INSERT statements for these tables:

### [dbo].[Canrule]
- `VehicleSerial` (NULL)
- `Id` (uniqueidentifier) - Generated GUID
- `CRC` (varchar) - From CSV, NULL if "\N"
- `Name` (varchar) - From `can_mod` column
- `Description` (NULL)

### [dbo].[CanruleDetails]
- `Id` (uniqueidentifier) - Generated with NEWID()
- `Canrules` (varchar) - From `can_rules` column
- `CanruleId` (uniqueidentifier) - References Canrule.Id

## Example Output Structure

```sql
-- ============================================
-- Model: 115(115A0)
-- Rules: 17
-- CRC: b1dd7cb4
-- GUID: C659EE92-73C3-4844-A070-8D7AEF3CC433
-- ============================================

IF NOT EXISTS (SELECT 1 FROM [dbo].[Canrule] WHERE [Name] = '115(115A0)')
BEGIN
    INSERT INTO [dbo].[Canrule] ([VehicleSerial], [Id], [CRC], [Name], [Description])
        VALUES (NULL, 'C659EE92-73C3-4844-A070-8D7AEF3CC433', 'b1dd7cb4', '115(115A0)', NULL)

    -- Insert CLRCAN
    IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CLRCAN' AND [CanruleId] = 'C659EE92-73C3-4844-A070-8D7AEF3CC433')
    BEGIN
        INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
            VALUES (NEWID(), 'CLRCAN', 'C659EE92-73C3-4844-A070-8D7AEF3CC433')
    END

    -- Insert CANCFG=2,500000,0,1
    IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = 'CANCFG=2,500000,0,1' AND [CanruleId] = 'C659EE92-73C3-4844-A070-8D7AEF3CC433')
    BEGIN
        INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
            VALUES (NEWID(), 'CANCFG=2,500000,0,1', 'C659EE92-73C3-4844-A070-8D7AEF3CC433')
    END

    -- ... more nested CanruleDetails inserts ...
END
ELSE
BEGIN
    PRINT 'Vehicle model ''115(115A0)'' already exists in database - skipping'
END
```

## Current Data Summary

- **Total CSV Records**: 2,915
- **Unique Vehicle Models**: 124
- **Total CAN Rules**: 1,537
- **Duplicates Removed**: 1,378
- **Generated SQL File Size**: ~648 KB
- **Generated SQL Lines**: 11,389

## Vehicle Models Included

The current dataset includes models from various manufacturers with detailed model codes:

### **Linde Models** (Major portion of dataset):
- **1110 Series**: 1110(1110C0), 1110(1110F0)
- **1111 Series**: 1111(1111A0), 1111(1111G0)  
- **115 Series**: 115, 115(115A0), 115(115M0), 115-ext
- **1120 Series**: 1120(1120D0), 1120-ext
- **1152 Series**: 1152, 1152(1152A0), 1152(1152F0), 1152(1152G0), 1152-ext
- **116 Series**: 116(116C0), 116-03 variants
- **1172-1183 Series**: 1172(1172A0), 1173(1173G), 1183(1183L)
- **1252-1276 Series**: 1252(1252L), 1275/1276 variants
- **131-132 Series**: 131(131F03797), 131(131U0), 132 variants
- **335-396 Series**: 335-03(335C0), 346(346L), 386-388 variants, 391-396 series
- **4000-5000 Series**: 4587, 4594(4594Y), 5195(5195L0), 5213-5222 series

### **Other Manufacturers**:
- **Baoli**: 1289(1289M)
- **Still**: 6342(6342M0)
- **Special Models**: Delete Can Rules, LINDE 1120 - V.Minto, Linde H50D-06(TEST)

## Logging and Monitoring

When you run the generated SQL, it will:
- Print messages for vehicle models that already exist
- Skip duplicate inserts safely
- Show progress as it processes each model
- Include verification queries to check the results

## Notes

- The `can_cd` column is ignored as requested
- Each unique `can_mod` gets its own GUID
- All rules for the same model share the same GUID
- The script handles SQL injection by escaping single quotes
- Empty or "\N" CRC values become NULL in SQL
- **Nested structure** ensures all related data is inserted together
- **Logging** shows which models are skipped due to existing data
- **High duplicate ratio**: 1,378 duplicates removed from 2,915 total records (47% duplicate rate)

## Troubleshooting

**Error: Input CSV file not found**
- Make sure `canrules.csv` exists in the same directory as the PowerShell script

**Large file processing**
- Current script handles **2,915 records** efficiently
- Processing time: ~15-20 seconds for full dataset
- Shows detailed progress and breakdown of all 124 vehicle models

**SQL execution errors**
- Review the generated SQL for any special characters that might need additional escaping
- Test with a small subset first before running the complete script
- Check the PRINT statements in SQL Server Messages tab to see which models were skipped

**Database connectivity**
- Ensure you have proper permissions to INSERT into both [dbo].[Canrule] and [dbo].[CanruleDetails] tables
- The script uses transactions implicitly through the nested IF NOT EXISTS structure

**Performance considerations**
- The generated SQL file is large (648 KB, 11,389 lines)
- Consider running during low-traffic periods
- Monitor SQL Server performance during execution
- The nested structure ensures data integrity but may take longer to execute than simple inserts 