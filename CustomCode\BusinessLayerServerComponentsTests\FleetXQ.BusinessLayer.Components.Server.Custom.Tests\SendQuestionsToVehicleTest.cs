﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using VDS.RDF;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2019.Presentation;
using FleetXQ.Data.DataProvidersExtensions.Custom;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class SendQuestionsToVehicleTest : TestBase
    {
        private ISendQuestionsToVehicle _sendQuestionsToVehicle;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"SendQuestionsToVehicleTestA-{Guid.NewGuid()}";



        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUp()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _sendQuestionsToVehicle = _serviceProvider.GetRequiredService<ISendQuestionsToVehicle>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task UploadChecklistQuestionsAsync_ValidVehicleId_InvokesSyncChecklistToVehicle()
        {
            // Arrange
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(vehicle, Is.Not.Null, "Test data setup failed: No vehicle found.");

            // Act
            bool isSynced = false; // Assuming there's a way to check if SyncChecklistToVehicle was called successfully
            try
            {
                await _sendQuestionsToVehicle.UploadChecklistQuestionsAsync(vehicle.Id);
                isSynced = true; // If the above call was successful, set isSynced to true
            }
            catch (Exception ex)
            {
                Assert.Fail($"Expected no exception, but got: {ex.Message}");
            }

            // Assert
            Assert.That(isSynced, Is.True, "Expected SyncChecklistToVehicle to be invoked successfully.");
        }

        [Test]
        public async Task UploadChecklistQuestionsAsync_InvalidVehicleId_ThrowsException()
        {
            // Arrange
            var invalidVehicleId = Guid.NewGuid();
            // Act & Assert
            try
            {
                await _sendQuestionsToVehicle.UploadChecklistQuestionsAsync(invalidVehicleId);
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Does.Contain($"unknow vehicle id {invalidVehicleId}"));
            }
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();

            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            //Create IOs
            var iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "0";
            iofields.Description = "ignition";
            iofields.IOType = " ";
            iofields.CANBUS = false;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "SEAT";
            iofields.Description = "Canbus Seat Switch Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "HYDL";
            iofields.Description = "Canbus Hydrolic Raising Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "TRACK";
            iofields.Description = "Canbus Traction/Movement Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);

            var sites = new List<string> { "Site 1", "Site 2", "Site 3" };
            var IoTHubIds1 = new string[] { "test_00000001", "test_00000002", "test_00000003", "test_00000004", "test_00000005", "test_00000006", "test_00000007", "test_00000008", "test_00000009", "test_00000010" };
            var IoTHubIds2 = new string[] { "test_00000011", "test_00000012", "test_00000013", "test_00000014", "test_00000015", "test_00000016", "test_00000017", "test_00000018", "test_00000019", "test_00000020" };
            var IoTHubIds3 = new string[] { "test_00000021", "test_00000022", "test_00000023", "test_00000024", "test_00000025", "test_00000026", "test_00000027", "test_00000028", "test_00000029", "test_00000030" };
            var VehicleHireNos1 = new string[] { "VH1", "VH2", "VH3", "VH4", "VH5", "VH6", "VH7", "VH8", "VH9", "VH10" };
            var VehicleHireNos2 = new string[] { "VH11", "VH12", "VH13", "VH14", "VH15", "VH16", "VH17", "VH18", "VH19", "VH20" };
            var VehicleHireNos3 = new string[] { "VH21", "VH22", "VH23", "VH24", "VH25", "VH26", "VH27", "VH28", "VH29", "VH30" };
            var VehicleSerialNos1 = new string[] { "VS1", "VS2", "VS3", "VS4", "VS5", "VS6", "VS7", "VS8", "VS9", "VS10" };
            var VehicleSerialNos2 = new string[] { "VS11", "VS12", "VS13", "VS14", "VS15", "VS16", "VS17", "VS18", "VS19", "VS20" };
            var VehicleSerialNos3 = new string[] { "VS21", "VS22", "VS23", "VS24", "VS25", "VS26", "VS27", "VS28", "VS29", "VS30" };
            var PersonFirstName1 = new string[] { "John", "Peter", "Paul", "Mark", "Luke", "Matthew", "James", "Jude", "Simon", "Andrew" };
            var PersonFirstName2 = new string[] { "Mary", "Elizabeth", "Anna", "Ruth", "Esther", "Sarah", "Rebecca", "Leah", "Rachel", "Deborah" };
            var PersonFirstName3 = new string[] { "David", "Solomon", "Elijah", "Elisha", "Isaiah", "Jeremiah", "Ezekiel", "Daniel", "Hosea", "Joel" };
            var PersonLastName1 = new string[] { "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor" };
            var PersonLastName2 = new string[] { "Anderson", "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson", "Garcia", "Martinez", "Robinson" };
            var PersonLastName3 = new string[] { "Clark", "Rodriguez", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "Hernandez", "King" };

            foreach (var siteName in sites)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.CustomerId = customer.Id;
                site.Name = siteName;
                site.TimezoneId = timeZone.Id;
                site.Id = Guid.NewGuid();

                await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

                var departmentNames = new List<string> { "Warehouse", "Logistics", "Production" };

                // create 3 departments for each site
                for (int j = 0; j < 3; j++)
                {
                    var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                    department.Id = Guid.NewGuid();
                    department.Name = siteName + " " + departmentNames[j];
                    department.SiteId = site.Id;
                    await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

                    // get only 3 models
                    var Models = new List<ModelDataObject>();
                    for (int i = 0; i < 3; i++)
                    {
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = "Model " + (i + 1).ToString();
                        model.Description = "Description for Model " + (i + 1).ToString();
                        model.DealerId = dealer.Id;
                        // Assigning Model.Type based on the ModelTypesEnum
                        switch (i)
                        {
                            case 0:
                                model.Type = ModelTypesEnum.Electric;
                                break;
                            case 1:
                                model.Type = ModelTypesEnum.ICForklifts;
                                break;
                            case 2:
                                model.Type = ModelTypesEnum.OrderPickers;
                                break;
                            // Additional cases can be added here for other types if necessary
                            default:
                                model.Type = ModelTypesEnum.PalletJack; // Default case if more than 3 models are created
                                break;
                        }
                        // Removed setting the non-existent Active property
                        await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);
                        Models.Add(model);
                    }
                    // create 10 vehicles for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = Guid.NewGuid();
                        vehicle.CustomerId = customer.Id;
                        vehicle.SiteId = site.Id;
                        vehicle.DepartmentId = department.Id;
                        vehicle.IDLETimer = 300;
                        vehicle.OnHire = true;
                        vehicle.ImpactLockout = true;
                        // set random modelId with index rand 0 to 2
                        if (Models.Any())
                        {
                            vehicle.ModelId = Models[k % 3].Id;
                        }
                        else
                        {
                            throw new InvalidOperationException("No models found to assign to vehicle.");
                        }
                        if (j == 0)
                        {
                            vehicle.HireNo = VehicleHireNos1[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos1[k] + department.Id;
                        }
                        else if (j == 1)
                        {
                            vehicle.HireNo = VehicleHireNos2[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos2[k] + department.Id;
                        }
                        else
                        {
                            vehicle.HireNo = VehicleHireNos3[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos3[k] + department.Id;
                        }

                        // create a module for the vehicle
                        var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                        module.Id = Guid.NewGuid();
                        module.Calibration = 100;
                        module.CCID = "CCID" + j + k;
                        // set FSSSBASE random from 100000 to 200000 in increment of 10000
                        Random random = new Random();
                        int randomNumber = random.Next(10, 21);
                        module.FSSSBase = randomNumber * 10000;
                        module.FSSXMulti = 1;
                        if (j == 0)
                            module.IoTDevice = IoTHubIds1[k] + department.Id;
                        else if (j == 1)
                            module.IoTDevice = IoTHubIds2[k] + department.Id;
                        else
                            module.IoTDevice = IoTHubIds3[k] + department.Id;
                        module.IsAllocatedToVehicle = true;
                        await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

                        vehicle.ModuleId1 = module.Id;
                        await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                    }
                    // create 10 persons for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                        person.Id = Guid.NewGuid();
                        person.CustomerId = customer.Id;
                        person.SiteId = site.Id;
                        person.DepartmentId = department.Id;
                        if (j == 0)
                        {
                            person.FirstName = PersonFirstName1[k];
                            person.LastName = PersonLastName1[k];
                        }
                        else if (j == 1)
                        {
                            person.FirstName = PersonFirstName2[k];
                            person.LastName = PersonLastName2[k];
                        }
                        else
                        {
                            person.FirstName = PersonFirstName3[k];
                            person.LastName = PersonLastName3[k];
                        }
                        person.IsDriver = true;
                        person.IsActiveDriver = true;

                        person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true); //Crear person and driver

                        var card = _serviceProvider.GetRequiredService<CardDataObject>();
                        card.Id = Guid.NewGuid();
                        // Facility Code is random between 1 to 254 in string
                        Random random = new Random();
                        card.FacilityCode = random.Next(1, 255).ToString();
                        // Card Number is random between 100001 to 675899 in string
                        card.CardNumber = random.Next(100001, 675900).ToString();
                        card.Active = true;
                        card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
                        card.Type = CardTypeEnum.CardID;

                        card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

                        // get the driver object from person and assign the card ID to the driver
                        var driver = person.Driver;
                        driver.CardDetailsId = card.Id;
                        driver = await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);
                    }
                }
            }

            // get all departments
            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true);
            // get all models
            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(null, skipSecurity: true);

            // create DepartmentChecklist for each department and model
            foreach (var department in departments)
            {
                foreach (var model in models)
                {
                    var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                    departmentChecklist.Id = Guid.NewGuid();
                    departmentChecklist.ModelId = model.Id;
                    departmentChecklist.DepartmentId = department.Id;
                    await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist, skipSecurity: true);
                }
            }
            // get all DepartmentChecklist
            var departmentChecklists = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, skipSecurity: true)).ToArray();

            var questions = new string[] { "Do the brakes work properly ?", "Is the steering operating correctly?" };
            // create 2 PreOperationalChecklist for each DepartmentChecklist
            foreach (var departmentChecklist in departmentChecklists)
            {
                for (int i = 0; i < 2; i++)
                {
                    var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                    preOperationalChecklist.Id = Guid.NewGuid();
                    preOperationalChecklist.SiteChecklistId = departmentChecklist.Id;
                    preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
                    preOperationalChecklist.Question = questions[i];
                    preOperationalChecklist.ExpectedAnswer = true;
                    preOperationalChecklist.Critical = true;
                    // order is i + 1 as short
                    preOperationalChecklist.Order = (short)(i + 1);
                    await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist, skipSecurity: true);
                }
            }
        } // End create demo data
    }
}
