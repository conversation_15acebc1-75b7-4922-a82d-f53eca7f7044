using NUnit.Framework;
using Moq;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System.Collections.Generic;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class DeviceMessageHandlerTests
    {
        private Mock<IConfiguration> _mockConfiguration;
        private Mock<IDataFacade> _mockDataFacade;
        private Mock<IServiceProvider> _mockServiceProvider;
        private Mock<IAuthentication> _mockAuthentication;
        private Mock<ILoggingService> _mockLogger;
        // We cannot easily mock ServiceClient or RegistryManager due to internal creation in the constructor.
        // Tests will focus on verifiable interactions with injected dependencies.

        private DeviceMessageHandler _handler;

        [SetUp]
        public void SetUp()
        {
            _mockConfiguration = new Mock<IConfiguration>();
            _mockDataFacade = new Mock<IDataFacade>();
            _mockServiceProvider = new Mock<IServiceProvider>();
            _mockAuthentication = new Mock<IAuthentication>();
            _mockLogger = new Mock<ILoggingService>();

            // --- Mock Configuration ---
            // Provide dummy connection strings so the constructor doesn't crash.
            // Testing the actual ServiceClient/RegistryManager interaction created
            // from these is not feasible in this test setup.
            var mockConfSection = new Mock<IConfigurationSection>();
            mockConfSection.Setup(s => s.Value).Returns("HostName=dummy.azure-devices.net;SharedAccessKeyName=dummy;SharedAccessKey=dummykey");

            _mockConfiguration.Setup(c => c.GetSection("IoThubConnectionString"))
                              .Returns(mockConfSection.Object);
            // Handle direct indexer access used for RegistryManager
            _mockConfiguration.Setup(c => c["IoThubConnectionString"])
                              .Returns("HostName=dummy.azure-devices.net;SharedAccessKeyName=dummy;SharedAccessKey=dummykey");

            // --- Mock Authentication ---
            // Using object initializer syntax based on examples from the codebase
            var userClaims = new UserClaims
            {
                UserId = new Guid("11111111-1111-1111-1111-111111111111"),
                UserName = "testuser"
                // No need to set roles and permissions if they're not used in the test
            };
            _mockAuthentication.Setup(auth => auth.GetCurrentUserClaimsAsync())
                               .ReturnsAsync(userClaims);

            // --- Instantiate the handler ---
            // This uses the mocked dependencies but will create real ServiceClient/RegistryManager
            // instances internally using the dummy connection strings.
            _handler = new DeviceMessageHandler(
                _mockConfiguration.Object,
                _mockDataFacade.Object,
                _mockServiceProvider.Object,
                _mockAuthentication.Object,
                _mockLogger.Object
            );
        }

        [Test]
        public async Task SendCloudToDeviceMessageAsync_HappyPath_GetsClaimsAndLogsMessage()
        {
            // Arrange
            string targetDevice = "testDevice123";
            string c2dMessagePayload = @"{""command"":""test""}";
            var expectedUserId = new Guid("11111111-1111-1111-1111-111111111111");
            var expectedUserName = "testuser";

            // Act
            // Wrap in try-catch to ignore potential exceptions from the actual
            // ServiceClient call using the dummy connection string. Our verification
            // is focused on the mocks before that call.
            try
            {
                await _handler.SendCloudToDeviceMessageAsync(targetDevice, c2dMessagePayload);
            }
            // Catch the specific GOServerException thrown by the method when SendAsync fails
            catch (GOServerException goEx) when (goEx.Message.StartsWith("Error sending C2D message:"))
            {
                // Swallow the expected exception from trying to use the dummy ServiceClient
                Console.WriteLine($"Ignoring expected GOServerException during SendAsync with dummy connection: {goEx.Message}");
                // We expect this when using the dummy connection string, so it's not a test failure.
            }
            catch (Exception ex)
            {
                // Catch any other unexpected exceptions
                Assert.Fail($"Unexpected exception during SendCloudToDeviceMessageAsync: {ex}");
            }

            // Assert
            // 1. Verify GetCurrentUserClaimsAsync was called exactly once.
            _mockAuthentication.Verify(auth => auth.GetCurrentUserClaimsAsync(), Times.Once);

            // 2. Instead of trying to match exact JSON patterns, verify that the logger was called with
            //    a simpler substring check that's less fragile and won't be affected by JSON escaping issues
            _mockLogger.Verify(log => log.LogInformation(It.Is<string>(s =>
                s.Contains(targetDevice) && // Device ID must be in the string
                s.Contains("test") &&       // Part of the command payload must be present
                s.Contains(expectedUserId.ToString()) && // User ID must be in the string  
                s.Contains(expectedUserName) // Username must be in the string
            )), Times.Once, "Expected log message was not found");

            // *** Limitation Note ***
            // This test verifies logging and authentication interaction but *cannot*
            // verify that ServiceClient.SendAsync was called with the correct parameters
            // due to ServiceClient being created internally. For full verification,
            // consider refactoring DeviceMessageHandler to accept ServiceClient
            // (or an interface wrapper like IServiceClient) via dependency injection.
        }

        [Test]
        public async Task SendCloudToDeviceMessageAsync_AuthenticationThrows_ThrowsGOServerException()
        {
            // Arrange
            string targetDevice = "testDevice456";
            string c2dMessagePayload = @"{""command"":""reset""}";
            var authException = new InvalidOperationException("Authentication failed"); // Example exception
            _mockAuthentication.Setup(auth => auth.GetCurrentUserClaimsAsync())
                               .ThrowsAsync(authException);

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() =>
                _handler.SendCloudToDeviceMessageAsync(targetDevice, c2dMessagePayload)
            );

            // Verify Logger was not called because the exception happened before logging
            _mockLogger.Verify(log => log.LogInformation(It.IsAny<string>()), Times.Never);

            // Assert on exception message
            Assert.That(ex.Message, Contains.Substring("Error sending C2D message"));
        }

        // Add more tests here for other scenarios if needed, keeping in mind the
        // limitations around testing the internal ServiceClient calls.
    }
}