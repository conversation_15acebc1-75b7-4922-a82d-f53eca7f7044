﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// CustomerPageUtilities Component
	///  
	/// </summary>
    public partial class CustomerPageUtilities : ICustomerPageUtilities 
    {
		/// <summary>
        /// CreateNewModel Method
		/// Opens up the Create New Model Form as Modal 
		  /// </summary>
		public System.Boolean CreateNewModel() 
		{
			// TODO: This is a custom component - Implementation should be provided
			return default(System.Boolean); 
		}
		
		public void Dispose()
		{
		}

	}
}
