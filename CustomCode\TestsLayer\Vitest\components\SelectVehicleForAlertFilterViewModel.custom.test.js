﻿import { describe, it, expect, beforeEach, vi } from 'vitest';
import ko from 'knockout';
import fs from 'fs';
import path from 'path';

// Create mock observables with subscribe functionality
const createObservable = (initialValue) => {
    const subscribers = [];
    const observable = function (newValue) {
        if (arguments.length === 0) {
            return observable.value;
        }
        observable.value = newValue;
        subscribers.forEach(fn => fn(newValue));
    };
    observable.value = initialValue;
    observable.subscribe = (fn) => {
        subscribers.push(fn);
        return {
            dispose: () => {
                const index = subscribers.indexOf(fn);
                if (index > -1) {
                    subscribers.splice(index, 1);
                }
            }
        };
    };
    return observable;
};

describe('SelectVehicleForAlertFilterViewModelCustom', () => {
    let viewmodel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {
                    Filters: {}
                }
            }
        };

        // Mock console.error
        global.console.error = vi.fn();

        // Setup setTimeout mock
        global.setTimeout = vi.fn((callback) => callback());

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/Filters/SelectVehicleForAlertFilterViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        viewmodel = {
            contextId: 'test-context',
            customerId: createObservable(null),
            siteId: createObservable(null),
            departmentId: createObservable(null),
            filterData: {
                fields: {
                    SiteValue: createObservable(null),
                    DepartmentValue: createObservable(null)
                }
            },
            statusData: {
                countFilterCollectionsLoaded: createObservable(0),
                isSiteSelectEnabled: createObservable(false)
            },
            SiteValues: ko.observableArray([]),
            DepartmentValues: ko.observableArray([]),
            DataStoreSite: {
                LoadObjectCollection: vi.fn(),
                LoadObject: vi.fn()
            },
            DataStoreDepartment: {
                LoadObjectCollection: vi.fn(),
                LoadObject: vi.fn()
            },
            onGetSiteCollectionDataError: vi.fn(),
            onGetDepartmentCollectionDataError: vi.fn(),
            onGetDepartmentCollectionDataSuccess: vi.fn()
        };

        customViewModel = new FleetXQ.Web.ViewModels.Filters.SelectVehicleForAlertFilterViewModelCustom(viewmodel);
        customViewModel.onBeforeInitialize();
    });

    it('should initialize observables', () => {
        expect(viewmodel.customerId()).toBeNull();
        expect(viewmodel.siteId()).toBeNull();
        expect(viewmodel.departmentId()).toBeNull();
    });

    it('should call getSiteCollectionData on customerId change', () => {
        viewmodel.customerId('test-customer-id');
        expect(viewmodel.DataStoreSite.LoadObjectCollection).toHaveBeenCalled();
        const config = viewmodel.DataStoreSite.LoadObjectCollection.mock.calls[0][0];
        expect(config.filterPredicate).toBe('CustomerId == @0');
        expect(config.filterParameters).toContain('test-customer-id');
    });

    it('should call getDepartmentCollectionData on SiteValue change', () => {
        const siteValue = {
            value: {
                Data: {
                    Id: createObservable('test-site-id')
                }
            }
        };
        viewmodel.filterData.fields.SiteValue(siteValue);
        expect(viewmodel.DataStoreDepartment.LoadObjectCollection).toHaveBeenCalled();
        const config = viewmodel.DataStoreDepartment.LoadObjectCollection.mock.calls[0][0];
        expect(config.filterPredicate).toBe('SiteId == @0');
        expect(config.filterParameters).toContain('test-site-id');
    });

    it('should handle onGetSiteCollectionDataSuccess correctly', () => {
        const objectsLoaded = [{
            Data: {
                Name: createObservable('Site 1')
            }
        }];
        customViewModel.onGetSiteCollectionDataSuccess(objectsLoaded);
        expect(viewmodel.SiteValues().length).toBe(2); // All + Site 1
        expect(viewmodel.SiteValues()[1].text).toBe('Site 1');
        expect(viewmodel.statusData.countFilterCollectionsLoaded()).toBe(1);
        expect(viewmodel.statusData.isSiteSelectEnabled()).toBe(true);
    });

    it('should handle loadDefaultSite correctly', () => {
        const siteId = 'test-site-id';
        const callback = vi.fn();
        viewmodel.loadDefaultSite(siteId, callback);
        expect(viewmodel.DataStoreSite.LoadObject).toHaveBeenCalled();
        const config = viewmodel.DataStoreSite.LoadObject.mock.calls[0][0];
        expect(config.pks.Id).toBe(siteId);
    });

    it('should handle loadDefaultDepartment correctly', () => {
        const departmentId = 'test-department-id';
        viewmodel.loadDefaultDepartment(departmentId);
        expect(viewmodel.DataStoreDepartment.LoadObject).toHaveBeenCalled();
        const config = viewmodel.DataStoreDepartment.LoadObject.mock.calls[0][0];
        expect(config.pks.Id).toBe(departmentId);
    });
});
