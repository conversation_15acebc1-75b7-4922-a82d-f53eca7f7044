﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- BEGIN LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
SET NOCOUNT ON
SET NOEXEC OFF
SET ARITHABORT ON
SET XACT_ABORT ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO
BEGIN TRAN
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- TRANSFER SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- RENAME TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (soft)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN RENAMEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN ADDs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 1: add column dbo.VehicleSlamcoreLocationHistory.Speed 
ALTER TABLE [dbo].[VehicleSlamcoreLocationHistory] 
	ADD [Speed] [decimal] (8, 6) NULL  
GO

IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 1, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 1' SET NOEXEC ON END
GO
-- step 2: add column dbo.VehicleSlamcoreLocationHistory.Bearing 
ALTER TABLE [dbo].[VehicleSlamcoreLocationHistory] 
	ADD [Bearing] [decimal] (8, 4) NULL  
GO

IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 2, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 2' SET NOEXEC ON END
GO
-- step 3: add column dbo.VehicleSlamcoreLocationHistory.TrailSequence 
ALTER TABLE [dbo].[VehicleSlamcoreLocationHistory] 
	ADD [TrailSequence] [int] NULL  
GO

IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 3, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 3' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN MODIFYs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (hard)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MODEL TO DATABASE SYNCHRONISATION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
UPDATE [GO.LiveUpdate].[ModelSync] SET [ModelRevisionId] = 3957, [When] = GETUTCDATE() WHERE Id = 'AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF'
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COMMIT LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
IF @@TRANCOUNT > 0 
BEGIN 
	COMMIT TRAN PRINT 'Synchronization completed successfully.' 
END
GO
SET NOEXEC OFF
GO
