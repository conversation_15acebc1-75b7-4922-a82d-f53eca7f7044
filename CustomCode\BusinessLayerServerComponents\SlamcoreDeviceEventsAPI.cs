﻿
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// SlamcoreDeviceEventsAPI Component
	///  
	/// </summary>
    public partial class SlamcoreDeviceEventsAPI : BaseServerComponent, ISlamcoreDeviceEventsAPI 
    {
		public SlamcoreDeviceEventsAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> StoreEventAsync(System.String message, Dictionary<string, object> parameters = null)
		{
            return System.Threading.Tasks.Task.FromResult(new ComponentResponse<System.Boolean>(false));
        }
    }
}
