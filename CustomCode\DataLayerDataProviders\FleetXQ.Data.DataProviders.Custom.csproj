﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Custom Data Providers</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" />
    <PackageReference Include="Microsoft.AspNetCore.Http.Features" />
    <PackageReference Include="Microsoft.CSharp" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    <PackageReference Include="System.Data.DataSetExtensions" />
    <PackageReference Include="System.IO.Pipelines" />
    <PackageReference Include="System.Text.Encodings.Web" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
  </ItemGroup>
</Project>

