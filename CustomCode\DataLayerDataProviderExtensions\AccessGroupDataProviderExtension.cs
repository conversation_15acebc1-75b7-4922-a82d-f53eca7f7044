﻿using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Office2019.Drawing.Model3D;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    /// <summary>
    /// Helper class for managing access rule mappings to reduce JWT token size
    /// </summary>
    public static class AccessRuleMapper
    {
        // Access rule mapping for shorter JWT tokens
        private static readonly Dictionary<string, string> AccessRuleMapping = new Dictionary<string, string>
        {
            // Main access permissions
            { "CanViewDashboard", "D" },
            { "HasCustomersAccess", "CA" },
            { "HasUsersAccess", "UA" },
            { "HasVehiclesAccess", "VA" },
            { "HasReportsAccess", "RA" },

            // Customer-related permissions
            { "CanViewCustomer", "VC" },
            { "CanViewCustomerSite", "VS" },
            { "CanCreateCustomerSite", "CS" },
            { "CanEditCustomerSite", "ES" },
            { "CanViewCustomerDepartment", "VD" },
            { "CanCreateCustomerDepartment", "CD" },
            { "CanEditCustomerDepartment", "ED" },
            { "CanViewCustomerEmailGroup", "VEG" },
            { "CanCreateCustomerEmailGroup", "CEG" },
            { "CanEditCustomerEmailGroup", "EEG" },
            { "CanDeleteCustomerEmailGroup", "DEG" },
            { "CanCreateCustomerEmailList", "CEL" },
            { "CanDeleteCustomerEmailList", "DEL" },
            { "CanViewCustomerFirmware", "VF" },
            { "CanEditCustomerFirmware", "EF" },
            { "CanViewCustomerModel", "VM" },
            { "CanViewAccessGroups", "VAG" },
            { "CanCreateCustomerAccessGroups", "CAG" },
            { "CanEditCustomerAccessGroups", "EAG" },

            // User-related permissions
            { "CanCreateUser", "CU" },
            { "CanEditUser", "EU" },
            { "CanDeleteUser", "DU" },
            { "CanViewUsers", "VU" },
            { "CanExportUsers", "XU" },
            { "CanViewUserCard", "VUC" },
            { "CanCreateUserCard", "CUC" },
            { "CanEditUserCard", "EUC" },
            { "CanViewVehicleAccess", "VVA" },
            { "CanEditVehicleAccess", "EVA" },
            { "CanViewUserLicense", "VUL" },
            { "CanCreateUserLicense", "CUL" },
            { "CanEditUserLicense", "EUL" },
            { "CanViewUserWebsiteAccess", "VUWA" },
            { "CanCreateUserWebsiteAccess", "CUWA" },
            { "CanEditUserWebsiteAccess", "EUWA" },
            { "CanViewUserSupervisorAccess", "VUSA" },
            { "CanEditUserSupervisorAccess", "EUSA" },
            { "CanViewUserReportSubscription", "VURS" },
            { "CanCreateUserReportSubscription", "CURS" },
            { "CanEditUserReportSubscription", "EURS" },
            { "CanDeleteUserReportSubscription", "DURS" },
            { "CanCreateUserAlert", "CUA" },
            { "CanEditUserAlert", "EUA" },
            { "CanDeleteUserAlert", "DUA" },
            { "CanViewUserAlert", "VUA" },

            // Vehicle-related permissions
            { "CanCreateVehicle", "CV" },
            { "CanEditVehicle", "EV" },
            { "CanViewVehicle", "VV" },
            { "CanExportVehicle", "XV" },
            { "CanViewVehicleSynchronization", "VVS" },
            { "CanViewVehicleChecklist", "VVC" },
            { "CanCreateVehicleChecklist", "CVC" },
            { "CanEditVehicleChecklist", "EVC" },
            { "CanDeleteVehicleChecklist", "DVC" },
            { "CanViewVehicleChecklistSetting", "VVCS" },
            { "CanCreateVehicleChecklistSetting", "CVCS" },
            { "CanEditVehicleChecklistSetting", "EVCS" },
            { "CanViewVehicleImpactSetting", "VVIS" },
            { "CanEditVehicleImpactSetting", "EVIS" },
            { "CanViewVehicleService", "VVSV" },
            { "CanCreateVehicleService", "CVS" },
            { "CanEditVehicleService", "EVS" },
            { "CanViewVehicleOtherSettingFullLockout", "VVOSFL" },
            { "CanEditVehicleOtherSettingFullLockout", "EVOSFL" },
            { "CanViewVehicleOtherSettingVorStatus", "VVOSVS" },
            { "CanEditVehicleOtherSettingVorStatus", "EVOSVS" },

            // Report-related permissions
            { "CanViewGeneralProductivityReport", "VGPR" },
            { "CanExportGeneralProductivityReport", "XGPR" },
            { "CanViewImpactReport", "VIR" },
            { "CanExportImpactReport", "XIR" },
            { "CanViewPreopChecklistReport", "VPCR" },
            { "CanExportPreopChecklistReport", "XPCR" },
            { "CanViewMachineUnlockReport", "VMUR" },
            { "CanExportMachineUnlockReport", "XMUR" },
            { "CanViewCurrentStatusReport", "VCSR" },
            { "CanExportCurrentStatusReport", "XCSR" },
            { "CanViewProficiencyReport", "VPR" },
            { "CanExportProficiencyReport", "XPR" },
            { "CanViewServiceCheckReport", "VSCR" },
            { "CanExportServiceCheckReport", "XSCR" }
        };

        /// <summary>
        /// Gets the short code for a given access rule name
        /// </summary>
        /// <param name="ruleName">The full access rule name</param>
        /// <returns>The short code, or the original rule name if no mapping exists</returns>
        public static string GetAccessRuleCode(string ruleName)
        {
            return AccessRuleMapping.TryGetValue(ruleName, out string code) ? code : ruleName;
        }

        /// <summary>
        /// Gets the full rule name for a given short code
        /// </summary>
        /// <param name="shortCode">The short code</param>
        /// <returns>The full rule name, or the original code if no reverse mapping exists</returns>
        public static string GetAccessRuleName(string shortCode)
        {
            var reverseMapping = AccessRuleMapping.ToDictionary(x => x.Value, x => x.Key);
            return reverseMapping.TryGetValue(shortCode, out string ruleName) ? ruleName : shortCode;
        }

        /// <summary>
        /// Gets all available access rule mappings
        /// </summary>
        /// <returns>Dictionary of rule name to short code mappings</returns>
        public static Dictionary<string, string> GetAllMappings()
        {
            return new Dictionary<string, string>(AccessRuleMapping);
        }
    }

    public class AccessGroupDataProviderExtension : IDataProviderExtension<AccessGroupDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IServiceProvider _serviceProvider;

        public AccessGroupDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler)
        {
            _serviceProvider = serviceProvider;
            _dataFacade = dataFacade;
            _deviceMessageHandler = deviceMessageHandler;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSetAsync;
            dataProvider.OnBeforeSave += OnBeforeSaveAsync;
        }

        private async Task OnBeforeSaveAsync(OnBeforeSaveEventArgs arg)
        {
            var accessGroup = arg.Entity as AccessGroupDataObject;

            if (accessGroup == null)
            {
                return;
            }

            var rules = new List<string>();

            // Parent rules first
            if (accessGroup.CanViewDashboard)
            {
                rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewDashboard"));
            }

            if (accessGroup.HasCustomersAccess)
            {
                rules.Add(AccessRuleMapper.GetAccessRuleCode("HasCustomersAccess"));

                // Child rules under HasCustomersAccess
                if (accessGroup.CanViewCustomer)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCustomer"));
                if (accessGroup.CanViewCustomerSite)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCustomerSite"));
                if (accessGroup.CanCreateCustomerSite)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateCustomerSite"));
                if (accessGroup.CanEditCustomerSite)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditCustomerSite"));

                if (accessGroup.CanViewCustomerDepartment)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCustomerDepartment"));
                if (accessGroup.CanCreateCustomerDepartment)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateCustomerDepartment"));
                if (accessGroup.CanEditCustomerDepartment)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditCustomerDepartment"));

                if (accessGroup.CanViewCustomerEmailGroup)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCustomerEmailGroup"));
                if (accessGroup.CanCreateCustomerEmailGroup)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateCustomerEmailGroup"));
                if (accessGroup.CanEditCustomerEmailGroup)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditCustomerEmailGroup"));
                if (accessGroup.CanDeleteCustomerEmailGroup)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanDeleteCustomerEmailGroup"));

                if (accessGroup.CanCreateCustomerEmailList)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateCustomerEmailList"));
                if (accessGroup.CanDeleteCustomerEmailList)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanDeleteCustomerEmailList"));

                if (accessGroup.CanViewCustomerFirmware)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCustomerFirmware"));
                if (accessGroup.CanEditCustomerFirmware)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditCustomerFirmware"));

                if (accessGroup.CanViewCustomerModel)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCustomerModel"));

                if (accessGroup.CanViewAccessGroups)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewAccessGroups"));
                if (accessGroup.CanCreateCustomerAccessGroups)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateCustomerAccessGroups"));
                if (accessGroup.CanEditCustomerAccessGroups)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditCustomerAccessGroups"));
            }

            if (accessGroup.HasUsersAccess)
            {
                rules.Add(AccessRuleMapper.GetAccessRuleCode("HasUsersAccess"));
                // Child rules under HasUsersAccess
                if (accessGroup.CanCreateUser)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateUser"));
                if (accessGroup.CanEditUser)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUser"));
                if (accessGroup.CanDeleteUser)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanDeleteUser"));
                if (accessGroup.CanViewUsers)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUsers"));
                if (accessGroup.CanExportUsers)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportUsers"));

                if (accessGroup.CanViewUserCard)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUserCard"));
                if (accessGroup.CanCreateUserCard)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateUserCard"));
                if (accessGroup.CanEditUserCard)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUserCard"));

                if (accessGroup.CanViewVehicleAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleAccess"));
                if (accessGroup.CanEditVehicleAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleAccess"));

                if (accessGroup.CanViewUserLicense)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUserLicense"));
                if (accessGroup.CanCreateUserLicense)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateUserLicense"));
                if (accessGroup.CanEditUserLicense)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUserLicense"));

                if (accessGroup.CanViewUserWebsiteAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUserWebsiteAccess"));
                if (accessGroup.CanCreateUserWebsiteAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateUserWebsiteAccess"));
                if (accessGroup.CanEditUserWebsiteAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUserWebsiteAccess"));

                if (accessGroup.CanViewUserSupervisorAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUserSupervisorAccess"));
                if (accessGroup.CanEditUserSupervisorAccess)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUserSupervisorAccess"));

                if (accessGroup.CanViewUserReportSubscription)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUserReportSubscription"));
                if (accessGroup.CanCreateUserReportSubscription)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateUserReportSubscription"));
                if (accessGroup.CanEditUserReportSubscription)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUserReportSubscription"));
                if (accessGroup.CanDeleteUserReportSubscription)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanDeleteUserReportSubscription"));

                if (accessGroup.CanCreateUserAlert)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateUserAlert"));
                if (accessGroup.CanEditUserAlert)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditUserAlert"));
                if (accessGroup.CanDeleteUserAlert)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanDeleteUserAlert"));
                if (accessGroup.CanViewUserAlert)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewUserAlert"));
            }

            if (accessGroup.HasVehiclesAccess)
            {
                rules.Add(AccessRuleMapper.GetAccessRuleCode("HasVehiclesAccess"));
                // Child rules under HasVehiclesAccess
                if (accessGroup.CanCreateVehicle)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateVehicle"));
                if (accessGroup.CanEditVehicle)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicle"));
                if (accessGroup.CanViewVehicle)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicle"));
                if (accessGroup.CanExportVehicle)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportVehicle"));

                if (accessGroup.CanViewVehicleSynchronization)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleSynchronization"));

                if (accessGroup.CanViewVehicleChecklist)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleChecklist"));
                if (accessGroup.CanCreateVehicleChecklist)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateVehicleChecklist"));
                if (accessGroup.CanEditVehicleChecklist)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleChecklist"));
                if (accessGroup.CanDeleteVehicleChecklist)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanDeleteVehicleChecklist"));

                if (accessGroup.CanViewVehicleChecklistSetting)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleChecklistSetting"));
                if (accessGroup.CanCreateVehicleChecklistSetting)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateVehicleChecklistSetting"));
                if (accessGroup.CanEditVehicleChecklistSetting)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleChecklistSetting"));

                if (accessGroup.CanViewVehicleImpactSetting)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleImpactSetting"));
                if (accessGroup.CanEditVehicleImpactSetting)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleImpactSetting"));

                if (accessGroup.CanViewVehicleService)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleService"));
                if (accessGroup.CanCreateVehicleService)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanCreateVehicleService"));
                if (accessGroup.CanEditVehicleService)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleService"));

                if (accessGroup.CanViewVehicleOtherSettingFullLockout)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleOtherSettingFullLockout"));
                if (accessGroup.CanEditVehicleOtherSettingFullLockout)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleOtherSettingFullLockout"));

                if (accessGroup.CanViewVehicleOtherSettingVorStatus)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleOtherSettingVorStatus"));
                if (accessGroup.CanEditVehicleOtherSettingVorStatus)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanEditVehicleOtherSettingVorStatus"));
            }

            if (accessGroup.HasReportsAccess)
            {
                rules.Add(AccessRuleMapper.GetAccessRuleCode("HasReportsAccess"));
                // Child rules under HasReportsAccess
                if (accessGroup.CanViewGeneralProductivityReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewGeneralProductivityReport"));
                if (accessGroup.CanExportGeneralProductivityReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportGeneralProductivityReport"));

                if (accessGroup.CanViewImpactReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewImpactReport"));
                if (accessGroup.CanExportImpactReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportImpactReport"));

                if (accessGroup.CanViewPreopChecklistReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewPreopChecklistReport"));
                if (accessGroup.CanExportPreopChecklistReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportPreopChecklistReport"));

                if (accessGroup.CanViewMachineUnlockReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewMachineUnlockReport"));
                if (accessGroup.CanExportMachineUnlockReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportMachineUnlockReport"));

                if (accessGroup.CanViewCurrentStatusReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewCurrentStatusReport"));
                if (accessGroup.CanExportCurrentStatusReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportCurrentStatusReport"));

                if (accessGroup.CanViewProficiencyReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewProficiencyReport"));
                if (accessGroup.CanExportProficiencyReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportProficiencyReport"));

                if (accessGroup.CanViewServiceCheckReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanViewServiceCheckReport"));
                if (accessGroup.CanExportServiceCheckReport)
                    rules.Add(AccessRuleMapper.GetAccessRuleCode("CanExportServiceCheckReport"));
            }

            // Set the AccessRules property with comma-delimited string of short codes
            accessGroup.AccessRules = string.Join(",", rules);
        }

        private async Task OnAfterSaveDataSetAsync(OnAfterSaveDataSetEventArgs e)
        {
            // return if update
            if (!e.EntityBeforeSave.IsNew)
            {
                return;
            }
            var accessGroup = e.EntityRefetched as AccessGroupDataObject;

            var customer = await accessGroup.LoadCustomerAsync();
            var sites = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customer.Id })).ToList();
            // remove sites that are already in access groups
            await accessGroup.LoadAccessGroupsToSitesAsync();
            var sitesToSave = sites.Where(c => !accessGroup.AccessGroupsToSites.Any(el => el.SiteId == c.Id)).ToList();
            // for each site, add the site in access groups by default
            foreach (var site in sitesToSave)
            {
                var accessGroupToSite = _serviceProvider.GetRequiredService<AccessGroupToSiteDataObject>();
                accessGroupToSite.AccessGroupId = accessGroup.Id;
                accessGroupToSite.SiteId = site.Id;
                accessGroup.AccessGroupsToSites.Add(accessGroupToSite);
            }
            await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup);
        }
    }
}
