# GitHub NuGet Authentication Setup Script
# This script configures GitHub Packages authentication for NuGet

param(
    [Parameter(Mandatory=$true)]
    [string]$GitHubUsername,
    
    [Parameter(Mandatory=$true)]
    [string]$GitHubToken
)

Write-Host "Setting up GitHub NuGet authentication..." -ForegroundColor Yellow

# Set environment variables
Write-Host "Setting environment variables..." -ForegroundColor Green
[Environment]::SetEnvironmentVariable("GITHUB_USERNAME", $GitHubUsername, "User")
[Environment]::SetEnvironmentVariable("GENERATIVEOBJECTS_GITHUB_TOKEN", $GitHubToken, "User")

# Load into current session
$env:GITHUB_USERNAME = $GitHubUsername
$env:GENERATIVEOBJECTS_GITHUB_TOKEN = $GitHubToken

Write-Host "Environment variables set successfully!" -ForegroundColor Green

# Test authentication
Write-Host "Testing GitHub Packages authentication..." -ForegroundColor Yellow

try {
    $headers = @{
        'Authorization' = "Bearer $GitHubToken"
        'User-Agent' = 'NuGet'
    }
    
    $response = Invoke-WebRequest -Uri "https://nuget.pkg.github.com/generative-objects-org/index.json" -Headers $headers -UseBasicParsing
    Write-Host "✅ GitHub Packages authentication successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ GitHub Packages authentication failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please verify your GitHub token has the correct permissions:" -ForegroundColor Yellow
    Write-Host "  - read:packages" -ForegroundColor Yellow
    Write-Host "  - repo (if accessing private repositories)" -ForegroundColor Yellow
}

# Test NuGet restore
Write-Host "Testing NuGet restore..." -ForegroundColor Yellow
try {
    dotnet restore --verbosity minimal
    Write-Host "✅ NuGet restore completed!" -ForegroundColor Green
} catch {
    Write-Host "❌ NuGet restore failed. Check the output above for details." -ForegroundColor Red
}

Write-Host "Setup complete! You may need to restart your IDE for changes to take effect." -ForegroundColor Cyan
