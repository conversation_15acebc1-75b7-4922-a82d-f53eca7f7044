﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// EmailGroupsToPersonUtilities Component
	///  
	/// </summary>
    public partial class EmailGroupsToPersonUtilities : BaseServerComponent, IEmailGroupsToPersonUtilities 
    {
		public EmailGroupsToPersonUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

		/// <summary>
        /// GetPersonsForCustomers Method
		/// </summary>
		/// <param name="EmailGroupId"></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<PersonDataObject>>> GetPersonsForCustomersAsync(Guid EmailGroupId, Dictionary<string, object> parameters = null)
        {
            return new ComponentResponse<DataObjectCollection<PersonDataObject>>(default(DataObjectCollection<PersonDataObject>));
        }
    }
}
