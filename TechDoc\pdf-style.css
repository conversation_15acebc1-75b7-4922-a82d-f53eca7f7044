body {
  font-size: 14px;
  line-height: 1.6;
  font-family: 'Segoe UI', Arial, sans-serif;
  max-width: none;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

h1 {
  font-size: 24px;
  margin-top: 20px;
  margin-bottom: 16px;
  color: #2c3e50;
}

h2 {
  font-size: 20px;
  margin-top: 18px;
  margin-bottom: 14px;
  color: #34495e;
}

h3 {
  font-size: 16px;
  margin-top: 16px;
  margin-bottom: 12px;
  color: #34495e;
}

h4 {
  font-size: 14px;
  margin-top: 14px;
  margin-bottom: 10px;
  font-weight: bold;
}

p {
  margin-bottom: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

ul, ol {
  margin-bottom: 12px;
  padding-left: 20px;
}

li {
  margin-bottom: 4px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

code {
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', monospace;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-all;
}

pre {
  font-size: 11px;
  background-color: #f8f8f8;
  padding: 12px;
  border-left: 4px solid #ddd;
  border-radius: 4px;
  margin: 12px 0;
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-all;
}

pre code {
  background-color: transparent;
  padding: 0;
  font-size: 11px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  word-break: break-all;
}

blockquote {
  background-color: #f0f8ff;
  padding: 12px 16px;
  border-left: 4px solid #0066cc;
  margin: 12px 0;
  font-style: italic;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  font-size: 13px;
  table-layout: fixed;
}

th, td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-all;
}

th {
  background-color: #f5f5f5;
  font-weight: bold;
}

/* Page break handling */
.page-break {
  page-break-before: always;
}

/* Specific handling for long strings and tokens */
.token, .api-key {
  word-break: break-all;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Better handling for URLs and HTTP examples */
a, .url {
  word-break: break-all;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Code highlighting preservation */
.hljs {
  background-color: #f8f8f8 !important;
}

.hljs-keyword {
  color: #0000ff;
  font-weight: bold;
}

.hljs-string {
  color: #a31515;
}

.hljs-comment {
  color: #008000;
  font-style: italic;
}

.hljs-number {
  color: #098658;
}

/* Mermaid diagram support */
.mermaid {
  text-align: center;
  margin: 20px 0;
}

/* Print-specific styles */
@media print {
  body {
    font-size: 12px;
  }
  
  h1 {
    page-break-before: auto;
    page-break-after: avoid;
  }
  
  h2, h3, h4 {
    page-break-after: avoid;
  }
  
  pre, blockquote {
    page-break-inside: avoid;
  }
  
  table {
    page-break-inside: avoid;
  }
} 