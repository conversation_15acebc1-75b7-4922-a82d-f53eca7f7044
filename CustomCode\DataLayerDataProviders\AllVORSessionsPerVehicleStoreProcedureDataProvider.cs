﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Data;
using System.Threading.Tasks;
using System.Data.SqlClient;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.Database;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Identity.Client;
using Microsoft.Extensions.Configuration;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Bibliography;
 

namespace FleetXQ.Data.DataProviders.Custom
{
    public class AllVORSessionsPerVehicleStoreProcedureDataProvider : DataProvider<AllVORSessionsPerVehicleStoreProcedureDataObject>
    {
        private readonly IConfiguration _configuration;
        public AllVORSessionsPerVehicleStoreProcedureDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<AllVORSessionsPerVehicleStoreProcedureDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetVORSessions", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }

        protected override async Task DoDeleteAsync(AllVORSessionsPerVehicleStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<AllVORSessionsPerVehicleStoreProcedureDataObject> DoGetAsync(AllVORSessionsPerVehicleStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
           using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetVORSessions", connection))
                {
                    AllVORSessionsPerVehicleStoreProcedureDataObject result = null;

                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@VehicleId", SqlDbType.UniqueIdentifier) { Value = entity.VehicleId });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                result = _serviceProvider.GetRequiredService<AllVORSessionsPerVehicleStoreProcedureDataObject>();
                                result.IsNew = false;

                                // Assuming the stored procedure returns Id, TimeSlot, NumberOfRedImpacts, and NumberOfAmberImpacts
                                result.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                result.VehicleId = reader.GetGuid(reader.GetOrdinal("VehicleId"));
                                result.VORReportCombinedViewId = reader.GetGuid(reader.GetOrdinal("VORReportCombinedViewId"));
                                result.TotalDuration = reader.GetString(reader.GetOrdinal("TotalDuration"));
                                result.TotalSeatHours = reader.GetString(reader.GetOrdinal("TotalSeatHours"));
                                result.TotalHydraulicHours = reader.GetString(reader.GetOrdinal("TotalHydraulicHours"));
                                result.TotalTractionHours = reader.GetString(reader.GetOrdinal("TotalTractionHours"));

                                context.AddObject(result);
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GeneralProductivityPerDriverViewLatestDataObject data", "Unable to get GeneralProductivityPerDriverViewLatestDataObject data", ex);
                    }
                }
            }
        }

        protected override async Task<DataObjectCollection<AllVORSessionsPerVehicleStoreProcedureDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
        var result = new DataObjectCollection<AllVORSessionsPerVehicleStoreProcedureDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetVORSessions", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if( filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] } );
                    }

                    if( filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] } );
                    }

                    if( filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] } );
                    }

                    if( filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] } );
                    }   
                    if( filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] } );
                    }

                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<AllVORSessionsPerVehicleStoreProcedureDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, VehicleId, LoggedHours, and SeatHours
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.VehicleId = reader.GetGuid(reader.GetOrdinal("VehicleId"));
                                    entity.VORReportCombinedViewId = reader.GetGuid(reader.GetOrdinal("VORReportCombinedViewId"));
                                    entity.TotalDuration = reader.GetString(reader.GetOrdinal("TotalDuration"));
                                    entity.TotalSeatHours = reader.GetString(reader.GetOrdinal("TotalSeatHours"));
                                    entity.TotalHydraulicHours = reader.GetString(reader.GetOrdinal("TotalHydraulicHours"));
                                    entity.TotalTractionHours = reader.GetString(reader.GetOrdinal("TotalTractionHours"));

                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GeneralProductivityPerVehicleView data", "Unable to get GeneralProductivityPerVehicleView data", ex);
                    }
                }
            }
        }

        protected override async Task<AllVORSessionsPerVehicleStoreProcedureDataObject> DoSaveAsync(AllVORSessionsPerVehicleStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
