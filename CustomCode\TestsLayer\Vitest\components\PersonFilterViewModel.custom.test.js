import { describe, it, expect, beforeEach, vi } from 'vitest';
import ko from 'knockout';
import fs from 'fs';
import path from 'path';

// Create mock observables with subscribe functionality
const createObservable = (initialValue) => {
    const subscribers = [];
    const observable = function (newValue) {
        if (arguments.length === 0) {
            return observable.value;
        }
        observable.value = newValue;
        subscribers.forEach(fn => fn(newValue));
    };
    observable.value = initialValue;
    observable.subscribe = (fn) => {
        subscribers.push(fn);
        return {
            dispose: () => {
                const index = subscribers.indexOf(fn);
                if (index > -1) {
                    subscribers.splice(index, 1);
                }
            }
        };
    };
    return observable;
};

describe('PersonFilterViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {
                    Filters: {}
                }
            }
        };

        // Mock console.error
        global.console.error = vi.fn();

        // Mock ApplicationController with user claims
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn().mockReturnValue({
                        role: [],
                        CustomerId: null
                    })
                }
            }
        };

        // Create base view model with required properties
        viewModel = {
            contextId: 'test-context',
            filterData: {
                fields: {
                    CustomerValue: createObservable(null),
                    SiteValue: createObservable(null),
                    DepartmentValue: createObservable(null)
                }
            },
            CustomerValues: createObservable([]),
            SiteValues: createObservable([]),
            DepartmentValues: createObservable([]),
            DataStoreSite: {
                LoadObjectCollection: vi.fn()
            },
            DataStoreDepartment: {
                LoadObjectCollection: vi.fn()
            },
            onGetSiteCollectionDataSuccess: vi.fn(),
            onGetSiteCollectionDataError: vi.fn(),
            onGetDepartmentCollectionDataSuccess: vi.fn(),
            onGetDepartmentCollectionDataError: vi.fn(),
            onGetCustomerCollectionDataSuccess: vi.fn()
        };

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Person/PersonFilterViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.Filters.PersonFilterViewModelCustom(viewModel);
        customViewModel.onBeforeInitialize();
    });

    it('should call original onGetCustomerCollectionDataSuccess handler', () => {
        // Setup spy on original handler
        const originalHandlerSpy = vi.spyOn(viewModel, 'onGetCustomerCollectionDataSuccess');

        // Create test data
        const testObjects = [{ Data: { Id: ko.observable('123') } }];

        // Call the overridden handler
        viewModel.onGetCustomerCollectionDataSuccess(testObjects);

        // Verify original handler was called
        expect(originalHandlerSpy).toHaveBeenCalledWith(testObjects);
    });

    it('should set default customer value when user has Customer role and CustomerId', () => {
        // Setup user claims with Customer role and CustomerId
        ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: ['Customer'],
            CustomerId: '123'
        });

        // Setup customer values
        const matchingCustomer = {
            value: {
                Data: {
                    Id: ko.observable('123')
                }
            }
        };
        viewModel.CustomerValues([matchingCustomer]);

        // Create test data
        const testObjects = [{ Data: { Id: ko.observable('123') } }];

        // Call the handler
        viewModel.onGetCustomerCollectionDataSuccess(testObjects);

        // Verify customer value was set
        expect(viewModel.filterData.fields.CustomerValue()).toEqual(matchingCustomer);
    });

    it('should not set customer value when user does not have Customer role', () => {
        // Setup user claims without Customer role
        ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: ['Admin'],
            CustomerId: '123'
        });

        // Setup customer values
        const matchingCustomer = {
            value: {
                Data: {
                    Id: ko.observable('123')
                }
            }
        };
        viewModel.CustomerValues([matchingCustomer]);

        // Create test data
        const testObjects = [{ Data: { Id: ko.observable('123') } }];

        // Call the handler
        viewModel.onGetCustomerCollectionDataSuccess(testObjects);

        // Verify customer value was not set
        expect(viewModel.filterData.fields.CustomerValue()).toBeNull();
    });

    it('should not set customer value when user has no CustomerId', () => {
        // Setup user claims with Customer role but no CustomerId
        ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: ['Customer'],
            CustomerId: null
        });

        // Setup customer values
        const matchingCustomer = {
            value: {
                Data: {
                    Id: ko.observable('123')
                }
            }
        };
        viewModel.CustomerValues([matchingCustomer]);

        // Create test data
        const testObjects = [{ Data: { Id: ko.observable('123') } }];

        // Call the handler
        viewModel.onGetCustomerCollectionDataSuccess(testObjects);

        // Verify customer value was not set
        expect(viewModel.filterData.fields.CustomerValue()).toBeNull();
    });

    it('should not set customer value when no matching customer found', () => {
        // Setup user claims with Customer role and CustomerId
        ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: ['Customer'],
            CustomerId: '999' // Non-existent customer ID
        });

        // Setup customer values with different ID
        const nonMatchingCustomer = {
            value: {
                Data: {
                    Id: ko.observable('123')
                }
            }
        };
        viewModel.CustomerValues([nonMatchingCustomer]);

        // Create test data
        const testObjects = [{ Data: { Id: ko.observable('123') } }];

        // Call the handler
        viewModel.onGetCustomerCollectionDataSuccess(testObjects);

        // Verify customer value was not set
        expect(viewModel.filterData.fields.CustomerValue()).toBeNull();
    });
}); 