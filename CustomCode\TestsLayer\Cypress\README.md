# Cypress Test Suite

This project contains Cypress end-to-end tests for the FleetXQ application.

## Getting Started

### Prerequisites

- Node.js (v14 or newer)
- npm

### Installation

```bash
npm install
```

## Running Tests

### Run All Tests in Order

The tests are configured to run in a specific order defined in `cypress.config.js`. To run all tests:

```bash
npm run cy:run
```

### Run Tests in UI Mode

To open the Cypress UI and run tests interactively:

```bash
npx cypress open
```

### Run a Specific Test File

To run a specific test file:

```bash
npx cypress run --spec "cypress/e2e/002 - customer.cy.js"
```

## Test Files

The tests are designed to run in this order:

1. `001 - dashboard.cy.js` - Dashboard tests
2. `002 - customer.cy.js` - Customer creation/editing tests
3. `003 - sites-flow.cy.js` - Sites-related tests
4. `999 - cleanup.cy.js` - Cleanup tests (removes created test data)

## Important Notes
- run 000 - prereq.cy.js first before doing any test to make sure you have the base customer/site/dept
- Do not run `000 - all.cy.js` directly. This file is just a placeholder to show test organization.
- Tests use global data from `cypress/fixtures/testData.json`
- Screenshot and videos are automatically saved in the `cypress/screenshots` and `cypress/videos` folders
- You can customize the base URL in `cypress.config.js`

## Troubleshooting

### Common Issues

- If tests are failing due to timeouts, try increasing the `defaultCommandTimeout` in `cypress.config.js`
- If elements aren't being found, check the selectors and make sure the application is in the expected state

### Debugging

- Run tests in UI mode for better debugging
- Check screenshots and videos for failed tests
- Use `cy.screenshot()` in your tests to capture specific states 