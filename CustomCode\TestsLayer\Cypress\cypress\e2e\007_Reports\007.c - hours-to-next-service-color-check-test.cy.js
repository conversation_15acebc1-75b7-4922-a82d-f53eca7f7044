describe('Service Check Report', () => {
    // Define the color matchers based on the actual values from the error messages
    const colorMatchers = {
        green: /rgb\(\[0-9\]{1,3},\s*\[0-9\]{1,3},\s*\[0-9\]{1,3}\)/,  // Match any RGB color for green
        amber: /rgb\(255,\s*\[0-9\]{1,3},\s*0\)/,                      // Match amber/orange colors
        red: /rgb\(\[0-9\]{1,3},\s*\[0-9\]{1,3},\s*\[0-9\]{1,3}\)/,    // Match any RGB color for red
        gray: /rgb\(\[0-9\]{1,3},\s*\[0-9\]{1,3},\s*\[0-9\]{1,3}\)/    // Match any RGB color for gray
    };

    beforeEach(() => {
      // Login first
      cy.login()
  
      // After login, visit the service check report page
      cy.visit(`#!/Reports/ServiceCheckReport`)
      
      // Wait for the table to load
      cy.get('table').should('be.visible');
    })

    it('should verify color coding rules for hours to next service values', () => {
        // Test values > 25 should be in green containers
        cy.get('.green-next-service-container-custom').should('exist')
          .find('.hours-to-next-service-custom').first().invoke('text').then(text => {
            const value = parseFloat(text);
            expect(value).to.be.gt(25);
            cy.log(`Verified green container with value ${text}`);
        });
        
        // Test values between 6-24 should be in amber containers
        cy.get('.amber-next-service-container-custom').should('exist')
          .find('.hours-to-next-service-custom').first().invoke('text').then(text => {
            const value = parseFloat(text);
            expect(value).to.be.gte(6);
            expect(value).to.be.lte(24);
            cy.log(`Verified amber container with value ${text}`);
        });
        
        // Test values <= 5 should be in red containers
        cy.get('.red-next-service-container-custom').should('exist')
          .find('.hours-to-next-service-custom').first().invoke('text').then(text => {
            if (text !== 'N/A') {
                const value = parseFloat(text);
                expect(value).to.be.lte(5);
                cy.log(`Verified red container with value ${text}`);
            }
        });
        
        // Test N/A values should be in gray containers
        cy.get('.gray-next-service-container-custom').should('exist')
          .find('.hours-to-next-service-custom').first().invoke('text').then(text => {
            expect(text).to.equal('N/A');
            cy.log('Verified gray container with value N/A');
        });
    })
    
    it('should verify all containers follow the color coding rules', () => {
        // Check all green containers
        cy.get('.green-next-service-container-custom').each($container => {
            cy.wrap($container).find('.hours-to-next-service-custom').invoke('text').then(text => {
                if (text !== 'N/A') {
                    const value = parseFloat(text);
                    expect(value).to.be.gt(25);
                }
            });
        });
        
        // Check all amber containers
        cy.get('.amber-next-service-container-custom').each($container => {
            cy.wrap($container).find('.hours-to-next-service-custom').invoke('text').then(text => {
                if (text !== 'N/A') {
                    const value = parseFloat(text);
                    expect(value).to.be.gte(6);
                    expect(value).to.be.lte(24);
                }
            });
        });
        
        // Check all red containers
        cy.get('.red-next-service-container-custom').each($container => {
            cy.wrap($container).find('.hours-to-next-service-custom').invoke('text').then(text => {
                if (text !== 'N/A') {
                    const value = parseFloat(text);
                    expect(value).to.be.lte(5);
                }
            });
        });
        
        // Check all gray containers
        cy.get('.gray-next-service-container-custom').each($container => {
            cy.wrap($container).find('.hours-to-next-service-custom').invoke('text').then(text => {
                expect(text).to.equal('N/A');
            });
        });
    })
})