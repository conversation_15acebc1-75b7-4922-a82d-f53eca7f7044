## Generate JIRA Task Description and Acceptance Criteria

Based on the provided **task title** and **additional information**, generate a detailed **JIRA task description** and **clear, testable acceptance criteria (AC)**.  
You have **direct access to the codebase** and are expected to analyze it automatically to gather relevant implementation details, affected modules, dependencies, and technical context. 
Output the .md file to prompts\jira-output. The .md file shouldn't contain special characters like "**" as the goal is a simple output for JIRA description field.

---

### Input:

- **Task Title**: Takes too long to load device IDs when creating vehicles (Needs Improvement)
- **Additional Information**: [Optional context such as user story, feature goal, background, or constraints]

---

###  Output Format:

#### Task Description:
Provide a concise but informative description of the task. Include:
- The purpose or goal of the task
- Relevant technical context based on your codebase analysis
- Key systems, files, or modules likely to be involved
- Any dependencies or architectural considerations

#### Acceptance Criteria:
List clear, testable criteria for task completion. Use bullet points

---

### Example Output:

#### Task Description (sample):
Enable profile picture updates via the user profile API. This enhancement supports new frontend requirements and involves updating the `UserController`, `UserService`, and related database schema. Based on code analysis, the `UserRepository` and image storage utility will also be affected.

#### Acceptance Criteria (sample):
- The API accepts a new `profile_picture` field in the update request.
- If an image is provided, it is stored in the configured S3 bucket.
- The image URL is saved in the user’s record in the database.
- Existing profile update functionality remains unchanged.
- Automated tests are updated to cover the new functionality.

---