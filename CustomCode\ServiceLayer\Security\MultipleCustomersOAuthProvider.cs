using System;
using System.Linq;
using System.Threading.Tasks;
using IdentityModel.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.OAuth;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.Transactions;
using System.Net.Http;

namespace CustomCode.ServiceLayer.Security
{
    public class MultipleCustomersOAuthProvider : OAuthProvider
    {
        private readonly IDataFacade _dataFacade;
        
        public MultipleCustomersOAuthProvider(IConfiguration configuration, IDataFacade dataFacade) : base(configuration)
        {
            _dataFacade = dataFacade;
        }

        public override (string uri, string code, string nonce) GetAuthorizationUrl(string username, string state)
        {
            // Call the async method synchronously
            return GetAuthorizationUrlAsync(username, state).GetAwaiter().GetResult();
        }

        // Create a new private async method to handle the async logic
        private async Task<(string uri, string code, string nonce)> GetAuthorizationUrlAsync(string username, string state)
        {
            // 1. Find GOUser for the username
            var goUser = (await _dataFacade.GOUserDataProvider.GetCollectionAsync(
                null, 
                "UserName == @0", 
                new object[] { username },
                skipSecurity: true
            )).SingleOrDefault();
            
            if (goUser == null)
            {
                throw new GOServerException($"User {username} not found.");
            }

            // 2. Identify the associated customer
            var person = await goUser.LoadPersonAsync(skipSecurity: true);
            if (person == null) {
                throw new GOServerException("User is not a customer user");
            }

            var customer = await person.LoadCustomerAsync(skipSecurity: true);
            if (customer == null)
            {
                throw new GOServerException("No customer associated with this user.");
            }

            // 3. Retrieve customer-specific OAuth configuration
            var customerConfigs = await customer.LoadCustomerSSODetailItemsAsync(skipSecurity: true);
            if (customerConfigs == null || !customerConfigs.Any())
            {
                throw new GOServerException($"No OAuth configuration found for customer {customer.CompanyName}.");
            }

            // Select the first customerConfig (adjust logic if multiple configs are possible)
            var customerConfig = customerConfigs.FirstOrDefault();
            if (customerConfig == null)
            {
                throw new GOServerException($"No valid OAuth configuration found for customer {customer.CompanyName}.");
            }
            var customerRedirectUri = customerConfig.RedirectURL;

            // Load OIDC configuration for this customer's authority
            var tenantId = customerConfig.TenantID;
            var discoveryUri = $"https://login.microsoftonline.com/{tenantId}/v2.0/.well-known/openid-configuration";

            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                discoveryUri, 
                new OpenIdConnectConfigurationRetriever()
            );
            
            var authorityConfiguration = await configurationManager.GetConfigurationAsync();

            // 4. Construct authorization URL using the customer's OAuth settings
            var nonce = Guid.NewGuid().ToString("N");
            var codeVerifier = PkceUtil.GenerateCodeVerifier();
            var codeChallenge = PkceUtil.GenerateCodeChallenge(codeVerifier);

            var request = new RequestUrl(authorityConfiguration.AuthorizationEndpoint);
            var authorizationUrl = request.CreateAuthorizeUrl(
                clientId: customerConfig.ClientID,
                responseType: "code id_token",
                responseMode: "form_post",
                scope: "openid profile email",
                redirectUri: GetRedirectUrl(customerConfig.RedirectURL),
                state: state,
                nonce: nonce,
                codeChallenge: codeChallenge,
                loginHint: username,
                codeChallengeMethod: "S256"
            );

            return (uri: authorizationUrl, code: codeVerifier, nonce: nonce);
        }

        /// <inheritdoc />
        public override async Task<OpenIdConnectConfiguration?> InitializeAsync(string username)
        {

            if (base._initialized)
            {
                return base._authorityConfiguration;
            }

            // 1. Find GOUser for the username
            var goUser = (await _dataFacade.GOUserDataProvider.GetCollectionAsync(
                null, 
                "UserName == @0", 
                new object[] { username },
                skipSecurity: true
            )).SingleOrDefault();
            
            if (goUser == null)
            {
                throw new GOServerException($"User {username} not found.");
            }

            // 2. Identify the associated customer
            var person = await goUser.LoadPersonAsync(skipSecurity: true);
            if (person == null) {
                throw new GOServerException("User is not a customer user");
            }

            var customer = await person.LoadCustomerAsync(skipSecurity: true);
            if (customer == null)
            {
                throw new GOServerException("No customer associated with this user.");
            }

            // 3. Retrieve customer-specific OAuth configuration
            var customerConfigs = await customer.LoadCustomerSSODetailItemsAsync(skipSecurity: true);
            if (customerConfigs == null || !customerConfigs.Any())
            {
                throw new GOServerException($"No OAuth configuration found for customer {customer.CompanyName}.");
            }

            // Select the first customerConfig (adjust logic if multiple configs are possible)
            var customerConfig = customerConfigs.FirstOrDefault();
            if (customerConfig == null)
            {
                throw new GOServerException($"No valid OAuth configuration found for customer {customer.CompanyName}.");
            }

            // 4. Set up the configuration using customer-specific values
            base._appConfiguration = new OidcAppOptions
            {
                ClientId = customerConfig.ClientID,
                RedirectUri = GetRedirectUrl(customerConfig.RedirectURL),
                ResponseType = "code id_token",
                Scope = "openid profile email"
            };

            // 5. Load OIDC configuration for this customer's authority
            var tenantId = customerConfig.TenantID;
            var discoveryUri = $"https://login.microsoftonline.com/{tenantId}/v2.0/.well-known/openid-configuration";
            
            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                discoveryUri,
                new OpenIdConnectConfigurationRetriever()
            );

            base._authorityConfiguration = await configurationManager.GetConfigurationAsync();
            
            if (base._authorityConfiguration.AuthorizationEndpoint == null || base._authorityConfiguration.TokenEndpoint == null)
            {
                using HttpClient client = new HttpClient();
                var obj = JsonConvert.DeserializeObject<OpenIdConnectConfiguration>(
                    await client.GetStringAsync(discoveryUri)
                );
                base._authorityConfiguration.AuthorizationEndpoint = obj.AuthorizationEndpoint;
                base._authorityConfiguration.TokenEndpoint = obj.TokenEndpoint;
            }

            base._initialized = true;
            return base._authorityConfiguration;
        }

        private string GetRedirectUrl(RedirectURLEnum redirectUrlEnum)
        {
            return redirectUrlEnum switch
            {
                RedirectURLEnum.US => "https://us.fleetxq.ciifm.com/Membership/AssertOAuth",
                RedirectURLEnum.UK => "https://uk.fleetxq.ciifm.com/Membership/AssertOAuth",
                RedirectURLEnum.AUFleetFocus => "https://fleetfocus.fleetxq.ciifm.com/Membership/AssertOAuth",
                RedirectURLEnum.AUFleetXQ => "https://au.fleetxq.ciifm.com/Membership/AssertOAuth",
                RedirectURLEnum.Staging => "https://pilot.fleetxq.ciifm.com/Membership/AssertOAuth",
                RedirectURLEnum.Development => "https://godev.collectiveintelligence.com.au/fleetxq-8735218d-3aeb-4563-bccb-8cdfcdf1188f/Membership/AssertOAuth",
                _ => throw new GOServerException($"Unsupported redirect URL enum value: {redirectUrlEnum}")
            };
        }
    }
}

