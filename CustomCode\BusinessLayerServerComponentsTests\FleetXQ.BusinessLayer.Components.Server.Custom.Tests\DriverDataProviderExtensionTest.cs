using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class DriverDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"DriverDataProviderExtensionTest-{Guid.NewGuid()}";
        private IDeviceTwinHandler _deviceTwinHandler;
        private DriverDataProviderExtension _driverDataProviderExtension;
        private Mock<IDataProviderExtensionProvider> _mockDataProvider;

        // Store test entities for reuse
        private Guid _testCustomerId;
        private Guid _testSiteId;
        private Guid _testDepartmentId;
        private Guid _testCardId;
        private Guid _testPersonId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add necessary services for testing
            services.AddTransient<DriverDataProviderExtension>();

            var mockDeviceMessageHandler = new Mock<IDeviceMessageHandler>();
            services.AddSingleton(mockDeviceMessageHandler.Object);

            var mockDeviceTwinHandler = new Mock<IDeviceTwinHandler>();
            mockDeviceTwinHandler.Setup(m => m.SyncDriverToVehicle(It.IsAny<string>(), It.IsAny<Guid>()))
                .Returns(Task.CompletedTask);
            services.AddSingleton(mockDeviceTwinHandler.Object);
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            _driverDataProviderExtension = _serviceProvider.GetRequiredService<DriverDataProviderExtension>();
            _mockDataProvider = new Mock<IDataProviderExtensionProvider>();
            _driverDataProviderExtension.Init(_mockDataProvider.Object);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            // Create test region (required for dealer)
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create test dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Create test customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);
            _testCustomerId = customer.Id;

            // Create test timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);
            _testSiteId = site.Id;

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);
            _testDepartmentId = department.Id;

            // Create permissions (required for card access)
            var normalDriverPermission = _serviceProvider.GetRequiredService<PermissionDataObject>();
            normalDriverPermission.Id = Guid.NewGuid();
            normalDriverPermission.Description = "Normal Driver";
            normalDriverPermission.LevelName = PermissionLevelEnum.NormalDriver;
            await _dataFacade.PermissionDataProvider.SaveAsync(normalDriverPermission, skipSecurity: true);

            var masterPermission = _serviceProvider.GetRequiredService<PermissionDataObject>();
            masterPermission.Id = Guid.NewGuid();
            masterPermission.Description = "Master";
            masterPermission.LevelName = PermissionLevelEnum.Master;
            await _dataFacade.PermissionDataProvider.SaveAsync(masterPermission, skipSecurity: true);

            // Create a test card 
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.CardNumber = "123456";
            card.FacilityCode = "45"; // Must be ≤ 255
            card.Active = true;
            card.Type = CardTypeEnum.CardID;
            card.KeypadReader = KeypadReaderEnum.Rosslare;
            card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);
            _testCardId = card.Id;

            // Create a test person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.FirstName = "John";
            person.LastName = "Doe";
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.IsDriver = true; // This creates a driver
            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
            _testPersonId = person.Id;
        }

        [Test]
        public async Task When_CardAssociatedWithDriver_Then_CardAccessIsCreated()
        {
            // Arrange
            // Create a person with required properties
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.FirstName = "John";
            person.LastName = "Doe";

            // Get a customer for the person
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(customer, Is.Not.Null, "Test data setup failed: No customer found");
            person.CustomerId = customer.Id;

            // Get a site for the person
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found");
            person.SiteId = site.Id;

            // Get a department for the person
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(department, Is.Not.Null, "Test data setup failed: No department found");
            person.DepartmentId = department.Id;

            // Make person a driver
            person.IsDriver = true;

            // Save the person (this will create a driver automatically)
            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Verify driver was created
            var driver = person.Driver;
            Assert.That(driver, Is.Not.Null, "Driver should be created when IsDriver=true");

            // Create card
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.CardNumber = "123456";
            card.FacilityCode = "45"; // Must be ≤ 255
            card.Active = true;
            card.Type = CardTypeEnum.CardID;
            card.KeypadReader = KeypadReaderEnum.Rosslare;
            // card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

            // Act - associate card with driver and update driver directly
            driver.Card = card;
            driver.CardDetailsId = card.Id;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Assert - check site access was created
            var siteAccesses = await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null,
                "CardId == @0 AND SiteId == @1", new object[] { card.Id, site.Id }, skipSecurity: true);
            Assert.That(siteAccesses, Is.Not.Empty, "Site card access should be created");

            // Check department access was created
            var departmentAccesses = await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.GetCollectionAsync(null,
                "CardId == @0 AND DepartmentId == @1", new object[] { card.Id, department.Id }, skipSecurity: true);
            Assert.That(departmentAccesses, Is.Not.Empty, "Department card access should be created");
        }
    }
}
