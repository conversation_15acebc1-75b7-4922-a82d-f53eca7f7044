﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class CustomerDataProviderExtension : IDataProviderExtension<CustomerDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private LocaleEnum? _originalPreferredLocale;

        public CustomerDataProviderExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterGet += OnAfterGetAsync;
            dataProvider.OnAfterGetCollection += OnAfterGetCollectionAsync;
            dataProvider.OnAfterSave += DataProvider_OnAfterSave;
            dataProvider.OnBeforeSave += DataProvider_OnBeforeSave;
        }

        private async Task DataProvider_OnBeforeSave(OnBeforeSaveEventArgs arg)
        {
            var customer = arg.Entity as CustomerDataObject;

            if (customer != null)
            {
                // Get the current preferred locale from the database
                if (!arg.Entity.IsNew)
                {
                    var currentCustomer = await _dataFacade.CustomerDataProvider.GetAsync(customer);
                    _originalPreferredLocale = currentCustomer?.PreferredLocale;
                }
                else
                {
                    _originalPreferredLocale = null;
                }

                if (customer.PreferredLocale.HasValue)
                {
                    customer.PreferredLocaleString = DataUtils.GetLocaleString(customer.PreferredLocale.Value);
                }
                else
                {
                    customer.PreferredLocaleString = null;
                }
            }
        }

        private async Task DataProvider_OnAfterSave(OnAfterSaveEventArgs arg)
        {
            var customer = arg.Entity as CustomerDataObject;
            if (customer == null) return;

            // Handle new customer creation
            if (arg.Entity.IsNew)
            {
                var defaultAccessGroupTemplates = await _dataFacade.AccessGroupTemplateDataProvider.GetCollectionAsync(null, "IsDefault == true");

                foreach (var accessGroupTemplate in defaultAccessGroupTemplates)
                {
                    var newAccessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
                    newAccessGroup.CustomerId = customer.Id;
                    newAccessGroup.Name = accessGroupTemplate.Name;
                    newAccessGroup.Description = accessGroupTemplate.Description;
                    newAccessGroup.CanDeleteCustomerEmailList = accessGroupTemplate.CanDeleteCustomerEmailList;
                    newAccessGroup.CanEditUserReportSubscription = accessGroupTemplate.CanEditUserReportSubscription;
                    newAccessGroup.CanViewPreopChecklistReport = accessGroupTemplate.CanViewPreopChecklistReport;
                    newAccessGroup.CanExportCurrentStatusReport = accessGroupTemplate.CanExportCurrentStatusReport;
                    newAccessGroup.CanCreateUser = accessGroupTemplate.CanCreateUser;
                    newAccessGroup.CanEditVehicleOtherSettingFullLockout = accessGroupTemplate.CanEditVehicleOtherSettingFullLockout;
                    newAccessGroup.CanViewUserSupervisorAccess = accessGroupTemplate.CanViewUserSupervisorAccess;
                    newAccessGroup.CanViewProficiencyReport = accessGroupTemplate.CanViewProficiencyReport;
                    newAccessGroup.CanViewAccessGroups = accessGroupTemplate.CanViewAccessGroups;
                    newAccessGroup.CanExportProficiencyReport = accessGroupTemplate.CanExportProficiencyReport;
                    newAccessGroup.CanEditCustomerFirmware = accessGroupTemplate.CanEditCustomerFirmware;
                    newAccessGroup.CanDeleteUserAlert = accessGroupTemplate.CanDeleteUserAlert;
                    newAccessGroup.CanDeleteUserReportSubscription = accessGroupTemplate.CanDeleteUserReportSubscription;
                    newAccessGroup.CanCreateCustomerSite = accessGroupTemplate.CanCreateCustomerSite;
                    newAccessGroup.CanEditVehicleOtherSettingVorStatus = accessGroupTemplate.CanEditVehicleOtherSettingVorStatus;
                    newAccessGroup.CanEditCustomerAccessGroups = accessGroupTemplate.CanEditCustomerAccessGroups;
                    newAccessGroup.CanExportPreopChecklistReport = accessGroupTemplate.CanExportPreopChecklistReport;
                    newAccessGroup.CanViewMachineUnlockReport = accessGroupTemplate.CanViewMachineUnlockReport;
                    newAccessGroup.CanViewVehicleSynchronization = accessGroupTemplate.CanViewVehicleSynchronization;
                    newAccessGroup.CanCreateVehicleChecklist = accessGroupTemplate.CanCreateVehicleChecklist;
                    newAccessGroup.CanDeleteCustomerEmailGroup = accessGroupTemplate.CanDeleteCustomerEmailGroup;
                    newAccessGroup.CanEditVehicle = accessGroupTemplate.CanEditVehicle;
                    newAccessGroup.CanViewVehicleImpactSetting = accessGroupTemplate.CanViewVehicleImpactSetting;
                    newAccessGroup.HasUsersAccess = accessGroupTemplate.HasUsersAccess;
                    newAccessGroup.CanViewUsers = accessGroupTemplate.CanViewUsers;
                    newAccessGroup.CanEditVehicleChecklist = accessGroupTemplate.CanEditVehicleChecklist;
                    newAccessGroup.CanViewGeneralProductivityReport = accessGroupTemplate.CanViewGeneralProductivityReport;
                    newAccessGroup.CanEditUserWebsiteAccess = accessGroupTemplate.CanEditUserWebsiteAccess;
                    newAccessGroup.CanViewDashboard = accessGroupTemplate.CanViewDashboard;
                    newAccessGroup.CanCreateCustomerEmailGroup = accessGroupTemplate.CanCreateCustomerEmailGroup;
                    newAccessGroup.CanCreateUserLicense = accessGroupTemplate.CanCreateUserLicense;
                    newAccessGroup.CanViewVehicleService = accessGroupTemplate.CanViewVehicleService;
                    newAccessGroup.CanViewServiceCheckReport = accessGroupTemplate.CanViewServiceCheckReport;
                    newAccessGroup.CanEditVehicleImpactSetting = accessGroupTemplate.CanEditVehicleImpactSetting;
                    newAccessGroup.CanEditCustomerSite = accessGroupTemplate.CanEditCustomerSite;
                    newAccessGroup.CanCreateCustomerAccessGroups = accessGroupTemplate.CanCreateCustomerAccessGroups;
                    newAccessGroup.CanViewVehicleChecklistSetting = accessGroupTemplate.CanViewVehicleChecklistSetting;
                    newAccessGroup.CanViewVehicleAccess = accessGroupTemplate.CanViewVehicleAccess;
                    newAccessGroup.CanViewVehicle = accessGroupTemplate.CanViewVehicle;
                    newAccessGroup.CanViewVehicleOtherSettingVorStatus = accessGroupTemplate.CanViewVehicleOtherSettingVorStatus;
                    newAccessGroup.CanCreateUserCard = accessGroupTemplate.CanCreateUserCard;
                    newAccessGroup.CanViewCustomer = accessGroupTemplate.CanViewCustomer;
                    newAccessGroup.CanViewImpactReport = accessGroupTemplate.CanViewImpactReport;
                    newAccessGroup.CanViewCustomerSite = accessGroupTemplate.CanViewCustomerSite;
                    newAccessGroup.CanExportServiceCheckReport = accessGroupTemplate.CanExportServiceCheckReport;
                    newAccessGroup.CanCreateUserWebsiteAccess = accessGroupTemplate.CanCreateUserWebsiteAccess;
                    newAccessGroup.CanViewCurrentStatusReport = accessGroupTemplate.CanViewCurrentStatusReport;
                    newAccessGroup.CanCreateVehicle = accessGroupTemplate.CanCreateVehicle;
                    newAccessGroup.CanViewUserReportSubscription = accessGroupTemplate.CanViewUserReportSubscription;
                    newAccessGroup.CanEditUserAlert = accessGroupTemplate.CanEditUserAlert;
                    newAccessGroup.CanCreateUserAlert = accessGroupTemplate.CanCreateUserAlert;
                    newAccessGroup.HasReportsAccess = accessGroupTemplate.HasReportsAccess;
                    newAccessGroup.CanViewVehicleOtherSettingFullLockout = accessGroupTemplate.CanViewVehicleOtherSettingFullLockout;
                    newAccessGroup.CanViewUserWebsiteAccess = accessGroupTemplate.CanViewUserWebsiteAccess;
                    newAccessGroup.CanViewCustomerDepartment = accessGroupTemplate.CanViewCustomerDepartment;
                    newAccessGroup.CanCreateCustomerEmailList = accessGroupTemplate.CanCreateCustomerEmailList;
                    newAccessGroup.CanExportGeneralProductivityReport = accessGroupTemplate.CanExportGeneralProductivityReport;
                    newAccessGroup.CanEditUser = accessGroupTemplate.CanEditUser;
                    newAccessGroup.CanViewCustomerEmailGroup = accessGroupTemplate.CanViewCustomerEmailGroup;
                    newAccessGroup.CanCreateVehicleService = accessGroupTemplate.CanCreateVehicleService;
                    newAccessGroup.CanViewUserLicense = accessGroupTemplate.CanViewUserLicense;
                    newAccessGroup.CanEditCustomerEmailGroup = accessGroupTemplate.CanEditCustomerEmailGroup;
                    newAccessGroup.CanViewUserCard = accessGroupTemplate.CanViewUserCard;
                    newAccessGroup.CanEditVehicleAccess = accessGroupTemplate.CanEditVehicleAccess;
                    newAccessGroup.CanCreateCustomerDepartment = accessGroupTemplate.CanCreateCustomerDepartment;
                    newAccessGroup.CanViewVehicleChecklist = accessGroupTemplate.CanViewVehicleChecklist;
                    newAccessGroup.CanEditCustomerDepartment = accessGroupTemplate.CanEditCustomerDepartment;
                    newAccessGroup.CanExportVehicle = accessGroupTemplate.CanExportVehicle;
                    newAccessGroup.HasVehiclesAccess = accessGroupTemplate.HasVehiclesAccess;
                    newAccessGroup.CanExportMachineUnlockReport = accessGroupTemplate.CanExportMachineUnlockReport;
                    newAccessGroup.CanEditUserSupervisorAccess = accessGroupTemplate.CanEditUserSupervisorAccess;
                    newAccessGroup.CanEditUserLicense = accessGroupTemplate.CanEditUserLicense;
                    newAccessGroup.CanEditUserCard = accessGroupTemplate.CanEditUserCard;
                    newAccessGroup.CanCreateUserReportSubscription = accessGroupTemplate.CanCreateUserReportSubscription;
                    newAccessGroup.HasCustomersAccess = accessGroupTemplate.HasCustomersAccess;
                    newAccessGroup.CanCreateVehicleChecklistSetting = accessGroupTemplate.CanCreateVehicleChecklistSetting;
                    newAccessGroup.CanViewUserAlert = accessGroupTemplate.CanViewUserAlert;
                    newAccessGroup.CanViewCustomerFirmware = accessGroupTemplate.CanViewCustomerFirmware;
                    newAccessGroup.CanExportUsers = accessGroupTemplate.CanExportUsers;
                    newAccessGroup.CanEditVehicleChecklistSetting = accessGroupTemplate.CanEditVehicleChecklistSetting;
                    newAccessGroup.CanViewCustomerModel = accessGroupTemplate.CanViewCustomerModel;
                    newAccessGroup.CanExportImpactReport = accessGroupTemplate.CanExportImpactReport;
                    newAccessGroup.CanDeleteVehicleChecklist = accessGroupTemplate.CanDeleteVehicleChecklist;
                    newAccessGroup.CanEditVehicleService = accessGroupTemplate.CanEditVehicleService;

                    await _dataFacade.AccessGroupDataProvider.SaveAsync(newAccessGroup);
                }
            }

            // Update preferred locale for all GO users linked to this customer only if the preferred locale has changed
            if (customer.PreferredLocale.HasValue && customer.PreferredLocale != _originalPreferredLocale)
            {
                // Get all persons linked to this customer
                var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, $"CustomerId == @0", new object[] { customer.Id });

                foreach (var person in persons)
                {
                    if (person.GOUserId.HasValue)
                    {
                        // Create a GOUserDataObject with the ID
                        var goUserKey = new GOUserDataObject(person.GOUserId.Value);

                        // Get the GO user linked to this person
                        var goUser = await _dataFacade.GOUserDataProvider.GetAsync(goUserKey);
                        if (goUser != null)
                        {
                            // Update the preferred locale
                            goUser.PreferredLocale = customer.PreferredLocale;
                            goUser.PreferredLocaleString = customer.PreferredLocaleString;

                            // Save the updated GO user
                            await _dataFacade.GOUserDataProvider.SaveAsync(goUser);
                        }
                    }
                }
            }
        }

        private async Task OnAfterGetCollectionAsync(OnAfterGetCollectionEventArgs e)
        {
            var customers = e.Result as DataObjectCollection<CustomerDataObject>;

            if (customers == null)
            {
                return;
            }

            foreach (var customer in customers)
            {
                await CalculateSitesCountAsync(customer, e.SkipSecurity);
            }
        }

        private async Task OnAfterGetAsync(OnAfterGetEventArgs e)
        {
            var customer = e.Entity as CustomerDataObject;

            if (customer != null)
            {
                return;
            }

            await CalculateSitesCountAsync(customer, e.SkipSecurity);
        }

        private async Task CalculateSitesCountAsync(CustomerDataObject customer, bool skipSecurity)
        {
            customer.SitesCount = (short)await _dataFacade.SiteDataProvider.CountAsync(null, "CustomerId == @0", new object[] { customer.Id }, skipSecurity: skipSecurity);
        }
    }
}
