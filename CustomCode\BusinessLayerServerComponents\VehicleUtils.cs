﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects.Custom;
using Microsoft.Azure.Devices;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public interface IVehicleUtils
    {
        Task<DeviceTwinReportedProperties> GetDeviceTwinReportedProperties(string deviceId);

        Task<String> GetLastActivityTime(string deviceId);
    }

    public partial class VehicleUtils : BaseServerComponent, IVehicleUtils
    {

        private RegistryManager _registryManager;

        public VehicleUtils(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
        {
            _registryManager = RegistryManager.CreateFromConnectionString(_configuration["IoThubConnectionString"]);
        }

        public async Task<DeviceTwinReportedProperties> GetDeviceTwinReportedProperties(string deviceId)
        {
            var queryString = "SELECT " +
                "properties.reported.configuration, " +
                "properties.reported.system, " +
                "properties.reported.diagnostics, " +
                "properties.reported.status " +
                "FROM devices where DeviceId = '" + deviceId + "'";
            var query = _registryManager.CreateQuery(queryString);

            DeviceTwinReportedProperties? deviceTwin = null;

            while (query.HasMoreResults)
            {
                var result = await query.GetNextAsJsonAsync();
                var firstResult = result.FirstOrDefault();
                if (firstResult != null)
                {
                    try
                    {
                        using (JsonDocument document = JsonDocument.Parse(firstResult))
                        {
                            var reportedProps = document.RootElement;

                            // Create a new JSON object with just the reported properties
                            var formattedJson = JsonSerializer.Serialize(new
                            {
                                configuration = reportedProps.GetProperty("configuration"),
                                system = reportedProps.GetProperty("system"),
                                diagnostics = reportedProps.GetProperty("diagnostics"),
                                status = reportedProps.GetProperty("status")
                            });

                            deviceTwin = JsonSerializer.Deserialize<DeviceTwinReportedProperties>(formattedJson);
                            break;
                        }
                    }
                    catch (JsonException ex)
                    {
                        Console.WriteLine($"Error parsing JSON: {ex.Message}");
                    }
                }

            }
            return deviceTwin;
        }

        public async Task<String> GetLastActivityTime(string deviceId)
        {
            var query = _registryManager.CreateQuery("SELECT LastActivityTime FROM devices where DeviceId = '" + deviceId + "'");
            List<DeviceLastActivityTime> results = new List<DeviceLastActivityTime>();
            while (query.HasMoreResults)
            {
                var result = await query.GetNextAsJsonAsync(); // Retrieve results as JSON
                foreach (var json in result)
                {
                    var device = Newtonsoft.Json.JsonConvert.DeserializeObject<DeviceLastActivityTime>(json); // Deserialize into your custom object
                    results.Add(device);
                }
            }

            if (results.Count > 0)
            {
                return results[0].LastActivityTime;
            }
            return "N/A";
        }

        private class DeviceLastActivityTime
        {
            public string LastActivityTime { get; set; }
        }
    }
}
