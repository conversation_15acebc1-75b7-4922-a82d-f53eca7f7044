using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using VDS.RDF;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class PersonDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"PersonDataProviderExtensionTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);

            // Run the seed script after database creation
            string sqlFileRootPath = "../../../../../../Scripts/OneOffScripts";
            string seedReportTypesPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "SeedReportTypes.sql"));
            if (!File.Exists(seedReportTypesPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedReportTypesPath}\"");
            }
            _databaseFactory.RunSqlScript(_testDatabaseName, File.ReadAllText(seedReportTypesPath));

            string seedAlertsPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "SeedAlerts.sql"));
            if (!File.Exists(seedAlertsPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedAlertsPath}\"");
            }
            _databaseFactory.RunSqlScript(_testDatabaseName, File.ReadAllText(seedAlertsPath));

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<SiteDataObject> CreateTestSiteAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            return await _dataFacade.SiteDataProvider.SaveAsync(site);
        }

        private async Task<DepartmentDataObject> CreateTestDepartmentAsync(SiteDataObject site)
        {
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.CustomerId = site.CustomerId;
            department.Name = "Test Department";
            return await _dataFacade.DepartmentDataProvider.SaveAsync(department);
        }

        private async Task<PersonDataObject> CreateTestPersonAsync(SiteDataObject site, DepartmentDataObject department, bool isDeleted = false)
        {
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = site.CustomerId;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.IsDriver = true;
            person.IsActiveDriver = true;
            person.FirstName = "Test";
            person.LastName = "Person";
            if (isDeleted)
            {
                person.DeletedAtUtc = DateTime.UtcNow;
            }
            return await _dataFacade.PersonDataProvider.SaveAsync(person);
        }

        private async Task<VehicleDataObject> CreateTestVehicleAsync(SiteDataObject site, DepartmentDataObject department)
        {
            // Create a test model first
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            var customerInit = _serviceProvider.GetRequiredService<CustomerDataObject>().Initialize(site.CustomerId);
            var customer = await _dataFacade.CustomerDataProvider.GetAsync(customerInit);
            model.DealerId = customer.DealerId; // Link model to the customer's dealer
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create a test module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.IoTDevice = "TEST-" + Guid.NewGuid().ToString();
            module.AmberImpact = 1.0;
            module.RedImpact = 1.0;
            module.FSSSBase = 1.0;
            module.FSSXMulti = 1.0;
            module.DealerId = customer.DealerId;
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = site.CustomerId;
            vehicle.SiteId = site.Id;
            vehicle.DepartmentId = department.Id;
            vehicle.HireNo = "Test Vehicle";
            vehicle.SerialNo = "TEST-" + Guid.NewGuid().ToString();
            vehicle.ModelId = model.Id;
            vehicle.ModuleId1 = module.Id;

            // Set other required fields with default values
            vehicle.ImpactLockout = false;
            vehicle.IsCanbus = false;
            vehicle.ModuleIsConnected = false;
            vehicle.OnHire = false;
            vehicle.TimeoutEnabled = false;

            return await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
        }

        private async Task<CardDataObject> CreateTestCardAsync(DriverDataObject driver)
        {
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            driver.CardDetailsId = card.Id;
            card.Weigand = "123";
            card.CardNumber = "TEST-" + Guid.NewGuid().ToString();
            card.FacilityCode = "1";
            card.Active = true;
            card.KeypadReader = KeypadReaderEnum.Smart;
            card.Type = CardTypeEnum.CardID;
            return await _dataFacade.CardDataProvider.SaveAsync(card);
        }

        [Test]
        public async Task OnBeforeGetCollection_ExcludesDeletedPersons()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);

            // Create active person
            var activePerson = await CreateTestPersonAsync(site, department, isDeleted: false);

            // Create deleted person
            var deletedPerson = await CreateTestPersonAsync(site, department, isDeleted: true);

            // Act
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(
                null, "SiteId == @0", new object[] { site.Id });

            // Assert
            Assert.That(persons.Count(), Is.EqualTo(1), "Should only return active persons");
            Assert.That(persons.First().Id, Is.EqualTo(activePerson.Id), "Should return the active person");
        }

        [Test]
        public async Task OnBeforeGetCollection_CombinesWithExistingFilter()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);

            // Create persons with different names
            var person1 = await CreateTestPersonAsync(site, department, isDeleted: false);
            person1.FirstName = "Test Person 1";
            await _dataFacade.PersonDataProvider.SaveAsync(person1);

            var person2 = await CreateTestPersonAsync(site, department, isDeleted: false);
            person2.FirstName = "Test Person 2";
            await _dataFacade.PersonDataProvider.SaveAsync(person2);

            var deletedPerson = await CreateTestPersonAsync(site, department, isDeleted: true);
            deletedPerson.FirstName = "Test Person 1";
            await _dataFacade.PersonDataProvider.SaveAsync(deletedPerson);

            // Act
            var persons = await _dataFacade.PersonDataProvider.GetCollectionAsync(
                null, "SiteId == @0 && FirstName == @1", new object[] { site.Id, "Test Person 1" });

            // Assert
            Assert.That(persons.Count(), Is.EqualTo(1), "Should only return active persons matching the filter");
            Assert.That(persons.First().Id, Is.EqualTo(person1.Id), "Should return the active person with matching name");
        }

        [Test]
        public async Task DataProvider_CalculateMasterMenuOptions_SetsCorrectValue()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department);

            // Test case 1: All permissions enabled - should be 15
            person.CanUnlockVehicle = true;
            person.NormalDriverAccess = true;
            person.VORActivateDeactivate = true;
            person.MaintenanceMode = true;

            await _dataFacade.PersonDataProvider.SaveAsync(person);
            var updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(updatedPerson.MasterMenuOptions, Is.EqualTo(15), "With all permissions enabled, MasterMenuOptions should be 15");

            // Test case 2: No permissions enabled - should be 0
            person.CanUnlockVehicle = false;
            person.NormalDriverAccess = false;
            person.VORActivateDeactivate = false;
            person.MaintenanceMode = false;

            await _dataFacade.PersonDataProvider.SaveAsync(person);
            updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(updatedPerson.MasterMenuOptions, Is.EqualTo(0), "With no permissions enabled, MasterMenuOptions should be 0");

            // Test case 3: Mixed permissions
            person.CanUnlockVehicle = true;  // Don't subtract 1
            person.NormalDriverAccess = false; // Subtract 2
            person.VORActivateDeactivate = true; // Don't subtract 4
            person.MaintenanceMode = false; // Subtract 8

            await _dataFacade.PersonDataProvider.SaveAsync(person);
            updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(updatedPerson.MasterMenuOptions, Is.EqualTo(5), "With mixed permissions, should be 15 - 2 - 8 = 5");

            // Test case 4: Different mixed permissions
            person.CanUnlockVehicle = false; // Subtract 1
            person.NormalDriverAccess = true; // Don't subtract 2
            person.VORActivateDeactivate = false; // Subtract 4
            person.MaintenanceMode = true; // Don't subtract 8

            await _dataFacade.PersonDataProvider.SaveAsync(person);
            updatedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(updatedPerson.MasterMenuOptions, Is.EqualTo(10), "With different mixed permissions, should be 15 - 1 - 4 = 10");
        }

        [Test]
        public async Task IsActiveDriver_WhenDeactivated_RemovesAllAccess()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var vehicle = await CreateTestVehicleAsync(site, department);

            // Create an active person with driver access
            var person = await CreateTestPersonAsync(site, department);
            var driver = await person.LoadDriverAsync();
            var card = await CreateTestCardAsync(driver);

            // Create initial vehicle access
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                "LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver })).FirstOrDefault();
            Assert.That(permission, Is.Not.Null, "Permission should exist");

            var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            vehicleAccess.CardId = card.Id;
            vehicleAccess.VehicleId = vehicle.Id;
            vehicleAccess.PermissionId = permission.Id;
            await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(vehicleAccess);

            // Act
            person.IsActiveDriver = false;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Wait a short time for background task to complete
            await Task.Delay(1000);

            // Assert
            // Reload card to check Weigand
            card = await _dataFacade.CardDataProvider.GetAsync(card);
            Assert.That(card.Weigand, Is.Null, "Weigand should be null when driver is deactivated");

            // Check vehicle access is removed
            await card.LoadPerVehicleNormalCardAccessItemsAsync();
            var activeAccess = card.PerVehicleNormalCardAccessItems?.Where(x => !x.IsMarkedForDeletion);
            Assert.That(activeAccess, Is.Empty, "All vehicle access should be marked for deletion");
        }

        [Test]
        public async Task IsActiveDriver_WhenReactivated_RestoresAccess()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var vehicle = await CreateTestVehicleAsync(site, department);

            // Create a deactivated person
            var person = await CreateTestPersonAsync(site, department);
            person.IsActiveDriver = false;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            var card = await CreateTestCardAsync(driver);

            // Act
            person.IsActiveDriver = true;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Assert
            // Check site access is created
            await card.LoadSiteVehicleNormalCardAccessItemsAsync();
            Assert.That(card.SiteVehicleNormalCardAccessItems, Is.Not.Empty, "Site access should be created");
            Assert.That(card.SiteVehicleNormalCardAccessItems.Any(x => x.SiteId == site.Id), Is.True, "Site access should be for correct site");

            // Check department access is created
            await card.LoadDepartmentVehicleNormalCardAccessItemsAsync();
            Assert.That(card.DepartmentVehicleNormalCardAccessItems, Is.Not.Empty, "Department access should be created");
            Assert.That(card.DepartmentVehicleNormalCardAccessItems.Any(x => x.DepartmentId == department.Id), Is.True, "Department access should be for correct department");

            // Check vehicle access is created
            await card.LoadPerVehicleNormalCardAccessItemsAsync();
            Assert.That(card.PerVehicleNormalCardAccessItems, Is.Not.Empty, "Vehicle access should be created");
            Assert.That(card.PerVehicleNormalCardAccessItems.Any(x => x.VehicleId == vehicle.Id), Is.True, "Vehicle access should be for correct vehicle");
        }

        [Test]
        public async Task IsActiveDriver_WhenReactivatedAsSupervisor_CreatesSupervisorAccess()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var vehicle = await CreateTestVehicleAsync(site, department);

            // Create a deactivated supervisor
            var person = await CreateTestPersonAsync(site, department);
            person.IsActiveDriver = false;
            person.Supervisor = true;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            var card = await CreateTestCardAsync(driver);

            var masterPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master })).FirstOrDefault();
            Assert.That(masterPermission, Is.Not.Null, "Master permission should exist");

            // Act
            person.IsActiveDriver = true;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Assert
            // Check supervisor site access is created
            await card.LoadSiteVehicleNormalCardAccessItemsAsync();
            Assert.That(card.SiteVehicleNormalCardAccessItems.Any(x =>
                x.SiteId == site.Id && x.PermissionId == masterPermission.Id),
                Is.True, "Supervisor site access should be created");

            // Check supervisor department access is created
            await card.LoadDepartmentVehicleNormalCardAccessItemsAsync();
            Assert.That(card.DepartmentVehicleNormalCardAccessItems.Any(x =>
                x.DepartmentId == department.Id && x.PermissionId == masterPermission.Id),
                Is.True, "Supervisor department access should be created");

            // Check supervisor vehicle access is created
            await card.LoadPerVehicleNormalCardAccessItemsAsync();
            Assert.That(card.PerVehicleNormalCardAccessItems.Any(x =>
                x.VehicleId == vehicle.Id && x.PermissionId == masterPermission.Id),
                Is.True, "Supervisor vehicle access should be created");
        }
    }
}