CREATE TABLE [dbo].[AccessGroup] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[AccessRules] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CanDeleteCustomerEmailList] [bit] NOT NULL, 
	[CanEditUserReportSubscription] [bit] NOT NULL, 
	[CanViewPreopChecklistReport] [bit] NOT NULL, 
	[CanExportCurrentStatusReport] [bit] NOT NULL, 
	[CanCreateUser] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanViewUserSupervisorAccess] [bit] NOT NULL, 
	[CanViewProficiencyReport] [bit] NOT NULL, 
	[CanDeleteCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewAccessGroups] [bit] NOT NULL, 
	[CanExportProficiencyReport] [bit] NOT NULL, 
	[CanEditCustomerFirmware] [bit] NOT NULL, 
	[CanDeleteUserAlert] [bit] NOT NULL, 
	[CanDeleteUserReportSubscription] [bit] NOT NULL, 
	[CanCreateCustomerSite] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanEditCustomerAccessGroups] [bit] NOT NULL, 
	[CanExportPreopChecklistReport] [bit] NOT NULL, 
	[CanViewMachineUnlockReport] [bit] NOT NULL, 
	[CanViewVehicleSynchronization] [bit] NOT NULL, 
	[CanCreateVehicleChecklist] [bit] NOT NULL, 
	[CanDeleteCustomerEmailGroup] [bit] NOT NULL, 
	[CanDeleteUser] [bit] NOT NULL, 
	[CanEditVehicle] [bit] NOT NULL, 
	[CanViewVehicleImpactSetting] [bit] NOT NULL, 
	[HasUsersAccess] [bit] NOT NULL, 
	[CanViewUsers] [bit] NOT NULL, 
	[CanEditVehicleChecklist] [bit] NOT NULL, 
	[CanViewGeneralProductivityReport] [bit] NOT NULL, 
	[CanEditUserWebsiteAccess] [bit] NOT NULL, 
	[CanViewDashboard] [bit] NOT NULL, 
	[CanCreateCustomerEmailGroup] [bit] NOT NULL, 
	[CanCreateUserLicense] [bit] NOT NULL, 
	[CanViewVehicleService] [bit] NOT NULL, 
	[CanViewServiceCheckReport] [bit] NOT NULL, 
	[CanEditVehicleImpactSetting] [bit] NOT NULL, 
	[CanEditCustomerSite] [bit] NOT NULL, 
	[CanCreateCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewVehicleAccess] [bit] NOT NULL, 
	[CanViewVehicle] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanCreateUserCard] [bit] NOT NULL, 
	[CanViewCustomer] [bit] NOT NULL, 
	[CanViewImpactReport] [bit] NOT NULL, 
	[CanViewCustomerSite] [bit] NOT NULL, 
	[CanExportServiceCheckReport] [bit] NOT NULL, 
	[CanCreateUserWebsiteAccess] [bit] NOT NULL, 
	[CanViewCurrentStatusReport] [bit] NOT NULL, 
	[CanCreateVehicle] [bit] NOT NULL, 
	[CanViewUserReportSubscription] [bit] NOT NULL, 
	[CanEditUserAlert] [bit] NOT NULL, 
	[CanCreateUserAlert] [bit] NOT NULL, 
	[HasReportsAccess] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanViewUserWebsiteAccess] [bit] NOT NULL, 
	[CanViewCustomerDepartment] [bit] NOT NULL, 
	[CanDeleteVehicleService] [bit] NOT NULL, 
	[CanCreateCustomerEmailList] [bit] NOT NULL, 
	[CanExportGeneralProductivityReport] [bit] NOT NULL, 
	[CanEditUser] [bit] NOT NULL, 
	[CanViewCustomerEmailGroup] [bit] NOT NULL, 
	[CanCreateVehicleService] [bit] NOT NULL, 
	[CanViewUserLicense] [bit] NOT NULL, 
	[CanEditCustomer] [bit] NOT NULL, 
	[CanEditCustomerEmailGroup] [bit] NOT NULL, 
	[CanViewUserCard] [bit] NOT NULL, 
	[CanEditVehicleAccess] [bit] NOT NULL, 
	[CanCreateCustomerDepartment] [bit] NOT NULL, 
	[CanViewVehicleChecklist] [bit] NOT NULL, 
	[CanEditCustomerDepartment] [bit] NOT NULL, 
	[CanExportVehicle] [bit] NOT NULL, 
	[HasVehiclesAccess] [bit] NOT NULL, 
	[CanExportMachineUnlockReport] [bit] NOT NULL, 
	[CanEditUserSupervisorAccess] [bit] NOT NULL, 
	[CanEditUserLicense] [bit] NOT NULL, 
	[CanEditUserCard] [bit] NOT NULL, 
	[CanCreateUserReportSubscription] [bit] NOT NULL, 
	[HasCustomersAccess] [bit] NOT NULL, 
	[CanCreateVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewUserAlert] [bit] NOT NULL, 
	[CanViewCustomerFirmware] [bit] NOT NULL, 
	[CanExportUsers] [bit] NOT NULL, 
	[CanEditVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewCustomerModel] [bit] NOT NULL, 
	[CanExportImpactReport] [bit] NOT NULL, 
	[CanDeleteVehicleChecklist] [bit] NOT NULL, 
	[CanEditVehicleService] [bit] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AccessGroupTemplate] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[CanDeleteUserAlert] [bit] NOT NULL, 
	[CanViewCustomerSite] [bit] NOT NULL, 
	[CanEditUserReportSubscription] [bit] NOT NULL, 
	[CanEditUserWebsiteAccess] [bit] NOT NULL, 
	[CanDeleteCustomerEmailList] [bit] NOT NULL, 
	[CanEditUserSupervisorAccess] [bit] NOT NULL, 
	[CanCreateVehicleChecklistSetting] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanDeleteCustomerEmailGroup] [bit] NOT NULL, 
	[CanEditVehicleAccess] [bit] NOT NULL, 
	[CanExportImpactReport] [bit] NOT NULL, 
	[IsDefault] [bit] NOT NULL, 
	[CanExportCurrentStatusReport] [bit] NOT NULL, 
	[CanViewVehicleChecklistSetting] [bit] NOT NULL, 
	[HasReportsAccess] [bit] NOT NULL, 
	[CanCreateUserLicense] [bit] NOT NULL, 
	[CanViewVehicleImpactSetting] [bit] NOT NULL, 
	[CanCreateCustomerEmailGroup] [bit] NOT NULL, 
	[CanEditVehicleService] [bit] NOT NULL, 
	[CanViewVehicleAccess] [bit] NOT NULL, 
	[CanCreateUser] [bit] NOT NULL, 
	[CanCreateUserAlert] [bit] NOT NULL, 
	[CanCreateUserReportSubscription] [bit] NOT NULL, 
	[CanViewCurrentStatusReport] [bit] NOT NULL, 
	[CanViewMachineUnlockReport] [bit] NOT NULL, 
	[CanViewAccessGroups] [bit] NOT NULL, 
	[CanViewVehicleService] [bit] NOT NULL, 
	[CanCreateCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewUserAlert] [bit] NOT NULL, 
	[CanViewUserLicense] [bit] NOT NULL, 
	[CanExportUsers] [bit] NOT NULL, 
	[CanEditCustomerEmailGroup] [bit] NOT NULL, 
	[CanEditUserAlert] [bit] NOT NULL, 
	[CanViewCustomerDepartment] [bit] NOT NULL, 
	[CanEditVehicleImpactSetting] [bit] NOT NULL, 
	[CanExportServiceCheckReport] [bit] NOT NULL, 
	[CanViewUserCard] [bit] NOT NULL, 
	[CanEditUser] [bit] NOT NULL, 
	[CanViewUserReportSubscription] [bit] NOT NULL, 
	[CanViewCustomerEmailGroup] [bit] NOT NULL, 
	[CanViewProficiencyReport] [bit] NOT NULL, 
	[CanCreateCustomerSite] [bit] NOT NULL, 
	[CanEditVehicle] [bit] NOT NULL, 
	[CanViewGeneralProductivityReport] [bit] NOT NULL, 
	[CanViewCustomer] [bit] NOT NULL, 
	[CanViewVehicleSynchronization] [bit] NOT NULL, 
	[CanDeleteVehicleChecklist] [bit] NOT NULL, 
	[CanCreateCustomerDepartment] [bit] NOT NULL, 
	[CanViewCustomerFirmware] [bit] NOT NULL, 
	[CanEditCustomerFirmware] [bit] NOT NULL, 
	[CanEditVehicleChecklistSetting] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanExportGeneralProductivityReport] [bit] NOT NULL, 
	[CanEditCustomerDepartment] [bit] NOT NULL, 
	[CanViewCustomerModel] [bit] NOT NULL, 
	[CanViewVehicleOtherSettingFullLockout] [bit] NOT NULL, 
	[CanEditVehicleChecklist] [bit] NOT NULL, 
	[CanCreateUserCard] [bit] NOT NULL, 
	[CanEditVehicleOtherSettingVorStatus] [bit] NOT NULL, 
	[CanExportProficiencyReport] [bit] NOT NULL, 
	[CanExportVehicle] [bit] NOT NULL, 
	[CanEditCustomerAccessGroups] [bit] NOT NULL, 
	[CanViewPreopChecklistReport] [bit] NOT NULL, 
	[CanViewDashboard] [bit] NOT NULL, 
	[CanCreateVehicleChecklist] [bit] NOT NULL, 
	[CanViewVehicle] [bit] NOT NULL, 
	[CanExportMachineUnlockReport] [bit] NOT NULL, 
	[CanViewServiceCheckReport] [bit] NOT NULL, 
	[CanViewImpactReport] [bit] NOT NULL, 
	[HasCustomersAccess] [bit] NOT NULL, 
	[CanEditUserCard] [bit] NOT NULL, 
	[CanCreateVehicleService] [bit] NOT NULL, 
	[CanViewUsers] [bit] NOT NULL, 
	[CanViewUserSupervisorAccess] [bit] NOT NULL, 
	[HasVehiclesAccess] [bit] NOT NULL, 
	[CanViewUserWebsiteAccess] [bit] NOT NULL, 
	[CanExportPreopChecklistReport] [bit] NOT NULL, 
	[HasUsersAccess] [bit] NOT NULL, 
	[CanCreateCustomerEmailList] [bit] NOT NULL, 
	[CanEditUserLicense] [bit] NOT NULL, 
	[CanEditCustomerSite] [bit] NOT NULL, 
	[CanViewVehicleChecklist] [bit] NOT NULL, 
	[CanCreateUserWebsiteAccess] [bit] NOT NULL, 
	[CanCreateVehicle] [bit] NOT NULL, 
	[CanDeleteUserReportSubscription] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AccessGroupToSite] 
(
	[AccessGroupId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Alert] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Paragraph1] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Subject] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Paragraph2] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Signature] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[AlertSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[IsActive] [bit] NOT NULL, 
	[PersonId] [uniqueidentifier] NOT NULL, 
	[GOUserId] [uniqueidentifier] NULL, 
	[AlertId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[BroadcastMessage] 
(
	[Timeout] [int] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Message] [nvarchar] (300) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Priority] [int] NOT NULL, 
	[ResponseOptions] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Canrule] 
(
	[VehicleSerial] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[CRC] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CanruleDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Canrules] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[CanruleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Card] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NULL, 
	[Weigand] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CardNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FacilityCode] [nvarchar] (3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Active] [bit] NOT NULL, 
	[Type] [int] NOT NULL, 
	[KeypadReader] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CardToCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CardDetailsId] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CategoryTemplate] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Picture] [nvarchar](1000) NULL, 
	[PictureFileSize] [int] NULL, 
	[PictureInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ChecklistDetail] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Answer] [bit] NOT NULL, 
	[Failed] [bit] NULL, 
	[ChecklistResultId] [uniqueidentifier] NOT NULL, 
	[PreOperationalChecklistId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ChecklistResult] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Comment] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[EndTime] [datetime] NULL, 
	[StartTime] [datetime] NOT NULL, 
	[SessionId1] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ChecklistSettings] 
(
	[QuestionTimeout] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ShowComment] [bit] NOT NULL, 
	[Randomisation] [bit] NOT NULL, 
	[Type] [int] NOT NULL, 
	[TimeslotTwo] [time] NULL, 
	[TimeslotThree] [time] NULL, 
	[TimeslotFour] [time] NULL, 
	[TimeslotOne] [time] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ContactPersonInformation] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Address] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[PhoneNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FirstName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Country] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Code] [nvarchar] (4) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Customer] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ContactNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ContractNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Prefix] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CompanyName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ConnectionString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PreferredLocaleString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Addess] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ContractDate] [datetime] NULL, 
	[Active] [bit] NOT NULL, 
	[DealerCustomer] [bit] NULL, 
	[PreferredLocale] [int] NULL, 
	[CustomerLogo] [nvarchar](1000) NULL, 
	[CustomerLogoFileSize] [int] NULL, 
	[CustomerLogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DealerId] [uniqueidentifier] NOT NULL, 
	[CountryId] [uniqueidentifier] NOT NULL, 
	[ContactPersonInformationId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerModel] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Polarity] [int] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerPreOperationalChecklistTemplate] 
(
	[Order] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Question] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Critical] [bit] NOT NULL, 
	[Active] [bit] NOT NULL, 
	[ExpectedAnswer] [bit] NOT NULL, 
	[AnswerType] [int] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[CustomerSSODetail] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ClientID] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[TenantID] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[RedirectURL] [int] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Dealer] 
(
	[ContractNumber] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[PortalURL] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Active] [bit] NOT NULL, 
	[IsAPIEnabled] [bit] NOT NULL, 
	[RegionId] [uniqueidentifier] NOT NULL, 
	[DealerConfigurationId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DealerConfiguration] 
(
	[Id] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DealerDriver] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[DriverType] [int] NOT NULL, 
	[CardId] [uniqueidentifier] NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Department] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Active] [bit] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DepartmentHourSettingsId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentChecklist] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[IsThaiEnabled] [bit] NOT NULL, 
	[ModelId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentHourSettings] 
(
	[SaturdayAvailableMinutes] [int] NULL, 
	[TuesdayAvailableHours] [int] NULL, 
	[ThursdayAvailableHours] [int] NULL, 
	[WednesdayAvailableHours] [int] NULL, 
	[MondayAvailableHours] [int] NULL, 
	[SundayAvailableMinutes] [int] NULL, 
	[FridayAvailableHours] [int] NULL, 
	[SaturdayAvailableHours] [int] NULL, 
	[FridayAvailableMinutes] [int] NULL, 
	[TuesdayAvailableMinutes] [int] NULL, 
	[MondayAvailableMinutes] [int] NULL, 
	[WednesdayAvailableMinutes] [int] NULL, 
	[SundayAvailableHours] [int] NULL, 
	[ThursdayAvailableMinutes] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[MondayOpenMinutes] [int] NULL, 
	[SundayBreaktimeMinutes] [int] NULL, 
	[SaturdayOpenHours] [int] NULL, 
	[FridayBreaktimeHours] [int] NULL, 
	[WednesdayOpenHours] [int] NULL, 
	[TuesdayBreaktimeHours] [int] NULL, 
	[TuesdayOpenHours] [int] NULL, 
	[SaturdayBreaktimeHours] [int] NULL, 
	[WednesdayBreaktimeHours] [int] NULL, 
	[MondayBreaktimeMinutes] [int] NULL, 
	[MondayBreaktimeHours] [int] NULL, 
	[ThursdayBreaktimeMinutes] [int] NULL, 
	[FridayOpenMinutes] [int] NULL, 
	[SaturdayBreaktimeMinutes] [int] NULL, 
	[ThursdayOpenMinutes] [int] NULL, 
	[ThursdayOpenHours] [int] NULL, 
	[WednesdayBreaktimeMinutes] [int] NULL, 
	[SaturdayOpenMinutes] [int] NULL, 
	[TuesdayBreaktimeMinutes] [int] NULL, 
	[TuesdayOpenMinutes] [int] NULL, 
	[FridayBreaktimeMinutes] [int] NULL, 
	[SundayOpenMinutes] [int] NULL, 
	[ThursdayBreaktimeHours] [int] NULL, 
	[WednesdayOpenMinutes] [int] NULL, 
	[SundayBreaktimeHours] [int] NULL, 
	[FridayOpenHours] [int] NULL, 
	[SundayOpenHours] [int] NULL, 
	[MondayOpenHours] [int] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentVehicleMasterCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[DepartmentVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Driver] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[LastSessionId] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastSessionDate] [datetime] NULL, 
	[LastSessionDateTzAdjusted] [datetime] NULL, 
	[VehicleAccess] [bit] NULL, 
	[Active] [bit] NOT NULL, 
	[LicenseMode] [int] NOT NULL, 
	[LicenceDetailId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NULL, 
	[SiteId] [uniqueidentifier] NULL, 
	[CardDetailsId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[EmailGroups] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[EmailGroupsToPerson] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[CustomerToPersonViewId] [uniqueidentifier] NULL, 
	[EmailGroupsId] [uniqueidentifier] NOT NULL, 
	[PersonId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Firmware] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Url] [nvarchar] (255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Version] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GOTask] 
(
	[Progress] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Info] [nvarchar] (1024) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Error] [nvarchar] (1024) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Latest] [datetime] NULL, 
	[End] [datetime] NULL, 
	[Start] [datetime] NOT NULL, 
	[TaskStatus] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GoUserToCustomer] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[GPSHistory] 
(
	[Longitude] [smallint] NULL, 
	[Latitude] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[GPSDateTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Help] 
(
	[Id] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Impact] 
(
	[Threshold] [float] NOT NULL, 
	[Longitude] [float] NULL, 
	[ShockValue] [float] NOT NULL, 
	[Latitude] [float] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ImpactDateTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Inspection] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Notes] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[NextInspectionDate] [datetime] NULL, 
	[InspectionDate] [datetime] NULL, 
	[UploadDocument] [nvarchar](1000) NULL, 
	[UploadDocumentFileSize] [int] NULL, 
	[UploadDocumentInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[IOFIELD] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Measurement] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[IOType] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[CANBUS] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[IoTDeviceMessageCache] 
(
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Message] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[EventType] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastUpdate] [datetime] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[LicenceDetail] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[LicenseNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ExpiryDate] [datetime] NOT NULL, 
	[Document] [nvarchar](1000) NULL, 
	[DocumentFileSize] [int] NULL, 
	[DocumentInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[LicenseByModel] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ExpiryDate] [datetime] NOT NULL, 
	[ModelImage] [nvarchar](1000) NULL, 
	[ModelImageFileSize] [int] NULL, 
	[ModelImageInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DriverId] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[MessageHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[DeliveredTimestamp] [datetime] NULL, 
	[SentTimestamp] [datetime] NOT NULL, 
	[MessageStatus] [int] NOT NULL, 
	[SyncType] [int] NOT NULL, 
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Model] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Type] [int] NULL, 
	[ModelPicture] [nvarchar](1000) NULL, 
	[ModelPictureFileSize] [int] NULL, 
	[ModelPictureInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DealerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ModelVehicleMasterCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ModelVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[ModelId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Module] 
(
	[FSSXMulti] [float] NOT NULL, 
	[SyncVersion] [int] NULL, 
	[FSSSBase] [float] NOT NULL, 
	[Calibration] [int] NULL, 
	[BlueImpact] [float] NULL, 
	[RedImpact] [float] NOT NULL, 
	[AmberImpact] [float] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[DeviceTwin] [nvarchar] (MAX) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[TechNumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[RANumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromDepartment] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromSite] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[IoTDevice] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[OldDeviceID] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Note] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromSerial] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SimCardNumber] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromCustomer] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CCID] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastActivityTime] [datetime] NULL, 
	[CalibrationDate] [datetime] NULL, 
	[LastUpdateTime] [datetime] NULL, 
	[CalibrationResetDate] [datetime] NULL, 
	[CCIDUpdateDateTime] [datetime] NULL, 
	[SimCardDate] [datetime] NULL, 
	[SwapDate] [datetime] NULL, 
	[IsAllocatedToVehicle] [bit] NULL, 
	[Status] [int] NULL, 
	[ModuleType] [int] NULL, 
	[DealerId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ModuleHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Note] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[RANumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[TechNumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CCID] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FromDeviceID] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[NewIoTDeviceId] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SimCardNumber] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[OldIoTDeviceId] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SwapDateTime] [datetime] NULL, 
	[SimCardDate] [datetime] NULL, 
	[EditDateTime] [datetime] NOT NULL, 
	[Status] [int] NULL, 
	[ModuleType] [int] NULL, 
	[ModuleId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[NetworkSettings] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[WifiPassword] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SSID] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[OnDemandSession] 
(
	[HoursFrom] [int] NOT NULL, 
	[Usage] [bigint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[EndTime] [datetime] NULL, 
	[StartTime] [datetime] NULL, 
	[SendFlag] [int] NOT NULL, 
	[DriverId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[OnDemandSettings] 
(
	[HourlyRate] [real] NULL, 
	[MaxHourlyRate] [real] NULL, 
	[SessionTime] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Authorised] [bit] NOT NULL, 
	[OnDemandCommand] [int] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Permission] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LevelName] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Person] 
(
	[YearOfStayCount] [smallint] NULL, 
	[MasterMenuOptions] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Notes] [nvarchar] (155) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Phone] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirstName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastMedicalDate] [datetime] NULL, 
	[ExitDate] [datetime] NULL, 
	[EntryDate] [datetime] NULL, 
	[DeletedAtUtc] [datetime] NULL, 
	[VehicleAccess] [bit] NOT NULL, 
	[HasLicense] [bit] NOT NULL, 
	[Supervisor] [bit] NULL, 
	[OnDemand] [bit] NOT NULL, 
	[VORActivateDeactivate] [bit] NOT NULL, 
	[IsActiveDriver] [bit] NULL, 
	[NormalDriverAccess] [bit] NOT NULL, 
	[WebSiteAccess] [bit] NULL, 
	[LicenseActive] [bit] NOT NULL, 
	[MaintenanceMode] [bit] NOT NULL, 
	[CanUnlockVehicle] [bit] NOT NULL, 
	[IsDriver] [bit] NULL, 
	[AccessLevel] [nvarchar](2000) NULL, 
	[Photo] [nvarchar](1000) NULL, 
	[PhotoFileSize] [int] NULL, 
	[PhotoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[WebsiteUserId] [uniqueidentifier] NULL, 
	[PersonChecklistLanguageSettingsId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NULL, 
	[GOUserId] [uniqueidentifier] NULL, 
	[AccessGroupId] [uniqueidentifier] NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PersonAllocation] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PersonId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DepartmentId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PersonChecklistLanguageSettings] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Language] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PerVehicleMasterCardAccess] 
(
	[SlotNumber] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[SiteVehicleNormalCardAccessId] [uniqueidentifier] NULL, 
	[DepartmentVehicleMasterCardAccessId] [uniqueidentifier] NULL, 
	[ModelVehicleMasterCardAccessId] [uniqueidentifier] NULL, 
	[CardId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PerVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PreOperationalChecklist] 
(
	[Order] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[Question] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ThaiQuestion] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Active] [bit] NOT NULL, 
	[ExcludeRandom] [bit] NOT NULL, 
	[ExpectedAnswer] [bit] NOT NULL, 
	[Critical] [bit] NOT NULL, 
	[AnswerType] [int] NOT NULL, 
	[SiteChecklistId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[PSTATDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Usage] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[UTCTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL, 
	[IOFIELDId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Region] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Subregion] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Active] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ReportSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Subject] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ReportStartTime] [datetime] NULL, 
	[ReportEndTime] [datetime] NULL, 
	[StartDate] [datetime] NOT NULL, 
	[NextRuntime] [datetime] NULL, 
	[Frequency] [int] NOT NULL, 
	[ReportGenerationTime] [int] NOT NULL, 
	[ReportTypeId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NULL, 
	[PersonId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NULL, 
	[GOUserId] [uniqueidentifier] NULL, 
	[EmailGroupsId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ReportType] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ReportType] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[ServiceSettings] 
(
	[CurrentMeterReading] [float] NULL, 
	[LastServiceHours] [float] NULL, 
	[NextServiceType] [float] NULL, 
	[CurrentMeterReadingHrs] [float] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[NextServiceDate] [datetime] NULL, 
	[LastServiceDate] [datetime] NULL, 
	[CANBUS] [bit] NOT NULL, 
	[ServiceHoursInterval] [int] NULL, 
	[DateIntervalValue] [int] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Session] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[StartTime] [datetime] NOT NULL, 
	[EndTime] [datetime] NULL, 
	[isVOR] [bit] NULL, 
	[DriverId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SessionDetails] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Usage] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SessionId] [uniqueidentifier] NOT NULL, 
	[IOFIELDId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Site] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Name] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Address] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Active] [bit] NOT NULL, 
	[UnlockSetting] [bit] NOT NULL, 
	[EnableUnlockReasonType] [int] NULL, 
	[TimezoneId] [uniqueidentifier] NOT NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SiteVehicleMasterCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[SiteVehicleNormalCardAccess] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[PermissionId] [uniqueidentifier] NOT NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[CardId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Timezone] 
(
	[UTCOffset] [smallint] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[TimezoneName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[Vehicle] 
(
	[IDLETimer] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[SerialNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LastSessionId] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[HireNo] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ModuleSwapNote] [nvarchar] (300) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastSessionDateTzAdjusted] [datetime] NULL, 
	[DehireTime] [datetime] NULL, 
	[HireTime] [datetime] NULL, 
	[LastSessionDate] [datetime] NULL, 
	[ModuleIsConnected] [bit] NOT NULL, 
	[OnHire] [bit] NOT NULL, 
	[ImpactLockout] [bit] NOT NULL, 
	[TimeoutEnabled] [bit] NOT NULL, 
	[IsCanbus] [bit] NOT NULL, 
	[VehicleImage] [nvarchar](1000) NULL, 
	[VehicleImageFileSize] [int] NULL, 
	[VehicleImageInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirmwareId] [uniqueidentifier] NULL, 
	[ModelId] [uniqueidentifier] NOT NULL, 
	[InspectionId] [uniqueidentifier] NULL, 
	[SiteId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NULL, 
	[DepartmentChecklistId] [uniqueidentifier] NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[VehicleOtherSettingsId] [uniqueidentifier] NULL, 
	[PersonId] [uniqueidentifier] NULL, 
	[ServiceSettingsId] [uniqueidentifier] NULL, 
	[ModuleId1] [uniqueidentifier] NOT NULL, 
	[ChecklistSettingsId] [uniqueidentifier] NULL, 
	[CustomerId] [uniqueidentifier] NOT NULL, 
	[CanruleId] [uniqueidentifier] NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleAlertSubscription] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[AlertSubscriptionId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleBroadcastMessage] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL, 
	[BroadcastMessageId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleDiagnostic] 
(
	[RedImpactThreshold] [int] NULL, 
	[DatabaseRedImpactThreshold] [int] NULL, 
	[ShockThreshold] [int] NULL, 
	[SurveyTimeouts] [int] NULL, 
	[SignalStrength] [int] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[HardwareVersion] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ModemVersion] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[APN] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ExpansionModuleVersion] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[SeatIdles] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CCID] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Timezone] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CANCRC] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirmwareVersion] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastPARUpdate] [datetime] NULL, 
	[LastPreopCheck] [datetime] NULL, 
	[KernelBuildDate] [datetime] NULL, 
	[IsSynchronized] [bit] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleGPS] 
(
	[Longitude] [decimal] (18, 8) NOT NULL, 
	[Latitude] [decimal] (18, 8) NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[GPSDateTime] [datetime] NOT NULL, 
	[SessionId] [uniqueidentifier] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleHireDehireHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[VehicleIdWhenSaved] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[SerialNoWhenSaved] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[DeviceIdWhenSaved] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[DehireTime] [datetime] NULL, 
	[HireTime] [datetime] NULL, 
	[DepartmentId] [uniqueidentifier] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleHireDehireSynchronizationOptions] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[WillSynchronize] [bit] NOT NULL, 
	[Setting] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleLockout] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Note] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Comment] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LockoutTime] [datetime] NOT NULL, 
	[UnlockDateTime] [datetime] NOT NULL, 
	[Reason] [int] NOT NULL, 
	[RealImpact] [int] NULL, 
	[SessionId] [uniqueidentifier] NOT NULL, 
	[DriverId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleOtherSettings] 
(
	[FullLockoutTimeout] [smallint] NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[VORStatus] [bit] NOT NULL, 
	[AmberAlertEnabled] [bit] NOT NULL, 
	[VORStatusConfirmed] [bit] NOT NULL, 
	[FullLockout] [bit] NOT NULL, 
	[DefaultTechnicianAccess] [bit] NOT NULL, 
	[PedestrianSafety] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VehicleSessionlessImpact] 
(
	[ShockValue] [float] NOT NULL, 
	[Threshold] [float] NOT NULL, 
	[Id] [uniqueidentifier] NOT NULL, 
	[ImpactDateTime] [datetime] NOT NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[VORSettingHistory] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[StartDateTime] [datetime] NOT NULL, 
	[EndDateTime] [datetime] NULL, 
	[Status] [int] NOT NULL, 
	[PersonId] [uniqueidentifier] NULL, 
	[VehicleId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[WebsiteRole] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[RoleName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[WebsiteUserId] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [dbo].[WebsiteUser] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[Username] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Password] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[LassPasswordUpdate] [datetime] NOT NULL, 
	[Active] [bit] NOT NULL 
) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Schema 'GOChangeTracking'
-- ----------------------------------------------------------------------------------------------------------------
CREATE SCHEMA [GOChangeTracking] /* AUTHORIZATION owner_name */
GO
-- -------[ Tables ]-----------------------------------------------------------------------------------------------
CREATE TABLE [GOChangeTracking].[CustomerAudit] 
(
	[fkRevisionDeleted] [int] NULL, 
	[fkRevisionLastModified] [int] NULL, 
	[fkRevisionCreated] [int] NULL, 
	[CreatedBy] [uniqueidentifier] NULL, 
	[LastModifiedBy] [uniqueidentifier] NULL, 
	[DeletedBy] [uniqueidentifier] NULL, 
	[fkCustomerId] [uniqueidentifier] NOT NULL, 
	[CreationDate] [datetime] NULL, 
	[LastModifiedDate] [datetime] NULL, 
	[DeletionDate] [datetime] NULL, 
	[IsDeleted] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[CustomerSnapshot] 
(
	[CustomerLogoFileSize] [int] NULL, 
	[CountryId] [uniqueidentifier] NOT NULL, 
	[fkCustomerId] [uniqueidentifier] NOT NULL, 
	[DealerId] [uniqueidentifier] NOT NULL, 
	[ContactPersonInformationId] [uniqueidentifier] NULL, 
	[ContractNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Addess] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ConnectionString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CustomerLogoInternalName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Email] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Prefix] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[CompanyName] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ContactNumber] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[ContractDate] [datetime] NULL, 
	[Addess_IsChanged] [bit] NULL, 
	[CustomerLogoInternalName_IsChanged] [bit] NULL, 
	[fkCustomerId_IsChanged] [bit] NULL, 
	[CompanyName_IsChanged] [bit] NULL, 
	[Description_IsChanged] [bit] NULL, 
	[Email_IsChanged] [bit] NULL, 
	[Active_IsChanged] [bit] NULL, 
	[CustomerLogo_IsChanged] [bit] NULL, 
	[ContractNumber_IsChanged] [bit] NULL, 
	[ContactNumber_IsChanged] [bit] NULL, 
	[Active] [bit] NOT NULL, 
	[Prefix_IsChanged] [bit] NULL, 
	[ContactPersonInformationId_IsChanged] [bit] NULL, 
	[CountryId_IsChanged] [bit] NULL, 
	[ConnectionString_IsChanged] [bit] NULL, 
	[CustomerLogoFileSize_IsChanged] [bit] NULL, 
	[DealerId_IsChanged] [bit] NULL, 
	[ContractDate_IsChanged] [bit] NULL, 
	[CustomerLogo] [nvarchar](1000) NULL, 
	[SnapshotId] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[GOChangeDelta] 
(
	[GOChangeDeltaId] [uniqueidentifier] NOT NULL, 
	[When] [datetime] NOT NULL, 
	[Who] [nvarchar] (128) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[How] [nvarchar] (32) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[What] [nvarchar] (128) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Key] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Path] [nvarchar] (128) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FromValue] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ToValue] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[Revision] 
(
	[RevisionId] [int] IDENTITY (1,1) NOT NULL, 
	[fkGOUserId] [uniqueidentifier] NULL, 
	[When] [datetime] NOT NULL, 
	[TransactionInProgress] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[Snapshot] 
(
	[fkRevisionId] [int] NOT NULL, 
	[SnapshotId] [int] IDENTITY (1,1) NOT NULL, 
	[Change] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOChangeTracking].[Tag] 
(
	[fkRevisionId] [int] NOT NULL, 
	[TagId] [uniqueidentifier] NOT NULL, 
	[Author] [uniqueidentifier] NULL, 
	[Label] [nvarchar] (256) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Version] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Description] [nvarchar] (2048) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Summary] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[When] [datetime] NOT NULL, 
	[IsSystem] [bit] NOT NULL 
) ON [PRIMARY]
GO 
-- ----------------------------------------------------------------------------------------------------------------
-- Schema 'GOSecurity'
-- ----------------------------------------------------------------------------------------------------------------
CREATE SCHEMA [GOSecurity] /* AUTHORIZATION owner_name */
GO
-- -------[ Tables ]-----------------------------------------------------------------------------------------------
CREATE TABLE [GOSecurity].[GOGroup] 
(
	[Description] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[DisplayName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Name] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[IsSpecialGroup] [bit] NOT NULL, 
	[SpecialGroup] [int] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOGroupRole] 
(
	[GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[GOGroupName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOLoginHistory] 
(
	[Id] [int] IDENTITY (1,1) NOT NULL, 
	[User] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Info] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[Timestamp] [datetime] NOT NULL, 
	[Result] [bit] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUser] 
(
	[Id] [uniqueidentifier] NOT NULL, 
	[EmailAddress] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[NewEmailAddress] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[UserName] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[FullName] [nvarchar] (250) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[LastName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PreferredLocaleString] [nvarchar] (50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[FirstName] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Password] [nvarchar] (150) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL, 
	[ExternalUserId] [nvarchar] (500) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[PasswordExpiry] [datetime] NULL, 
	[EmailValidated] [bit] NOT NULL, 
	[UserValidated] [bit] NOT NULL, 
	[NewEmailValidated] [bit] NULL, 
	[Blocked] [bit] NOT NULL, 
	[Unregistered] [bit] NOT NULL, 
	[DealerAdmin] [bit] NULL, 
	[EmailChangeValidationInProgress] [bit] NOT NULL, 
	[PreferredLocale] [int] NULL, 
	[DealerId] [uniqueidentifier] NULL, 
	[GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUser2FA] 
(
	[_2FAConnectionFailedCounter] [smallint] NOT NULL, 
	[OTPSecret] [nvarchar] (100) COLLATE SQL_Latin1_General_CP1_CI_AS NULL, 
	[Last2FAConnexionTry] [datetime] NULL, 
	[OTPGenerationDateTime] [datetime] NULL, 
	[Is2FAEnabled] [bit] NULL, 
	[Id] [uniqueidentifier] NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUserGroup] 
(
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[GOGroupName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 
CREATE TABLE [GOSecurity].[GOUserRole] 
(
	[GOUserId] [uniqueidentifier] NOT NULL, 
	[GORoleName] [nvarchar] (200) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL 
) ON [PRIMARY]
GO 