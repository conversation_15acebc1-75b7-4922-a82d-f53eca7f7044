﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.Data.DataObjects.Custom;
using Microsoft.Azure.Devices;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ExceptionHandling;
using FleetXQ.BusinessLayer.Components.Server.Custom;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// GPSMessage Component
	/// API for processing gps message 
	/// </summary>
    public partial class GPSMessage : BaseServerComponent, IGPSMessage
    {
        private readonly ILoggingService _logger;
        public GPSMessage(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, ILoggingService logger) : base(serviceProvider, configuration, dataFacade)
        {
            _logger = logger;
        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> storeGpsMessageAsync(string Message, Dictionary<string, object> parameters = null)
        {
            //{ "event_type":"GPSE","payload":"GPSE=9A4,**********,**********,-276461250","session_id":"9cf5acaa-2ba5-48fa-a1f9-658d6370c506"}
            var payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            if (payloadObject == null || payloadObject.EventType != "GPSE")
            {
                _logger.LogError(new GOServerException("Invalid Payload"));
                throw new GOServerException("Invalid Payload");
            }

            // Check if IoTDeviceId is missing in the payload, try to get it from the outer payload if available
            if (string.IsNullOrEmpty(payloadObject.IoTDeviceId) && parameters != null && parameters.ContainsKey("IotDeviceId"))
            {
                payloadObject.IoTDeviceId = parameters["IotDeviceId"].ToString();
            }

            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { payloadObject.IoTDeviceId })).SingleOrDefault();
            if (module == null)
            {
                _logger.LogError(new GOServerException("Invalid IoTDeviceId"));
                throw new GOServerException("Invalid IoTDeviceId");
            }

            var vehicle = await module.LoadVehicleAsync();
            if (vehicle == null)
            {
                _logger.LogError(new GOServerException("Invalid Vehicle"));
                throw new GOServerException("Invalid Vehicle");
            }
            var sessiondId = payloadObject.SessionId;
            var payloadArray = payloadObject.Payload.Split('=').Last().Split(',');

            // Handle both old (4 elements) and new (13 elements) formats
            if (payloadArray.Length != 4 && payloadArray.Length != 13)
            {
                _logger.LogError(new GOServerException($"Invalid Payload: Expected 4 or 13 elements, got {payloadArray.Length}"));
                throw new GOServerException("Invalid Payload");
            }

            var gpsObject = new
            {
                cardId = payloadArray[0],
                timeStamp = payloadArray[1],
                longitude = payloadArray.Length == 4 ? payloadArray[2] : payloadArray[8], // For 13-element format, use element 8 for longitude
                latitude = payloadArray.Length == 4 ? payloadArray[3] : payloadArray[4]   // For 13-element format, use element 4 for latitude
            };

            // get the latest vehicle gps data if available
            var vehicleGPS = (await _dataFacade.VehicleGPSDataProvider.GetCollectionAsync(null, "VehicleId == @0", new object[] { vehicle.Id }, "GPSDateTime DESC", 1)).FirstOrDefault();
            if (vehicleGPS == null)
            {
                vehicleGPS = _serviceProvider.GetRequiredService<VehicleGPSDataObject>();
                vehicleGPS.VehicleId = vehicle.Id;
            }

            if (sessiondId != null)
            {
                vehicleGPS.SessionId = Guid.Parse(sessiondId);
            }
            vehicleGPS.Latitude = Convert.ToDecimal(gpsObject.latitude) / 10000000;
            vehicleGPS.Longitude = Convert.ToDecimal(gpsObject.longitude) / 10000000;
            vehicleGPS.GPSDateTime = DataUtils.UnixTimeStampToDateTime(Convert.ToInt64(gpsObject.timeStamp));

            // do not save gps data if location is 0
            if (vehicleGPS.Latitude == 0 && vehicleGPS.Longitude == 0)
            {
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Vehicel GPS Not found")));
            }

            await _dataFacade.VehicleGPSDataProvider.SaveAsync(vehicleGPS);

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }
    }
}
