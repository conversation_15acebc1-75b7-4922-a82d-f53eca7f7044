﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;
 

namespace FleetXQ.Data.DataProviders.Custom
{
    public class DetailedSessionViewDataProvider : DataProvider<DetailedSessionViewDataObject>
    {
        protected readonly IConfiguration _configuration;
        public DetailedSessionViewDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<DetailedSessionViewDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetDetailedSession", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasDriverId)
                    {
                        command.Parameters.Add(new SqlParameter("@DriverId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DriverIdParameterNumber] });
                    }

                    if (filter.HasVehicleId)
                    {
                        command.Parameters.Add(new SqlParameter("@VehicleId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.VehicleIdParameterNumber] });
                    }

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }

        protected override async Task DoDeleteAsync(DetailedSessionViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DetailedSessionViewDataObject> DoGetAsync(DetailedSessionViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DataObjectCollection<DetailedSessionViewDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<DetailedSessionViewDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetDetailedSession", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasDriverId)
                    {
                        command.Parameters.Add(new SqlParameter("@DriverId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DriverIdParameterNumber] });
                    }

                    if (filter.HasVehicleId)
                    {
                        command.Parameters.Add(new SqlParameter("@VehicleId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.VehicleIdParameterNumber] });
                    }

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }

                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<DetailedSessionViewDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, VehicleId, LoggedHours, and SeatHours
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.SessionId = reader.GetGuid(reader.GetOrdinal("SessionId"));
                                    entity.DriverId = reader.GetGuid(reader.GetOrdinal("DriverId"));
                                    entity.VehicleId = reader.GetGuid(reader.GetOrdinal("VehicleId"));
                                    entity.TotalDuration = reader.GetString(reader.GetOrdinal("TotalDuration"));
                                    entity.TotalSeatHours = reader.GetString(reader.GetOrdinal("TotalSeatHours"));
                                    entity.TotalHydraulicHours = reader.GetString(reader.GetOrdinal("TotalHydraulicHours"));
                                    entity.TotalTractionHours = reader.GetString(reader.GetOrdinal("TotalTractionHours"));
                                    entity.TimezoneAdjustedStartDateTime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedStartDateTime"));
                                    entity.TimezoneAdjustedEndDateTime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedEndDateTime"));

                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GeneralProductivityPerDriverViewLatest data", "Unable to get GeneralProductivityPerDriverViewLatest data", ex);
                    }
                }
            }
        }

        protected override async Task<DetailedSessionViewDataObject> DoSaveAsync(DetailedSessionViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
