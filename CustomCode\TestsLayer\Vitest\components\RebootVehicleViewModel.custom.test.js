import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('RebootVehicleViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let originalOnRebootVehicleSuccess;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/RebootVehicleViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        originalOnRebootVehicleSuccess = vi.fn();
        viewModel = {
            onRebootVehicleSuccess: originalOnRebootVehicleSuccess,
            closePopup: vi.fn(),
            StatusData: {
                isPopup: ko.observable(true)
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.RebootVehicleViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('onRebootVehicleSuccess', () => {
        it('should call the original onRebootVehicleSuccess and closePopup', () => {
            const testData = { id: 123 };

            // Call the overridden function
            viewModel.onRebootVehicleSuccess(testData);

            // Verify that the original function was called with the correct data
            expect(originalOnRebootVehicleSuccess).toHaveBeenCalledWith(testData);

            // Verify that closePopup was called with false
            expect(viewModel.closePopup).toHaveBeenCalledWith(false);
        });

        it('should maintain the original function context', () => {
            const testData = { id: 123 };
            const originalThis = { someProperty: 'value' };

            // Call the overridden function with a specific this context
            viewModel.onRebootVehicleSuccess.call(originalThis, testData);

            // Verify that the original function was called with the correct this context
            expect(originalOnRebootVehicleSuccess).toHaveBeenCalledWith(testData);
        });
    });
}); 