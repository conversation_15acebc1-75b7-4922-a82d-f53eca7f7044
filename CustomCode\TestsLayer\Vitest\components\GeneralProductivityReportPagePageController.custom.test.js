import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import fs from 'fs';
import path from 'path';

// Mock the global objects and namespaces needed
global.FleetXQ = {
  Web: {
    Controllers: {}
  }
};

global.GO = {
  Filter: {
    hasUrlFilter: vi.fn().mockReturnValue(false)
  }
};

// Read and evaluate the actual controller file
const controllerFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/Controllers/GeneralProductivityReportPagePageController.custom.js');
const controllerFileContent = fs.readFileSync(controllerFilePath, 'utf8');
eval(controllerFileContent);

describe('GeneralProductivityReportPagePageController', () => {
  let controller;
  let mockApplicationController;
  let mockViewModelSecurity;
  let mockDataObjects;
  let mockViewFormViewModel;
  let mockReportFilterFormViewModel;
  let customController;
  let originalFilterDataCommand;
  let mockShowError;
  
  beforeEach(() => {
    // Setup mocks
    mockDataObjects = {
      CustomerId: vi.fn(),
      SiteId: vi.fn(),
      DepartmentId: vi.fn(),
      StartDate: vi.fn(),
      EndDate: vi.fn(),
      MultiSearch: vi.fn().mockReturnValue(null)
    };
    
    mockViewFormViewModel = {
      GeneralProductivityPerDriverViewLatestItemsGridViewModel: {
        LoadGeneralProductivityPerDriverViewLatestObjectCollection: vi.fn(),
        exportFilterPredicate: null,
        exportFilterParameters: null,
        StartDate: null,
        EndDate: null,
        CustomerId: null,
        SiteId: null,
        DepartmentId: null
      },
      GeneralProductivityPerVehicleViewItemsGridViewModel: {
        LoadGeneralProductivityPerVehicleViewObjectCollection: vi.fn(),
        exportFilterPredicate: null,
        exportFilterParameters: null,
        StartDate: null,
        EndDate: null,
        CustomerId: null,
        SiteId: null,
        DepartmentId: null
      },
      // Add missing mocks for UnitUtilisation and UnitUnutilisation grids
      UnitUtilisationStoreProcedureItemsGridViewModel: {
        LoadUnitUtilisationStoreProcedureObjectCollection: vi.fn(),
        exportFilterPredicate: null,
        exportFilterParameters: null
      },
      UnitUnutilisationStoreProcedureItemsGridViewModel: {
        LoadUnitUnutilisationStoreProcedureObjectCollection: vi.fn(),
        exportFilterPredicate: null,
        exportFilterParameters: null
      }
    };
    
    // Mock ShowError function to track calls
    mockShowError = vi.fn();
    
    // Create original FilterDataCommand to track if it was called
    originalFilterDataCommand = vi.fn();
    
    mockReportFilterFormViewModel = {
      CurrentObject: vi.fn().mockReturnValue({
        Data: mockDataObjects
      }),
      filterData: null,
      ShowError: mockShowError,
      Commands: {
        FilterDataCommand: originalFilterDataCommand
      }
    };
    
    mockViewModelSecurity = {
      currentUserClaims: vi.fn().mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: null
      })
    };
    
    mockApplicationController = {
      viewModel: {
        security: mockViewModelSecurity
      }
    };
    
    controller = {
      applicationController: mockApplicationController,
      GeneralProductivityViewFormViewModel: mockViewFormViewModel,
      GeneralProductivityReportFilterFormViewModel: mockReportFilterFormViewModel,
      LoggedHoursVersusSeatHoursViewReportViewModel: {
        LoadLoggedHoursVersusSeatHoursViewObjectCollection: vi.fn()
      }
    };
    
    // Create instance of the controller we're testing
    customController = new FleetXQ.Web.Controllers.GeneralProductivityReportPagePageControllerCustom(controller);
    
    // Mock sessionStorage
    global.sessionStorage = {
      getItem: vi.fn().mockReturnValue('true'),
      setItem: vi.fn(),
      removeItem: vi.fn()
    };
    
    // Mock window.location
    global.window = {
      location: {
        reload: vi.fn()
      }
    };
    
    // Initialize the controller
    customController.initialize();
  });
  
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  // HAPPY PATH TESTS
  
  describe('Happy Path Tests', () => {
    it('should correctly apply filter with customerId, startDate and endDate', () => {
      // Setup test data
      const customerId = '12345678-1234-1234-1234-123456789012';
      const startDate = '2023-01-01T00:00:00';
      const endDate = '2023-01-31T00:00:00';
      
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.SiteId.mockReturnValue(null);
      mockDataObjects.DepartmentId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(startDate);
      mockDataObjects.EndDate.mockReturnValue(endDate);
      
      // Call the filter function
      controller.GeneralProductivityReportFilterFormViewModel.filterData();
      
      // Check if the models have been updated correctly
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate).toBe(startDate);
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate).toBe(endDate);
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.CustomerId).toBe(customerId);
      
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.StartDate).toBe(startDate);
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.EndDate).toBe(endDate);
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.CustomerId).toBe(customerId);
      
      // Verify that the load functions were called
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection).toHaveBeenCalled();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection).toHaveBeenCalled();
    });
    
    it('should correctly handle filter with only customerId', () => {
      // Setup test data
      const customerId = '12345678-1234-1234-1234-123456789012';
      
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.SiteId.mockReturnValue(null);
      mockDataObjects.DepartmentId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(null);
      mockDataObjects.EndDate.mockReturnValue(null);
      
      // Call the filter function
      controller.GeneralProductivityReportFilterFormViewModel.filterData();
      
      // Check if the models have been updated correctly
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.CustomerId).toBe(customerId);
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.CustomerId).toBe(customerId);
      
      // StartDate and EndDate should not be set
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate).toBeNull();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate).toBeNull();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.StartDate).toBeNull();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.EndDate).toBeNull();
    });
  });
  
  // UNHAPPY PATH TESTS - REPRODUCING THE BUG
  
  describe('Unhappy Path Tests', () => {
    it('should CLEAR dates when they are set then cleared (fixed behavior)', () => {
      // STEP 1: First filter with customerId, startDate, and endDate
      const customerId = '12345678-1234-1234-1234-123456789012';
      const startDate = '2023-01-01T00:00:00';
      const endDate = '2023-01-31T00:00:00';
      
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.SiteId.mockReturnValue(null);
      mockDataObjects.DepartmentId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(startDate);
      mockDataObjects.EndDate.mockReturnValue(endDate);
      
      // Call the filter function
      controller.GeneralProductivityReportFilterFormViewModel.filterData();
      
      // Verify the filter was applied correctly
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate).toBe(startDate);
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate).toBe(endDate);
      
      // STEP 2: Now clear the date fields (simulating user deleting the values) but keep customerId
      // With the fixed implementation, dates should be cleared correctly
      mockDataObjects.StartDate.mockReturnValue(null); // User cleared the field
      mockDataObjects.EndDate.mockReturnValue(null);   // User cleared the field
      
      // Reset the load function mocks to check if they're called again
      controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection.mockClear();
      controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection.mockClear();
      
      // Call the filter function again
      controller.GeneralProductivityReportFilterFormViewModel.filterData();
      
      // With our fix, the dates should now be cleared
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate).toBeNull();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate).toBeNull();
      
      // Verify the load functions were called
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection).toHaveBeenCalled();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection).toHaveBeenCalled();
    });
    
    it('should correctly handle empty string dates (fixed behavior)', () => {
      // Setup
      const customerId = '12345678-1234-1234-1234-123456789012';
      
      // Simulate empty string dates (this would happen when field is cleared)
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      mockDataObjects.StartDate.mockReturnValue(""); // Empty string
      mockDataObjects.EndDate.mockReturnValue("");   // Empty string
      
      // Execute
      const config = customController.getGeneralProductivityConfiguration();
      
      // With our fix, empty strings should NOT be included in the filter
      expect(config.filterPredicate).not.toContain('StartDate ==');
      expect(config.filterPredicate).not.toContain('EndDate ==');
      
      // Call the filter function and verify it works correctly
      controller.GeneralProductivityReportFilterFormViewModel.filterData();
      
      // Verify dates are handled as null when they're empty strings
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate).toBeNull();
      expect(controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate).toBeNull();
    });
  });
  
  describe('getGeneralProductivityConfiguration', () => {
    it('should include startDate and endDate in filter when they exist', () => {
      // Setup
      const startDate = '2023-01-01T00:00:00';
      const endDate = '2023-01-31T00:00:00';
      
      mockDataObjects.CustomerId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(startDate);
      mockDataObjects.EndDate.mockReturnValue(endDate);
      
      // Execute
      const config = customController.getGeneralProductivityConfiguration();
      
      // Verify
      expect(config.filterPredicate).toContain('StartDate == @0');
      expect(config.filterPredicate).toContain('EndDate == @1');
      
      const params = JSON.parse(config.filterParameters);
      expect(params[0].Value).toBe(startDate);
      expect(params[1].Value).toBe(endDate);
    });
    
    it('should properly handle empty string dates (fixed behavior)', () => {
      // Setup
      // Simulate empty string dates (this would happen when field is cleared)
      mockDataObjects.CustomerId.mockReturnValue(null);
      mockDataObjects.StartDate.mockReturnValue(""); // Empty string
      mockDataObjects.EndDate.mockReturnValue("");   // Empty string
      
      // Execute
      const config = customController.getGeneralProductivityConfiguration();
      
      // Verify FIX: Empty string dates should NOT be included in the filter
      expect(config.filterPredicate).not.toContain('StartDate ==');
      expect(config.filterPredicate).not.toContain('EndDate ==');
      
      // The filter parameters should not contain the empty string dates
      const params = JSON.parse(config.filterParameters);
      expect(params.length).toBe(0); // No parameters
    });
  });

  // NEW TESTS FOR DEALERADMIN CUSTOMER VALIDATION FEATURE
  describe('DealerAdmin Customer Validation', () => {
    // HAPPY PATH - DealerAdmin with customer selected
    it('should allow filtering when DealerAdmin has selected a customer', () => {
      // Setup test data
      const customerId = '12345678-1234-1234-1234-123456789012';
      mockDataObjects.CustomerId.mockReturnValue(customerId);
      
      // Set user as DealerAdmin
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['DealerAdmin']
      });
      
      // Call the filter command directly
      controller.GeneralProductivityReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that:
      // 1. ShowError was not called (no error shown)
      expect(mockShowError).not.toHaveBeenCalled();
      
      // 2. Original FilterDataCommand was called (filter continues)
      expect(originalFilterDataCommand).toHaveBeenCalled();
    });

    // UNHAPPY PATH - DealerAdmin with no customer selected
    it('should block filtering and show error when DealerAdmin has not selected a customer', () => {
      // Setup test data - no customer selected
      mockDataObjects.CustomerId.mockReturnValue(null);
      
      // Set user as DealerAdmin
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['DealerAdmin']
      });
      
      // Call the filter command directly
      controller.GeneralProductivityReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that:
      // 1. ShowError was called with correct error message
      expect(mockShowError).toHaveBeenCalledWith("Please select a customer", "Error");
      
      // 2. Original FilterDataCommand was NOT called (filter stopped)
      expect(originalFilterDataCommand).not.toHaveBeenCalled();
    });

    // HAPPY PATH - Other roles don't need customer selected
    it('should allow filtering for non-DealerAdmin roles even without customer selected', () => {
      // Setup test data - no customer selected
      mockDataObjects.CustomerId.mockReturnValue(null);
      
      // Set user with a different role
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['StandardUser']
      });
      
      // Call the filter command directly
      controller.GeneralProductivityReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that:
      // 1. ShowError was not called (no error shown)
      expect(mockShowError).not.toHaveBeenCalled();
      
      // 2. Original FilterDataCommand was called (filter continues)
      expect(originalFilterDataCommand).toHaveBeenCalled();
    });

    // EDGE CASE - Empty string for customerId should be treated as not selected
    it('should block filtering when DealerAdmin has empty string for customerId', () => {
      // Setup test data - customer is empty string
      mockDataObjects.CustomerId.mockReturnValue("");
      
      // Set user as DealerAdmin
      mockViewModelSecurity.currentUserClaims.mockReturnValue({
        CustomerId: null,
        AllowedSiteIds: "{}",
        role: ['DealerAdmin']
      });
      
      // Call the filter command directly
      controller.GeneralProductivityReportFilterFormViewModel.Commands.FilterDataCommand();
      
      // Verify that error was shown and filter was blocked
      expect(mockShowError).toHaveBeenCalled();
      expect(originalFilterDataCommand).not.toHaveBeenCalled();
    });
  });
});
