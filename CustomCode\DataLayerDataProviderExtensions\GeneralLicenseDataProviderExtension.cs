﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class GeneralLicenseDataProviderExtension : IDataProviderExtension<LicenceDetailDataObject>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IDataFacade _dataFacade;

        public GeneralLicenseDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade)
        {
            _serviceProvider = serviceProvider;
            _dataFacade = dataFacade;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSetAsync;
        }

        private async Task OnAfterSaveDataSetAsync(OnAfterSaveDataSetEventArgs e)
        {
            // return if it is an update
            if (!e.EntityBeforeSave.IsNew) return;
            
            var generalLicense = e.EntityBeforeSave as LicenceDetailDataObject;

            // get the person data of the driver, and add "Driver" enum to Person.AccessLevel enum field and save person data after
            var driver = await generalLicense.LoadDriverAsync();

            var person = await driver.LoadPersonAsync();

            // check if person.AccessLevel is null and instantiate a new list of PersonAccessLevelEnum for it if it is null
            if (person.AccessLevel == null)
            {
                person.AccessLevel = new List<PersonAccessLevelEnum>();
            }

            if (!person.AccessLevel.Contains(PersonAccessLevelEnum.License))
            {
                person.AccessLevel.Add(PersonAccessLevelEnum.License);

                // here we make sure we clone the person object recursive = false to only get the person data, and not the full dataset. If we would get the full dataset then the save will fail because the dataset is the data before save , therefore tagged isNew = true and it would try to create again the license data 
                await _dataFacade.PersonDataProvider.SaveAsync(person.Clone(recursive : false) as PersonDataObject);
            }
        }

    }
}
