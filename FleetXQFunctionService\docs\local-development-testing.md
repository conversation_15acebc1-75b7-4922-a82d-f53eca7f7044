# Local Pipeline Testing Guide

This guide provides multiple ways to test the Azure DevOps pipeline locally before deploying to the cloud.

## Prerequisites

1. **.NET 7.0 SDK** - Required for building and running the Azure Function
   ```bash
   dotnet --version  # Should show 7.x.x
   ```

2. **Azure Functions Core Tools** (optional, for local testing)
   ```bash
   npm install -g azure-functions-core-tools@4 --unsafe-perm true
   ```

3. **Azure CLI** (optional, for deployment testing)
   ```bash
   az --version
   ```

## Method 1: Quick Scripts (Recommended)

### Windows Command Prompt
```cmd
cd VehicleAccessProcessor
local-pipeline-test.cmd
```

### PowerShell (Recommended)
```powershell
cd VehicleAccessProcessor
.\local-pipeline-test.ps1
```

## Method 2: Manual Step-by-Step

Run these commands in the `VehicleAccessProcessor` directory:

```bash
# Step 1: Restore dependencies
dotnet restore VehicleAccessProcessor.csproj

# Step 2: Build the project
dotnet build VehicleAccessProcessor.csproj --configuration Release --no-restore

# Step 3: Publish the function
dotnet publish VehicleAccessProcessor.csproj --configuration Release --output bin/publish

# Step 4: Test locally (optional)
func start --port 7071
```

## Method 3: Docker Simulation

Create a `Dockerfile.pipeline-test` to simulate the Azure DevOps agent environment:

```dockerfile
FROM mcr.microsoft.com/dotnet/sdk:7.0-windowsservercore-ltsc2022
WORKDIR /src
COPY . .
RUN dotnet restore VehicleAccessProcessor/VehicleAccessProcessor.csproj
RUN dotnet build VehicleAccessProcessor/VehicleAccessProcessor.csproj --configuration Release --no-restore
RUN dotnet publish VehicleAccessProcessor/VehicleAccessProcessor.csproj --configuration Release --output /app/publish
```

## Method 4: Azure DevOps Extension (VS Code)

1. Install the "Azure DevOps Extension for Visual Studio Code"
2. Use the command palette: `Azure DevOps: Run Pipeline`
3. Select your pipeline file for validation

## Testing the Function Locally

After building/publishing, you can test the function:

### 1. Start the Function Host
```bash
cd VehicleAccessProcessor
func start --port 7071
```

### 2. Test the Health Check Endpoint
```bash
curl http://localhost:7071/api/HealthCheck
```

### 3. Test Service Bus Trigger (if needed)
You'll need to:
- Set up a local Service Bus emulator, or
- Use Azure Storage Queue for local development
- Configure `local.settings.json` with connection strings

## Configuration for Local Testing

Create or update `local.settings.json`:

```json
{
  "IsEncrypted": false,
  "Values": {
    "AzureWebJobsStorage": "UseDevelopmentStorage=true",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "ServiceBusConnection": "your-local-connection-string",
    "VehicleAccessQueue": "vehicle-access-creation",
    "FleetXQApiBaseUrl": "https://your-dev-api.com",
    "FleetXQUsername": "test-user",
    "FleetXQPassword": "test-password"
  }
}
```

## Troubleshooting

### Common Issues:

1. **"No files matched the search pattern"**
   - Ensure you're in the correct directory
   - Check that `VehicleAccessProcessor.csproj` exists

2. **Missing SDK version**
   - Install .NET 7.0 SDK from https://dotnet.microsoft.com/download

3. **Function won't start locally**
   - Check Azure Functions Core Tools installation
   - Verify `host.json` and `local.settings.json` are present

4. **Build errors**
   - Run `dotnet restore` first
   - Check for missing NuGet packages

## What Each Method Tests

- **Scripts/Manual**: Build, restore, publish (same as pipeline)
- **Docker**: Full environment simulation
- **Local Function Host**: Runtime behavior and endpoints
- **VS Code Extension**: YAML validation and basic pipeline checks

## Next Steps

Once local testing passes:
1. Commit your changes
2. Push to trigger the actual Azure DevOps pipeline
3. Monitor the pipeline execution
4. Test the deployed function in Azure

## Notes

- The local scripts test the **build process** but not the **deployment**
- For deployment testing, you need Azure credentials and target resources
- The pipeline includes additional steps (archiving, Azure deployment) that can't be fully simulated locally 