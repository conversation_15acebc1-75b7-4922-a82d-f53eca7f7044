using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Threading.Tasks;
using System.Linq;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Moq;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using FleetXQ.Data.DataProviders.Custom;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class AllImpactsViewDataProviderTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"AllImpactsViewDataProviderTest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _siteId;
        private Guid _departmentId;
        private Guid _vehicleId;
        private Guid _sessionId;
        private Guid _impactId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Register the data provider
            services.AddTransient<AllImpactsViewDataProvider>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            // Get required services
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();

            // Create database first
            CreateTestDatabase(_testDatabaseName);

            try
            {
                // Create test data
                await CreateTestDataAsync();

                // Create and initialize test user
                var testUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
                testUser.Id = Guid.NewGuid();
                testUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Customer;
                testUser.UserName = "TestUser";
                testUser.Password = "TestPass";
                testUser.EmailAddress = "<EMAIL>";
                testUser.EmailValidated = true;
                testUser.PreferredLocale = 0;
                testUser.PreferredLocaleString = "en-US";
                testUser.UserValidated = true;
                testUser.Blocked = false;
                testUser.Unregistered = false;
                testUser.EmailChangeValidationInProgress = false;

                // Save user first and wait for completion
                Console.WriteLine($"Saving user with ID: {testUser.Id}");
                testUser = await _dataFacade.GOUserDataProvider.SaveAsync(testUser);
                Console.WriteLine($"User saved. Returned ID: {testUser.Id}");

                // Verify user exists in database before creating role
                var savedUser = await _dataFacade.GOUserDataProvider.GetAsync(testUser);
                if (savedUser == null)
                {
                    throw new Exception($"Failed to retrieve saved user with ID {testUser.Id}");
                }
                Console.WriteLine($"Retrieved user from DB. ID: {savedUser.Id}");

                // Now create the role
                var userRole = _serviceProvider.GetRequiredService<GOUserRoleDataObject>();
                userRole.GOUserId = savedUser.Id;
                userRole.GORoleName = "Customer";
                Console.WriteLine($"Creating role for user ID: {userRole.GOUserId}, Role: {userRole.GORoleName}");

                // Try to get existing role first
                var existingRoles = await _dataFacade.GOUserRoleDataProvider.GetCollectionAsync(
                    filterPredicate: "GOUserId == @0",
                    filterArguments: new object[] { savedUser.Id }
                );

                if (existingRoles?.Any() == true)
                {
                    Console.WriteLine($"Found {existingRoles.Count} existing roles for user");
                    foreach (var role in existingRoles)
                    {
                        Console.WriteLine($"Existing role: UserId={role.GOUserId}, Role={role.GORoleName}");
                    }
                }
                else
                {
                    Console.WriteLine("No existing roles found for user");
                }

                await _dataFacade.GOUserRoleDataProvider.SaveAsync(userRole);
                Console.WriteLine("Role saved successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in test setup: {ex.GetType().Name}");
                Console.WriteLine($"Message: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                throw;
            }
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test data hierarchy
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test Dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Name = "Test Site";
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);
            _siteId = site.Id;

            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Name = "Test Department";
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);
            _departmentId = department.Id;

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create test module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.AmberImpact = 5.0f;
            module.FSSXMulti = 1.0f;
            module.FSSSBase = 1.0f;
            module.RedImpact = 10.0f;
            module.IoTDevice = "TEST-IOT-001";
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create test vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.HireNo = "TEST001";
            vehicle.SerialNo = "SN001";
            vehicle.ModuleId1 = module.Id;
            vehicle.CustomerId = customer.Id;
            vehicle.OnHire = true;
            vehicle.ModuleIsConnected = false;
            vehicle.ImpactLockout = false;
            vehicle.TimeoutEnabled = false;
            vehicle.IsCanbus = false;
            vehicle.SiteId = site.Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            _vehicleId = vehicle.Id;

            // Create test driver
            var driver = _serviceProvider.GetRequiredService<DriverDataObject>();
            driver.Id = Guid.NewGuid();
            driver.Active = true;
            driver.LicenseMode = 0;  // Assuming 0 is a valid license mode
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Create test session
            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.StartTime = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);
            session.DriverId = driver.Id;  // Now we have a valid driver ID
            session = await _dataFacade.SessionDataProvider.SaveAsync(session);
            _sessionId = session.Id;

            // Create test impacts (Red, Amber, Blue)
            await CreateTestImpact(ImpactTypeEnum.Red);
            await CreateTestImpact(ImpactTypeEnum.Amber);
            await CreateTestImpact(ImpactTypeEnum.Blue);
        }

        private async Task CreateTestImpact(ImpactTypeEnum impactType)
        {
            // Create the base impact
            var impact = _serviceProvider.GetRequiredService<ImpactDataObject>();
            impact.Id = Guid.NewGuid();
            impact.SessionId = _sessionId;
            impact.ImpactDateTime = new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc);

            // Set ShockValue and Threshold to get the desired impact type
            impact.Threshold = 1.0;  // Base threshold
            switch (impactType)
            {
                case ImpactTypeEnum.Red:
                    impact.ShockValue = impact.Threshold * 11.0;  // > 10x threshold for Red
                    break;
                case ImpactTypeEnum.Amber:
                    impact.ShockValue = impact.Threshold * 7.0;  // Between 5x and 10x threshold for Amber
                    break;
                case ImpactTypeEnum.Blue:
                    impact.ShockValue = impact.Threshold * 3.0;  // Between 1x and 5x threshold for Blue
                    break;
            }

            TestContext.WriteLine($"Creating {impactType} impact with ShockValue: {impact.ShockValue}, Threshold: {impact.Threshold}, Ratio: {impact.ShockValue / impact.Threshold}");
            impact = await _dataFacade.ImpactDataProvider.SaveAsync(impact);
            TestContext.WriteLine($"Created impact with ID: {impact.Id}");
        }

        [Test]
        public async Task GetCollection_AllLevelsFilter_IncludesAllImpacts()
        {
            // Arrange
            var context = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var filterPredicate = "ImpactLevel == @0 AND StartDate == @1 AND EndDate == @2";
            var filterArguments = new object[] {
                (int)ImpactLevelReportNameEnum.AllLevels,
                new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),  // StartDate
                new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc)   // EndDate
            };

            // Verify test data exists
            var impacts = await _dataFacade.ImpactDataProvider.GetCollectionAsync();
            TestContext.WriteLine($"Found {impacts.Count} impacts in database");
            foreach (var impact in impacts)
            {
                TestContext.WriteLine($"Impact ID: {impact.Id}, SessionId: {impact.SessionId}, ShockValue: {impact.ShockValue}, Threshold: {impact.Threshold}, DateTime: {impact.ImpactDateTime}");
            }

            // Verify session exists
            var sessionQuery = _serviceProvider.GetRequiredService<SessionDataObject>();
            sessionQuery.Id = _sessionId;
            var session = await _dataFacade.SessionDataProvider.GetAsync(sessionQuery);
            TestContext.WriteLine($"Session: ID={session?.Id}, VehicleId={session?.VehicleId}, DriverId={session?.DriverId}, StartTime={session?.StartTime}");

            // Verify vehicle exists
            var vehicleQuery = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicleQuery.Id = _vehicleId;
            var vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicleQuery);
            TestContext.WriteLine($"Vehicle: ID={vehicle?.Id}, SiteId={vehicle?.SiteId}, DepartmentId={vehicle?.DepartmentId}, CustomerId={vehicle?.CustomerId}");

            // Act
            var result = await _dataFacade.AllImpactsViewDataProvider.GetCollectionAsync(
                filterPredicate: filterPredicate,
                filterArguments: filterArguments,
                pageNumber: 1,
                pageSize: 10,
                context: context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(3), "Should return all impact types");
            Assert.That(result.Any(i => i.ImpactType == ImpactTypeEnum.Blue), Is.True, "Should include Blue impacts");
        }

        [Test]
        public async Task GetCollection_NoFilter_ExcludesBlueImpacts()
        {
            // Arrange
            var context = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            // Act
            var result = await _dataFacade.AllImpactsViewDataProvider.GetCollectionAsync(
                filterPredicate: "StartDate == @0 AND EndDate == @1",
                filterArguments: new object[] {
                    new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),  // StartDate
                    new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc)   // EndDate
                },
                pageNumber: 1,
                pageSize: 10,
                context: context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(2), "Should only return Red and Amber impacts");
            Assert.That(result.Any(i => i.ImpactType == ImpactTypeEnum.Blue), Is.False, "Should not include Blue impacts");
        }

        [Test]
        public async Task GetCollection_RedLevelOnlyFilter_OnlyIncludesRedImpacts()
        {
            // Arrange
            var context = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var filterPredicate = "ImpactLevel == @0 AND StartDate == @1 AND EndDate == @2";
            var filterArguments = new object[] {
                (int)ImpactLevelReportNameEnum.RedLevelOnly,
                new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc),  // StartDate
                new DateTime(2024, 1, 1, 12, 0, 0, DateTimeKind.Utc)   // EndDate
            };

            // Act
            var result = await _dataFacade.AllImpactsViewDataProvider.GetCollectionAsync(
                filterPredicate: filterPredicate,
                filterArguments: filterArguments,
                pageNumber: 1,
                pageSize: 10,
                context: context);

            // Assert
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Count, Is.EqualTo(1), "Should only return Red impacts");
            Assert.That(result.All(i => i.ImpactType == ImpactTypeEnum.Red), Is.True, "Should only include Red impacts");
        }
    }
}
