import { describe, it, expect, beforeEach, vi } from 'vitest';
import ko from 'knockout';
import fs from 'fs';
import path from 'path';

function createViewModel(mockApplicationController) {
    return {
        controller: {
            applicationController: mockApplicationController
        },
        contextId: 'test-context-id',
        CardObject: ko.observable({
            Data: {
                KeypadReader: ko.observable(),
                Type: ko.observable(),
                FacilityCode: ko.observable(),
                KeypadReaderValues: ko.observableArray([
                    { selectvalue: 2 },
                    { selectvalue: 4 }
                ])
            }
        }),
        CurrentObject: null, // will be set below
        subscriptions: [],
        StatusData: {
            IsTypeReadOnly: ko.observable(false),
            IsKeypadReaderReadOnly: ko.observable(false),
            IsFacilityCodeReadOnly: ko.observable(false),
            IsCardNumberReadOnly: ko.observable(false),
            IsActiveReadOnly: ko.observable(false)
        }
    };
}

// Setup FleetXQ namespace and ko.utils once for all tests
beforeAll(() => {
    global.FleetXQ = {
        Web: {
            ViewModels: {}
        }
    };
    // Assign imported ko to global.ko
    global.ko = ko;
    ko.utils = {
        arrayFirst: vi.fn((array, predicate) => {
            for (let i = 0; i < array.length; i++) {
                if (predicate(array[i])) {
                    return array[i];
                }
            }
            return null;
        })
    };
    // Read and evaluate the actual custom file
    const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Card/CardDetailsFormViewModel.custom.js');
    let customFileContent = fs.readFileSync(customFilePath, 'utf8');
    // Replace ko.utils.arrayFirst with our mock
    customFileContent = customFileContent.replace(/ko\.utils\.arrayFirst/g, 'ko.utils.arrayFirst');
    try {
        eval(customFileContent);
    } catch (e) {
        // eslint-disable-next-line no-console
        console.error('Error evaluating custom file:', e);
    }
    global.ApplicationController = {
        viewModel: {
            security: {
                currentUserClaims: () => ({
                    HasUsersAccess: true,
                    CanEditUserCard: 'True'
                })
            }
        }
    };
});

describe('CardDetailsFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let mockApplicationController;

    describe('KeypadReader subscription', () => {
        beforeEach(() => {
            mockApplicationController = { showAlertPopup: vi.fn() };
            viewModel = createViewModel(mockApplicationController);
            viewModel.CurrentObject = ko.pureComputed(function () {
                return this.CardObject();
            }, viewModel);
            customViewModel = new FleetXQ.Web.ViewModels.CardDetailsFormViewModelCustom(viewModel);
            customViewModel.initialize();
            // Re-assign CardObject to a new object to trigger all subscriptions
            const newObj = {
                Data: {
                    KeypadReader: ko.observable(),
                    Type: ko.observable(),
                    FacilityCode: ko.observable(),
                    KeypadReaderValues: ko.observableArray([
                        { selectvalue: 2 },
                        { selectvalue: 4 }
                    ])
                }
            };
            viewModel.CardObject(newObj);
        });

        it('should show alert popup when KeypadReader value is 2', () => {
            const expectedMessage = "Please swipe the card in the reader";
            const expectedTitle = "Card Reader";
            viewModel.CurrentObject().Data.KeypadReader(2);
            expect(mockApplicationController.showAlertPopup).toHaveBeenCalledWith(
                viewModel,
                expectedMessage,
                expectedTitle,
                null,
                viewModel.contextId
            );
        });

        it('should not show alert popup when KeypadReader value is not 2', () => {
            viewModel.CurrentObject().Data.KeypadReader(1);
            expect(mockApplicationController.showAlertPopup).not.toHaveBeenCalled();
        });
    });

    describe('Type subscription', () => {
        beforeEach(() => {
            mockApplicationController = { showAlertPopup: vi.fn() };
            viewModel = createViewModel(mockApplicationController);
            viewModel.CurrentObject = ko.pureComputed(function () {
                return this.CardObject();
            }, viewModel);
            customViewModel = new FleetXQ.Web.ViewModels.CardDetailsFormViewModelCustom(viewModel);
            customViewModel.initialize();
            // Re-assign CardObject to a new object to trigger all subscriptions
            const newObj = {
                Data: {
                    KeypadReader: ko.observable(),
                    Type: ko.observable(),
                    FacilityCode: ko.observable(),
                    KeypadReaderValues: ko.observableArray([
                        { selectvalue: 2 },
                        { selectvalue: 4 }
                    ])
                }
            };
            viewModel.CardObject(newObj);
        });

        it('should set FacilityCode to 0 when Type is 1', () => {
            viewModel.CurrentObject().Data.Type(1);
            expect(viewModel.CurrentObject().Data.FacilityCode()).toBe(0);
        });

        it('should remove KeypadReader values 2 and 4 when Type is 1', () => {
            viewModel.CurrentObject().Data.Type(1);
            const keypadValues = viewModel.CardObject().Data.KeypadReaderValues();
            expect(keypadValues.length).toBe(0);
        });

        it('should restore KeypadReader values 2 and 4 when Type changes from 1 to other value', () => {
            viewModel.CurrentObject().Data.Type(1);
            expect(viewModel.CardObject().Data.KeypadReaderValues().length).toBe(0);
            viewModel.CurrentObject().Data.Type(2);
            const keypadValues = viewModel.CardObject().Data.KeypadReaderValues();
            expect(keypadValues.length).toBe(2);
            expect(keypadValues[0].selectvalue).toBe(2);
            expect(keypadValues[1].selectvalue).toBe(4);
        });
    });
}); 