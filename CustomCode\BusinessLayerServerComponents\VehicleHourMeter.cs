﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// VehicleHourMeter Component
	///  
	/// </summary>
    public partial class VehicleHourMeter : BaseServerComponent, IVehicleHourMeter 
    {
		public VehicleHourMeter(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async Task<ComponentResponse<object>> BySerialNoAsync(string serialNo, Dictionary<string, object> parameters)
        {
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "SerialNo == @0", new object[] { serialNo })).SingleOrDefault();
            if (vehicle == null)
            {
                throw new GOServerException("Invalid serialNo");
            }

            var customer = await vehicle.LoadCustomerAsync();
            if (customer == null)
            {
                throw new GOServerException("Customer is null");
            }

            var dealer = await customer.LoadDealerAsync();
            if (dealer == null)
            {
                throw new GOServerException("Dealer is null");
            }

            if (!dealer.IsAPIEnabled)
            {
                throw new ForbiddenAccessException();
            }

            var vehicleServiceSettings = await vehicle.LoadServiceSettingsAsync();

            var vehicleHourMeter = new VehicleHourMeterResponse(vehicle.Id, vehicle.SerialNo, vehicleServiceSettings?.CurrentMeterReading ?? 0);

            return new ComponentResponse<object>(vehicleHourMeter);
        }

        public async Task<ComponentResponse<object[]>> ListAllAsync(Dictionary<string, object> parameters)
        {
            var dealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(null, "IsAPIEnabled == @0", new object[] { true });

            if (dealers == null || !dealers.Any())
            {
                return new ComponentResponse<object[]>();
            }

            var customersList = new List<CustomerDataObject>();

            foreach (var dealer in dealers)
            {
                var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "DealerId == @0", new object[] { dealer.Id });

                if (customers?.Any() == true)
                {
                    customersList.AddRange(customers);
                }
            }

            var vehiclesList = new List<VehicleDataObject>();

            if (customersList?.Any() == true)
            {
                foreach (var customer in customersList)
                {
                    var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customer.Id });

                    if (vehicles?.Any() == true)
                    {
                        vehiclesList.AddRange(vehicles);
                    }
                }
            }

            var vehicleHourMetersList = new List<VehicleHourMeterResponse>();

            if (vehiclesList?.Any() == true)
            {
                foreach (var vehicle in vehiclesList)
                {
                    var vehicleServiceSettings = await vehicle.LoadServiceSettingsAsync();

                    var vehicleHourMeter = new VehicleHourMeterResponse(vehicle.Id, vehicle.SerialNo, vehicleServiceSettings?.CurrentMeterReading ?? 0);

                    vehicleHourMetersList.Add(vehicleHourMeter);
                }
            }

            return new ComponentResponse<object[]>(vehicleHourMetersList.Select(v => (object)v).ToArray());
        }
    }

    public record VehicleHourMeterResponse(Guid VehicleId, string SerialNo, double CurrentMeterReading);
}
