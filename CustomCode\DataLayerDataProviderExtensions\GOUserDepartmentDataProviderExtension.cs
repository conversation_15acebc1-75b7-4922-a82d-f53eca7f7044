using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Generic;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class GOUserDepartmentDataProviderExtension : IDataProviderExtension<GOUserDepartmentDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;

        public GOUserDepartmentDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSave += OnAfterSaveAsync;
            dataProvider.OnAfterSaveDataSet += DataProvider_OnAfterSaveDataSet;
            dataProvider.OnAfterDelete += OnAfterDeleteAsync;
        }

        private async Task DataProvider_OnAfterSaveDataSet(OnAfterSaveDataSetEventArgs arg)
        {
            var goUserDepartment = arg.EntityBeforeSave as GOUserDepartmentDataObject;
            if (goUserDepartment == null) return;

            await UpdateGOUserAllowedDepartmentNamesAsync(goUserDepartment.GOUserId);
        }

        private async Task OnAfterSaveAsync(OnAfterSaveEventArgs e)
        {
            var goUserDepartment = e.Entity as GOUserDepartmentDataObject;
            if (goUserDepartment == null) return;

            await UpdateGOUserAllowedDepartmentNamesAsync(goUserDepartment.GOUserId);
        }

        private async Task OnAfterDeleteAsync(OnAfterDeleteEventArgs e)
        {
            var goUserDepartment = e.Entity as GOUserDepartmentDataObject;
            if (goUserDepartment == null) return;

            await UpdateGOUserAllowedDepartmentNamesAsync(goUserDepartment.GOUserId);
        }

        private async Task UpdateGOUserAllowedDepartmentNamesAsync(Guid goUserId)
        {
            try
            {
                // Get the GOUser
                var goUser = await _dataFacade.GOUserDataProvider.GetAsync(
                    new GOUserDataObject(goUserId),
                    skipSecurity: true
                );

                if (goUser == null) return;

                // Get all GOUserDepartment items for this user
                var userDepartments = await _dataFacade.GOUserDepartmentDataProvider.GetCollectionAsync(
                    null,
                    "GOUserId == @0",
                    new object[] { goUserId },
                    skipSecurity: true
                );

                // Get all department names for this user
                var departmentNames = new List<string>();
                foreach (var userDept in userDepartments)
                {
                    var department = await _dataFacade.DepartmentDataProvider.GetAsync(
                        new DepartmentDataObject(userDept.DepartmentId),
                        skipSecurity: true
                    );

                    if (department != null && !string.IsNullOrEmpty(department.Name))
                    {
                        departmentNames.Add(department.Name);
                    }
                }

                // Update the AllowedDepartmentNames field
                var allowedDepartmentNames = string.Join(",", departmentNames);
                goUser.AllowedDepartmentNames = allowedDepartmentNames;

                // Save the updated GOUser
                await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);
            }
            catch (Exception ex)
            {
                // Log the error but don't throw to avoid breaking the main operation
                // You might want to add proper logging here
                System.Diagnostics.Debug.WriteLine($"Error updating GOUser AllowedDepartmentNames: {ex.Message}");
            }
        }
    }
}
