using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.ExceptionHandling;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NHibernate.Util;
using System.Text.RegularExpressions;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class DealerDataProviderExtension : IDataProviderExtension<DealerDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IServiceProvider _serviceProvider;

        public DealerDataProviderExtension(IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, IServiceProvider serviceProvider)
        {
            _dataFacade = dataFacade;
            _deviceMessageHandler = deviceMessageHandler;
            _serviceProvider = serviceProvider;
        }
        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSet;
           dataProvider.OnBeforeSave += OnBeforeSave;
        }

        private async Task OnBeforeSave(OnBeforeSaveEventArgs e)
        {
            var dealer = e.Entity as DealerDataObject;
            if (dealer != null)
            {
                // Existing subdomain validation
                if (!string.IsNullOrEmpty(dealer.SubDomain))
                {
                    var normalizedSubdomain = dealer.SubDomain.ToLowerInvariant();
                    dealer.SubDomain = normalizedSubdomain;

                    var existingDealers = await _dataFacade.DealerDataProvider.GetCollectionAsync(
                        filterPredicate: "SubDomain == @0 AND Id != @1",
                        filterArguments: new object[] { normalizedSubdomain, dealer.Id }
                    );

                    if (existingDealers?.Count > 0)
                    {
                        throw new GOServerException($"A dealer with subdomain '{normalizedSubdomain}' already exists. Subdomains must be unique.");
                    }
                }

                // Theme color validation - use IsNullOrWhiteSpace instead of IsNullOrEmpty
                if (!string.IsNullOrWhiteSpace(dealer.ThemeColor) && !System.Text.RegularExpressions.Regex.IsMatch(dealer.ThemeColor, "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"))
                {
                    throw new GOServerException($"Theme color '{dealer.ThemeColor}' is not a valid hex color format. Use #RGB or #RRGGBB format.");
                }
            }
        }

        private async Task OnAfterSaveDataSet(OnAfterSaveDataSetEventArgs e)
        {
            // skip if it is an update
            if (e.EntityBeforeSave.IsNew)
            {
                // create a customer for the dealer with DealerCustomer = true for that customer when a new dealer is created
                var dealer = e.EntityRefetched as DealerDataObject;
                // get all customers of the dealer
                var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "DealerId == @0", new object[] { dealer.Id });
                // check if there are any customers with DealerCustomer = true (can be null)
                var dealerCustomer = customers.FirstOrDefault(c => c.DealerCustomer == true);
                if (dealerCustomer == null)
                {

                    // get all countries
                    var countries = await _dataFacade.CountryDataProvider.GetCollectionAsync();
                    if (countries.Count == 0)
                    {
                        throw new GOServerException("There are no countries in the database.");
                    }

                    var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                    customer.DealerId = dealer.Id;
                    customer.DealerCustomer = true;
                    // append (Dealer) to the name
                    customer.CompanyName = dealer.Name + " (Dealer)";
                    // assign the first country to the customer
                    customer.CountryId = countries.FirstOrDefault().Id;

                    // save the customer
                    await _dataFacade.CustomerDataProvider.SaveAsync(customer);
                }

                var categoryTemplates = await _dataFacade.CategoryTemplateDataProvider.GetCollectionAsync();

                if (categoryTemplates.Any())
                {
                    foreach (var categoryTemplate in categoryTemplates)
                    {
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();

                        model.Name = categoryTemplate.Name;
                        model.Description = categoryTemplate.Description;
                        model.ModelPicture = categoryTemplate.Picture;
                        model.ModelPictureFileSize = categoryTemplate.PictureFileSize;
                        model.ModelPictureInternalName = categoryTemplate.PictureInternalName;
                        model.DealerId = dealer.Id;

                        await _dataFacade.ModelDataProvider.SaveAsync(model);
                    }
                }

                return;
            }

            // skip if it is an update
            return;
        }
    }
}
