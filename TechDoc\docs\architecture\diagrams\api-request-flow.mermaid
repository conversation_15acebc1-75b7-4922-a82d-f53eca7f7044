sequenceDiagram
    participant Client
    participant LoadBalancer
    participant APIGateway
    participant RateLimiter
    participant AuthService
    participant BusinessService
    participant Database
    participant Cache

    Client->>LoadBalancer: HTTP Request
    LoadBalancer->>APIGateway: Route Request
    APIGateway->>RateLimiter: Check Rate Limit
    RateLimiter-->>APIGateway: Rate Limit OK
    APIGateway->>AuthService: Validate Token
    AuthService-->>APIGateway: Token Valid
    APIGateway->>BusinessService: Process Request
    BusinessService->>Cache: Check Cache
    alt Cache Hit
        Cache-->>BusinessService: Cached Data
    else Cache Miss
        BusinessService->>Database: Query Data
        Database-->>BusinessService: Data
        BusinessService->>Cache: Store Data
    end
    BusinessService-->>APIGateway: Response
    APIGateway-->>LoadBalancer: HTTP Response
    LoadBalancer-->>Client: Response