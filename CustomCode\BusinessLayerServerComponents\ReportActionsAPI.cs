﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// ReportActionsAPI Component
	///  
	/// </summary>
    public partial class ReportActionsAPI : BaseServerComponent, IReportActionsAPI 
    {
        private readonly IAuthentication _authentication;
        private readonly IEmailService _emailService;
        public ReportActionsAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IAuthentication authentication, IEmailService emailService) : base(serviceProvider, configuration, dataFacade)
        {
            _authentication = authentication;
            _emailService = emailService;
        }

        public Task<ComponentResponse<bool>> EmailAsync(EmailDataObject email, int reportType, System.String filterPredicate, System.String filterParameters, Dictionary<string, object> parameters = null)
        {
            return _emailService.SendReportEmailAsync(email.Email, reportType, filterPredicate, filterParameters, parameters);
        }

        public async Task<ComponentResponse<ReportSubscriptionDataObject>> SaveAsync(ReportSubscriptionDataObject reportSubscription, int reportType, Dictionary<string, object> parameters)
        {
            // Get the current authenticated user's claims
            var claims = await _authentication.GetCurrentUserClaimsAsync();
            // Retrieve the full user object using the user ID from claims
            var callinguser = await _dataFacade.GOUserDataProvider.GetAsync(new GOUserDataObject(claims.UserId.Value));

            // Try to get the associated person record for the user
            var person = await callinguser.LoadPersonAsync();

            // If a person record exists, associate it with the subscription
            if (person != null)
            {
                reportSubscription.PersonId = person.Id;
            }

            // Always associate the subscription with the calling user's ID
            reportSubscription.GOUserId = callinguser.Id;

            var reportTypes = await _dataFacade.ReportTypeDataProvider.GetCollectionAsync(null);

            var reportTypeObject = reportTypes.FirstOrDefault(x => (int)x.ReportType == reportType);

            if (reportTypeObject == null)
            {
                throw new Exception($"Report type with value: {reportType} not found");
            }

            reportSubscription.ReportTypeId = reportTypeObject.Id;

            var result = await _dataFacade.ReportSubscriptionDataProvider.SaveAsync(reportSubscription, skipSecurity: true);

            return new ComponentResponse<ReportSubscriptionDataObject>(result);
        }

        public async Task<ComponentResponse<bool>> UnsubscribeAsync(AllEmailSubscriptionStoreProcedureDataObject emailSubscription, Dictionary<string, object> parameters = null)
        {
            var reportSub = _serviceProvider.GetRequiredService<ReportSubscriptionDataObject>();
            reportSub.Id = emailSubscription.ReportSubscriptionId;
            reportSub = await _dataFacade.ReportSubscriptionDataProvider.GetAsync(reportSub);
            await _dataFacade.ReportSubscriptionDataProvider.DeleteAsync(reportSub, skipSecurity: true);

            return new ComponentResponse<bool>(true);
        }
    }
}
