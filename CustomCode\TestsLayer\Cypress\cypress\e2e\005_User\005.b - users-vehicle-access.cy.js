describe("005b - Users License", () => {
    let testFirstName;
    let testLastName;
    let uniqueSiteName;
    let cypressCompanyName;
    let uniqueDepartmentName;
    before(() => {
        cy.fixture('testData').then((testData) => {
            testFirstName = testData.cypressFirstName;
            testLastName = testData.cypressLastName;
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            cypressCompanyName = testData.cypressCompanyName;
            uniqueDepartmentName = testData.uniqueDepartmentName;
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login()

        // Navigate to the users section
        cy.get(`[data-bind="'enable' : navigation.isUserManagementEnabled(), 'visible' : navigation.isUserManagementVisible()"] > .nav-link`).click();
        cy.wait(1000);

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/customer/list*').as('getCustomerList');

        // Search for the user we just created
        cy.get('input.filterTextInputCustom.form-control')
            .should('exist')
            .should('be.visible')
            .click({ force: true })
            .clear({ force: true })
            .type(testFirstName, { force: true });


        // Click on the search button
        cy.get('.btn-primary')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // select the first user
        cy.get(`[data-bind="jqStopBubble: 'a'"] > a`)
            .first()
            .should('be.visible')
            .click();

        cy.wait(3000);

        // click on the vehicle tab
        cy.get('[data-id="PersonFormControl-PersonForm-tabs-2"]')
            .should('be.visible')
            .click();

        cy.wait(1000);

    });

    it("Should create/edit card details and vehicle access", () => {
        // find in body No Card Details data available 
        cy.get('body').then($body => {
            const $noCardDetails = $body.find(`.form-spacing-custom.no-data-message`);
            if ($noCardDetails.length > 0 && $noCardDetails.is(':visible')) {
                // ADD LICENSE DETAILS
                cy.log('No Card Details data available');
                // ENABLE EDIT MODE
                cy.get('#PersonFormControlCommands > .btn-group > .btn')
                    .should('be.visible')
                    .click();

                // New Card Details Button
                cy.get(`[data-bind="'enable' : false"] > :nth-child(1) > .subform-field > .optional-relation-command-zone > .command-button`)
                    .should('be.visible')
                    .click();

                // Select Pin ID from dropdown
                cy.get(`.basicForm.edit > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'edit' && CardFormViewModel.StatusData.IsTypeVisible()"] > .form-field-control-container > .enum-field > .form-select`)
                    .should('be.visible')
                    .select('Pin ID');

                // enter random 5 digits pin number
                cy.get(`.basicForm.edit > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'edit' && CardFormViewModel.StatusData.IsCardNumberVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .type(Math.floor(10000 + Math.random() * 90000).toString(), { force: true });

                // click on the save button
                cy.get('button.save.command-button')
                    .should('be.visible')
                    .click();

                cy.wait(3000);

                // confirm if User vehicle access panel is visible
                cy.get('.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm > :nth-child(1)')
                    .should('be.visible');

                // check weigand is not empty
                cy.get(`.basicForm.view > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'view' && CardFormViewModel.StatusData.IsWeigandVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('not.be.empty');
            } else {
                // UPDATE VEHICLE ACCESS

                // CLICK ON DEPARTMENT LEVEL ACCESS TAB
                cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm > :nth-child(1) > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm-PersonVehicleAccessFormControl > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm-PersonVehicleAccessFormData > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm-PersonVehicleAccessForm-tabs > .tabs > [data-bind="'visible' : PersonVehicleAccessFormFormViewModel.StatusData.IsDepartementTabVisible()"] > a`)
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // SELECT SITE LEVEL ACCESS
                cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm > :nth-child(1) > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm-PersonVehicleAccessFormControl > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm-PersonVehicleAccessFormData > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm-PersonVehicleAccessForm-tabs > .tabs > [data-bind="'visible' : PersonVehicleAccessFormFormViewModel.StatusData.IsSiteTabVisible()"] > a`)
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // ENABLE EDIT MODE
                cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormFormCommands > .nav > .edit`)
                    .should('be.visible')
                    .click();

                // CLICK SELECT ALL ACCESS
                cy.get(`.basicForm.view > [data-bind=""] > :nth-child(1) > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormForm > :nth-child(1) > #PersonFormControl-PersonForm-PersonVehicleAccessFormFormCommands > .nav > .selectall`)
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // CLICK SAVE
                cy.get('button.save.command-button')
                    .first()
                    .should('be.visible')
                    .click();

                cy.wait(3000);
            }
        });
    });

    it("Should create/edit general license details", () => {
        cy.get('body').then($body => {
            const $noGeneralLicenceData = $body.find(`[data-bind="'visible' : DriverFormViewModel.GeneralLicenceFormViewModel.StatusData.IsEmpty() && !DriverFormViewModel.GeneralLicenceFormViewModel.StatusData.IsBusy()"]`);
            if ($noGeneralLicenceData.length > 0 && $noGeneralLicenceData.is(':visible') && $noGeneralLicenceData.text().includes('No General Licence data')) {
                // ADD GENERAL LICENCE DETAILS
                cy.log('No General Licence data available');

                // ENABLE EDIT MODE
                cy.get('#PersonFormControlCommands > .btn-group > .btn')
                    .should('be.visible')
                    .click();

                // New General Licence Button 
                cy.get(`[data-bind="'enable' : !DriverFormViewModel.StatusData.IsGeneralLicenceReadOnly(), 'css' : { errorControl : !DriverFormViewModel.DriverObject().StatusData.isGeneralLicenceValid() }"] > :nth-child(1) > .subform-field > .optional-relation-command-zone > .pointer`)
                    .scrollIntoView()
                    .should('be.visible')
                    .click();

                // Enter random 10 digits number
                cy.get(`[data-bind="'enable' : !DriverFormViewModel.StatusData.IsGeneralLicenceReadOnly(), 'css' : { errorControl : !DriverFormViewModel.DriverObject().StatusData.isGeneralLicenceValid() }"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-DriverForm-GeneralLicenceFormControl > #PersonFormControl-PersonForm-DriverForm-GeneralLicenceFormData > :nth-child(1) > .basicForm > .form-field.optionalField > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .type(Math.floor(1000000000 + Math.random() * 9000000000).toString(), { force: true });

                // select the expiry date
                cy.get('.requiredField > .form-field-control-container > .datetime-field > input')
                    .should('be.visible')
                    .click();
                cy.wait(1000);
                cy.get('.today')
                    .should('be.visible')
                    .click();

                // click on the save button
                cy.get('.save.command-button')
                    .should('be.visible')
                    .click();
            } else {
                // UPDATE GENERAL LICENCE DETAILS
                cy.log('General Licence data available');

                // ENABLE EDIT MODE
                cy.get('#PersonFormControlCommands > .btn-group > .btn')
                    .should('be.visible')
                    .click();

                // Enter random 10 digits number
                cy.get(`[data-bind="'enable' : !DriverFormViewModel.StatusData.IsGeneralLicenceReadOnly(), 'css' : { errorControl : !DriverFormViewModel.DriverObject().StatusData.isGeneralLicenceValid() }"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-DriverForm-GeneralLicenceFormControl > #PersonFormControl-PersonForm-DriverForm-GeneralLicenceFormData > :nth-child(1) > .basicForm > .form-field.optionalField > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .scrollIntoView()
                    .should('be.visible')
                    .type(Math.floor(1000000000 + Math.random() * 9000000000).toString(), { force: true });

                // select the expiry date
                cy.get('.requiredField > .form-field-control-container > .datetime-field > input')
                    .should('be.visible')
                    .click();
                cy.wait(1000);
                cy.get('.today')
                    .should('be.visible')
                    .click();

                // click on the save button
                cy.get('.save.command-button')
                    .should('be.visible')
                    .click();
            }
        });
    });

    it('should preserve CardDetailsForm data when canceling', () => {
        // Store initial card values
        const cardValues = {}

        // Click Modify button
        cy.get('#PersonFormControlCommands > .btn-group > .btn')
            .should('be.visible')
            .click()

        cy.wait(1000);

        // Get initial values before modification with better wait conditions
        cy.get(`.basicForm.edit > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'edit' && CardFormViewModel.StatusData.IsTypeVisible()"] > .form-field-control-container > .enum-field > .form-select option:selected`)
            .should('be.visible')
            .invoke('text')
            .then(text => {
                cardValues.type = text?.trim() || ''
            })

        cy.get(`.basicForm.edit > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'edit' && CardFormViewModel.StatusData.IsKeypadReaderVisible()"] > .form-field-control-container > .enum-field > .form-select option:selected`)
            .should('be.visible')
            .invoke('text')
            .then(text => {
                cardValues.keypadReader = text?.trim() || ''
            })

        cy.get(`.basicForm.edit > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > .edit.optionalField > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .invoke('val')
            .then(text => {
                cardValues.facilityCode = text || '0'
            })

        cy.get(`.basicForm.edit > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'edit' && CardFormViewModel.StatusData.IsCardNumberVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .invoke('val')
            .then(text => {
                cardValues.cardNumber = text || ''
            })

        // Click Cancel
        cy.get('.canceledit')
            .should('be.visible')
            .click()

        cy.wait(1000);


        // Now verify the view values
        cy.get(`.basicForm.view > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'view' && CardFormViewModel.StatusData.IsTypeVisible()"] > .form-field-control-container > .enum-field > span`)
            .should('be.visible')
            .invoke('text')
            .then(text => {
                expect(text?.trim()).to.equal(cardValues.type)
            })

        cy.get(`.basicForm.view > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'view' && CardFormViewModel.StatusData.IsKeypadReaderVisible()"] > .form-field-control-container > .enum-field > span`)
            .should('be.visible')
            .invoke('text')
            .then(text => {
                expect(text?.trim()).to.equal(cardValues.keypadReader)
            })

        cy.get(`.basicForm.view > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > .view.optionalField > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .invoke('val')
            .then(text => {
                expect(text || '0').to.equal(cardValues.facilityCode)
            })

        cy.get(`.basicForm.view > [data-bind="'visible':StatusData.IsDriver_CardVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .subform-field > :nth-child(1) > #PersonFormControl-PersonForm-CardFormControl > #PersonFormControl-PersonForm-CardFormData > :nth-child(1) > .basicForm > [data-bind="'visible':CardFormViewModel.StatusData.DisplayMode() == 'view' && CardFormViewModel.StatusData.IsCardNumberVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .invoke('text')
            .then(text => {
                expect(text || '').to.equal(cardValues.cardNumber)
            })
    })
});