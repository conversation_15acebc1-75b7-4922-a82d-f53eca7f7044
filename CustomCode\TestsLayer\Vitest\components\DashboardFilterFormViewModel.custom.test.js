import { describe, it, expect, beforeEach, vi } from 'vitest';
import ko from 'knockout';

// Mock the FleetXQ namespace
global.FleetXQ = {
    Web: {
        ViewModels: {
            DashboardFilterFormViewModelCustom: class {
                constructor(viewModel) {
                    this.viewModel = viewModel;
                    this.initialize = function() {
                        try {
                            const userClaims = ApplicationController.viewModel.security.currentUserClaims();
                            if (userClaims.role?.includes('Customer') && userClaims.CustomerId) {
                                setTimeout(() => {
                                    this.viewModel.DataStoreCustomer.LoadObject({
                                        contextId: this.viewModel.CustomerContextId,
                                        pks: { Id: userClaims.CustomerId },
                                        successHandler: vi.fn(),
                                        errorHandler: this.viewModel.ShowError
                                    });
                                }, 1000);
                            }
                        } catch {}
                        this.viewModel.Modify();
                        this.viewModel.subscribeToMessages();
                    };

                    // Computed observables for dropdown visibility
                    this.IsCustomerDropdownIconVisible = ko.pureComputed(() => true);

                    this.IsSiteDropdownIconVisible = ko.pureComputed(() => {
                        const customerId = this.viewModel.DashboardFilterObject().Data.CustomerId();
                        return customerId !== null && customerId !== undefined;
                    });

                    this.IsDepartmentDropdownIconVisible = ko.pureComputed(() => {
                        const siteId = this.viewModel.DashboardFilterObject().Data.SiteId();
                        return siteId !== null && siteId !== undefined;
                    });
                }
            }
        }
    }
};

describe('DashboardFilterFormViewModel Custom Tests', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Mock the base view model
        viewModel = {
            DashboardFilterObject: ko.observable({
                Data: {
                    CustomerId: ko.observable(null),
                    SiteId: ko.observable(null),
                    DepartmentId: ko.observable(null)
                }
            }),
            CustomerContextId: 'test-context',
            Customer_CompanyName: ko.observable(),
            Customer_lookupItem: ko.observable(),
            Commands: {
                SearchCommand: vi.fn()
            },
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            ShowError: vi.fn(),
            Modify: vi.fn(),
            subscribeToMessages: vi.fn(),
            selectiveLoadDataForSite: vi.fn()
        };

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.DashboardFilterFormViewModelCustom(viewModel);
    });

    describe('Dropdown Icon Visibility Tests', () => {
        it('should always show Customer dropdown icon', () => {
            expect(customViewModel.IsCustomerDropdownIconVisible()).toBe(true);
        });

        it('should not show Site dropdown icon when CustomerId is null', () => {
            viewModel.DashboardFilterObject().Data.CustomerId(null);
            expect(customViewModel.IsSiteDropdownIconVisible()).toBe(false);
        });

        it('should show Site dropdown icon when CustomerId is set', () => {
            viewModel.DashboardFilterObject().Data.CustomerId('test-customer-id');
            expect(customViewModel.IsSiteDropdownIconVisible()).toBe(true);
        });

        it('should not show Department dropdown icon when SiteId is null', () => {
            viewModel.DashboardFilterObject().Data.SiteId(null);
            expect(customViewModel.IsDepartmentDropdownIconVisible()).toBe(false);
        });

        it('should show Department dropdown icon when SiteId is set', () => {
            viewModel.DashboardFilterObject().Data.SiteId('test-site-id');
            expect(customViewModel.IsDepartmentDropdownIconVisible()).toBe(true);
        });
    });

    describe('Initialize Function Tests', () => {
        beforeEach(() => {
            // Mock the ApplicationController
            global.ApplicationController = {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn()
                    }
                }
            };
        });

        it('should not set customer data for non-customer users', () => {
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: 'Admin',
                CustomerId: null
            });

            customViewModel.initialize();
            expect(viewModel.DataStoreCustomer.LoadObject).not.toHaveBeenCalled();
        });

        it('should set customer data for customer role users', () => {
            const customerId = 'test-customer-id';
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                CustomerId: customerId
            });

            vi.useFakeTimers();
            customViewModel.initialize();
            
            // Fast-forward timers
            vi.advanceTimersByTime(1000);

            expect(viewModel.DataStoreCustomer.LoadObject).toHaveBeenCalledWith(
                expect.objectContaining({
                    contextId: viewModel.CustomerContextId,
                    pks: { Id: customerId }
                })
            );

            vi.useRealTimers();
        });

        it('should handle errors gracefully during initialization', () => {
            ApplicationController.viewModel.security.currentUserClaims.mockImplementation(() => {
                throw new Error('Test error');
            });

            expect(() => customViewModel.initialize()).not.toThrow();
            expect(viewModel.Modify).toHaveBeenCalled();
            expect(viewModel.subscribeToMessages).toHaveBeenCalled();
        });
    });
});
