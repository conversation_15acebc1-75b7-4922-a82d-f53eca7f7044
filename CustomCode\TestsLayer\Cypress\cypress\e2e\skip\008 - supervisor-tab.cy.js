//import './005 - users.cy';

describe("008 - SUpervisor Tab", () => {
    beforeEach(() => {
        // Use the global login function defined in Cypress support file
        cy.login(); // This assumes `cy.login()` is globally set up in Cypress' support file.
    });

    it("tests 006 - Vehicle Tab", () => {
        // Clicking on the relevant navigation after login
        cy.get("[data-test-id='\\32 cdc2ef2-af43-4274-97ea-54e2987e29af']").click();

        // Intercept and wait for the initial API call to load the person list
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/list*')
            .as('getPersonList');

        // Wait for the initial person list load
        // Wait for the initial person list load
        cy.wait('@getPersonList', { timeout: 30000 });

        // Function to search for "Alex_Active_Test"
        function searchForAlexActiveTest() {
            // Initial search for "Alex_Active_Test"
            cy.get('input.filterTextInputCustom.form-control')
                .should('exist')         // Ensure the input exists
                .should('be.visible')     // Ensure the input is visible
                .clear()                  // Clear any existing text
                .type('Alex_Active_Test'); // Type the desired search text

            cy.get("[data-test-id='searchCommand']").click();

            // Intercept the API response for the person list again after the search
            cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/list*')
                .as('getPersonListAfterSearch');

            // Wait for the search result and start table search
            cy.wait('@getPersonListAfterSearch').then(() => {
                performTableSearch();
            });
        }

        // Function to search the table for a valid row
        function performTableSearch() {
            let validRecordFound = false; // Flag to check if a valid record is found


            cy.wait('@getPersonList', { timeout: 30000 });

            cy.get('table tbody tr').each(($row) => {
                const userName = $row.find('td:nth-of-type(2)'); // "Card/Pin" column (6th column)

                // Check if both the "Card/Pin" and "Weigand" columns are empty
                if (userName.text().trim()) {
                    // If both columns are empty, click the first anchor found in the row
                    cy.wrap($row)
                        .find('a[href^="#!/UserManagement/UserDetail/"]') // First find the anchor with UserDetail in href
                        .should('exist') // Ensure it exists
                        .then(($anchor) => {
                            cy.wrap($anchor).click(); // Click the anchor link
                        });

                    validRecordFound = true; // Mark that we found a valid row
                    return false; // Exit the loop
                }
            }).then(() => {
                // If no valid record was found in the table, try the next page
                if (!validRecordFound) {
                    goToNextPageIfExists(); // Check for next page
                }
            });
        }

        // Function to handle pagination and go to the next page if available
        function goToNextPageIfExists() {
            cy.get('li.page-numbers.page-controls:not(.invisible)')  // Select all li elements without the 'invisible' class
                .last()                                               // Select the last visible pagination element
                .then($pagination => {
                    if ($pagination.length > 0) {                     // Check if the pagination element exists
                        cy.wrap($pagination)
                            .click()                                  // Click the pagination button
                            .then(() => {
                                // Call the performTableSearch function again after the click
                                performTableSearch();
                            });
                    } else {
                        // No more pages, exit the flow
                        cy.log("No valid records found, and no more pages.");
                    }
                });
        }

        // Start by searching for "Alex_Active_Test" and validate
        searchForAlexActiveTest();

        //Intercept and wait for the API call for person details
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/byid/*')
            .as('getPersonDetails');

        cy.wait('@getPersonDetails', { timeout: 30000 });

        cy.get('span[data-id="PersonFormControl-PersonForm-tabs-4"]') // Target the "Supervisor" tab by its data-id
            .should('exist') // Ensure it exists
            .click(); // Click the tab

        cy.wait(2000);

        cy.get('button[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]') // Target the button by its data-test-id
            .should('exist') // Ensure it exists
            .click(); // Click the button

        

        cy.get("[data-test-id='edit_bdd1410f-7319-4c54-af12-36ed563de62c']").click();
        //cy.wait(2000);
        cy.get("[data-test-id='bdf5d451-849f-4118-8b6f-55a4dde083ea']").click();

        cy.get("div.row > div:nth-of-type(2) > div:nth-of-type(1) [data-test-id='\\38 5088310-1bbb-4069-b9a1-ba730d92cfef']").click();
        //cy.get('button[data-test-id="7547924b-8856-4ee2-9bba-417fbf5de8a1"]').click({ force: true });

        cy.wait(2000);
        cy.get("div.row > div:nth-of-type(2) > div:nth-of-type(1) [data-test-id='\\37 547924b-8856-4ee2-9bba-417fbf5de8a1']").click();
        cy.get("html > body > #form > [data-test-id='main-page-content'] > div.main-wrapper > div.content-wrapper > div > div > div > div > [data-test-id='contentZone_MainZone'] > [data-test-id='\\32 cdbeb9c-1683-41c4-8f60-7469a720fe08'] > div > div:nth-of-type(1) > [data-test-id='\\32 cdbeb9c-1683-41c4-8f60-7469a720fe08'] > #PersonFormControl-PersonFormData > #PersonFormControl-PersonForm-tabs > div.tabs-container > [data-test-id='\\37 36f47d4-4967-49b0-b674-8fd1b91e32c3'] > div.row > div:nth-of-type(2) > div:nth-of-type(1) > div > [data-test-id='\\35 917aa2c-8323-4587-9894-ed7f545fb945'] > span > div > [data-test-id='view_5917aa2c-8323-4587-9894-ed7f545fb945'] > div > [data-test-id='ffdadd52-de63-4336-9d07-8c7e053a525c'] > div > div:nth-of-type(1) > [data-test-id='ffdadd52-de63-4336-9d07-8c7e053a525c'] > div.uiContainer > div > div.tabs-container > [data-test-id='\\34 2c75d55-9533-4082-8a4d-310bc7da0b59'] > div > div > [data-test-id='ee34fbfc-77a1-4142-b7ad-a6dbac2d0e44'] div.uiContainer > div > div:nth-of-type(4) [data-test-id='edit_0e19e9ec-49c5-4714-b4e8-769114219055']").eq(1).click();
        cy.get("div.row > div:nth-of-type(2) > div:nth-of-type(1) [data-test-id='\\34 75a7929-9239-4316-b184-1042481b5e25']").click();
    });
});
