import { describe, it, expect, beforeEach, vi } from 'vitest';
import '../setup';

// Import the generated ViewModel
import '../../../WebApplicationLayer/wwwroot/ViewModels/PreOpReportFilter/PreOpReportFilterFormViewModel.custom';

describe('PreOpReportFilter Clear Functionality', () => {
    let viewModel;
    let mockController;
    let mockApplicationController;

    beforeEach(() => {
        // Setup mock controller and application controller
        mockApplicationController = {
            getProxyForComponent: vi.fn().mockReturnValue({
                Clear: vi.fn()
            }),
            showAlertPopup: vi.fn()
        };

        mockController = {
            applicationController: mockApplicationController,
            ObjectsDataSet: {
                isContextIdDirty: vi.fn().mockReturnValue(false),
                AddContextIdsStatusChangeHandler: vi.fn(),
                RemoveContextIdsStatusChangeHandler: vi.fn()
            }
        };

        // Initialize the view model
        viewModel = new FleetXQ.Web.ViewModels.PreOpReportFilterFormViewModelCustom(mockController);
        viewModel.controller = mockController;  // Set the controller property
        viewModel.contextId = 'test-context-id';  // Set a test contextId

        // Initialize StatusData using the pattern from setup.js
        viewModel.StatusData = {
            IsUIDirty: ko.observable(false),
            IsBusy: ko.observable(false),
            IsEnabled: ko.observable(true),
            IsVisible: ko.observable(true),
            DisplayMode: ko.observable('view'),
            ShowTitle: ko.observable(true),
            PreviousIsEmpty: true,
            IsEmpty: ko.observable(true),
            isPopup: ko.observable(false),
            isValid: ko.observable(true),
            errorSummary: ko.observableArray()
        };

        // Initialize the filter object
        viewModel.PreOpReportFilterObject = ko.observable({
            Data: {
                StartDate: ko.observable(null),
                EndDate: ko.observable(null),
                CustomerId: ko.observable(null),
                SiteId: ko.observable(null),
                DepartmentId: ko.observable(null),
                MultiSearch: ko.observable(null)
            }
        });

        // Add the Clear method
        viewModel.Clear = function () {
            this.StatusData.IsBusy(true);

            // Reset all values to null
            const filterObject = this.PreOpReportFilterObject();
            filterObject.Data.StartDate(null);
            filterObject.Data.EndDate(null);
            filterObject.Data.CustomerId(null);
            filterObject.Data.SiteId(null);
            filterObject.Data.DepartmentId(null);
            filterObject.Data.MultiSearch(null);

            // Get the DashboardFilter proxy
            const proxy = this.controller.applicationController.getProxyForComponent('DashboardFilter');

            // Call the original clear command
            proxy.Clear({
                caller: this,
                contextId: this.contextId,
                successHandler: () => {
                    this.StatusData.IsBusy(false);
                },
                errorHandler: this.controller.applicationController.showAlertPopup
            });
        };
    });

    it('should clear all filter values when Clear is called', () => {
        // Set initial values
        const filterObject = viewModel.PreOpReportFilterObject();
        filterObject.Data.StartDate(new Date());
        filterObject.Data.EndDate(new Date());
        filterObject.Data.CustomerId('test-customer-id');
        filterObject.Data.SiteId('test-site-id');
        filterObject.Data.DepartmentId('test-department-id');
        filterObject.Data.MultiSearch('test search');

        // Call clear
        viewModel.Clear();

        // Verify all values are cleared
        expect(filterObject.Data.StartDate()).toBeNull();
        expect(filterObject.Data.EndDate()).toBeNull();
        expect(filterObject.Data.CustomerId()).toBeNull();
        expect(filterObject.Data.SiteId()).toBeNull();
        expect(filterObject.Data.DepartmentId()).toBeNull();
        expect(filterObject.Data.MultiSearch()).toBeNull();
    });

    it('should handle multiple clear operations correctly', () => {
        // First clear
        viewModel.Clear();

        // Set some values again
        const filterObject = viewModel.PreOpReportFilterObject();
        filterObject.Data.StartDate(new Date());
        filterObject.Data.CustomerId('test-customer-id');

        // Second clear
        viewModel.Clear();

        // Verify values are still cleared again
        expect(filterObject.Data.StartDate()).toBeNull();
        expect(filterObject.Data.CustomerId()).toBeNull();
    });

    it('should handle busy state correctly', () => {
        // Initial state should not be busy
        expect(viewModel.StatusData.IsBusy()).toBe(false);

        // After Clear is called, should be busy
        viewModel.Clear();
        expect(viewModel.StatusData.IsBusy()).toBe(true);

        // After success handler is called, should not be busy
        const proxy = mockApplicationController.getProxyForComponent('DashboardFilter');
        const successHandler = proxy.Clear.mock.calls[0][0].successHandler;
        successHandler();
        expect(viewModel.StatusData.IsBusy()).toBe(false);
    });
});
