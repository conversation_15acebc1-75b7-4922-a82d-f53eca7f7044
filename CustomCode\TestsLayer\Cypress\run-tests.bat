@echo off
echo Starting Cypress Test Sequence...

echo Running Step 1: Prerequisites
call npx cypress run --spec "cypress/e2e/000 - prereq.cy.js"
if %errorlevel% neq 0 echo Step 1 failed with error code %errorlevel% but continuing...

echo Running Step 2: Dashboard Tests
call npx cypress run --spec "cypress/e2e/001 - dashboard.cy.js"
if %errorlevel% neq 0 echo Step 2 failed with error code %errorlevel% but continuing...

echo Running Step 3: Customer Tests
call npx cypress run --spec "cypress/e2e/002.a - customer-temp.cy.js"
if %errorlevel% neq 0 echo Step 3 failed with error code %errorlevel% but continuing...

echo Running Step 4: Sites Tests
call npx cypress run --spec "cypress/e2e/003.a - sites-temp.cy.js"
if %errorlevel% neq 0 echo Step 4 failed with error code %errorlevel% but continuing...

echo Running Step 5: Sites Department Tests
call npx cypress run --spec "cypress/e2e/003.b - sites-department-temp.cy.js"
if %errorlevel% neq 0 echo Step 5 failed with error code %errorlevel% but continuing...

echo Running Step 6: Users Tests
call npx cypress run --spec "cypress/e2e/005.a - users.cy.js"
if %errorlevel% neq 0 echo Step 6 failed with error code %errorlevel% but continuing...

echo Running Step 7: Users Vehicle Access Tests
call npx cypress run --spec "cypress/e2e/005.b - users-vehicle-access.cy.js"
if %errorlevel% neq 0 echo Step 7 failed with error code %errorlevel% but continuing...

echo Running Step 8: Users Supervisor Tests
call npx cypress run --spec "cypress/e2e/005.c - users-supervisor.cy.js"
if %errorlevel% neq 0 echo Step 8 failed with error code %errorlevel% but continuing...

echo Running Step 9: Users Website Access Tests
call npx cypress run --spec "cypress/e2e/005.d - users-website-access.cy.js"
if %errorlevel% neq 0 echo Step 9 failed with error code %errorlevel% but continuing...

echo Running Step 10: Cleanup
call npx cypress run --spec "cypress/e2e/000- cleanup.cy.js"

echo All Cypress Tests Completed!
pause 