import { defineConfig } from 'vitest/config'
import { resolve } from 'path'

export default defineConfig({
  test: {
    globals: true,
    // Use jsdom for browser-like environment
    environment: 'jsdom',
    include: ['**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules'],
    // Setup files to run before tests
    setupFiles: ['./setup.js'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      exclude: ['node_modules/']
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, '.'),
      // Add path to your actual Knockout code
      '@src': resolve(__dirname, '../../wwwroot/js'),
    },
  },
})
