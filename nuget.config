﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <!-- Clear any inherited sources -->
    <clear />
    
    <!-- Official NuGet.org for external packages -->
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    
    <!-- Local development feed for GO framework packages -->
    <!-- Note: The C:\dev\nuget-local directory is automatically created during build/restore if it doesn't exist -->
    <add key="local-dev" value="C:\dev\nuget-local" />
    
    <!-- GitHub Packages private feed for production packages -->
    <add key="github-generative-objects" value="https://nuget.pkg.github.com/generative-objects-org/index.json" />
    <!-- Note: GitHubPackagesFeedUrl should be: https://nuget.pkg.github.com/generative-objects-org/index.json -->   
  </packageSources>
  
  <!-- Package source mapping for security and performance -->
  <packageSourceMapping>
    <!-- GO packages from local development feed (highest priority) -->
    <packageSource key="local-dev">
      <package pattern="GenerativeObjects.*" />
      <package pattern="FleetXQ.*" />
    </packageSource>

    <!-- GO packages from GitHub Packages (fallback when not available locally) -->
    <packageSource key="github-generative-objects">
      <package pattern="GenerativeObjects.*" />
      <package pattern="FleetXQ.*" />
    </packageSource>

    <!-- All other packages from nuget.org -->
    <packageSource key="nuget.org">
      <package pattern="*" />
    </packageSource>
  </packageSourceMapping>
  
  <!-- Package source credentials for GitHub Packages -->
  <packageSourceCredentials>
    <github-generative-objects>
      <add key="Username" value="your-github-username" />
      <add key="ClearTextPassword" value="%GENERATIVEOBJECTS_GITHUB_TOKEN%" />
    </github-generative-objects>
  </packageSourceCredentials>
  
  <!-- Global package management settings -->
  <config>
    <!-- Automatically check for missing packages during build -->
    <add key="automatic-package-restore" value="true" />
    
    <!-- Skip duplicate packages during restore -->
    <add key="skip-duplicate-packages" value="true" />
    
    <!-- Use package source mapping (requires NuGet 6.0+) -->
    <add key="usePackageSourceMapping" value="true" />
  </config>
  
  <!-- 
    Package source credentials for GitHub Packages:
    Authentication is configured above using the GENERATIVEOBJECTS_GITHUB_TOKEN environment variable.
    
    Set up the environment variable by running:
    .\Scripts\SetGitHubToken.ps1
    
    Alternative manual setup:
    dotnet nuget add source "https://nuget.pkg.github.com/generative-objects-org/index.json" DASH DASH name "GitHub-GenerativeObjects" DASH DASH username "your-username" DASH DASH password "your-token" DASH DASH store-password-in-clear-text
    
    If the source already exists, update it instead:
    dotnet nuget update source "GitHub-GenerativeObjects" DASH DASH username "your-username" DASH DASH password "your-token" DASH DASH store-password-in-clear-text
  -->
</configuration> 
