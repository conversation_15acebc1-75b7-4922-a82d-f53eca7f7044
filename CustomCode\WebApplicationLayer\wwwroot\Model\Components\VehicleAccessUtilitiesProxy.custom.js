////////////////////////////////////////////////////////////////////////////////////////////
// This is Custom Code to override generated VehicleAccessUtilitiesProxy
// Purpose: Add missing PermissionLevel parameter to Update<PERSON>cc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> calls
////////////////////////////////////////////////////////////////////////////////////////////

// Override the UpdateAccessesForPerson method to include PermissionLevel parameter
FleetXQ.Web.Model.Components.VehicleAccessUtilitiesProxy.prototype.UpdateAccessesForPerson = function (configuration) {
    if (!configuration) {
        throw new Error("Configuration parameter is required");
    }

    console.log("[CUSTOM PROXY] UpdateAccess<PERSON><PERSON><PERSON><PERSON><PERSON> called with PermissionLevel:", configuration.permissionLevel);

    return new Promise((resolve, reject) => {
        var Guid = FleetXQ.Web.Model.Components.Guid;

        // Validate required parameters
        if (!configuration.personId) {
            reject(new Error("personId is required"));
            return;
        }

        $.ajax({
            url: "/api/vehicleaccessutilities/updateaccessesforperson",
            dataType: "json",
            type: "POST",
            headers: {
                'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
            },
            data: {
                dateformat: "ISO8601",
                personToSiteAccesses: JSON.stringify(FleetXQ.Web.Model.DataStores.MapDataSetToJSON(configuration.personToSiteAccesses, configuration.contextId)),
                personToDepartmentAccesses: JSON.stringify(FleetXQ.Web.Model.DataStores.MapDataSetToJSON(configuration.personToDepartmentAccesses, configuration.contextId)),
                personToModelAccesses: JSON.stringify(FleetXQ.Web.Model.DataStores.MapDataSetToJSON(configuration.personToModelAccesses, configuration.contextId)),
                personToVehicleAccesses: JSON.stringify(FleetXQ.Web.Model.DataStores.MapDataSetToJSON(configuration.personToVehicleAccesses, configuration.contextId)),
                personId: configuration.personId == null ? Guid.Empty : configuration.personId,
                // CRITICAL: Include PermissionLevel parameter that was missing in generated proxy
                PermissionLevel: configuration.permissionLevel || 3
            },
            success: function (result) {
                console.log("[CUSTOM PROXY] UpdateAccessesForPerson SUCCESS with PermissionLevel:", configuration.permissionLevel);
                if (configuration.successHandler) {
                    configuration.successHandler(result);
                }
                resolve(result);
            },
            error: function (xhr, status, error) {
                console.error("[CUSTOM PROXY] UpdateAccessesForPerson ERROR:", error, "Status:", status);
                if (configuration.errorHandler) {
                    configuration.errorHandler(xhr, status, error);
                }
                reject(new Error(`AJAX Error: ${status} - ${error}`));
            }
        });
    });
};

console.log("[CUSTOM PROXY] VehicleAccessUtilitiesProxy.UpdateAccessesForPerson override loaded successfully");