﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Azure.Devices;
using GenerativeObjects.Practices.ExceptionHandling;
using Newtonsoft.Json;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.DependencyInjection;
namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class DeviceMessageHandler : IDeviceMessageHandler
    {
        ServiceClient _serviceClient;
        private readonly IConfiguration _configuration;
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthentication _authentication;
        private RegistryManager _registryManager;
        private readonly ILoggingService _logger;
        public DeviceMessageHandler(IConfiguration configuration, IDataFacade dataFacade, IServiceProvider serviceProvider, IAuthentication authentication, ILoggingService logger)
        {
            _configuration = configuration;
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _authentication = authentication;
            _serviceClient = ServiceClient.CreateFromConnectionString(_configuration["IoThubConnectionString"]);
            _registryManager = RegistryManager.CreateFromConnectionString(_configuration["IoThubConnectionString"]);
            _logger = logger;
        }
        public async Task SendCloudToDeviceMessageAsync(string targetDevice, String c2dMessage)
        {
            try
            {
                var sessionId = Guid.NewGuid().ToString();
                // get the current login user   
                var claims = await _authentication.GetCurrentUserClaimsAsync();
                var userId = claims.UserId.Value;
                var userName = claims.UserName;

                var jsonMessage = new
                {
                    session_id = sessionId,
                    type = "cmd",
                    payload = c2dMessage,
                    user_id = userId,
                    user_name = userName
                };
                _logger.LogInformation($"Sending C2D message to {targetDevice}: {JsonConvert.SerializeObject(jsonMessage)}");
                var message = new Message(Encoding.ASCII.GetBytes(JsonConvert.SerializeObject(jsonMessage)));
                await _serviceClient.SendAsync(targetDevice, message);
            }
            catch (Exception ex)
            {
                throw new GOServerException($"Error sending C2D message: {ex.Message}");
            }
        }
        public async Task UpdateDesiredProperties(string targetDevice, object desired, int syncType, Guid? userId = null)
        {
            var twin = await _registryManager.GetTwinAsync(targetDevice);
            try
            {
                if (twin != null)
                {
                    var patch = new
                    {
                        properties = new
                        {
                            desired
                        }
                    };
                    var updatedTwin = await _registryManager.UpdateTwinAsync(twin.DeviceId, JsonConvert.SerializeObject(patch, Formatting.None), twin.ETag);

                    // save message as history in MessageHistory
                    await SaveMessageHistory(targetDevice, syncType, userId);

                }
            }
            catch (Exception ex)
            {
                throw new GOServerException($"Error sending C2D message: {ex.Message}");
            }
        }

        private async Task SaveMessageHistory(string targetDevice, int syncType, Guid? userId = null)
        {
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { targetDevice }, skipSecurity: true)).SingleOrDefault();
            var vehicle = await module.LoadVehicleAsync(skipSecurity: true);
            var message = _serviceProvider.GetRequiredService<MessageHistoryDataObject>();

            try
            {
                if (userId == null)
                {
                    var claims = await _authentication.GetCurrentUserClaimsAsync();
                    userId = claims?.UserId.Value;
                }
                if (userId == null)
                {
                    // Skip message history for background tasks without user context
                    return;
                }
                message.GOUserId = userId.Value;
            }
            catch
            {
                // Skip message history for background tasks without user context
                return;
            }

            message.SentTimestamp = DateTime.UtcNow;
            message.MessageStatus = (MessageStatusEnum)1; // SENT
            message.VehicleId = vehicle.Id;

            switch (syncType)
            {
                case 0:
                    message.SyncType = (SynchronizationTypeEnum)0; // All Setting
                    break;
                case 1:
                    message.SyncType = (SynchronizationTypeEnum)1; // Driver & Supervisor List
                    break;
                case 2:
                    message.SyncType = (SynchronizationTypeEnum)2; // Pre-Op Checklist
                    break;
                case 3:
                    message.SyncType = (SynchronizationTypeEnum)3; // Checklist Setting
                    break;
                case 4:
                    message.SyncType = (SynchronizationTypeEnum)4; // Timezone
                    break;
                case 5:
                    message.SyncType = (SynchronizationTypeEnum)5; // VOR Setting
                    break;
                case 6:
                    message.SyncType = (SynchronizationTypeEnum)6; // Full Lockout Setting
                    break;
                case 7:
                    message.SyncType = (SynchronizationTypeEnum)7; // Firmware Update
                    break;
                case 8:
                    message.SyncType = (SynchronizationTypeEnum)8; // CAN Rules
                    break;
                case 9:
                    message.SyncType = (SynchronizationTypeEnum)9; // Impact Setting
                    break;
            }

            await _dataFacade.MessageHistoryDataProvider.SaveAsync(message, skipSecurity: true);
        }
    }
}
