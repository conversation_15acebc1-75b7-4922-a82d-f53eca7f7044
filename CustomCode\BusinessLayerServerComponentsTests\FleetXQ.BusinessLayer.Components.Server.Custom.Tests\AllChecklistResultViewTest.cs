using System;
using System.Data;
using System.Data.SqlClient;
using NUnit.Framework;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class AllChecklistResultViewTest
    {
        private SqlConnection _connection;
        private string _connectionString = "data source=.\\SQLEXPRESS; initial catalog=FleetXQ;integrated security=SSPI;MultipleActiveResultSets=True";

        [SetUp]
        public void Setup()
        {
            _connection = new SqlConnection(_connectionString);
            _connection.Open();
        }

        [TearDown]
        public void TearDown()
        {
            if (_connection != null && _connection.State == ConnectionState.Open)
            {
                _connection.Close();
            }
        }

        [Test]
        public void AllChecklistResultView_ShouldHaveHasCriticalQuestionsColumn()
        {
            // Arrange
            var sql = @"
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME = 'AllChecklistResultView' 
                AND COLUMN_NAME = 'HasCriticalQuestions'";

            // Act
            using (var command = new SqlCommand(sql, _connection))
            {
                var result = command.ExecuteScalar();

                // Assert
                Assert.That(result, Is.Not.Null, "HasCriticalQuestions column should exist in AllChecklistResultView");
                Assert.That(result.ToString(), Is.EqualTo("HasCriticalQuestions"));
            }
        }

        [Test]
        public void OtherViews_ShouldNotHaveHasCriticalQuestionsColumn()
        {
            // Arrange
            var sql = @"
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_NAME IN ('ChecklistFailureView', 'ChecklistFailurePerVechicleView')
                AND COLUMN_NAME = 'HasCriticalQuestions'";

            // Act
            using (var command = new SqlCommand(sql, _connection))
            {
                var result = command.ExecuteScalar();

                // Assert
                Assert.That(result, Is.Null, "HasCriticalQuestions column should not exist in other checklist views");
            }
        }

        [Test]
        public void GetAllChecklistResults_ShouldReturnHasCriticalQuestions()
        {
            // Arrange
            var sql = @"
                -- Execute the stored procedure and check if HasCriticalQuestions column exists
                EXEC [dbo].[GetAllChecklistResults] 
                    @CustomerId = NULL,
                    @SiteId = NULL,
                    @DepartmentId = NULL,
                    @StartDate = NULL,
                    @EndDate = NULL,
                    @PageIndex = 0,
                    @PageSize = 1,
                    @ReturnTotalCount = 0,
                    @ResultType = NULL,
                    @ChecklistResultId = NULL,
                    @MultiSearch = NULL;";

            // Act
            using (var command = new SqlCommand(sql, _connection))
            {
                using (var reader = command.ExecuteReader())
                {
                    // Assert
                    Assert.That(reader.HasRows, Is.True, "GetAllChecklistResults should return rows");
                    Assert.That(reader.FieldCount, Is.GreaterThan(0), "GetAllChecklistResults should return columns");

                    // Check if HasCriticalQuestions column exists
                    bool hasColumn = false;
                    for (int i = 0; i < reader.FieldCount; i++)
                    {
                        if (reader.GetName(i) == "HasCriticalQuestions")
                        {
                            hasColumn = true;
                            break;
                        }
                    }

                    Assert.That(hasColumn, Is.True, "GetAllChecklistResults should return HasCriticalQuestions column");
                }
            }
        }
    }
}
