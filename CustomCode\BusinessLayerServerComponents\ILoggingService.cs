﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public interface ILoggingService
    {
        void LogError(Exception ex, string message = null);
        void LogInformation(string message);
        void LogDebug(string message);
        void LogWarning(string message);
    }
}
