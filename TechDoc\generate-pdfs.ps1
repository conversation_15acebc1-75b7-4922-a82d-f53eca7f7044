# PDF Generation Script for GO Platform Documentation
# This script generates PDFs for key documentation files using optimized settings

param(
    [string]$Section = "all",  # Options: all, features, core-models, guides
    [switch]$Verbose = $false
)

Write-Host "🔄 Starting PDF generation for GO Platform Documentation..." -ForegroundColor Green
Write-Host "📋 Section: $Section" -ForegroundColor Cyan
Write-Host ""

# Check if md-to-pdf is installed
try {
    $null = Get-Command md-to-pdf -ErrorAction Stop
    Write-Host "✅ md-to-pdf found" -ForegroundColor Green
} catch {
    Write-Host "❌ md-to-pdf not found. Installing..." -ForegroundColor Red
    npm install -g md-to-pdf
    Write-Host "✅ md-to-pdf installed" -ForegroundColor Green
}

Write-Host ""

# Define the files to convert based on section
$allFiles = @()

# Core documentation files
if ($Section -eq "all" -or $Section -eq "core") {
    $allFiles += @(
        @{ Path = "docs/README.md"; Name = "GO Platform Overview" },
        @{ Path = "docs/introduction/README.md"; Name = "Introduction" },
        @{ Path = "docs/meta-approach/README.md"; Name = "Meta-Approach" },
        @{ Path = "docs/core-meta-model/README.md"; Name = "Core Meta-Model" }
    )
}

# Core generation models
if ($Section -eq "all" -or $Section -eq "core-models") {
    $allFiles += @(
        @{ Path = "docs/core-generation-models/README.md"; Name = "Core Generation Models Overview" },
        @{ Path = "docs/core-generation-models/generation-application-architecture.md"; Name = "Generation Application Architecture" },
        @{ Path = "docs/core-generation-models/go-core-model.md"; Name = "GO Core Model" },
        @{ Path = "docs/core-generation-models/business-model.md"; Name = "Business Model" },
        @{ Path = "docs/core-generation-models/data-model.md"; Name = "Data Model" },
        @{ Path = "docs/core-generation-models/service-model.md"; Name = "Service Model" },
        @{ Path = "docs/core-generation-models/web-application-model.md"; Name = "Web Application Model" },
        @{ Path = "docs/core-generation-models/client-sdk-model.md"; Name = "Client SDK Model" }
    )
}

# Features documentation
if ($Section -eq "all" -or $Section -eq "features") {
    $allFiles += @(
        @{ Path = "docs/core-generation-models/features/README.md"; Name = "Generated Application Features Overview" },
        
        # API Key Management
        @{ Path = "docs/core-generation-models/features/api-key/README.md"; Name = "API Key Feature Documentation" },
        @{ Path = "docs/core-generation-models/features/api-key/integration-guide.md"; Name = "API Key Integration Guide" },
        
        # Security Features
        @{ Path = "docs/core-generation-models/features/security/README.md"; Name = "Security Features Overview" },
        @{ Path = "docs/core-generation-models/features/security/security-flow.md"; Name = "Security Flow Implementation" },
        
        # Internationalization
        @{ Path = "docs/core-generation-models/features/internationalization/README.md"; Name = "Internationalization Overview" },
        @{ Path = "docs/core-generation-models/features/internationalization/setup.md"; Name = "i18n Setup Guide" },
        @{ Path = "docs/core-generation-models/features/internationalization/frontend-usage.md"; Name = "Frontend i18n Usage" },
        @{ Path = "docs/core-generation-models/features/internationalization/backend-usage.md"; Name = "Backend i18n Usage" },
        @{ Path = "docs/core-generation-models/features/internationalization/data-usage.md"; Name = "Database i18n Usage" },
        @{ Path = "docs/core-generation-models/features/internationalization/organization.md"; Name = "Translation Organization" },
        @{ Path = "docs/core-generation-models/features/internationalization/best-practices.md"; Name = "i18n Best Practices" }
    )
}

# Technical guides
if ($Section -eq "all" -or $Section -eq "guides") {
    $allFiles += @(
        @{ Path = "docs/technical-guides/README.md"; Name = "Technical Guides Overview" },
        @{ Path = "docs/go-modeler/README.md"; Name = "GO Modeler Documentation" },
        @{ Path = "docs/extending-meta-model/README.md"; Name = "Extending Meta-Model Overview" }
    )
}

# Special files
if ($Section -eq "all" -or $Section -eq "special") {
    $allFiles += @(
        @{ Path = "PDF_Generation_Guide.md"; Name = "PDF Generation Guide" }
    )
}

# Generate PDFs
$successCount = 0
$totalCount = $allFiles.Count

Write-Host "📊 Processing $totalCount files..." -ForegroundColor Yellow
Write-Host ""

foreach ($doc in $allFiles) {
    Write-Host "📄 Generating: $($doc.Name)..." -ForegroundColor Cyan
    
    if ($Verbose) {
        Write-Host "   📁 Source: $($doc.Path)" -ForegroundColor Gray
    }
    
    try {
        # Check if source file exists
        if (-not (Test-Path $doc.Path)) {
            Write-Host "   ❌ Source file not found: $($doc.Path)" -ForegroundColor Red
            continue
        }
        
        # Generate PDF
        $pdfPath = $doc.Path -replace '\.md$', '.pdf'
        
        if ($Verbose) {
            Write-Host "   🔧 Running: md-to-pdf ""$($doc.Path)"" --config-file ""pdf-config.json""" -ForegroundColor Gray
        }
        
        md-to-pdf $doc.Path --config-file "pdf-config.json"
        
        # Check if PDF was created
        if (Test-Path $pdfPath) {
            $fileSize = [math]::Round((Get-Item $pdfPath).Length / 1KB, 1)
            Write-Host "   ✅ Generated: $pdfPath ($fileSize KB)" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "   ❌ PDF generation failed for: $($doc.Path)" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "   ❌ Error generating PDF: $($_.Exception.Message)" -ForegroundColor Red
        if ($Verbose) {
            Write-Host "   🔍 Full error: $($_.Exception)" -ForegroundColor Gray
        }
    }
    
    Write-Host ""
}

# Summary
Write-Host "📊 Generation Summary:" -ForegroundColor Yellow
Write-Host "   Section: $Section" -ForegroundColor Cyan
Write-Host "   Success: $successCount/$totalCount files" -ForegroundColor Green

if ($successCount -eq $totalCount) {
    Write-Host "🎉 All PDFs generated successfully!" -ForegroundColor Green
} elseif ($successCount -gt 0) {
    Write-Host "⚠️  Some PDFs failed to generate. Check errors above." -ForegroundColor Yellow
} else {
    Write-Host "❌ No PDFs were generated. Check your setup." -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Troubleshooting steps:" -ForegroundColor Cyan
    Write-Host "   1. Ensure you're in the Documentation directory" -ForegroundColor White
    Write-Host "   2. Verify md-to-pdf is installed: npm list -g md-to-pdf" -ForegroundColor White
    Write-Host "   3. Check that pdf-config.json and pdf-style.css exist" -ForegroundColor White
    Write-Host "   4. Run with -Verbose for detailed error information" -ForegroundColor White
}

Write-Host ""
Write-Host "💡 Usage examples:" -ForegroundColor Cyan
Write-Host "   .\generate-pdfs.ps1                    # Generate all PDFs" -ForegroundColor White
Write-Host "   .\generate-pdfs.ps1 -Section features  # Generate only features PDFs" -ForegroundColor White
Write-Host "   .\generate-pdfs.ps1 -Verbose           # Generate with verbose output" -ForegroundColor White
Write-Host ""
Write-Host "📁 Generated PDFs are located alongside their source markdown files" -ForegroundColor Cyan
Write-Host "🔧 To modify PDF settings, edit pdf-config.json and pdf-style.css" -ForegroundColor Cyan