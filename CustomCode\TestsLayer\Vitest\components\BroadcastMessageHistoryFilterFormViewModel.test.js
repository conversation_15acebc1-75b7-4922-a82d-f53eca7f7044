import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

describe('BroadcastMessageHistoryFilterFormViewModel', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Mock the global FleetXQ object
        global.FleetXQ = {
            Web: {
                ViewModels: {
                    BroadcastMessageHistoryFilterFormViewModelCustom: function(vm) {
                        this.viewModel = vm;
                        this.initialize = function() {
                            try {
                                // Set default times on initialization if dates are not set
                                var startDate = this.viewModel.BroadcastMessageHistoryFilterObject().Data.StartTime();
                                var endDate = this.viewModel.BroadcastMessageHistoryFilterObject().Data.EndTime();

                                if (!startDate) {
                                    var defaultStart = new Date();
                                    defaultStart.setHours(0, 0, 0, 0);
                                    this.viewModel.BroadcastMessageHistoryFilterObject().Data.StartTime(defaultStart);
                                }

                                if (!endDate) {
                                    var defaultEnd = new Date();
                                    defaultEnd.setHours(23, 59, 59, 999);
                                    this.viewModel.BroadcastMessageHistoryFilterObject().Data.EndTime(defaultEnd);
                                }

                                // Time selection handlers
                                this.viewModel.startTimeSelectionChanged = function (selectedDate) {
                                    if (selectedDate) {
                                        this.viewModel.BroadcastMessageHistoryFilterObject().Data.StartTime(new Date(selectedDate));
                                    }
                                }.bind(this);

                                this.viewModel.endTimeSelectionChanged = function (selectedDate) {
                                    if (selectedDate) {
                                        this.viewModel.BroadcastMessageHistoryFilterObject().Data.EndTime(new Date(selectedDate));
                                    }
                                }.bind(this);

                                const userClaims = ApplicationController.viewModel.security.currentUserClaims();
                                const isCustomerRole = userClaims.role?.includes('Customer');

                                if (isCustomerRole && userClaims.CustomerId) {
                                    setTimeout(() => {
                                        this.viewModel.DataStoreCustomer.LoadObject({
                                            contextId: this.viewModel.CustomerContextId,
                                            pks: { Id: userClaims.CustomerId },
                                            successHandler: () => {},
                                            errorHandler: this.viewModel.ShowError
                                        });
                                    }, 1000);
                                }

                                this.viewModel.Modify();
                            } catch (e) {
                                console.error("Error in initialize:", e);
                            }
                        };
                    }
                }
            }
        };

        // Mock the global ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn().mockReturnValue({
                        role: ['Customer'],
                        CustomerId: 'test-customer-id'
                    })
                }
            }
        };

        // Mock the base viewModel
        viewModel = {
            BroadcastMessageHistoryFilterObject: vi.fn().mockReturnValue({
                Data: {
                    StartTime: vi.fn().mockReturnValue(null),
                    EndTime: vi.fn().mockReturnValue(null),
                    Type: vi.fn(),
                    CustomerId: vi.fn(),
                    SiteId: vi.fn(),
                    DepartmentId: vi.fn(),
                    MultiSearch: vi.fn()
                }
            }),
            CustomerContextId: 'test-context',
            Customer_CompanyName: vi.fn(),
            Customer_lookupItem: vi.fn(),
            Commands: {
                FilterCommand: vi.fn()
            },
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            ShowError: vi.fn(),
            selectiveLoadDataForSite: vi.fn(),
            Modify: vi.fn(),
            Clear: function() {
                const filterData = this.BroadcastMessageHistoryFilterObject().Data;
                filterData.StartTime(null);
                filterData.EndTime(null);
                filterData.Type(null);
                filterData.CustomerId(null);
                filterData.SiteId(null);
                filterData.DepartmentId(null);
                filterData.MultiSearch(null);
            }
        };

        // Create instance of custom view model
        customViewModel = new FleetXQ.Web.ViewModels.BroadcastMessageHistoryFilterFormViewModelCustom(viewModel);
    });

    afterEach(() => {
        vi.clearAllMocks();
        delete global.FleetXQ;
        delete global.ApplicationController;
    });

    describe('initialize', () => {
        it('should set default start time to beginning of day when not set', () => {
            const startTimeSpy = vi.spyOn(viewModel.BroadcastMessageHistoryFilterObject().Data, 'StartTime');
            customViewModel.initialize();
            
            expect(startTimeSpy).toHaveBeenCalled();
            const setDate = startTimeSpy.mock.calls[1][0]; // Second call contains the set date
            expect(setDate.getHours()).toBe(0);
            expect(setDate.getMinutes()).toBe(0);
        });

        it('should set default end time to end of day when not set', () => {
            const endTimeSpy = vi.spyOn(viewModel.BroadcastMessageHistoryFilterObject().Data, 'EndTime');
            customViewModel.initialize();
            
            expect(endTimeSpy).toHaveBeenCalled();
            const setDate = endTimeSpy.mock.calls[1][0]; // Second call contains the set date
            expect(setDate.getHours()).toBe(23);
            expect(setDate.getMinutes()).toBe(59);
        });

        it('should set up time selection handlers', () => {
            customViewModel.initialize();
            
            expect(typeof viewModel.startTimeSelectionChanged).toBe('function');
            expect(typeof viewModel.endTimeSelectionChanged).toBe('function');
        });

        it('should load customer data for customer role users', () => {
            vi.useFakeTimers();
            customViewModel.initialize();
            
            vi.advanceTimersByTime(1000);
            expect(viewModel.DataStoreCustomer.LoadObject).toHaveBeenCalledWith({
                contextId: 'test-context',
                pks: { Id: 'test-customer-id' },
                successHandler: expect.any(Function),
                errorHandler: viewModel.ShowError
            });
            vi.useRealTimers();
        });
    });

    describe('Clear', () => {
        it('should reset all filter values', () => {
            customViewModel.initialize();
            
            const filterData = viewModel.BroadcastMessageHistoryFilterObject().Data;
            viewModel.Clear();

            expect(filterData.StartTime).toHaveBeenCalledWith(null);
            expect(filterData.EndTime).toHaveBeenCalledWith(null);
            expect(filterData.Type).toHaveBeenCalledWith(null);
            expect(filterData.CustomerId).toHaveBeenCalledWith(null);
            expect(filterData.SiteId).toHaveBeenCalledWith(null);
            expect(filterData.DepartmentId).toHaveBeenCalledWith(null);
            expect(filterData.MultiSearch).toHaveBeenCalledWith(null);
        });
    });
}); 