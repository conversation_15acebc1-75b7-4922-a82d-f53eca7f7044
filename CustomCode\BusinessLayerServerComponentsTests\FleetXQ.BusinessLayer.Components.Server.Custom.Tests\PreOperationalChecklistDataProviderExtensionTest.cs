using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class PreOperationalChecklistDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"PreOperationalChecklistDataProviderExtensionTests-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            services.AddScoped<IDataProviderExtension<PreOperationalChecklistDataObject>, PreOperationalChecklistDataProviderExtension>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUp()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<DepartmentChecklistDataObject> CreateTestDepartmentAndChecklistAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = customer.Id;
            site.Name = "Test Site";
            site.TimezoneId = timeZone.Id;
            site.Id = Guid.NewGuid();
            await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Description";
            model.DealerId = dealer.Id;
            model.Type = ModelTypesEnum.Electric;
            await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = department.Id;
            return await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist, skipSecurity: true);
        }

        [Test]
        public async Task Save_WithExistingQuestion_KeepsOriginalOrder()
        {
            // Arrange
            var departmentChecklist = await CreateTestDepartmentAndChecklistAsync();
            Assert.That(departmentChecklist, Is.Not.Null, "Test data setup failed: DepartmentChecklist creation failed.");

            // Create a question with order 1
            var question = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question.Id = Guid.NewGuid();
            question.SiteChecklistId = departmentChecklist.Id;
            question.Question = "Test Question";
            question.Active = true;
            question = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question, skipSecurity: true);
            Assert.That(question.Order, Is.EqualTo(1), "Initial question should be assigned order 1");

            // Create another question with order 2
            var question2 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question2.Id = Guid.NewGuid();
            question2.SiteChecklistId = departmentChecklist.Id;
            question2.Question = "Test Question 2";
            question2.Active = true;
            question2 = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question2, skipSecurity: true);
            Assert.That(question2.Order, Is.EqualTo(2), "Second question should be assigned order 2");

            // Update the first question
            question.Question = "Updated Test Question";
            question = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question, skipSecurity: true);

            // Assert
            Assert.That(question.Order, Is.EqualTo(1), "Updated question should maintain its original order");
        }

        [Test]
        public async Task Save_WithNoActiveQuestions_AssignsOrderOne()
        {
            // Arrange
            var departmentChecklist = await CreateTestDepartmentAndChecklistAsync();
            Assert.That(departmentChecklist, Is.Not.Null, "Test data setup failed: DepartmentChecklist creation failed.");

            var question = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question.Id = Guid.NewGuid();
            question.SiteChecklistId = departmentChecklist.Id;
            question.Question = "Test Question";
            question.Active = true;

            // Act
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question, skipSecurity: true);

            // Assert
            Assert.That(question.Order, Is.EqualTo(1), "First question should be assigned order 1");
        }

        [Test]
        public async Task Save_WithSequentialOrders_AssignsNextNumber()
        {
            // Arrange
            var departmentChecklist = await CreateTestDepartmentAndChecklistAsync();
            Assert.That(departmentChecklist, Is.Not.Null, "Test data setup failed: DepartmentChecklist creation failed.");

            // Create questions with sequential orders
            for (int i = 1; i <= 3; i++)
            {
                var question = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                question.Id = Guid.NewGuid();
                question.SiteChecklistId = departmentChecklist.Id;
                question.Question = $"Test Question {i}";
                question.Active = true;
                question = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question, skipSecurity: true);
                Assert.That(question.Order, Is.EqualTo(i), $"Question {i} should be assigned order {i}");
            }

            // Create a new question
            var newQuestion = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            newQuestion.Id = Guid.NewGuid();
            newQuestion.SiteChecklistId = departmentChecklist.Id;
            newQuestion.Question = "New Test Question";
            newQuestion.Active = true;

            // Act
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(newQuestion, skipSecurity: true);

            // Assert
            Assert.That(newQuestion.Order, Is.EqualTo(4), "New question should be assigned order 4");
        }

        [Test]
        public async Task Save_WithGapsInOrders_AssignsMinimumAvailable()
        {
            // Arrange
            var departmentChecklist = await CreateTestDepartmentAndChecklistAsync();
            Assert.That(departmentChecklist, Is.Not.Null, "Test data setup failed: DepartmentChecklist creation failed.");

            // Create questions with gaps in order numbers
            var question1 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question1.Id = Guid.NewGuid();
            question1.SiteChecklistId = departmentChecklist.Id;
            question1.Question = "Test Question 1";
            question1.Active = true;
            question1 = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question1, skipSecurity: true);

            var question2 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question2.Id = Guid.NewGuid();
            question2.SiteChecklistId = departmentChecklist.Id;
            question2.Question = "Test Question 2";
            question2.Active = true;
            question2 = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question2, skipSecurity: true);

            // Deactivate question 1 to create a gap
            question1.Active = false;
            question1 = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question1, skipSecurity: true);

            // Create a new question
            var newQuestion = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            newQuestion.Id = Guid.NewGuid();
            newQuestion.SiteChecklistId = departmentChecklist.Id;
            newQuestion.Question = "New Test Question";
            newQuestion.Active = true;

            // Act
            newQuestion = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(newQuestion, skipSecurity: true);

            // Assert
            Assert.That(newQuestion.Order, Is.EqualTo(1), "New question should be assigned order 1 (minimum available)");
        }

        [Test]
        public async Task Save_WithInactiveQuestions_IgnoresInactive()
        {
            // Arrange
            var departmentChecklist = await CreateTestDepartmentAndChecklistAsync();
            Assert.That(departmentChecklist, Is.Not.Null, "Test data setup failed: DepartmentChecklist creation failed.");

            // Create an inactive question
            var inactiveQuestion = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            inactiveQuestion.Id = Guid.NewGuid();
            inactiveQuestion.SiteChecklistId = departmentChecklist.Id;
            inactiveQuestion.Question = "Inactive Question";
            inactiveQuestion.Active = false;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(inactiveQuestion, skipSecurity: true);

            // Create a new active question
            var newQuestion = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            newQuestion.Id = Guid.NewGuid();
            newQuestion.SiteChecklistId = departmentChecklist.Id;
            newQuestion.Question = "New Test Question";
            newQuestion.Active = true;

            // Act
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(newQuestion, skipSecurity: true);

            // Assert
            Assert.That(newQuestion.Order, Is.EqualTo(1), "New question should be assigned order 1 (ignoring inactive question)");
        }

        private async Task CreateTestDataAsync()
        {
            // This method is now empty as we create test data in each test
        }
    }
}