<!--
// This is Custom Code - Department Master Access Filter
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter "Person to department vehicle master access view Filter" Internal name : "PersonToDepartmentVehicleMasterAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToDepartmentVehicleMasterAccessViewFilter"
        data-test-id="1730190a-3719-4d87-bbf3-a2595141c89c">
        <form
            data-bind="submit: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.commands.searchCommand">
            <div class="uiSearchContainer" style="margin-top: 8px;">
                <div class="filterFieldSetContent">
                    <div class="row g-2 align-items-end">
                        <!-- Department Name Field -->
                        <div class="col-auto"
                            data-bind="visible: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.statusData.isDepartmentNameVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToDepartmentVehicleMasterAccessView/filters/PersonToDepartmentVehicleMasterAccessViewFilter:filterFields.DepartmentName.displayName'">Department</span>
                                </label>
                                <input type="text" class="form-control form-control-sm"
                                    style="min-width: 150px; padding-left: 12px;"
                                    data-bind="value: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.filterData.fields.DepartmentName, enable: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="3a3b3aae-379f-4852-874b-3f0b214f2300" />
                            </div>
                        </div>

                        <!-- Has Access Field -->
                        <div class="col-auto"
                            data-bind="visible: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.statusData.isHasAccessVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToDepartmentVehicleMasterAccessView/filters/PersonToDepartmentVehicleMasterAccessViewFilter:filterFields.HasAccess.displayName'">Has
                                        access</span>
                                </label>
                                <select class="form-control form-control-sm"
                                    style="min-width: 120px; padding-left: 12px;"
                                    data-bind="value: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.HasAccessValues, enable: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"></select>
                            </div>
                        </div>

                        <!-- Search Buttons -->
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="submit" class="btn btn-primary btn-sm"
                                    data-bind="click: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.commands.searchCommand, i18n: 'buttons.search', enable: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="searchCommand">SEARCH</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    data-bind="click: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.PersonToDepartmentVehicleNormalAccessViewFilterViewModel.commands.clearCommand, i18n: 'buttons.clear', enable: SupervisorVehicleAccessFormFormViewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="clearCommand">CLEAR</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter "Person to department vehicle master access view Filter" Internal name : "PersonToDepartmentVehicleMasterAccessViewFilter"-->