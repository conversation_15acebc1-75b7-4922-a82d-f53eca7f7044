using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class CustomerDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"CustomerDataProviderExtensionTest-{Guid.NewGuid()}";
        private CustomerDataProviderExtension _customerDataProviderExtension;
        private Mock<IDataProviderExtensionProvider> _mockDataProvider;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add necessary services for testing
            services.AddTransient<CustomerDataProviderExtension>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            _customerDataProviderExtension = _serviceProvider.GetRequiredService<CustomerDataProviderExtension>();
            _mockDataProvider = new Mock<IDataProviderExtensionProvider>();
            _customerDataProviderExtension.Init(_mockDataProvider.Object);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            // Create test region (required for dealer)
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create test dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test Dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Create test customer FIRST
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Create test timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            // Create test site AFTER customer
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;  // Now customer exists
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // Create test department AFTER site and customer
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;  // Now site exists
            department.CustomerId = customer.Id;  // Now customer exists
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);

            // Create test module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID1";
            module.FSSSBase = 100000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000001" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

            // Create test vehicle AFTER all dependencies
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = customer.Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle";
            vehicle.SerialNo = "Test Serial No";
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
        }

        [Test]
        public async Task DataProvider_OnAfterSave_UpdatesUserPreferredLocale_WhenCustomerLocaleChanges()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            customer.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Get the department for the person
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).First();

            // Create a person with GO user
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.DepartmentId = department.Id; // Required field
            person.SiteId = department.SiteId; // Add SiteId from department
            person.FirstName = "Test"; // Required field
            person.LastName = "User"; // Required field
            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedKingdom; // Different from customer
            goUser.EmailAddress = "<EMAIL>"; // Unique email
            goUser.Password = "TestPassword123!"; // Required field
            goUser.UserName = "testuser1"; // Unique username
            goUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            person.GOUserId = goUser.Id;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Act - Change customer's preferred locale
            customer.PreferredLocale = LocaleEnum.EnglishUnitedKingdom;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Assert
            var updatedGoUser = await _dataFacade.GOUserDataProvider.GetAsync(goUser);
            Assert.That(updatedGoUser.PreferredLocale, Is.EqualTo(LocaleEnum.EnglishUnitedKingdom), "GO user's preferred locale should be updated to match customer's new locale");
            Assert.That(updatedGoUser.PreferredLocaleString, Is.EqualTo("en-GB"), "GO user's preferred locale string should be updated to match customer's new locale");
        }

        [Test]
        public async Task DataProvider_OnAfterSave_DoesNotUpdateUserPreferredLocale_WhenCustomerLocaleUnchanged()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            customer.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Get the department for the person
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).First();

            // Create a person with GO user
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.DepartmentId = department.Id; // Required field
            person.SiteId = department.SiteId; // Add SiteId from department
            person.FirstName = "Test"; // Required field
            person.LastName = "User"; // Required field
            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates; // Same as customer
            goUser.EmailAddress = "<EMAIL>"; // Unique email
            goUser.Password = "TestPassword123!"; // Required field
            goUser.UserName = "testuser2"; // Unique username
            goUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            person.GOUserId = goUser.Id;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Act - Update customer without changing locale
            customer.CompanyName = "Updated Customer Name";
            await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Assert
            var updatedGoUser = await _dataFacade.GOUserDataProvider.GetAsync(goUser);
            Assert.That(updatedGoUser.PreferredLocale, Is.EqualTo(LocaleEnum.EnglishUnitedStates), "GO user's preferred locale should remain unchanged");
            Assert.That(updatedGoUser.PreferredLocaleString, Is.EqualTo("en-US"), "GO user's preferred locale string should remain unchanged");
        }

        [Test]
        public async Task DataProvider_OnAfterSave_ClearsOriginalPreferredLocale()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            customer.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Get the department for the person
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).First();

            // Create a person with GO user
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.DepartmentId = department.Id; // Required field
            person.SiteId = department.SiteId; // Add SiteId from department
            person.FirstName = "Test"; // Required field
            person.LastName = "User"; // Required field
            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.EmailAddress = "<EMAIL>"; // Unique email
            goUser.Password = "TestPassword123!"; // Required field
            goUser.UserName = "testuser3"; // Unique username
            goUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            person.GOUserId = goUser.Id;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Act - Change customer's preferred locale
            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer); // Get fresh instance
            customer.PreferredLocale = LocaleEnum.EnglishUnitedKingdom;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Act - Make another change to customer
            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer); // Get fresh instance
            customer.CompanyName = "Updated Customer Name";
            await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Assert - The second save should not trigger locale updates since the locale didn't change
            var customerPersons = await _dataFacade.PersonDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customer.Id });
            foreach (var customerPerson in customerPersons)
            {
                if (customerPerson.GOUserId.HasValue)
                {
                    var customerGoUser = await _dataFacade.GOUserDataProvider.GetAsync(new GOUserDataObject(customerPerson.GOUserId.Value));
                    Assert.That(customerGoUser.PreferredLocale, Is.EqualTo(LocaleEnum.EnglishUnitedKingdom), "GO user's preferred locale should remain as EnglishUnitedKingdom");
                    Assert.That(customerGoUser.PreferredLocaleString, Is.EqualTo("en-GB"), "GO user's preferred locale string should remain as en-GB");
                }
            }
        }

        [Test]
        public async Task DataProvider_OnBeforeGetCollection_FiltersDeletedCustomers()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            
            // Create a new customer that will be marked as deleted
            var deletedCustomer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            deletedCustomer.Id = Guid.NewGuid();
            deletedCustomer.CompanyName = "Deleted Customer";
            deletedCustomer.CountryId = customer.CountryId; // Use existing country
            deletedCustomer.DealerId = customer.DealerId; // Use existing dealer
            deletedCustomer.Active = true;
            deletedCustomer.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.CustomerDataProvider.SaveAsync(deletedCustomer, skipSecurity: true);

            // Act
            // Test with no filter
            var customersNoFilter = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null);
            
            // Test with existing filter
            var customersWithFilter = await _dataFacade.CustomerDataProvider.GetCollectionAsync(
                null, 
                "Active == true"
            );

            // Assert
            Assert.Multiple(() =>
            {
                // Check that deleted customer is not in results when no filter
                Assert.That(customersNoFilter.Any(c => c.Id == deletedCustomer.Id), Is.False, 
                    "Deleted customer should not appear in unfiltered results");

                // Check that deleted customer is not in results with existing filter
                Assert.That(customersWithFilter.Any(c => c.Id == deletedCustomer.Id), Is.False, 
                    "Deleted customer should not appear in filtered results");

                // Check that non-deleted customer is still present
                Assert.That(customersNoFilter.Any(c => c.Id == customer.Id), Is.True, 
                    "Non-deleted customer should appear in results");
                Assert.That(customersWithFilter.Any(c => c.Id == customer.Id), Is.True, 
                    "Non-deleted customer should appear in filtered results");
            });
        }
    }
}