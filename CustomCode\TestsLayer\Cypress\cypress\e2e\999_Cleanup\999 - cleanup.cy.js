describe("000 - Cleanup", () => {
    let tempCompanyName, cypressFirstName, cypressLastName;

    before(() => {
        // Load test data from fixture
        cy.fixture('testData').then((testData) => {
            tempCompanyName = testData.tempCompanyName;
            cypressFirstName = testData.cypressFirstName;
            cypressLastName = testData.cypressLastName;
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login();

        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company else if not found continue and create
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(tempCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // Customer data found, select the customer
        cy.get('td[data-bind="jqStopBubble: \'a\'"]')
            .should('exist')
            .should('be.visible')
            .first()  // Select the first matching element if multiple exist
            .click();

        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

    });

    it("Should delete cypress test department, site and customer", () => {
        // CLEAR THE ACCESS GROUP FIRST
        // make sure to clear the access groups first
        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-5"]')
            .should('exist')
            .should('be.visible')
            .click();

        // iterate through the items in grid container and click on each item
        cy.get('#-AccessGroupItemsGridViewModel-grid-widget-AccessGroupGrid- > .data-grid-container')
            .should('exist')
            .should('be.visible')
            .then($grid => {
                // First check if there are any rows to process
                const rows = $grid.find('tr.pointer');
                if (rows.length === 0) {
                    cy.log('No items found in the grid to process');
                    return;
                }

                // Create an array with reversed indices to process rows from bottom to top
                const indices = [];
                for (let i = rows.length - 1; i >= 0; i--) {
                    indices.push(i);
                }

                // Process each row one by one from bottom to top
                cy.wrap(indices).each((rowIndex) => {
                    cy.log(`Processing grid item ${rowIndex + 1} of ${rows.length} (bottom to top)`);


                    // Select the specific row using eq() to ensure we get the right one
                    cy.get('#-AccessGroupItemsGridViewModel-grid-widget-AccessGroupGrid- > .data-grid-container tr.pointer')
                        .eq(rowIndex)
                        .click({ force: true });

                    cy.wait(2000);

                    // OPEN THE ACCESS GROUP and remove the site from the access group
                    cy.get(':nth-child(2) > .command-button')
                        .should('exist')
                        .should('be.visible')
                        .click();

                    cy.wait(1000);

                    // REMOVE THE SITE FROM THE ACCESS GROUP
                    cy.get('#popupContainer0-AccessGroupsToSitesGridViewModel-grid-widget-AccessGroupToSiteGrid- > .data-grid-container > .data-grid > tbody > .pointer > .multiline')
                        .should('exist')
                        .should('be.visible')
                        .click();

                    cy.wait(1000);

                    // DELETE THE SITE FROM THE ACCESS GROUP
                    cy.get('#AccessGroupForm-AccessGroupsToSitesGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(2) > .command-button')
                        .should('exist')
                        .should('be.visible')
                        .click();

                    cy.wait(1000);

                    // CONFIRM THE DELETE
                    cy.get('[data-bind="click: clickOk, hasfocus: true, text: FleetXQ.Web.Messages.okLabel"]')
                        .should('exist')
                        .should('be.visible')
                        .click();

                    cy.wait(1000);

                    // CLOSE THE SITE ACCESS GROUP
                    cy.get('.overlayClose > .bi')
                        .should('exist')
                        .should('be.visible')
                        .click();

                    cy.wait(1000);

                    // DELETE THE ACCESS GROUP
                    cy.get(':nth-child(4) > .command-button')
                        .should('exist')
                        .should('be.visible')
                        .click();
                    cy.wait(1000);
                    cy.get('[data-bind="click: clickOk, hasfocus: true, text: FleetXQ.Web.Messages.okLabel"]')
                        .should('exist')
                        .should('be.visible')
                        .click();

                    cy.wait(1000);
                });
            });


        // DELETE THE SITE
        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-2"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // CLICK THE SITE TO DELETE
        cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
            .should('exist')
            .should('be.visible')
            .first()
            .click();
        cy.wait(1000);

        // CLEAN UP THE DEPARTMENT if it exists
        // check if the department exists
        cy.get('body').then($body => {
            // cy.get('#SiteForm1-SiteFormForm-DepartmentItemsGrid > [data-bind="css: { hideElt : false }"] > :nth-child(3) > .no-data-message > span')
            if ($body.find('.no-data-message > span').length > 0) {
                cy.log('Department not found, skipping department deletion');
            } else {
                // Click on the department to edit
                cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // CLICK THE DELETE DEPARTMENT BUTTON
                cy.get('#SiteForm1-SiteFormForm-DepartmentItemsGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(3) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // CONFIRM THE DELETE
                cy.get('[data-bind="click: clickOk, hasfocus: true, text: FleetXQ.Web.Messages.okLabel"]')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);
            }
        });

        // CLICK THE DELETE SITE BUTTON
        cy.get(':nth-child(3) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        cy.get('[data-bind="click: clickOk, hasfocus: true, text: FleetXQ.Web.Messages.okLabel"]')
            .should('exist')
            .should('be.visible')
            .click();


        // DELETE THE CUSTOMER
        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-1"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(300);

        // Delete the customer
        cy.get('.delete')
            .should('exist')
            .should('be.visible')
            .click();

        // Confirm the deletion
        cy.get('[data-bind="click: clickOk, hasfocus: true, text: FleetXQ.Web.Messages.okLabel"]')
            .should('exist')
            .should('be.visible')
            .click();
    });

});
