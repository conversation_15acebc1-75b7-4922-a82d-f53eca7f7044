import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock the dependencies
global.ko = {
    observable: vi.fn((x) => {
        const obs = function (newValue) {
            if (arguments.length > 0) {
                obs._value = newValue;
                return;
            }
            return obs._value;
        };
        obs._value = x;
        return obs;
    }),
    observableArray: vi.fn((arr = []) => {
        const obsArray = function () {
            return obsArray._array;
        };
        obsArray._array = [...arr];
        obsArray.push = vi.fn((item) => { obsArray._array.push(item); });
        obsArray.remove = vi.fn((itemOrFn) => {
            if (typeof itemOrFn === 'function') {
                obsArray._array = obsArray._array.filter((item) => !itemOrFn(item));
            } else {
                obsArray._array = obsArray._array.filter((item) => item !== itemOrFn);
            }
        });
        obsArray.removeAll = vi.fn(() => { obsArray._array = []; });
        obsArray.subscribe = vi.fn((callback) => {
            callback(obsArray._array);
            return { dispose: vi.fn() };
        });
        return obsArray;
    }),
    pureComputed: vi.fn()
};

// Mock FleetXQ
global.FleetXQ = {
    Web: {
        ViewModels: {
            CustomerModelGrid1ViewModelCustom: null
        }
    }
};

// Import the file to test - using require instead of import
// This ensures our mocks are set up before the file is loaded
vi.mock('../../../WebApplicationLayer/wwwroot/ViewModels/CustomerModel/CustomerModelGrid1ViewModel.custom.js', () => {
    return {};
});
require('../../../WebApplicationLayer/wwwroot/ViewModels/CustomerModel/CustomerModelGrid1ViewModel.custom.js');

describe('CustomerModelGrid1ViewModel', () => {
    let viewmodel;
    let customViewModel;

    beforeEach(() => {
        // Create a mock viewmodel with required properties
        viewmodel = {
            CustomerModelObjectCollection: global.ko.observableArray(),
            checkedStates: global.ko.observableArray(),
            selectedModelIds: global.ko.observableArray(),
            toggleChecked: vi.fn(),
            isModelAlreadyApplied: vi.fn(() => false), // Default implementation
            modelHasNoVehicles: vi.fn(() => false) // Default implementation
        };

        // Initialize custom view model
        customViewModel = new FleetXQ.Web.ViewModels.CustomerModelGrid1ViewModelCustom(viewmodel);

        // Set up test data
        const mockModelData = [
            { Data: { ModelId: vi.fn().mockReturnValue('model1'), Name: vi.fn().mockReturnValue('Model 1') } },
            { Data: { ModelId: vi.fn().mockReturnValue('model2'), Name: vi.fn().mockReturnValue('Model 2') } },
            { Data: { ModelId: vi.fn().mockReturnValue('model3'), Name: vi.fn().mockReturnValue('Model 3') } },
            { Data: { ModelId: vi.fn().mockReturnValue('model4'), Name: vi.fn().mockReturnValue('Model 4') } }
        ];

        viewmodel.CustomerModelObjectCollection = global.ko.observableArray(mockModelData);

        // Initialize checkedStates with all unchecked
        viewmodel.updateCheckStates();
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    it('should have selectAll and deselectAll functions defined', () => {
        expect(typeof viewmodel.selectAll).toBe('function');
        expect(typeof viewmodel.deselectAll).toBe('function');
    });

    it('should call toggleChecked for all visible unchecked items in selectAll', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Simulate model4 having no vehicles (filtered out)
        viewmodel.modelHasNoVehicles = vi.fn((modelId) => modelId === 'model4');

        // Simulate some items are already checked
        viewmodel.checkedStates()[1]._value = true;

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was called twice (for model1 and model3 that were unchecked and visible)
        expect(toggleCheckedSpy).toHaveBeenCalledTimes(2);
        expect(toggleCheckedSpy).toHaveBeenCalledWith(0, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(2, expect.any(Object));
    });

    it('should call toggleChecked for all visible checked items in deselectAll', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Simulate model4 having no vehicles (filtered out)
        viewmodel.modelHasNoVehicles = vi.fn((modelId) => modelId === 'model4');

        // Simulate some items are checked
        viewmodel.checkedStates()[0]._value = true;
        viewmodel.checkedStates()[2]._value = true;

        // Run deselectAll function
        viewmodel.deselectAll();

        // Verify toggleChecked was called twice (for model1 and model3 that were checked and visible)
        expect(toggleCheckedSpy).toHaveBeenCalledTimes(2);
        expect(toggleCheckedSpy).toHaveBeenCalledWith(0, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(2, expect.any(Object));
    });

    it('should not toggle already applied models in selectAll', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Simulate model1 is already applied
        viewmodel.isModelAlreadyApplied = vi.fn((modelId) => modelId === 'model1');

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was called 3 times (all models except model1)
        expect(toggleCheckedSpy).toHaveBeenCalledTimes(3);
        expect(toggleCheckedSpy).not.toHaveBeenCalledWith(0, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(1, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(2, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(3, expect.any(Object));
    });

    it('should not call toggleChecked if all eligible models are in correct state', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Setup for selectAll: all models checked or already applied
        viewmodel.isModelAlreadyApplied = vi.fn((modelId) => modelId === 'model1');
        viewmodel.checkedStates()[1]._value = true;
        viewmodel.checkedStates()[2]._value = true;
        viewmodel.checkedStates()[3]._value = true;

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was not called
        expect(toggleCheckedSpy).not.toHaveBeenCalled();

        // Reset all states and the spy
        viewmodel.checkedStates()[0]._value = false;
        viewmodel.checkedStates()[1]._value = false;
        viewmodel.checkedStates()[2]._value = false;
        viewmodel.checkedStates()[3]._value = false;
        toggleCheckedSpy.mockClear();

        // For deselectAll with nothing checked
        viewmodel.deselectAll();

        // Verify toggleChecked was not called
        expect(toggleCheckedSpy).not.toHaveBeenCalled();
    });

    it('should pass an object with stopPropagation to toggleChecked', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was called with an object that has stopPropagation
        const eventArg = toggleCheckedSpy.mock.calls[0][1];
        expect(typeof eventArg.stopPropagation).toBe('function');

        // Reset spy
        toggleCheckedSpy.mockClear();

        // Setup for deselectAll
        viewmodel.checkedStates()[0]._value = true;

        // Run deselectAll function
        viewmodel.deselectAll();

        // Verify toggleChecked was called with an object that has stopPropagation
        const eventArg2 = toggleCheckedSpy.mock.calls[0][1];
        expect(typeof eventArg2.stopPropagation).toBe('function');
    });
});
