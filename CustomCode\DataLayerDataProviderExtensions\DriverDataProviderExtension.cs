﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Azure.Devices;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    /// <summary>
    /// Extension provider for handling driver-related data operations including card access management,
    /// permission assignments, and IoT device synchronization.
    /// </summary>
    /// <remarks>
    /// This extension handles:
    /// - Driver creation and updates
    /// - Card access management for drivers
    /// - Permission assignments for normal drivers and supervisors
    /// - IoT device synchronization for vehicles
    /// - Access level management for drivers and licenses
    /// </remarks>
    public class DriverDataProviderExtension : IDataProviderExtension<DriverDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IDeviceTwinHandler _deviceTwinHandler;
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthentication _authentication;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILoggingService _logger;

        /// <summary>
        /// Initializes a new instance of the DriverDataProviderExtension class.
        /// </summary>
        /// <param name="dataFacade">Data facade for accessing various data providers</param>
        /// <param name="deviceMessageHandler">Handler for device messages</param>
        /// <param name="deviceTwinHandler">Handler for device twin operations</param>
        /// <param name="serviceProvider">Service provider for dependency injection</param>
        /// <param name="authentication">Authentication service for user context</param>
        /// <param name="serviceScopeFactory">Service scope factory for creating scoped services</param>
        /// <param name="logger">Logging service for performance and error tracking</param>
        public DriverDataProviderExtension(IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler,
            IDeviceTwinHandler deviceTwinHandler, IServiceProvider serviceProvider, IAuthentication authentication,
            IServiceScopeFactory serviceScopeFactory, ILoggingService logger)
        {
            _dataFacade = dataFacade;
            _deviceMessageHandler = deviceMessageHandler;
            _deviceTwinHandler = deviceTwinHandler;
            _serviceProvider = serviceProvider;
            _authentication = authentication;
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        /// <summary>
        /// Initializes the extension by registering event handlers for data operations.
        /// </summary>
        /// <param name="dataProvider">The data provider extension provider</param>
        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSetAsync;
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSetAsync;
            dataProvider.OnBeforeDelete += DataProvider_OnBeforeDelete;
        }

        private Task DataProvider_OnBeforeDelete(OnBeforeDeleteEventArgs arg)
        {
            if (arg.DeleteRoot is DepartmentDataObject)
            {
                throw new GOServerException("Cannot delete department: Department contains drivers.");
            }

            return Task.CompletedTask;
        }

        /// <summary>
        /// Handles operations before saving driver data, particularly managing license-related access levels.
        /// </summary>
        /// <param name="e">Event arguments containing the entity being saved</param>
        /// <remarks>
        /// This method specifically handles the case when a general license is being removed from a driver,
        /// updating the person's access level accordingly.
        /// </remarks>
        private async Task OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
        {
            if (e.Entity.IsNew)
            {
                return;
            }

            var driver = e.Entity as DriverDataObject;

            // Check the case when we delete a General License
            var existingDriver = await _dataFacade.DriverDataProvider.GetAsync(driver, skipSecurity: true);
            if (existingDriver.LicenceDetailId != null && driver.LicenceDetailId == null)
            {
                // we are deleting the general license => update access level
                var person = await driver.LoadPersonAsync(skipSecurity: true);

                if (person.AccessLevel != null && person.AccessLevel.Contains(PersonAccessLevelEnum.License))
                {
                    person.AccessLevel.Remove(PersonAccessLevelEnum.License);
                    await _dataFacade.PersonDataProvider.SaveAsync(person.Clone(recursive: false) as PersonDataObject, skipSecurity: true);
                }
            }
        }

        /// <summary>
        /// Handles operations after saving driver data, including card access creation and driver access level management.
        /// </summary>
        /// <param name="e">Event arguments containing the saved entity information</param>
        /// <remarks>
        /// This method handles two main scenarios:
        /// 1. Creating card access when a new card is associated with a driver
        /// 2. Setting up initial access levels for newly created drivers
        /// </remarks>
        private async Task OnAfterSaveDataSetAsync(OnAfterSaveDataSetEventArgs e)
        {
            var driver = e.EntityBeforeSave as DriverDataObject;
            if (driver != null)
            {
                var cardIsNew = driver.Card?.IsNew ?? false;

                // Handle creating access when driver gets associated with a card
                if (cardIsNew)
                {
                    var newCardRelationship = true;

                    if (newCardRelationship)
                    {
                        try
                        {
                            var person = e.EntityRefetched as PersonDataObject;
                            DriverDataObject savedDriver = driver;

                            if (person != null)
                            {
                                savedDriver = await person.LoadDriverAsync();
                            }
                            await CreateCardAccessAsync(savedDriver);
                        }
                        catch (Exception ex)
                        {
                            throw new GOServerException($"Error creating card access for driver {driver.Id}: {ex.Message}", ex.Message);
                        }
                    }
                }
            }

            // Original driver creation logic for new drivers
            if (e.EntityBeforeSave.IsNew)
            {
                var newDriver = e.EntityRefetched as DriverDataObject;
                // get the person data of the driver, and add "Driver" enum to Person.AccessLevel enum field and save person data after
                var newPerson = await newDriver.LoadPersonAsync(skipSecurity: true);

                if (newPerson == null) return;

                // check if person.AccessLevel is null and instantiate a new list of PersonAccessLevelEnum for it if it is null
                if (newPerson.AccessLevel == null)
                {
                    newPerson.AccessLevel = new List<PersonAccessLevelEnum>();
                }
                newPerson.AccessLevel.Add(PersonAccessLevelEnum.Driver);
                await _dataFacade.PersonDataProvider.SaveAsync(newPerson, skipSecurity: true);
            }
        }

        /// <summary>
        /// Creates card access permissions for a driver when a card is associated with them.
        /// </summary>
        /// <param name="driver">The driver object for which to create card access</param>
        /// <returns>A task representing the asynchronous operation</returns>
        /// <exception cref="GOServerException">
        /// Thrown when:
        /// - The associated card is not found
        /// - The person record is not found
        /// - Required permissions are not found in the system
        /// </exception>
        private async Task CreateCardAccessAsync(DriverDataObject driver)
        {
            // Load the card
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = driver.CardDetailsId ?? Guid.Empty;
            card = await _dataFacade.CardDataProvider.GetAsync(card, skipSecurity: true);
            if (card == null)
            {
                throw new GOServerException($"Cannot create card access: Card not found for ID {driver.CardDetailsId}");
            }

            // Load the person
            var person = await driver.LoadPersonAsync(skipSecurity: true);
            if (person == null)
            {
                throw new GOServerException($"Cannot create card access: Person not found for driver ID {driver.Id}");
            }

            // Get permission for normal driver
            var normalDriverPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                "LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver })).FirstOrDefault();
            if (normalDriverPermission == null)
            {
                throw new GOServerException("Cannot create card access: Normal Driver permission not found in the system");
            }

            // Get permission for master (supervisor)
            var masterPermission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null,
                "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master })).FirstOrDefault();
            if (masterPermission == null)
            {
                throw new GOServerException("Cannot create card access: Master permission not found in the system");
            }

            // Create card access items
            var iotDevicesToSync = await CreateCardAccessForPersonAsync(card, person, normalDriverPermission, masterPermission);

            // Get current user ID for authorization
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            // Sync driver to vehicles
            _ = Task.Run(async () =>
            {
                var syncStart = DateTime.UtcNow;
                try
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                    if (currentUserId == null)
                    {
                        _logger?.LogInformation($"[DriverDataProviderExtension] CreateCardAccessAsync: currentUserId is null, skipping sync");
                        return;
                    }

                    foreach (var iotDevice in iotDevicesToSync)
                    {
                        try
                        {
                            await scopedDeviceTwinHandler.SyncDriverToVehicle(iotDevice, currentUserId.Value);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, $"Failed to sync device {iotDevice}: {ex.Message}");
                        }
                    }

                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogInformation($"[PERF] DriverDataProviderExtension card access sync completed in {syncDuration}ms for {iotDevicesToSync.Count} devices");
                }
                catch (Exception ex)
                {
                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogError(ex, $"[PERF] DriverDataProviderExtension card access sync failed after {syncDuration}ms: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// Creates comprehensive card access items for a person based on their site, department, and vehicle assignments.
        /// </summary>
        /// <param name="card">The card object to associate with access permissions</param>
        /// <param name="person">The person object for whom to create access</param>
        /// <param name="normalDriverPermission">The normal driver permission object</param>
        /// <param name="masterPermission">The master (supervisor) permission object</param>
        /// <returns>
        /// A HashSet of IoT device identifiers that need to be synchronized after access creation
        /// </returns>
        /// <remarks>
        /// This method creates multiple levels of access:
        /// - Site-level access
        /// - Department-level access
        /// - Vehicle model-level access
        /// - Individual vehicle access
        /// For supervisors, additional elevated permissions are created at each level.
        /// </remarks>
        private async Task<HashSet<string>> CreateCardAccessForPersonAsync(
            CardDataObject card,
            PersonDataObject person,
            PermissionDataObject normalDriverPermission,
            PermissionDataObject masterPermission)
        {
            // Collection to track IoT devices that need syncing
            HashSet<string> iotDevicesToSync = new HashSet<string>();

            // Check if person has a site
            if (person.SiteId != null)
            {
                // Create SiteVehicleNormalCardAccessItem
                var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                siteAccess.CardId = card.Id;
                siteAccess.SiteId = person.SiteId;
                siteAccess.PermissionId = normalDriverPermission.Id;
                await _dataFacade.SiteVehicleNormalCardAccessDataProvider.SaveAsync(siteAccess, skipSecurity: true);

                // If person is a supervisor, create supervisor access too
                if (person.Supervisor == true)
                {
                    var siteSupervisorAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                    siteSupervisorAccess.CardId = card.Id;
                    siteSupervisorAccess.SiteId = person.SiteId;
                    siteSupervisorAccess.PermissionId = masterPermission.Id;
                    await _dataFacade.SiteVehicleNormalCardAccessDataProvider.SaveAsync(siteSupervisorAccess, skipSecurity: true);
                }
            }

            // Check if person has a department
            if (person.DepartmentId != null)
            {
                // Create DepartmentVehicleNormalCardAccessItem
                var departmentAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                departmentAccess.CardId = card.Id;
                departmentAccess.DepartmentId = person.DepartmentId;
                departmentAccess.PermissionId = normalDriverPermission.Id;
                await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.SaveAsync(departmentAccess, skipSecurity: true);

                // If person is a supervisor, create supervisor access too
                if (person.Supervisor == true)
                {
                    var departmentSupervisorAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                    departmentSupervisorAccess.CardId = card.Id;
                    departmentSupervisorAccess.DepartmentId = person.DepartmentId;
                    departmentSupervisorAccess.PermissionId = masterPermission.Id;
                    await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.SaveAsync(departmentSupervisorAccess, skipSecurity: true);
                }

                // Get all models of vehicles for the department
                var department = await _dataFacade.DepartmentDataProvider.GetAsync(
                    _serviceProvider.GetRequiredService<DepartmentDataObject>().Initialize(person.DepartmentId), skipSecurity: true);

                if (department != null)
                {
                    var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null,
                        "DepartmentId == @0", new object[] { department.Id }, skipSecurity: true);

                    // Group vehicles by model and create ModelVehicleNormalCardAccessItems
                    var modelIds = vehicles.Where(v => v.ModelId != null)
                        .Select(v => v.ModelId)
                        .Distinct();

                    foreach (var modelId in modelIds)
                    {
                        // Create ModelVehicleNormalCardAccessItem
                        var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                        modelAccess.CardId = card.Id;
                        modelAccess.ModelId = modelId;
                        modelAccess.PermissionId = normalDriverPermission.Id;
                        modelAccess.DepartmentId = person.DepartmentId;
                        await _dataFacade.ModelVehicleNormalCardAccessDataProvider.SaveAsync(modelAccess, skipSecurity: true);

                        // If person is a supervisor, create supervisor access too
                        if (person.Supervisor == true)
                        {
                            var modelSupervisorAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                            modelSupervisorAccess.CardId = card.Id;
                            modelSupervisorAccess.ModelId = modelId;
                            modelSupervisorAccess.PermissionId = masterPermission.Id;
                            modelSupervisorAccess.DepartmentId = person.DepartmentId;
                            await _dataFacade.ModelVehicleNormalCardAccessDataProvider.SaveAsync(modelSupervisorAccess, skipSecurity: true);
                        }
                    }

                    // Create PerVehicleNormalCardAccessItems for each vehicle in the department
                    foreach (var vehicle in vehicles)
                    {
                        var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                        vehicleAccess.CardId = card.Id;
                        vehicleAccess.VehicleId = vehicle.Id;
                        vehicleAccess.PermissionId = normalDriverPermission.Id;
                        await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(vehicleAccess, skipSecurity: true);

                        // If person is a supervisor, create supervisor access too
                        if (person.Supervisor == true)
                        {
                            var vehicleSupervisorAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                            vehicleSupervisorAccess.CardId = card.Id;
                            vehicleSupervisorAccess.VehicleId = vehicle.Id;
                            vehicleSupervisorAccess.PermissionId = masterPermission.Id;
                            await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(vehicleSupervisorAccess, skipSecurity: true);
                        }

                        // Add to IoT devices list for syncing
                        var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                        if (module != null && !string.IsNullOrEmpty(module.IoTDevice))
                        {
                            iotDevicesToSync.Add(module.IoTDevice);
                        }
                    }
                }
            }
            return iotDevicesToSync;
        }
    }
}
