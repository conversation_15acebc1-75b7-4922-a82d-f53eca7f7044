describe("014 - More Settings-Tab", () => {
    // Use beforeEach to ensure login is handled before each test
    beforeEach(() => {
        cy.login(); // Assuming you have already implemented cy.login() globally
    });

    it("tests 014 - More Settings Tab", () => {
        // Access Vehicles
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

        // Search for a vehicle
        cy.get("input").first().type("Test");
        cy.get("[data-test-id='searchCommand']").click();
        cy.get("tr:nth-of-type(1) a").click();

        // Intercept the vehicle by ID API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/vehicle/byid/*')
            .as('getVehicleById');


        cy.wait('@getVehicleById', { timeout: 30000 });


        cy.get("[data-test-id='tab_link_61190748-4d15-456c-ae30-38e02fa45f7a'] > a > span:nth-of-type(1)").click();
        cy.get("[data-test-id='fa575ace-682c-4f85-be2c-8b13abf9f558']").click();
        cy.get("[data-test-id='edit_2a38a529-25ed-4e90-8134-35ff70fe936f']").click();
        cy.get("[data-test-id='edit_69a61117-2e7d-4c48-a049-e365018ed092']").click();
        cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']").click();
        cy.get("[data-test-id='\\31 ccaec90-3022-4ea8-84db-70944021fc32']").click();
        cy.get("[data-test-id='edit_c34dd2e9-2f53-4af7-a74a-21d45fffe1eb']").click();
        cy.get("[data-test-id='edit_c34dd2e9-2f53-4af7-a74a-21d45fffe1eb']").type("123");
        //cy.get("html > body > #form > [data-test-id='main-page-content'] > #popupContainer > #popupContainer0 > [data-test-id='\\30 684839b-be0f-49d0-956c-9296831b6e47'] > div > div:nth-of-type(1) > [data-test-id='\\30 684839b-be0f-49d0-956c-9296831b6e47'] > #NetworkSettingsFormData > div > div > [data-test-id='\\31 f02ccba-1d9f-4490-bff0-568573ab2eab'] div > div").click();
        cy.get("[data-test-id='edit_1f02ccba-1d9f-4490-bff0-568573ab2eab']").click();
        cy.get("[data-test-id='edit_1f02ccba-1d9f-4490-bff0-568573ab2eab']").type("123");
        cy.get("[data-test-id='a7b33310-f9c9-489a-aa2d-2154e57d271e']").click();
    });
});
