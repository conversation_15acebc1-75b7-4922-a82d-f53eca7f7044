﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class ModuleValidator : IModuleValidator
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IDataFacade _dataFacade;
        public ModuleValidator(IServiceProvider serviceProvider, IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }
        public async System.Threading.Tasks.Task<ModuleDataObject> EnforceModuleForVehicleAsync(String IotDeviceId)
        {
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { IotDeviceId })).SingleOrDefault();
            if (module == null)
            {
                module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                module.Id = Guid.NewGuid();
                module.IoTDevice = IotDeviceId;
                module.BlueImpact = 0;
                module.AmberImpact = 0;
                module.RedImpact = 0;
                module.FSSSBase = 0;
                module.FSSXMulti = 0;
                module = await _dataFacade.ModuleDataProvider.SaveAsync(module);
            }
            return module;
        }
    }
}
