﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

/**
 * The Custom Folder allow you to set Custom Code to extend your application.
 * Every Custom files you will create should have a name ending by : ".custom.js"
 * Custom Code for Routes should be set in  : 
 * /Custom/Application/Routes.custom.js
 *
 * Custom Code for SourceHandling should be set in :
 * /Custom/Application/SourceHandler.custom.js
 * This file will allow you to load specific files when accessing to a page.
 *
 * Custom Code for Custom Fields Providers should be set in : 
 * /Custom/Model/CustomFields/MyEntityCustomFields.custom.js
 * Note : A stub has been generated once you have create a Custom Field on you entity
 *
 * Custom Code for Entity Factories should be set in :
 * /Custom/Model/DataObjects/MyEntityObject.custom.js
 *
 * To extend the behavior of your pages. Put your code in : 
 * /Custom/Controllers/MyPage.custom.js
 *
 *
 * How to extend Behavior of Validators
 * In each DataObjectValidator, there is a call of the specific CustomValidator : FleetXQ.Web.Model.DataObjects.Validation.MyEntityValidatorCustom
 * In this function, you can define new functions:
 * bool validate(MyEntityDataObject dataobject)
 *	Return wether or not the validation should continue
 * void OnAfterValidate(MyEntityDataObject dataobject)
 *	You can modify the behavior after the validation.
 * void validateMyEntity(MyEntityDataObject dataobject)
 *	Generative Objects didn't provided enough tools to write your Custom Validation Rule ? Just put your Custom Validation here.
 *
 * 
 * To extend the behavior of you ViewModels. Put your code in : 
 * /Custom/ViewModels/(Feature/MyEntity)/(MyEntity/Feature)(Form|Grid|List)ViewModel.custom.js
 * You are free to chose the name of your field.
 * 
 * Rules to merge the content of your custom code can be founded in FleetXQ.Application.Web.csproj.
 * Involved Targets are : Merge_CustomFields / Merge_ViewModels / Merge_Controllers
 *
 **/
