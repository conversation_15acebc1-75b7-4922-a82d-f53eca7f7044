import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehicleFirmwaresViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {
                    Filters: {
                        VehicleFirmwareSettingsFilterViewModel: function () {
                            this.siteId = ko.observable(null);
                        }
                    }
                },
                Model: {
                    DataObjects: {
                        UpdateFirmwareRequestObjectFactory: {
                            createNew: vi.fn(() => ({
                                Data: { Id: vi.fn() },
                                popupParameter: {}
                            }))
                        }
                    }
                }
            }
        };

        // Mock ApplicationController
        global.ApplicationController = {
            showEditPopup: vi.fn()
        };

        // Mock console methods
        global.console.log = vi.fn();
        global.console.error = vi.fn();

        // Mock ko.isObservable
        global.ko.isObservable = vi.fn((obj) => {
            return obj && typeof obj === 'function' && obj.subscribe;
        });

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehicleFirmwaresViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            subscriptions: [],
            siteId: ko.observable(null),
            VehicleFirmwareSettingsFilterViewModel: new global.FleetXQ.Web.ViewModels.Filters.VehicleFirmwareSettingsFilterViewModel(),
            controller: {
                applicationController: {
                    showEditPopup: vi.fn(),
                    ObjectsDataSet: { AddObject: vi.fn(), setContextIdsDirty: vi.fn() },
                    getNextContextId: vi.fn(() => [1])
                },
                ObjectsDataSet: { AddObject: vi.fn(), setContextIdsDirty: vi.fn() }
            },
            UpdateFirmware: vi.fn(),
            contextId: [1]
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.VehicleFirmwaresViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    afterEach(() => {
        // Cleanup
        if (customViewModel && customViewModel.release) {
            customViewModel.release();
        }
    });

    describe('Initialization', () => {
        it('should initialize siteId observable', () => {
            expect(viewModel.siteId).toBeDefined();
            // Accept either ko.isObservable or function with subscribe (for robust test)
            const isKnockoutObservable = ko.isObservable(viewModel.siteId);
            const isFunctionWithSubscribe = typeof viewModel.siteId === 'function' && typeof viewModel.siteId.subscribe === 'function';
            expect(isKnockoutObservable || isFunctionWithSubscribe).toBe(true);
        });

        it('should override the UpdateFirmware method', () => {
            expect(viewModel.UpdateFirmware).toBeDefined();
            expect(typeof viewModel.UpdateFirmware).toBe('function');
        });

        it('should set base filter to exclude deleted records', () => {
            // Verify that the base filter is set to exclude deleted records
            expect(viewModel.baseFilterPredicate).toBe('DeletedAtUtc == null');
            expect(viewModel.baseFilterParameters).toBeNull();
            expect(viewModel.baseFilterParametersCount).toBe(0);
        });

        it('should have IsShowUpdateFirmwareVersionPopupCommandVisible function', () => {
            expect(viewModel.IsShowUpdateFirmwareVersionPopupCommandVisible).toBeDefined();
            expect(typeof viewModel.IsShowUpdateFirmwareVersionPopupCommandVisible).toBe('function');
        });
    });

    describe('UpdateFirmware method', () => {
        it('should call showEditPopup with correct parameters when siteId is available', () => {
            viewModel.siteId('site-123');

            viewModel.UpdateFirmware();

            expect(global.ApplicationController.showEditPopup).toHaveBeenCalledWith(
                "BulkUpdateFirmwareForm",
                viewModel,
                expect.any(Object),
                true,
                viewModel.contextId,
                '70%',
                true,
                expect.any(Object)
            );
        });

        it('should pass siteId in popupParameter when available', () => {
            viewModel.siteId('site-456');

            viewModel.UpdateFirmware();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const popupParameter = callArgs[7]; // The last argument is the popupParameter

            // Check that the popupParameter contains the siteId
            expect(popupParameter).toBeDefined();
            expect(popupParameter.siteId).toBe('site-456');
        });

        it('should handle null siteId gracefully', () => {
            viewModel.siteId(null);

            viewModel.UpdateFirmware();

            expect(global.ApplicationController.showEditPopup).toHaveBeenCalled();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const popupParameter = callArgs[7];

            // Should still create popupParameter but with null siteId
            expect(popupParameter).toBeDefined();
            expect(popupParameter.siteId).toBeNull();
        });

        it('should handle undefined siteId gracefully', () => {
            viewModel.siteId(undefined);

            viewModel.UpdateFirmware();

            expect(global.ApplicationController.showEditPopup).toHaveBeenCalled();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const popupParameter = callArgs[7];

            // Should still create popupParameter but with undefined siteId
            expect(popupParameter).toBeDefined();
            expect(popupParameter.siteId).toBeUndefined();
        });

        it('should create new UpdateFirmwareRequestObject', () => {
            viewModel.siteId('site-789');

            viewModel.UpdateFirmware();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const newObject = callArgs[2];

            // Should create a new object with proper structure
            expect(newObject).toBeDefined();
            expect(newObject.Data).toBeDefined();
            expect(newObject.Data.Id).toBeDefined();
        });

        it('should pass correct memoryOnly and isEditMode parameters', () => {
            viewModel.siteId('site-123');

            viewModel.UpdateFirmware();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const memoryOnly = callArgs[3];
            const isEditMode = callArgs[6];

            expect(memoryOnly).toBe(true);
            expect(isEditMode).toBe(true);
        });

        it('should pass correct popup width and height parameters', () => {
            viewModel.siteId('site-123');

            viewModel.UpdateFirmware();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const popupWidth = callArgs[5]; // 5th argument is width
            const popupParameter = callArgs[7]; // 8th argument is popupParameter (not height)

            expect(popupWidth).toBe('70%');
            // Note: The custom code doesn't pass a height parameter, it passes popupParameter as the last argument
            expect(popupParameter).toBeDefined();
        });
    });

    describe('Site ID propagation', () => {
        it('should set siteId when called', () => {
            const testSiteId = 'test-site-id';

            viewModel.siteId(testSiteId);

            expect(viewModel.siteId()).toBe(testSiteId);
        });

        it('should update siteId when changed', () => {
            const initialSiteId = 'initial-site';
            const newSiteId = 'new-site';

            viewModel.siteId(initialSiteId);
            expect(viewModel.siteId()).toBe(initialSiteId);

            viewModel.siteId(newSiteId);
            expect(viewModel.siteId()).toBe(newSiteId);
        });
    });

    describe('Error handling', () => {
        it('should handle missing controller gracefully', () => {
            viewModel.controller = null;

            // Should throw an error when controller is null
            expect(() => {
                viewModel.UpdateFirmware();
            }).toThrow();
        });

        it('should handle missing applicationController gracefully', () => {
            viewModel.controller = {};

            // Should not throw an error since it uses global ApplicationController
            expect(() => {
                viewModel.UpdateFirmware();
            }).not.toThrow();
        });

        it('should handle missing showEditPopup method gracefully', () => {
            viewModel.controller.applicationController = {};

            // Should not throw an error since it uses global ApplicationController
            expect(() => {
                viewModel.UpdateFirmware();
            }).not.toThrow();
        });
    });

    describe('Integration with parent components', () => {
        it('should work with FirmwareSettingsViewModel siteId propagation', () => {
            // Simulate the siteId being set by FirmwareSettingsViewModel
            const siteIdFromParent = 'parent-site-id';
            viewModel.siteId(siteIdFromParent);

            viewModel.UpdateFirmware();

            const callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            const popupParameter = callArgs[7];

            expect(popupParameter.siteId).toBe(siteIdFromParent);
        });

        it('should maintain siteId across multiple UpdateFirmware calls', () => {
            const persistentSiteId = 'persistent-site';
            viewModel.siteId(persistentSiteId);

            // First call
            viewModel.UpdateFirmware();
            let callArgs = global.ApplicationController.showEditPopup.mock.calls[0];
            let popupParameter = callArgs[7];
            expect(popupParameter.siteId).toBe(persistentSiteId);

            // Second call
            viewModel.UpdateFirmware();
            callArgs = global.ApplicationController.showEditPopup.mock.calls[1];
            popupParameter = callArgs[7];
            expect(popupParameter.siteId).toBe(persistentSiteId);
        });
    });
}); 