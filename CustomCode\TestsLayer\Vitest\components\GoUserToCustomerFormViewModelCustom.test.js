import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the FleetXQ global object
const mockFleetXQ = {
    Web: {
        ViewModels: {
            GoUserToCustomerFormViewModelCustom: null // Will be assigned by the IIFE
        }
    }
};
global.FleetXQ = mockFleetXQ;

// --- Code from GoUserToCustomerFormViewModel.custom.js ---
// Paste the IIFE from your custom JS file here.
(function () {
    // filter
    FleetXQ.Web.ViewModels.GoUserToCustomerFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.initialize = function () {

            self.viewmodel.subscriptions.push(self.viewmodel.CurrentObject.subscribe(function () {
                self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.GOUser = self.viewmodel.CurrentObject().getGOUser();

                if (self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.applyDealerFilter &&
                    typeof self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.applyDealerFilter === 'function') {
                    self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.applyDealerFilter();
                }
            }));

            // Mocking other subscriptions for completeness, assuming they exist as in your file
            if (self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel && self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.Events) {
                if (self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.Events.CollectionLoaded) {
                    self.viewmodel.subscriptions.push(self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.Events.CollectionLoaded.subscribe(function () {
                        if (self.viewmodel.popupCaller) {
                            self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.selectedCustomers = self.viewmodel.popupCaller.GoUserToCustomerObjectCollection;
                        }
                        if (self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.updateCheckStates) {
                            self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.updateCheckStates();
                        }
                    }));
                }
                if (self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.Events.CollectionSorted) {
                    self.viewmodel.subscriptions.push(self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.Events.CollectionSorted.subscribe(function () {
                        if (self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.updateCheckStates) {
                            self.viewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.updateCheckStates();
                        }
                    }));
                }
            } else {
                // console.warn('Test mock: SelectCustomersAccessForDealerUserGridGridViewModel or its Events are not defined on viewmodel for full subscription setup');
            }
        };

        this.onBeforeSave = function () {
            if (self.viewmodel.closePopup) self.viewmodel.closePopup(true);
            return false;
        };
    };
}());
// --- End of pasted code ---

const GoUserToCustomerFormViewModelCustom = FleetXQ.Web.ViewModels.GoUserToCustomerFormViewModelCustom;

describe('GoUserToCustomerFormViewModelCustom', () => {
    let mockParentViewmodel;
    let mockChildGridViewModel;
    let currentObjectSubscribeCallback;
    let customVMInstance;
    let mockGOUserInstance; // To hold the instance returned by getGOUser

    beforeEach(() => {
        mockChildGridViewModel = {
            GOUser: null,
            applyDealerFilter: vi.fn(),
            Events: {
                CollectionLoaded: { subscribe: vi.fn() },
                CollectionSorted: { subscribe: vi.fn() }
            },
            updateCheckStates: vi.fn()
        };

        mockGOUserInstance = { Data: { DealerId: 'test-dealer-id' } };

        // Mock for self.viewmodel.CurrentObject as a Knockout-like observable
        const underlyingCurrentObjectWithValue = {
            getGOUser: vi.fn(() => mockGOUserInstance)
        };

        const mockCurrentObjectAsObservable = vi.fn(() => underlyingCurrentObjectWithValue); // When called as a function, returns the object with getGOUser
        mockCurrentObjectAsObservable.subscribe = vi.fn((callback) => {
            currentObjectSubscribeCallback = callback;
            return { dispose: vi.fn() };
        });

        mockParentViewmodel = {
            CurrentObject: mockCurrentObjectAsObservable, // CurrentObject is now a mock function/observable
            SelectCustomersAccessForDealerUserGridGridViewModel: mockChildGridViewModel,
            subscriptions: [],
            popupCaller: { GoUserToCustomerObjectCollection: [] },
            closePopup: vi.fn()
        };

        customVMInstance = new GoUserToCustomerFormViewModelCustom(mockParentViewmodel);
        customVMInstance.initialize();
    });

    it('should set GOUser on child grid and call applyDealerFilter when CurrentObject changes', () => {
        expect(currentObjectSubscribeCallback).toBeDefined();
        if (currentObjectSubscribeCallback) {
            currentObjectSubscribeCallback();
        }
        // CurrentObject() is called internally by the SUT's subscribe callback
        expect(mockParentViewmodel.CurrentObject().getGOUser).toHaveBeenCalled();
        expect(mockParentViewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.GOUser).toEqual(mockGOUserInstance);
        expect(mockChildGridViewModel.applyDealerFilter).toHaveBeenCalled();
    });

    it('should subscribe to child grid CollectionLoaded and CollectionSorted events if they exist', () => {
        // Initialize is called in beforeEach
        expect(mockChildGridViewModel.Events.CollectionLoaded.subscribe).toHaveBeenCalled();
        expect(mockChildGridViewModel.Events.CollectionSorted.subscribe).toHaveBeenCalled();
    });

    it('onBeforeSave should call viewmodel.closePopup and return false', () => {
        const result = customVMInstance.onBeforeSave();
        expect(mockParentViewmodel.closePopup).toHaveBeenCalledWith(true);
        expect(result).toBe(false);
    });

    it('should not throw if applyDealerFilter does not exist on child', () => {
        mockChildGridViewModel.applyDealerFilter = undefined;

        expect(currentObjectSubscribeCallback).toBeDefined();
        expect(() => {
            if (currentObjectSubscribeCallback) {
                currentObjectSubscribeCallback();
            }
        }).not.toThrow();
        // CurrentObject() is called internally by the SUT's subscribe callback
        expect(mockParentViewmodel.CurrentObject().getGOUser).toHaveBeenCalled();
        expect(mockParentViewmodel.SelectCustomersAccessForDealerUserGridGridViewModel.GOUser).toEqual(mockGOUserInstance);
    });
});
