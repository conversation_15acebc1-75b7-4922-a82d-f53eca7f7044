import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

/**
 * Mock the global FleetXQ namespace that the custom view model depends on
 */
global.FleetXQ = {
    Web: {
        ViewModels: {}
    }
};

/**
 * Mock the knockout functionality
 */
global.ko = {
    observable: (value) => {
        let currentValue = value;
        const observable = function (newValue) {
            if (arguments.length === 0) {
                return currentValue;
            }
            currentValue = newValue;
            // Call subscribers
            if (observable.subscribers) {
                observable.subscribers.forEach(fn => fn(newValue));
            }
            return observable;
        };
        observable.subscribe = (fn) => {
            if (!observable.subscribers) {
                observable.subscribers = [];
            }
            observable.subscribers.push(fn);
            return {
                dispose: () => {
                    const index = observable.subscribers.indexOf(fn);
                    if (index > -1) {
                        observable.subscribers.splice(index, 1);
                    }
                }
            };
        };
        return observable;
    },
    observableArray: (array = []) => {
        let currentArray = array || [];
        const observable = function (newArray) {
            if (arguments.length === 0) {
                return currentArray;
            }
            currentArray = newArray;
            // Call subscribers
            if (observable.subscribers) {
                observable.subscribers.forEach(fn => fn(newArray));
            }
            return observable;
        };
        observable.push = (item) => {
            currentArray.push(item);
            if (observable.subscribers) {
                observable.subscribers.forEach(fn => fn(currentArray));
            }
        };
        observable.indexOf = (item) => currentArray.indexOf(item);
        observable.removeAll = () => {
            currentArray = [];
            if (observable.subscribers) {
                observable.subscribers.forEach(fn => fn(currentArray));
            }
        };
        observable.subscribe = (fn) => {
            if (!observable.subscribers) {
                observable.subscribers = [];
            }
            observable.subscribers.push(fn);
            return {
                dispose: () => {
                    const index = observable.subscribers.indexOf(fn);
                    if (index > -1) {
                        observable.subscribers.splice(index, 1);
                    }
                }
            };
        };
        return observable;
    }
};

/**
 * Define the CustomerPreOperationalChecklistTemplateForm2ViewModelCustom class
 */
FleetXQ.Web.ViewModels.CustomerPreOperationalChecklistTemplateForm2ViewModelCustom = function (viewmodel) {
    var self = this;
    this.viewmodel = viewmodel;

    this.initialize = function () {
        // Track models that already have this checklist applied
        self.viewmodel.modelsWithChecklist = ko.observableArray([]);
        
        // Track models that have no department checklists
        self.viewmodel.modelsWithNoVehicles = ko.observableArray([]);
        
        // Process template data when the template object changes
        self.viewmodel.subscriptions.push(self.viewmodel.CustomerPreOperationalChecklistTemplateObject.subscribe(function () {
            if (self.viewmodel.CustomerPreOperationalChecklistTemplateObject() && 
                self.viewmodel.CustomerPreOperationalChecklistTemplateObject().Data) {
                self.identifyAppliedChecklists();
            }
        }));

        self.viewmodel.subscriptions.push(self.viewmodel.CustomerModelItemsGridViewModel.Events.CollectionLoaded.subscribe(function () {
            self.viewmodel.CustomerModelItemsGridViewModel.updateCheckStates();
        }));

        self.viewmodel.subscriptions.push(self.viewmodel.CustomerModelItemsGridViewModel.Events.CollectionSorted.subscribe(function () {
            // after sorting a collection of models, update the checked states to reflect the new order
            self.viewmodel.CustomerModelItemsGridViewModel.updateCheckStates();
        }));

        // Function to check if a model already has the checklist applied
        self.viewmodel.isModelAlreadyApplied = function(modelId) {
            return self.viewmodel.modelsWithChecklist().indexOf(modelId) !== -1;
        };
        
        // Function to check if a model has no vehicles/department checklists
        self.viewmodel.modelHasNoVehicles = function(modelId) {
            return self.viewmodel.modelsWithNoVehicles().indexOf(modelId) !== -1;
        };

        // Use prefetch path to identify models with applied checklists
        self.identifyAppliedChecklists = vi.fn(function() {
            var template = self.viewmodel.CustomerPreOperationalChecklistTemplateObject();
            if (!template || !template.Data || !template.Data.Question()) {
                return;
            }

            var checklistQuestion = template.Data.Question().toLowerCase();
            var customer = template.getCustomer();
            if (!customer) {
                return;
            }

            var appliedModels = [];
            var noVehiclesModels = [];
            
            // Get all customer model IDs
            var customerModelIds = [];
            var customerModels = customer.Data.CustomerModelItems();
            if (!customerModels || !customerModels.length) {
                return;
            }
            
            customerModels.forEach(function(modelItem) {
                customerModelIds.push(modelItem.Data.ModelId());
                // Initially assume all models have no vehicles
                noVehiclesModels.push(modelItem.Data.ModelId());
            });
            
            // Get all customer sites
            var sites = customer.Data.Sites();
            if (!sites || !sites.length) {
                // If no sites, all models have no vehicles
                self.viewmodel.modelsWithNoVehicles(noVehiclesModels);
                self.viewmodel.modelsWithChecklist(appliedModels);
                return;
            }

            // Create a map to track departments and question status for each model
            var modelDepartmentMap = {};
            customerModelIds.forEach(function(modelId) {
                modelDepartmentMap[modelId] = {
                    departmentsWithChecklists: 0,
                    departmentsWithQuestion: 0
                };
            });
            
            // For each site, check all departments
            sites.forEach(function(site) {
                var departments = site.Data.DepartmentItems();
                if (!departments || !departments.length) {
                    return;
                }
                
                // For each department, check all models
                departments.forEach(function(department) {
                    var departmentId = department.Data.Id();
                    
                    // For each model, check if the department has a checklist for it
                    customerModelIds.forEach(function(modelId) {
                        // Check if this department has any checklist for the model
                        var hasChecklistForModel = self.departmentHasModelChecklist(department, modelId);
                        
                        // If a department has a checklist for this model, remove it from the noVehicles list
                        if (hasChecklistForModel) {
                            var index = noVehiclesModels.indexOf(modelId);
                            if (index !== -1) {
                                noVehiclesModels.splice(index, 1);
                            }
                            
                            // Count this department as having a checklist for this model
                            modelDepartmentMap[modelId].departmentsWithChecklists++;
                            
                            // Check if this department's checklist has the specific question
                            if (self.checklistHasQuestion(department, modelId, checklistQuestion)) {
                                modelDepartmentMap[modelId].departmentsWithQuestion++;
                            }
                        }
                    });
                });
            });
            
            // Check which models have the question in ALL of their department checklists
            customerModelIds.forEach(function(modelId) {
                var departmentStats = modelDepartmentMap[modelId];
                
                // Only consider a model "applied" if ALL departments that have checklists
                // for this model include the template question
                if (departmentStats.departmentsWithChecklists > 0 && 
                    departmentStats.departmentsWithChecklists === departmentStats.departmentsWithQuestion) {
                    appliedModels.push(modelId);
                }
            });
            
            // Update the observable arrays with our findings
            self.viewmodel.modelsWithNoVehicles(noVehiclesModels);
            self.viewmodel.modelsWithChecklist(appliedModels);
        });
        
        // Check if a department has any checklist for the specific model
        self.departmentHasModelChecklist = vi.fn(function(department, modelId) {
            var siteChecklistItems = department.getSiteChecklistItems();
            if (!siteChecklistItems || !siteChecklistItems.length) {
                return false;
            }
            
            // Find the site checklist for this model
            for (var i = 0; i < siteChecklistItems.length; i++) {
                if (siteChecklistItems[i].Data.ModelId() === modelId) {
                    return true;
                }
            }
            
            return false;
        });
        
        // Check if a department has a checklist with the specific question for the given model
        self.checklistHasQuestion = vi.fn(function(department, modelId, checklistQuestion) {
            // Try to find any department checklists for this model
            var siteChecklistItems = department.getSiteChecklistItems();
            if (!siteChecklistItems || !siteChecklistItems.length) {
                return false;
            }
            
            // Find the site checklist for this model
            var departmentChecklist = null;
            for (var i = 0; i < siteChecklistItems.length; i++) {
                if (siteChecklistItems[i].Data.ModelId() === modelId) {
                    departmentChecklist = siteChecklistItems[i];
                    break;
                }
            }
            
            if (!departmentChecklist) {
                return false;
            }
            
            // Check if this checklist has the specific question
            var preOpChecklists = departmentChecklist.Data.PreOperationalChecklists();
            if (!preOpChecklists || !preOpChecklists.length) {
                return false;
            }
            
            // Check if any checklist question matches our template question
            for (var j = 0; j < preOpChecklists.length; j++) {
                var question = preOpChecklists[j].Data.Question();
                if (question && question.toLowerCase() === checklistQuestion) {
                    return true;
                }
            }
            
            return false;
        });

        // Extend the CustomerModelItemsGridViewModel to handle the already applied state
        self.viewmodel.CustomerModelItemsGridViewModel.isModelAlreadyApplied = function(modelId) {
            return self.viewmodel.isModelAlreadyApplied(modelId);
        };
        
        // Add check for models with no vehicles
        self.viewmodel.CustomerModelItemsGridViewModel.modelHasNoVehicles = function(modelId) {
            return self.viewmodel.modelHasNoVehicles(modelId);
        };
        
        // Update the original updateCheckStates function to respect already applied status
        var originalUpdateCheckStates = self.viewmodel.CustomerModelItemsGridViewModel.updateCheckStates;
        self.viewmodel.CustomerModelItemsGridViewModel.updateCheckStates = function() {
            if (originalUpdateCheckStates) {
                originalUpdateCheckStates.call(self.viewmodel.CustomerModelItemsGridViewModel);
            } else {
                // Fallback implementation if original is not available
                self.viewmodel.CustomerModelItemsGridViewModel.checkedStates.removeAll();
                
                self.viewmodel.CustomerModelItemsGridViewModel.CustomerModelObjectCollection().forEach(function(item, index) {
                    var isChecked = self.viewmodel.CustomerModelItemsGridViewModel.selectedModelIds && 
                        self.viewmodel.CustomerModelItemsGridViewModel.selectedModelIds().indexOf(item.Data.ModelId()) !== -1;
                    self.viewmodel.CustomerModelItemsGridViewModel.checkedStates.push(ko.observable(isChecked));
                });
            }
        };

        self.viewmodel.Apply = vi.fn(function () {
            var configuration = {};
            configuration.caller = self.viewmodel;
            configuration.contextId = self.viewmodel.contextId;
            configuration.successHandler = self.onApplySuccess;
            configuration.errorHandler = self.viewmodel.ShowError;
            configuration.customerId = self.viewmodel.CustomerPreOperationalChecklistTemplateObject().Data.CustomerId();
            configuration.checklistTemplateId = self.viewmodel.CustomerPreOperationalChecklistTemplateObject().Data.Id();
            configuration.modelIds = self.viewmodel.CustomerModelItemsGridViewModel.selectedModelIds();
            self.viewmodel.setIsBusy(true);
            self.viewmodel.controller.applicationController.getProxyForComponent("CustomerUtilities").ApplyChecklistTemplates(configuration);

            self.viewmodel.closePopup(false);
        });
        
        self.onApplySuccess = vi.fn(function(data) {
            // Refresh the applied checklists list after applying
            self.identifyAppliedChecklists();
            self.viewmodel.setIsBusy(false);
        });
    };

    this.onBeforeSave = vi.fn(function () {
        self.viewmodel.closePopup(true);
        return false;
    });
};

describe('CustomerPreOperationalChecklistTemplateForm2ViewModelCustom Tests', () => {
    let viewModel;
    let customViewModel;
    let mockTemplateObj;
    let mockCustomerObj;
    let mockSiteObj;
    let mockDepartmentObj;

    beforeEach(() => {
        // Reset mocks
        vi.clearAllMocks();

        // Mock checklist template object
        mockTemplateObj = {
            Data: {
                Question: vi.fn(() => 'Test Question'),
                Id: vi.fn(() => 'template-1'),
                CustomerId: vi.fn(() => 'customer-1')
            },
            getCustomer: vi.fn()
        };

        // Mock customer object with models
        mockCustomerObj = {
            Data: {
                Id: vi.fn(() => 'customer-1'),
                CustomerModelItems: vi.fn(() => [
                    { Data: { ModelId: vi.fn(() => 'model-1'), Description: vi.fn(() => 'Model 1') } },
                    { Data: { ModelId: vi.fn(() => 'model-2'), Description: vi.fn(() => 'Model 2') } },
                    { Data: { ModelId: vi.fn(() => 'model-3'), Description: vi.fn(() => 'Model 3') } }
                ]),
                Sites: vi.fn(() => [])
            }
        };

        // Setup mock site with departments
        mockDepartmentObj = {
            Data: {
                Id: vi.fn(() => 'dept-1'),
                Name: vi.fn(() => 'Department 1')
            },
            getSiteChecklistItems: vi.fn()
        };

        mockSiteObj = {
            Data: {
                Id: vi.fn(() => 'site-1'),
                Name: vi.fn(() => 'Site 1'),
                DepartmentItems: vi.fn(() => [mockDepartmentObj])
            }
        };

        // Connect mocks
        mockTemplateObj.getCustomer.mockReturnValue(mockCustomerObj);

        // Setup the view model
        viewModel = {
            contextId: ['test-context'],
            controller: {
                applicationController: {
                    getNextContextId: vi.fn(() => 'new-test-context'),
                    getProxyForComponent: vi.fn(() => ({ 
                        ApplyChecklistTemplates: vi.fn() 
                    }))
                }
            },
            StatusData: {
                IsBusy: ko.observable(false)
            },
            setIsBusy: vi.fn(function(isBusy) {
                viewModel.StatusData.IsBusy(isBusy);
            }),
            closePopup: vi.fn(),
            ShowError: vi.fn(),
            Events: {},
            CustomerPreOperationalChecklistTemplateObject: ko.observable(mockTemplateObj),
            subscriptions: [],
            CustomerModelItemsGridViewModel: {
                Events: {
                    CollectionLoaded: { subscribe: vi.fn(() => ({ dispose: vi.fn() })) },
                    CollectionSorted: { subscribe: vi.fn(() => ({ dispose: vi.fn() })) }
                },
                CustomerModelObjectCollection: ko.observableArray([
                    { Data: { ModelId: vi.fn(() => 'model-1'), InternalObjectId: vi.fn(() => 1) } },
                    { Data: { ModelId: vi.fn(() => 'model-2'), InternalObjectId: vi.fn(() => 2) } },
                    { Data: { ModelId: vi.fn(() => 'model-3'), InternalObjectId: vi.fn(() => 3) } }
                ]),
                checkedStates: ko.observableArray([]),
                selectedModelIds: ko.observableArray([]),
                updateCheckStates: vi.fn()
            }
        };

        // Create custom view model
        customViewModel = new FleetXQ.Web.ViewModels.CustomerPreOperationalChecklistTemplateForm2ViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    describe('Happy Path Scenarios', () => {
        it('should initialize modelsWithChecklist and modelsWithNoVehicles arrays', () => {
            expect(viewModel.modelsWithChecklist).toBeDefined();
            expect(viewModel.modelsWithNoVehicles).toBeDefined();
            expect(viewModel.modelsWithChecklist()).toEqual([]);
            expect(viewModel.modelsWithNoVehicles()).toEqual([]);
        });

        it('should identify models with no vehicles when there are no sites', () => {
            // Simulate identifyAppliedChecklists being triggered
            const templateChangeHandler = viewModel.CustomerPreOperationalChecklistTemplateObject.subscribers[0];
            templateChangeHandler(mockTemplateObj);

            // Models with no sites should be marked as having no vehicles
            expect(viewModel.modelsWithNoVehicles()).toEqual(['model-1', 'model-2', 'model-3']);
            expect(viewModel.modelsWithChecklist()).toEqual([]);
        });

        it('should correctly identify models with applied checklists', () => {
            // Setup site with departments that have checklists
            mockCustomerObj.Data.Sites.mockReturnValue([mockSiteObj]);
            
            // Setup department with checklists for model-1 and model-2
            const mockChecklistItems = [
                { 
                    Data: { 
                        ModelId: vi.fn(() => 'model-1'),
                        PreOperationalChecklists: vi.fn(() => [
                            { Data: { Question: vi.fn(() => 'test question') } }
                        ])
                    }
                },
                { 
                    Data: { 
                        ModelId: vi.fn(() => 'model-2'),
                        PreOperationalChecklists: vi.fn(() => [
                            { Data: { Question: vi.fn(() => 'test question') } }
                        ])
                    }
                }
            ];
            
            mockDepartmentObj.getSiteChecklistItems.mockReturnValue(mockChecklistItems);
            
            // Mock checklist question check to return true
            customViewModel.checklistHasQuestion.mockImplementation(() => true);
            
            // Mock department has model checklist to only return true for model-1 and model-2
            customViewModel.departmentHasModelChecklist.mockImplementation((department, modelId) => {
                return modelId === 'model-1' || modelId === 'model-2';
            });
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // model-1 and model-2 should be marked as having checklists
            expect(viewModel.modelsWithChecklist()).toContain('model-1');
            expect(viewModel.modelsWithChecklist()).toContain('model-2');
            
            // model-3 should not be marked as having checklists
            expect(viewModel.modelsWithNoVehicles()).not.toContain('model-1');
            expect(viewModel.modelsWithNoVehicles()).not.toContain('model-2');
            expect(viewModel.modelsWithNoVehicles()).toContain('model-3');
        });

        it('should correctly handle the Apply function', () => {
            // Setup selected models
            viewModel.CustomerModelItemsGridViewModel.selectedModelIds(['model-1', 'model-2']);
            
            // Call Apply
            viewModel.Apply();
            
            // Verify the proxy is called
            expect(viewModel.controller.applicationController.getProxyForComponent).toHaveBeenCalledWith('CustomerUtilities');
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
            expect(viewModel.closePopup).toHaveBeenCalledWith(false);
        });

        it('should handle onApplySuccess correctly', () => {
            // Call onApplySuccess
            customViewModel.onApplySuccess({});
            
            // Should call identifyAppliedChecklists and setIsBusy(false)
            expect(customViewModel.identifyAppliedChecklists).toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });

        it('should correctly check if a model is already applied', () => {
            // Set up applied models
            viewModel.modelsWithChecklist(['model-1']);
            
            // Check that isModelAlreadyApplied works correctly
            expect(viewModel.isModelAlreadyApplied('model-1')).toBe(true);
            expect(viewModel.isModelAlreadyApplied('model-2')).toBe(false);
        });

        it('should correctly check if a model has no vehicles', () => {
            // Set up models with no vehicles
            viewModel.modelsWithNoVehicles(['model-3']);
            
            // Check that modelHasNoVehicles works correctly
            expect(viewModel.modelHasNoVehicles('model-3')).toBe(true);
            expect(viewModel.modelHasNoVehicles('model-1')).toBe(false);
        });
    });

    describe('Unhappy Path Scenarios', () => {
        it('should handle template with no data', () => {
            // Set template with no data
            const emptyTemplate = { Data: null, getCustomer: vi.fn() };
            viewModel.CustomerPreOperationalChecklistTemplateObject(emptyTemplate);
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // Should exit early
            expect(viewModel.modelsWithChecklist()).toEqual([]);
            expect(viewModel.modelsWithNoVehicles()).toEqual([]);
        });

        it('should handle template with no question', () => {
            // Set template with empty question
            mockTemplateObj.Data.Question.mockReturnValue(null);
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // Should exit early
            expect(viewModel.modelsWithChecklist()).toEqual([]);
            expect(viewModel.modelsWithNoVehicles()).toEqual([]);
        });

        it('should handle template with no customer', () => {
            // Set template with no customer
            mockTemplateObj.getCustomer.mockReturnValue(null);
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // Should exit early
            expect(viewModel.modelsWithChecklist()).toEqual([]);
            expect(viewModel.modelsWithNoVehicles()).toEqual([]);
        });

        it('should handle customer with no models', () => {
            // Set customer with no models
            mockCustomerObj.Data.CustomerModelItems.mockReturnValue([]);
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // Should exit early
            expect(viewModel.modelsWithChecklist()).toEqual([]);
            expect(viewModel.modelsWithNoVehicles()).toEqual([]);
        });

        it('should handle site with no departments', () => {
            // Set up site with no departments
            mockSiteObj.Data.DepartmentItems.mockReturnValue([]);
            mockCustomerObj.Data.Sites.mockReturnValue([mockSiteObj]);
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // All models should be marked as having no vehicles
            expect(viewModel.modelsWithNoVehicles()).toEqual(['model-1', 'model-2', 'model-3']);
            expect(viewModel.modelsWithChecklist()).toEqual([]);
        });

        it('should handle department with no checklists', () => {
            // Set up department with no checklists
            mockDepartmentObj.getSiteChecklistItems.mockReturnValue([]);
            mockCustomerObj.Data.Sites.mockReturnValue([mockSiteObj]);
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // All models should be marked as having no vehicles
            expect(viewModel.modelsWithNoVehicles()).toEqual(['model-1', 'model-2', 'model-3']);
            expect(viewModel.modelsWithChecklist()).toEqual([]);
        });

        it('should handle mixed case when checking questions', () => {
            // Setup site with departments that have checklists
            mockCustomerObj.Data.Sites.mockReturnValue([mockSiteObj]);
            
            const mockChecklistItems = [
                { 
                    Data: { 
                        ModelId: vi.fn(() => 'model-1'),
                        PreOperationalChecklists: vi.fn(() => [
                            { Data: { Question: vi.fn(() => 'TEST QUESTION') } }
                        ])
                    }
                }
            ];
            
            mockDepartmentObj.getSiteChecklistItems.mockReturnValue(mockChecklistItems);
            
            // Let the real implementation run to test case insensitivity
            customViewModel.checklistHasQuestion.mockRestore();
            
            // Mock department has model checklist to return true only for model-1
            customViewModel.departmentHasModelChecklist.mockImplementation((department, modelId) => {
                return modelId === 'model-1';
            });
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // model-1 should be marked as having checklists (case insensitive match)
            expect(viewModel.modelsWithChecklist()).toContain('model-1');
        });

        it('should handle partial application (not all departments have the checklist)', () => {
            // Setup two departments, one with the checklist and one without
            const dept1 = { 
                Data: { Id: vi.fn(() => 'dept-1'), Name: vi.fn(() => 'Department 1') },
                getSiteChecklistItems: vi.fn()
            };
            
            const dept2 = { 
                Data: { Id: vi.fn(() => 'dept-2'), Name: vi.fn(() => 'Department 2') },
                getSiteChecklistItems: vi.fn()
            };
            
            mockSiteObj.Data.DepartmentItems.mockReturnValue([dept1, dept2]);
            mockCustomerObj.Data.Sites.mockReturnValue([mockSiteObj]);
            
            // Dept1 has the checklist for model-1
            const mockChecklistItems1 = [
                { 
                    Data: { 
                        ModelId: vi.fn(() => 'model-1'),
                        PreOperationalChecklists: vi.fn(() => [
                            { Data: { Question: vi.fn(() => 'test question') } }
                        ])
                    }
                }
            ];
            
            // Dept2 has the model-1 but without the checklist question
            const mockChecklistItems2 = [
                { 
                    Data: { 
                        ModelId: vi.fn(() => 'model-1'),
                        PreOperationalChecklists: vi.fn(() => [
                            { Data: { Question: vi.fn(() => 'different question') } }
                        ])
                    }
                }
            ];
            
            dept1.getSiteChecklistItems.mockReturnValue(mockChecklistItems1);
            dept2.getSiteChecklistItems.mockReturnValue(mockChecklistItems2);
            
            // Setup to match one department but not the other
            customViewModel.departmentHasModelChecklist.mockImplementation(() => true);
            customViewModel.checklistHasQuestion.mockImplementation((department, modelId, checklistQuestion) => {
                return department === dept1; // Only match for dept1
            });
            
            // Trigger identification
            customViewModel.identifyAppliedChecklists();
            
            // model-1 should NOT be marked as having checklist completely applied
            // since only one of the two departments has it
            expect(viewModel.modelsWithChecklist()).not.toContain('model-1');
            expect(viewModel.modelsWithNoVehicles()).not.toContain('model-1');
        });
    });
}); 