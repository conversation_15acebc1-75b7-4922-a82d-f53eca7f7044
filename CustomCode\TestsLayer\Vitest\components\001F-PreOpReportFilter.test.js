import { describe, it, expect, beforeEach, vi } from 'vitest';

/**
 * Mock the global FleetXQ namespace that the custom view model depends on
 * This is required because the custom view model is wrapped in an IIFE that adds
 * its constructor to the FleetXQ.Web.ViewModels namespace
 */
global.FleetXQ = {
    Web: {
        ViewModels: {}
    }
};

/**
 * Mock the ApplicationController that's used to check user roles
 * This is required for testing the customer role initialization logic
 */
global.ApplicationController = {
    viewModel: {
        security: {
            currentUserClaims: vi.fn()
        }
    }
};

describe('Pre Op Report Filter', () => {
    let viewModel;
    let customViewModel;

    /**
     * Set up test environment before each test
     * This ensures each test starts with a fresh view model instance
     */
    beforeEach(() => {
        // Create a mock view model with all required properties and methods
        viewModel = {
            // Mock the filter object that holds the date values
            PreOpReportFilterObject: vi.fn().mockReturnValue({
                Data: {
                    StartDate: vi.fn(),  // Mock function to set start date
                    EndDate: vi.fn(),    // Mock function to set end date
                    CustomerId: vi.fn()  // Mock function to set customer ID
                }
            }),
            // Mock required view model methods
            Modify: vi.fn(),
            subscribeToMessages: vi.fn(),
            Customer_CompanyName: vi.fn(),
            Customer_lookupItem: vi.fn(),
            selectiveLoadDataForSite: vi.fn(),
            // Mock the customer data store
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            ShowError: vi.fn(),
            CustomerContextId: 'test-context'
        };

        // Import the custom view model file
        require('../../../WebApplicationLayer/wwwroot/ViewModels/PreOpReportFilter/PreOpReportFilterFormViewModel.custom');

        // Get the constructor from the mocked namespace and create a new instance
        const CustomViewModel = global.FleetXQ.Web.ViewModels.PreOpReportFilterFormViewModelCustom;
        customViewModel = new CustomViewModel(viewModel);
    });

    /**
     * Test suite for start date selection behavior
     * Verifies that start dates are always set to midnight (00:00:00.000)
     */
    describe('Start Date Selection', () => {
        it('should allow modification of date and time', () => {
            // Arrange
            customViewModel.initialize();
            const spy = vi.spyOn(viewModel.PreOpReportFilterObject().Data, 'StartDate');

            // Test various date/time combinations
            const testCases = [
                { date: '2024-03-15T09:00:00', description: 'morning' },
                { date: '2024-03-15T12:00:00', description: 'noon' },
                { date: '2024-03-15T15:30:00', description: 'afternoon' },
                { date: '2024-03-15T23:59:59', description: 'end of day' }
            ];

            // Act & Assert
            testCases.forEach(({ date, description }) => {
                const selectedDate = new Date(date);
                viewModel.startDateSelectionChanged(selectedDate);
                expect(spy).toHaveBeenCalledWith(selectedDate);
            });
        });

        it('should preserve time when only date changes', () => {
            // Arrange
            customViewModel.initialize();
            const spy = vi.spyOn(viewModel.PreOpReportFilterObject().Data, 'StartDate');

            // Set initial time to 9:00 AM
            const initialDate = new Date('2024-03-15T09:00:00');
            viewModel.startDateSelectionChanged(initialDate);

            // Change only the date, keeping the same time
            const newDate = new Date('2024-03-16T09:00:00');
            viewModel.startDateSelectionChanged(newDate);

            // Assert
            expect(spy).toHaveBeenCalledWith(newDate);
            expect(newDate.getHours()).toBe(9);
            expect(newDate.getMinutes()).toBe(0);
        });
    });

    /**
     * Test suite for end date selection behavior
     * Verifies that end dates are always set to end of day (23:59:59.999)
     */
    describe('End Date Selection', () => {
        it('should allow modification of date and time', () => {
            // Arrange
            customViewModel.initialize();
            const spy = vi.spyOn(viewModel.PreOpReportFilterObject().Data, 'EndDate');

            // Test various date/time combinations
            const testCases = [
                { date: '2024-03-15T09:00:00', description: 'morning' },
                { date: '2024-03-15T12:00:00', description: 'noon' },
                { date: '2024-03-15T15:30:00', description: 'afternoon' },
                { date: '2024-03-15T23:59:59', description: 'end of day' }
            ];

            // Act & Assert
            testCases.forEach(({ date, description }) => {
                const selectedDate = new Date(date);
                viewModel.endDateSelectionChanged(selectedDate);
                expect(spy).toHaveBeenCalledWith(selectedDate);
            });
        });

        it('should preserve time when only date changes', () => {
            // Arrange
            customViewModel.initialize();
            const spy = vi.spyOn(viewModel.PreOpReportFilterObject().Data, 'EndDate');

            // Set initial time to 3:00 PM
            const initialDate = new Date('2024-03-15T15:00:00');
            viewModel.endDateSelectionChanged(initialDate);

            // Change only the date, keeping the same time
            const newDate = new Date('2024-03-16T15:00:00');
            viewModel.endDateSelectionChanged(newDate);

            // Assert
            expect(spy).toHaveBeenCalledWith(newDate);
            expect(newDate.getHours()).toBe(15);
            expect(newDate.getMinutes()).toBe(0);
        });
    });

    /**
     * Test suite for view model initialization
     * Verifies proper setup of date handlers
     */
    describe('Initialization', () => {
        it('should set default times on initialization', () => {
            // Arrange
            const today = new Date();
            const expectedStartDate = new Date(today);
            expectedStartDate.setHours(0, 0, 0, 0);

            const expectedEndDate = new Date(today);
            expectedEndDate.setHours(23, 59, 59, 999);

            // Act
            customViewModel.initialize();

            // Assert
            expect(viewModel.PreOpReportFilterObject().Data.StartDate).toHaveBeenCalledWith(expectedStartDate);
            expect(viewModel.PreOpReportFilterObject().Data.EndDate).toHaveBeenCalledWith(expectedEndDate);
        });
    });
}); 