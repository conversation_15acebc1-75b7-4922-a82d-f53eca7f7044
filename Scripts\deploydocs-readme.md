# VuePress Documentation Deployment Scripts

This directory contains scripts to automatically build and deploy the VuePress documentation site located in the `TechDoc/` folder.

## Available Scripts

### 1. `deploydocs.cmd` (Windows Batch)
A Windows batch script that builds and deploys the VuePress documentation with local server options.

**Basic Usage:**
```cmd
cd Scripts
deploydocs.cmd
```

**Advanced Usage:**
```cmd
# Deploy only locally (skip IIS deployment)
deploydocs.cmd --local-only
```

**Note**: The local development server **always starts automatically** after deployment.

**Features:**
- Checks for Node.js installation
- Installs npm dependencies if needed
- Builds the VuePress documentation
- Deploys to IIS web server location
- **NEW**: Local deployment option for easy access
- **NEW**: Built-in development server (localhost)
- **NEW**: Automatic browser opening
- Provides detailed error handling and status messages

## Local Development Server

The script now includes a built-in local development server for easy preview:

### How It Works

**Every time you run the script**, it will:
1. Build the VuePress documentation
2. Deploy it to the appropriate location(s)
3. **Automatically start a local development server**
4. **Open your browser** to view the documentation
5. Serve the documentation at `http://localhost:8080` or `http://localhost:8000`

**Examples:**
```cmd
# Full deployment (IIS + local) + server
deploydocs.cmd

# Local only deployment + server  
deploydocs.cmd --local-only
```

### Server Options

The script automatically detects and uses the best available server:

1. **Node.js `serve` package** (preferred) - `http://localhost:8080`
   ```cmd
   npm install -g serve
   ```

2. **Python built-in server** (fallback) - `http://localhost:8000`
   - Requires Python 3.x installed

3. **Direct file access** (last resort)
   - Opens `index.html` directly in your browser if no server is available

## Deployment Locations

### Production Deployment
- **IIS Location**: `C:\inetpub\wwwroot\GOApps\fleetxq-docs`
- Follows the same pattern as the existing `deployapplication.cmd` script

### Local Development
- **Local Location**: `%USERPROFILE%\fleetxq-docs-local`
- Example: `C:\Users\<USER>\fleetxq-docs-local`
- Used for local preview and development server

## Prerequisites

- **Node.js 18.0.0 or higher** - Required for VuePress
- **npm** - Package manager (comes with Node.js)
- **Write permissions** to the deployment directory

## How It Works

1. **Validation**: Checks that Node.js is installed and TechDoc directory exists
2. **Dependencies**: Installs npm packages if `node_modules` folder doesn't exist
3. **Build**: Runs `npm run docs:build` to generate static files
4. **Deploy**: Uses `robocopy` to efficiently copy files to web server
5. **Cleanup**: Purges old files from deployment directory

## Build Output

VuePress builds the documentation to: `TechDoc/docs/.vuepress/dist/`

This directory contains all the static HTML, CSS, and JavaScript files ready for web deployment.

## Troubleshooting

### Common Issues

1. **"Node.js is not installed"**
   - Install Node.js from https://nodejs.org/
   - Ensure it's added to your system PATH

2. **"Failed to install npm dependencies"**
   - Check internet connection
   - Try running `npm install` manually in the TechDoc directory
   - Clear npm cache: `npm cache clean --force`

3. **"Failed to build documentation"**
   - Check for syntax errors in markdown files
   - Verify VuePress configuration
   - Look for broken links or missing images

4. **"Failed to deploy documentation"**
   - Check write permissions to deployment directory
   - Ensure the deployment path exists or can be created
   - Verify robocopy is available (standard on Windows)

### Manual Testing

To test the build process manually:

```bash
cd TechDoc
npm install
npm run docs:build
```

The built files will be in `docs/.vuepress/dist/`

## Integration with CI/CD

These scripts can be integrated into automated deployment pipelines:

```yaml
# Example Azure DevOps step
- script: |
    cd Scripts
    deploydocs.cmd
  displayName: 'Deploy Documentation'
```

## Customization

To modify the deployment location, edit the `INETPATH` variable in `deploydocs.cmd` or use the `-DeployPath` parameter in `deploydocs.ps1`.
