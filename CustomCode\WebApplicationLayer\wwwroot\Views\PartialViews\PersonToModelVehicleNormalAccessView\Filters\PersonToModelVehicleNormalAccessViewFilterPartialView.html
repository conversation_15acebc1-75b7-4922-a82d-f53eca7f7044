<!--
// This is Custom Code
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter " Person to model vehicle normal access view Filter" Internal name : "PersonToModelVehicleNormalAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToModelVehicleNormalAccessViewFilter"
        data-test-id="69d7ddaf-e4f8-43fe-8831-34524d917db1">
        <form
            data-bind="submit: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.commands.searchCommand">
            <div class="uiSearchContainer" style="margin-top: 8px;">
                <div class="filterFieldSetContent">
                    <div class="row g-2 align-items-end">
                        <!-- Model Name Field -->
                        <div class="col-auto"
                            data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.statusData.isModelNameVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToModelVehicleNormalAccessView/filters/PersonToModelVehicleNormalAccessViewFilter:filterFields.ModelName.displayName'">Model</span>
                                </label>
                                <input type="text" class="form-control form-control-sm"
                                    style="min-width: 150px; padding-left: 12px;"
                                    data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.filterData.fields.ModelName, enable: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="95e88b83-e73f-43e2-b7f7-36b2e0e9fbba" />
                            </div>
                        </div>

                        <!-- Has Access Field -->
                        <div class="col-auto"
                            data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.statusData.isHasAccessVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToModelVehicleNormalAccessView/filters/PersonToModelVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">Has
                                        access</span>
                                </label>
                                <select class="form-control form-control-sm"
                                    style="min-width: 120px; padding-left: 12px;"
                                    data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.HasAccessValues, enable: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"></select>
                            </div>
                        </div>

                        <!-- Search Buttons -->
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="submit" class="btn btn-primary btn-sm"
                                    data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.commands.searchCommand, i18n: 'buttons.search', enable: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="searchCommand">SEARCH</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.commands.clearCommand, i18n: 'buttons.clear', enable: PersonVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="clearCommand">CLEAR</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter " Person to model vehicle normal access view Filter" Internal name : "PersonToModelVehicleNormalAccessViewFilter"-->