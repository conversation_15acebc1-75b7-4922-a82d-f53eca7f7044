using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class DealerDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"DealerDataProviderExtensionTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // The extension is automatically registered, no need to add it manually
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test region (required for dealer)
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Test Region";
            region.Id = Guid.NewGuid();
            region.Active = true;
            await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create test country (required by DealerDataProviderExtension.OnAfterSaveDataSet)
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Test Country";
            country.Id = Guid.NewGuid();
            await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);
        }

        #region Theme Color Validation Tests

        [Test]
        public async Task OnBeforeSave_ValidHexColor_6Characters_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FF0000"; // Valid 6-character hex

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        [Test]
        public async Task OnBeforeSave_ValidHexColor_3Characters_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FFF"; // Valid 3-character hex

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        [Test]
        public async Task OnBeforeSave_ValidHexColor_LowerCase_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#ff00ff"; // Valid lowercase hex

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        [Test]
        public async Task OnBeforeSave_ValidHexColor_MixedCase_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#Ff00Aa"; // Valid mixed case hex

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        [Test]
        public async Task OnBeforeSave_InvalidHexColor_MissingHash_ShouldThrowException()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "FF0000"; // Missing # prefix

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain("Theme color 'FF0000' is not a valid hex color format"));
            Assert.That(exception.Message, Does.Contain("Use #RGB or #RRGGBB format"));
        }

        [Test]
        public async Task OnBeforeSave_InvalidHexColor_TooShort_ShouldThrowException()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FF"; // Too short (2 characters)

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain("Theme color '#FF' is not a valid hex color format"));
        }

        [Test]
        public async Task OnBeforeSave_InvalidHexColor_TooLong_ShouldThrowException()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FF00000"; // Too long (7 characters)

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain("Theme color '#FF00000' is not a valid hex color format"));
        }

        [Test]
        public async Task OnBeforeSave_InvalidHexColor_InvalidCharacters_ShouldThrowException()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FFGG00"; // Invalid characters (G)

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain("Theme color '#FFGG00' is not a valid hex color format"));
        }

        [Test]
        public async Task OnBeforeSave_InvalidHexColor_4Characters_ShouldThrowException()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FFFF"; // Invalid length (4 characters)

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain("Theme color '#FFFF' is not a valid hex color format"));
        }

        [Test]
        public async Task OnBeforeSave_InvalidHexColor_5Characters_ShouldThrowException()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "#FF000"; // Invalid length (5 characters)

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain("Theme color '#FF000' is not a valid hex color format"));
        }

        [Test]
        public async Task OnBeforeSave_EmptyThemeColor_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = ""; // Empty string should be allowed

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        [Test]
        public async Task OnBeforeSave_NullThemeColor_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = null; // Null should be allowed

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        [Test]
        public async Task OnBeforeSave_WhitespaceThemeColor_ShouldSaveSuccessfully()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            dealer.ThemeColor = "   "; // Whitespace should be treated as empty

            // Act & Assert
            Assert.DoesNotThrowAsync(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            });
        }

        #endregion

        #region Subdomain Validation Tests (existing functionality)

        [Test]
        public async Task OnBeforeSave_ValidSubdomain_ShouldNormalizeToLowerCase()
        {
            // Arrange
            var dealer = await CreateTestDealerAsync();
            var uniqueSubdomain = $"TestDealer{Guid.NewGuid().ToString("N")[..8]}"; // Use unique subdomain
            dealer.SubDomain = uniqueSubdomain; // Mixed case

            // Act
            var savedDealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Assert
            Assert.That(savedDealer.SubDomain, Is.EqualTo(uniqueSubdomain.ToLower()));
        }

        [Test]
        public async Task OnBeforeSave_DuplicateSubdomain_ShouldThrowException()
        {
            // Arrange
            var uniqueSubdomain = $"testdealer{Guid.NewGuid().ToString("N")[..8]}"; // Use unique subdomain
            
            var dealer1 = await CreateTestDealerAsync();
            dealer1.SubDomain = uniqueSubdomain;
            await _dataFacade.DealerDataProvider.SaveAsync(dealer1, skipSecurity: true);

            var dealer2 = await CreateTestDealerAsync();
            dealer2.SubDomain = uniqueSubdomain.ToUpper(); // Same subdomain, different case

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () => 
            {
                await _dataFacade.DealerDataProvider.SaveAsync(dealer2, skipSecurity: true);
            });

            Assert.That(exception.Message, Does.Contain($"A dealer with subdomain '{uniqueSubdomain}' already exists"));
        }

        #endregion

        #region Helper Methods

        private async Task<DealerDataObject> CreateTestDealerAsync()
        {
            var region = (await _dataFacade.RegionDataProvider.GetCollectionAsync(null, skipSecurity: true))[0];
            
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = $"Test Dealer {Guid.NewGuid()}";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            
            return dealer;
        }

        #endregion
    }
}
