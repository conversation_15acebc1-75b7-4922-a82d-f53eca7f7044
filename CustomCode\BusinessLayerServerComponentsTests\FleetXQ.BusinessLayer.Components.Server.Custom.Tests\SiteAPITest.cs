using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class SiteAPITest : TestBase
    {
        private IDataFacade _dataFacade;
        private ISiteAPI _siteAPI;
        private readonly string _testDatabaseName = $"SiteAPITest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _siteAPI = _serviceProvider.GetRequiredService<ISiteAPI>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);
        }

        [Test]
        public async Task SoftDeleteAsync_UnknownSiteId_ThrowsException()
        {
            // Arrange
            var unknownSiteId = Guid.NewGuid();

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _siteAPI.SoftDeleteAsync(unknownSiteId));

            Assert.That(exception.Message, Is.EqualTo($"unknow site id {unknownSiteId}"));
        }

        [Test]
        public async Task SoftDeleteAsync_SiteWithDepartments_ThrowsException()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var timeZone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _siteAPI.SoftDeleteAsync(site.Id));

            Assert.That(exception.Message, Is.EqualTo("Cannot delete site: Site contains departments. Please delete all departments first."));
        }

        [Test]
        public async Task SoftDeleteAsync_ValidSite_SuccessfullySoftDeletes()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var timeZone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Act
            var response = await _siteAPI.SoftDeleteAsync(site.Id);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Is.True);

            // Verify site was soft deleted
            var deletedSite = await _dataFacade.SiteDataProvider.GetAsync(site);
            Assert.That(deletedSite, Is.Not.Null);
            Assert.That(deletedSite.DeletedAtUtc, Is.Not.Null);
        }
    }
}