﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// DepartmentAPI Component
	///  
	/// </summary>
    public partial class DepartmentAPI : BaseServerComponent, IDepartmentAPI 
    {
		public DepartmentAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SoftDeleteAsync(Guid departmentId, Dictionary<string, object> parameters = null)
        {
            var department = _serviceProvider.GetService<DepartmentDataObject>();
            department.Id = departmentId;

            department = await _dataFacade.DepartmentDataProvider.GetAsync(department);

            if (department == null)
            {
                throw new GOServerException($"unknow department id {departmentId}");
            }

            // Check for vehicles
            var vehicles = await department.LoadVehiclesAsync();
            if (vehicles.Any())
            {
                throw new GOServerException("Cannot delete department: Department contains vehicles.");
            }

            // Check for users
            var users = await department.LoadPersonItemsAsync();
            if (users.Any())
            {
                throw new GOServerException("Cannot delete department: Department contains users.");
            }

            department.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            return new ComponentResponse<bool>(true);
        }
    }
}
