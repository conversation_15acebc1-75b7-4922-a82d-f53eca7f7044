using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using System.Threading.Tasks;
using System;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class NetworkSettingsDataProviderExtension : IDataProviderExtension<NetworkSettingsDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDeviceTwinHandler _deviceTwinHandler;

        public NetworkSettingsDataProviderExtension(
            IServiceProvider serviceProvider,
            IDataFacade dataFacade,
            IDeviceTwinHandler deviceTwinHandler)
        {
            _serviceProvider = serviceProvider;
            _dataFacade = dataFacade;
            _deviceTwinHandler = deviceTwinHandler;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSave += OnAfterSaveAsync;
        }

        private async Task OnAfterSaveAsync(OnAfterSaveEventArgs arg)
        {
            var networkSettings = arg.Entity as NetworkSettingsDataObject;
            if (networkSettings == null)
            {
                return;
            }

            var vehicle = await networkSettings.LoadVehicleAsync(skipSecurity: true);
            if (vehicle == null)
            {
                return;
            }

            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
            if (module?.IoTDevice == null)
            {
                return;
            }

            await _deviceTwinHandler.SyncGeneralSettings(module.IoTDevice);
        }
    }
} 