﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// SiteAPI Component
	///  
	/// </summary>
    public partial class SiteAPI : BaseServerComponent, ISiteAPI 
    {
		public SiteAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SoftDeleteAsync(Guid siteId, Dictionary<string, object> parameters = null)
        {
            var site = _serviceProvider.GetService<SiteDataObject>();
            site.Id = siteId;

            site = await _dataFacade.SiteDataProvider.GetAsync(site);

            if (site == null)
            {
                throw new GOServerException($"unknow site id {siteId}");
            }

            var departments = await site.LoadDepartmentItemsAsync();
            if (departments.Any())
            {
                throw new GOServerException("Cannot delete site: Site contains departments. Please delete all departments first.");
            }

            site.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            return new ComponentResponse<bool>(true);
        }
    }
}
