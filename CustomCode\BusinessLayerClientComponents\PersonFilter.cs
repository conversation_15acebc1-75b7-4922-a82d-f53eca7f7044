﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// PersonFilter Component
	///  
	/// </summary>
    public partial class PersonFilter : IPersonFilter 
    {
		/// <summary>
        /// Clear Method
		///  
		  /// </summary>
		public Task<ComponentResponse<System.Boolean>> ClearAsync(Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return Task.FromResult(new ComponentResponse<System.Boolean>(default(System.Boolean))); 
		}
		
		/// <summary>
        /// Search Method
		///  
		  /// </summary>
		public Task<ComponentResponse<System.Boolean>> SearchAsync(Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return Task.FromResult(new ComponentResponse<System.Boolean>(default(System.Boolean))); 
		}
		
		public void Dispose()
		{
		}

	}
}
