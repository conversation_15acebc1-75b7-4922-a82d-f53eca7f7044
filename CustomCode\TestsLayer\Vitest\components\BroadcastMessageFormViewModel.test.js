import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock FleetXQ global object
global.FleetXQ = {
    Web: {
        Model: {
            DataObjects: {
                VehicleBroadcastMessageObjectFactory: {
                    createNew: vi.fn().mockImplementation((dataSet, contextId) => ({
                        Data: {
                            Id: ko.observable('test-id'),
                            VehicleId: ko.observable(null),
                            IsNew: ko.observable(true),
                            IsMarkedForDeletion: ko.observable(false)
                        },
                        setBroadcastMessage: vi.fn()
                    }))
                }
            }
        },
        ViewModels: {}
    }
};

// Mock ko (knockout) functionality
const ko = {
    observable: (val) => {
        const obs = function (newVal) {
            if (arguments.length > 0) {
                obs.value = newVal;
                obs.subscribers.forEach(fn => fn(newVal));
            }
            return obs.value;
        };
        obs.value = val;
        obs.subscribers = [];
        obs.subscribe = function (fn) {
            obs.subscribers.push(fn);
            return {
                dispose: () => {
                    const index = obs.subscribers.indexOf(fn);
                    if (index > -1) obs.subscribers.splice(index, 1);
                }
            };
        };
        return obs;
    },
    observableArray: (initial = []) => {
        const obs = ko.observable(initial);
        obs.push = function (item) {
            const current = obs();
            obs([...current, item]);
        };
        obs.remove = function (predicate) {
            const current = obs();
            const filtered = current.filter(item => !predicate(item));
            obs(filtered);
            return current.filter(predicate);
        };
        obs.removeAll = function () {
            obs([]);
        };
        return obs;
    }
};

describe('BroadcastMessageFormViewModel', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Create base viewModel with required properties
        viewModel = {
            contextId: 'test-context',
            BroadcastMessageObject: ko.observable({
                message: 'Test broadcast message'
            }),
            SelectVehiclesForBroadcastMessageGridGridViewModel: {
                VehicleObjectCollection: ko.observableArray([]),
                checkedStates: ko.observableArray([]),
                broadcastMessage: null
            },
            subscriptions: [],
            onSendSuccess: vi.fn(),
            ShowError: vi.fn()
        };

        // Initialize custom viewModel
        FleetXQ.Web.ViewModels.BroadcastMessageFormViewModelCustom = function (vm) {
            this.viewmodel = vm;
            var self = this;
            this.initialize = function () {
                // Set initial broadcast message
                self.viewmodel.SelectVehiclesForBroadcastMessageGridGridViewModel.broadcastMessage = self.viewmodel.BroadcastMessageObject;

                // Subscribe to changes in the broadcast message
                self.viewmodel.subscriptions.push(
                    self.viewmodel.BroadcastMessageObject.subscribe(function () {
                        self.viewmodel.SelectVehiclesForBroadcastMessageGridGridViewModel.broadcastMessage = self.viewmodel.BroadcastMessageObject;
                    })
                );

                // Initialize Send function
                self.viewmodel.Send = function () {
                    var configuration = {};
                    configuration.caller = self.viewmodel;
                    configuration.contextId = self.viewmodel.contextId;
                    configuration.successHandler = self.viewmodel.onSendSuccess;
                    configuration.errorHandler = self.viewmodel.ShowError;
                    configuration.broadcastMessage = self.viewmodel.BroadcastMessageObject();

                    // Get the vehicle IDs from the SelectVehiclesForBroadcastMessage grid's selected vehicles
                    configuration.vehicleIds = [];

                    var selectedVehicles = self.viewmodel.SelectVehiclesForBroadcastMessageGridGridViewModel.VehicleObjectCollection();
                    var checkedStates = self.viewmodel.SelectVehiclesForBroadcastMessageGridGridViewModel.checkedStates();

                    if (selectedVehicles && selectedVehicles.length > 0 && checkedStates && checkedStates.length > 0) {
                        selectedVehicles.forEach(function (vehicle, index) {
                            if (checkedStates[index]()) {
                                configuration.vehicleIds.push(vehicle.Data.Id());
                            }
                        });
                    }

                    if (configuration.vehicleIds.length === 0) {
                        self.viewmodel.ShowError("Please select at least one vehicle to send the broadcast message.");
                        return;
                    }

                    // Log the configuration object
                    console.log('Sending broadcast message with configuration:', {
                        vehicleIds: configuration.vehicleIds,
                        broadcastMessage: configuration.broadcastMessage
                    });
                };
            };
        };

        customViewModel = new FleetXQ.Web.ViewModels.BroadcastMessageFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('initialization', () => {
        it('should set broadcast message in grid view model', () => {
            expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.broadcastMessage)
                .toBe(viewModel.BroadcastMessageObject);
        });

        it('should subscribe to broadcast message changes', () => {
            const newMessage = { message: 'Updated message' };
            viewModel.BroadcastMessageObject(newMessage);

            expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.broadcastMessage)
                .toBe(viewModel.BroadcastMessageObject);
        });
    });

    describe('Send function', () => {
        beforeEach(() => {
            viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle-1') } },
                { Data: { Id: ko.observable('vehicle-2') } }
            ]);

            viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.checkedStates([
                ko.observable(true),
                ko.observable(false)
            ]);
        });

        it('should show error when no vehicles are selected', () => {
            // Deselect all vehicles
            viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.checkedStates([
                ko.observable(false),
                ko.observable(false)
            ]);

            viewModel.Send();

            expect(viewModel.ShowError).toHaveBeenCalledWith(
                'Please select at least one vehicle to send the broadcast message.'
            );
        });

        it('should prepare correct configuration for selected vehicles', () => {
            // Mock console.log to capture the configuration
            const originalConsoleLog = console.log;
            const mockConsoleLog = vi.fn();
            console.log = mockConsoleLog;

            viewModel.Send();

            // Verify the configuration logged
            expect(mockConsoleLog).toHaveBeenCalledWith(
                'Sending broadcast message with configuration:',
                {
                    vehicleIds: ['vehicle-1'],
                    broadcastMessage: viewModel.BroadcastMessageObject()
                }
            );

            // Restore console.log
            console.log = originalConsoleLog;
        });

        it('should call onSendSuccess when configuration is valid', () => {
            viewModel.Send();

            // Verify the success handler is available in configuration
            expect(viewModel.onSendSuccess).toBeDefined();
        });

        it('should handle error callback when configuration is valid', () => {
            viewModel.Send();

            // Verify the error handler is available and is ShowError
            expect(viewModel.ShowError).toBeDefined();
        });
    });
});
