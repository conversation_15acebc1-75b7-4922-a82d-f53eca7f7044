﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;


namespace FleetXQ.Data.DataProviders.Custom
{
    public class AllEmailSubscriptionStoreProcedureDataProvider : DataProvider<AllEmailSubscriptionStoreProcedureDataObject>
    {
        protected readonly IConfiguration _configuration;
        public AllEmailSubscriptionStoreProcedureDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<AllEmailSubscriptionStoreProcedureDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetEmailSubscription", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }

        protected override async Task DoDeleteAsync(AllEmailSubscriptionStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<AllEmailSubscriptionStoreProcedureDataObject> DoGetAsync(AllEmailSubscriptionStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DataObjectCollection<AllEmailSubscriptionStoreProcedureDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<AllEmailSubscriptionStoreProcedureDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetEmailSubscription", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<AllEmailSubscriptionStoreProcedureDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, VehicleId, LoggedHours, and SeatHours
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.ReportSubscriptionId = reader.GetGuid(reader.GetOrdinal("ReportSubscriptionId"));
                                    entity.FrequencyDays = reader.GetString(reader.GetOrdinal("FrequencyDays"));

                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GetEmailSubscription data", "Unable to get GetEmailSubscription data", ex);
                    }
                }
            }
        }

        protected override async Task<AllEmailSubscriptionStoreProcedureDataObject> DoSaveAsync(AllEmailSubscriptionStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
