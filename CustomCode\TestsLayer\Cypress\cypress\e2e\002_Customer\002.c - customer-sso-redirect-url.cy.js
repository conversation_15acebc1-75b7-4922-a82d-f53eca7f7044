describe("002.c - Customer SSO Redirect URL Test", () => {
    beforeEach(() => {
        // Prevent uncaught exceptions from failing tests
        Cypress.on('uncaught:exception', (err, runnable) => {
            console.log('Uncaught exception:', err.message);
            return false;
        });
        cy.login();
    });

    it("verifies customer SSO redirect URL dropdown functionality", () => {
        // Wait after login
        cy.wait(5000);

        // Click Administration menu
        cy.get("[data-test-id='24d0ddb5-9a41-46dd-b9a1-b4e6b333e848']")
            .first()
            .click({ force: true });

        // Click Customer SSO under Administration
        cy.get("[data-test-id='35fe1cf1-99ef-406f-9359-8a3609503aa9']")
            .first()
            .click({ force: true });

        // Wait for the customer list to load
        cy.wait(2000);

        // Click on the first customer in the list
        // Looking for any tr with pointer class that has a company name link
        cy.get('tr.pointer')
            .first()
            .within(() => {
                // Find and click the first cell (which contains the company name link)
                cy.get('td')
                    .first()
                    .click();
            });

        // Click View Detail button
        cy.get("[data-test-id='ad67fa30-7503-43f8-96c5-8e28ae809a8b']")
            .first()
            .click({ force: true });

        // Wait for details to load
        cy.wait(2000);

        // Verify Redirect URL field
        cy.get("[data-test-id='view_21ae6bda-17a4-414c-913c-a0ce0da40524']")
            .should('be.visible')
            .should('exist')
            .then($element => {
                // Verify the element contains text (e.g., "Development")
                expect($element.text().trim()).to.not.be.empty;

                // Log the current value
                cy.log(`Current Redirect URL value: ${$element.text().trim()}`);
            });

        // Since this is a read-only view, we can verify:
        // 1. The element exists
        // 2. It has a value
        // 3. It's visible to the user
        // 4. It's within the form-field-control-container
        cy.get('.form-field-control-container')
            .find("[data-test-id='view_21ae6bda-17a4-414c-913c-a0ce0da40524']")
            .should('exist')
            .should('be.visible')
            .invoke('text')
            .should('not.be.empty');
    });
}); 