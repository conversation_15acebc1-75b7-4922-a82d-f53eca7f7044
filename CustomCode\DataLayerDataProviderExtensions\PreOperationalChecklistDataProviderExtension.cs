using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class PreOperationalChecklistDataProviderExtension : IDataProviderExtension<PreOperationalChecklistDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;

        public PreOperationalChecklistDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSave += DataProvider_OnBeforeSave;
        }

        private async Task DataProvider_OnBeforeSave(OnBeforeSaveEventArgs arg)
        {
            var preOpChecklist = arg.Entity as PreOperationalChecklistDataObject;
            if (preOpChecklist == null || !preOpChecklist.SiteChecklistId.HasValue)
                return;

            // If the question is not active, keep its original order
            if (!preOpChecklist.Active)
            {
                return;
            }

            // Get all active checklists for the same site checklist, excluding the current question
            var activeChecklists = await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(
                null,
                "SiteChecklistId == @0 AND Active == @1 AND Id != @2",
                new object[] { preOpChecklist.SiteChecklistId.Value, true, preOpChecklist.Id }
            );

            // If the question has an order and it's not used by any active question, use it
            if (preOpChecklist.Order > 0 && !activeChecklists.Any(x => x.Order == preOpChecklist.Order))
            {
                return; // Use the provided order
            }

            // Find the minimum available order number
            var usedOrders = activeChecklists.Select(q => q.Order).OrderBy(o => o).ToList();
            short minOrder = 1;

            // If there are no active checklists, use 1
            if (!usedOrders.Any())
            {
                preOpChecklist.Order = minOrder;
                return;
            }

            // Find the first gap in the sequence
            for (int i = 0; i < usedOrders.Count; i++)
            {
                if (usedOrders[i] != i + 1)
                {
                    minOrder = (short)(i + 1);
                    break;
                }
                minOrder = (short)(usedOrders[i] + 1);
            }

            // Set the minimum available order
            preOpChecklist.Order = minOrder;
        }
    }
}