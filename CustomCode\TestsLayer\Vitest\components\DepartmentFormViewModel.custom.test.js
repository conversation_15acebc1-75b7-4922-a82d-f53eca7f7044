import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('DepartmentFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Mock window.location
        global.window = {
            location: {
                reload: vi.fn(),
                hash: ''
            }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Messages: {
                    confirmDeleteMessage: 'Are you sure you want to delete %ENTITY%?',
                    confirmDeletePopupTitle: 'Confirm Delete'
                }
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Department/DepartmentFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        HasCustomersAccess: 'True',
                        CanEditDepartment: 'True'
                    })
                }
            }
        };

        // Create base view model with required properties
        viewModel = {
            StatusData: {
                DisplayMode: ko.observable('view'),
                IsEmpty: ko.observable(false)
            },
            DepartmentObject: ko.observable({
                Data: {
                    Id: ko.observable('123')
                }
            }),
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            closePopup: vi.fn(),
            ShowError: vi.fn(),
            onDeleteSuccess: vi.fn(),
            controller: {
                applicationController: {
                    showConfirmPopup: vi.fn(),
                    getProxyForComponent: vi.fn().mockReturnValue({
                        SoftDelete: vi.fn()
                    })
                }
            },
            DataStore: {
                CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.DepartmentFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('IsModifyCommandVisible', () => {
        it('should return true when user has all required permissions', () => {
            expect(customViewModel.IsModifyCommandVisible()).toBe(true);
        });

        it('should return false when user does not have CanEditDepartment permission', () => {
            global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                HasCustomersAccess: 'True',
                CanEditDepartment: 'False'
            });
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false when user does not have HasCustomersAccess permission', () => {
            global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                HasCustomersAccess: 'False',
                CanEditDepartment: 'True'
            });
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false when in edit mode', () => {
            viewModel.StatusData.DisplayMode('edit');
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false when data is empty', () => {
            viewModel.StatusData.IsEmpty(true);
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });
    });

    describe('Delete functionality', () => {
        it('should show confirmation popup when Delete is called', () => {
            viewModel.Delete();
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                FleetXQ.Web.Messages.confirmDeleteMessage.replace(/%ENTITY%/g, "Department"),
                FleetXQ.Web.Messages.confirmDeletePopupTitle,
                expect.any(Function),
                viewModel.contextId
            );
        });

        it('should call SoftDelete when confirmation is accepted', () => {
            const departmentAPI = viewModel.controller.applicationController.getProxyForComponent("DepartmentAPI");

            // Simulate confirmation
            viewModel.onConfirmDelete(true);

            expect(departmentAPI.SoftDelete).toHaveBeenCalledWith({
                caller: viewModel,
                contextId: viewModel.contextId,
                successHandler: expect.any(Function),
                errorHandler: viewModel.ShowError,
                departmentId: '123'
            });
        });

        it('should not call SoftDelete when confirmation is rejected', () => {
            const departmentAPI = viewModel.controller.applicationController.getProxyForComponent("DepartmentAPI");

            // Simulate rejection
            viewModel.onConfirmDelete(false);

            expect(departmentAPI.SoftDelete).not.toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });

        it('should handle successful deletion', () => {
            const departmentAPI = viewModel.controller.applicationController.getProxyForComponent("DepartmentAPI");
            let capturedSuccessHandler;

            // Mock the SoftDelete function to capture the success handler
            departmentAPI.SoftDelete = vi.fn((config) => {
                capturedSuccessHandler = config.successHandler;
            });

            // Trigger the delete confirmation
            viewModel.onConfirmDelete(true);

            // Simulate successful deletion
            capturedSuccessHandler({ success: true });

            expect(viewModel.onDeleteSuccess).toHaveBeenCalledWith({ success: true });
            expect(viewModel.closePopup).toHaveBeenCalledWith(true);
        });
    });
}); 