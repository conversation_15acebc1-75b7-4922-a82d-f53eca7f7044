using FleetXQ.BusinessLayer.Components.Server.Custom.Tests;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class CardDataProviderExtensionTests : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"CardDataProviderExtensionTests-{Guid.NewGuid()}";

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            CreateTestDatabase(_testDatabaseName);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<SiteDataObject> CreateTestSiteAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            return await _dataFacade.SiteDataProvider.SaveAsync(site);
        }

        private async Task<DepartmentDataObject> CreateTestDepartmentAsync(SiteDataObject site)
        {
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            return await _dataFacade.DepartmentDataProvider.SaveAsync(department);
        }

        private async Task<PersonDataObject> CreateTestPersonAsync(SiteDataObject site, Guid departmentId, bool isActiveDriver = true)
        {
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.FirstName = "Test";
            person.LastName = "Person";
            person.SiteId = site.Id;
            person.DepartmentId = departmentId;
            person.CustomerId = site.CustomerId;
            person.IsDriver = true;
            person.IsActiveDriver = isActiveDriver;
            return await _dataFacade.PersonDataProvider.SaveAsync(person);
        }

        private async Task<CardDataObject> CreateTestCardAsync(PersonDataObject person, string cardNumber, string facilityCode, CardTypeEnum cardType, KeypadReaderEnum keypadReader)
        {
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.CardNumber = cardNumber;
            card.FacilityCode = facilityCode;
            card.Type = cardType;
            card.KeypadReader = keypadReader;
            card.Active = true;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var driver = person.Driver;
            driver.CardDetailsId = card.Id;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            card.Driver = driver;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            return card;
        }

        [Test]
        public async Task OnBeforeSaveDataSet_WithInactiveDriver_ClearsWeigand()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department.Id, isActiveDriver: false);
            var card = await CreateTestCardAsync(person, "12345", "1", CardTypeEnum.CardID, KeypadReaderEnum._48BitWeigand);

            person = await _dataFacade.PersonDataProvider.GetAsync(person);
            var driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();
            card.Weigand = "123456"; // Set initial Weigand

            // Act
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);
            driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Assert
            Assert.That(card.Weigand, Is.Null);
        }

        [Test]
        public async Task OnBeforeSaveDataSet_WithCaptureCard_RemovesLeadingZeros()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department.Id);
            var card = await CreateTestCardAsync(person, "12345", "1", CardTypeEnum.CardID, KeypadReaderEnum.CaptureCard);

            person = await _dataFacade.PersonDataProvider.GetAsync(person);
            var driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            card.Weigand = "000123456"; // Set Weigand with leading zeros

            // Act
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);
            driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Assert
            Assert.That(card.Weigand, Is.EqualTo("123456"));
        }

        [Test]
        public async Task OnBeforeSaveDataSet_WithPinIDAndSmartReader_GeneratesCorrectWeigand()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department.Id);
            var card = await CreateTestCardAsync(person, "1234", "1", CardTypeEnum.PinID, KeypadReaderEnum.Smart);

            person = await _dataFacade.PersonDataProvider.GetAsync(person);
            var driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Act
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);
            driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Assert
            Assert.That(card.Weigand, Is.Not.Null);
            Assert.That(card.Weigand, Is.Not.Empty);
            Assert.That(card.Weigand, Does.Match(@"^[0-9A-F]+$")); // Should be a valid hex string
        }

        [Test]
        public async Task OnBeforeSaveDataSet_WithPinIDAndRosslareReader_GeneratesCorrectWeigand()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department.Id);
            var card = await CreateTestCardAsync(person, "1234", "1", CardTypeEnum.PinID, KeypadReaderEnum.Rosslare);

            person = await _dataFacade.PersonDataProvider.GetAsync(person);
            var driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Act
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);
            driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Assert
            Assert.That(card.Weigand, Is.Not.Null);
            Assert.That(card.Weigand, Is.Not.Empty);
            Assert.That(card.Weigand, Does.Match(@"^[0-9A-F]+$")); // Should be a valid hex string
        }

        [Test]
        public async Task OnBeforeSaveDataSet_WithCardIDAnd48BitWeigand_GeneratesCorrectWeigand()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department.Id);
            var card = await CreateTestCardAsync(person, "12345", "1", CardTypeEnum.CardID, KeypadReaderEnum._48BitWeigand);

            person = await _dataFacade.PersonDataProvider.GetAsync(person);
            var driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Act
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);
            driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Assert
            Assert.That(card.Weigand, Is.Not.Null);
            Assert.That(card.Weigand, Is.Not.Empty);
            Assert.That(card.Weigand, Does.Match(@"^[0-9A-F]+$")); // Should be a valid hex string
        }

        [Test]
        public async Task OnBeforeSaveDataSet_WithFailedWeigandGeneration_ThrowsException()
        {
            // Arrange
            var site = await CreateTestSiteAsync();
            var department = await CreateTestDepartmentAsync(site);
            var person = await CreateTestPersonAsync(site, department.Id);
            var card = await CreateTestCardAsync(person, "invalid", "1", CardTypeEnum.CardID, KeypadReaderEnum._48BitWeigand);

            person = await _dataFacade.PersonDataProvider.GetAsync(person);
            var driver = await person.LoadDriverAsync();
            card = await driver.LoadCardAsync();

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() => _dataFacade.PersonDataProvider.SaveAsync(person));
            Assert.That(ex.Message, Does.StartWith("Error generating Weigand code:"));
        }
    }
}