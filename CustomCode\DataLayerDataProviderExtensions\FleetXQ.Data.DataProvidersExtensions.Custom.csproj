﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Business Layer Data Provider Custom Extensions</Description>
  </PropertyGroup>  
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" />
    <PackageReference Include="Microsoft.Azure.Devices" />
    <PackageReference Include="Microsoft.CSharp" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="NLog" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
  </ItemGroup>
</Project>