﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;


namespace FleetXQ.Data.DataProviders.Custom
{
    public class GeneralProductivityPerDriverViewLatestDataProvider : DataProvider<GeneralProductivityPerDriverViewLatestDataObject>
    {
        protected readonly IConfiguration _configuration;

        public GeneralProductivityPerDriverViewLatestDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<GeneralProductivityPerDriverViewLatestDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetGeneralProductivityPerDriver", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }

        protected override async Task DoDeleteAsync(GeneralProductivityPerDriverViewLatestDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<GeneralProductivityPerDriverViewLatestDataObject> DoGetAsync(GeneralProductivityPerDriverViewLatestDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetGeneralProductivityPerDriver", connection))
                {
                    GeneralProductivityPerDriverViewLatestDataObject result = null;

                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@DriverId", SqlDbType.UniqueIdentifier) { Value = entity.DriverId });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                result = _serviceProvider.GetRequiredService<GeneralProductivityPerDriverViewLatestDataObject>();
                                result.IsNew = false;

                                // Assuming the stored procedure returns Id, TimeSlot, NumberOfRedImpacts, and NumberOfAmberImpacts
                                result.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                result.DriverId = reader.GetGuid(reader.GetOrdinal("DriverId"));
                                result.GeneralProductivityViewId = reader.GetGuid(reader.GetOrdinal("GeneralProductivityViewId"));
                                result.TotalDuration = reader.GetString(reader.GetOrdinal("TotalDuration"));
                                result.TotalSeatHours = reader.GetString(reader.GetOrdinal("TotalSeatHours"));
                                result.TotalHydraulicHours = reader.GetString(reader.GetOrdinal("TotalHydraulicHours"));
                                result.TotalTractionHours = reader.GetString(reader.GetOrdinal("TotalTractionHours"));
                                result.PercentageOfActivity = reader.GetString(reader.GetOrdinal("PercentageOfActivity"));
                                result.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));

                                context.AddObject(result);
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GeneralProductivityPerDriverViewLatestDataObject data", "Unable to get GeneralProductivityPerDriverViewLatestDataObject data", ex);
                    }
                }
            }
        }

        protected override async Task<DataObjectCollection<GeneralProductivityPerDriverViewLatestDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<GeneralProductivityPerDriverViewLatestDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetGeneralProductivityPerDriver", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<GeneralProductivityPerDriverViewLatestDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, VehicleId, LoggedHours, and SeatHours
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.DriverId = reader.GetGuid(reader.GetOrdinal("DriverId"));
                                    entity.GeneralProductivityViewId = reader.GetGuid(reader.GetOrdinal("GeneralProductivityViewId"));
                                    entity.TotalDuration = reader.GetString(reader.GetOrdinal("TotalDuration"));
                                    entity.TotalSeatHours = reader.GetString(reader.GetOrdinal("TotalSeatHours"));
                                    entity.TotalHydraulicHours = reader.GetString(reader.GetOrdinal("TotalHydraulicHours"));
                                    entity.TotalTractionHours = reader.GetString(reader.GetOrdinal("TotalTractionHours"));
                                    entity.PercentageOfActivity = reader.GetString(reader.GetOrdinal("PercentageOfActivity"));
                                    entity.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));

                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get GeneralProductivityPerDriverViewLatest data", "Unable to get GeneralProductivityPerDriverViewLatest data", ex);
                    }
                }
            }
        }

        protected override async Task<GeneralProductivityPerDriverViewLatestDataObject> DoSaveAsync(GeneralProductivityPerDriverViewLatestDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
