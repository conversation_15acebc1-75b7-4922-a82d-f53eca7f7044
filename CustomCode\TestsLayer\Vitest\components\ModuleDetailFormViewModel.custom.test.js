import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('ModuleDetailFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console functions
        global.console.log = vi.fn();
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Module/ModuleDetailFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            ModuleObject: ko.observable({
                Data: {
                    Id: ko.observable('test-id'),
                    Status: ko.observable(0),
                    IsNew: ko.observable(false)
                },
                StatusData: {
                    isValid: ko.observable(true)
                }
            }),
            StatusData: {
                IsVisible: ko.observable(true),
                IsEnabled: ko.observable(true),
                DisplayMode: ko.observable('view'),
                IsEmpty: ko.observable(false)
            },
            controller: {
                applicationController: {
                    getContextProperty: vi.fn()
                }
            },
            contextId: 'test-context'
        };

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.ModuleDetailFormViewModelCustom(viewModel);
    });

    it('should make the Status field editable when status is not 2', () => {
        // Set status to a value other than 2
        viewModel.ModuleObject().Data.Status(1);
        
        // Check if IsStatusReadOnly returns false (editable)
        expect(customViewModel.IsStatusReadOnly()).toBe(false);
        
        // Verify console.log was called with correct status value
        expect(console.log).toHaveBeenCalledWith("Module Status Value:", 1);
    });

    it('should make the Status field read-only when status is 2', () => {
        // Set status to 2 (which should make it read-only)
        viewModel.ModuleObject().Data.Status(2);
        
        // Check if IsStatusReadOnly returns true (read-only)
        expect(customViewModel.IsStatusReadOnly()).toBe(true);
        
        // Verify console.log was called with correct status value
        expect(console.log).toHaveBeenCalledWith("Module Status Value:", 2);
    });

    it('should handle null ModuleObject gracefully', () => {
        // Set ModuleObject to null
        viewModel.ModuleObject(null);
        
        // Check if IsStatusReadOnly handles this case
        expect(customViewModel.IsStatusReadOnly()).toBe(false);
        
        // Verify no error occurred and no console.log was called with status value
        expect(console.error).not.toHaveBeenCalled();
    });

    it('should handle exceptions when accessing Status value', () => {
        // Create an object with Data but without Status function
        viewModel.ModuleObject({
            Data: {}
        });
        
        // Check if IsStatusReadOnly handles this case
        expect(customViewModel.IsStatusReadOnly()).toBe(false);
        
        // Verify error was logged
        expect(console.error).toHaveBeenCalled();
    });
}); 