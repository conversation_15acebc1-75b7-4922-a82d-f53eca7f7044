﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.DependencyInjection;

namespace FleetXQ.Client.Model
{
    public static class CustomContainer
    {
        public static void RegisterCustomTypes(IServiceCollection services)
        {
            //TODO : register here your custom types
            // example:
            // service.AddScoped<IDataProviderExtension<MyDataObject>, MyDataProviderExtension>();
            // other example: (no singleton here to avoid collisions)
            //service.AddTransient<IImportExportComponentExtension<MyImportComponent, MyDataObject>, MyImportExtension>();



        }
    }
}
