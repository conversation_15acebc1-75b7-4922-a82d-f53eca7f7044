describe("003 - Sites Flow", () => {
    let uniqueSiteName;
    let tempCompanyName;
   
    before(() => {
        // Load test data from fixture
        cy.fixture('testData').then((testData) => {
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            tempCompanyName = testData.tempCompanyName;
        });
    });
    beforeEach(() => {
        // Perform the login using the login command
        cy.login();

        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company else if not found continue and create
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(tempCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // CHECK IF THE COMPANY IS FOUND OR NOT
        cy.get('body').then($body => {
            // Check if the no-data-message exists and is visible
            if ($body.find('.no-data-message > span:visible').length > 0) {
                cy.log('Company not found, please customer.cy to create a company');
                cy.fail('Company not found, please customer.cy to create a company');
            }
        });

        // Customer data found, select the customer
        cy.get('td[data-bind="jqStopBubble: \'a\'"]')
        .should('exist')
        .should('be.visible')
        .first()  // Select the first matching element if multiple exist
        .click();

        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-2"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);
        
    });

    it("Should Create and verifies site creation in table", () => {
        // Check if "No Site data available" exists or if there are existing sites
        cy.get('body').then($body => {
            const noSiteMessage = $body.find('#CustomerFormControl-CustomerForm-SitesGrid > [data-bind="css: { hideElt : false }"] > :nth-child(3) > .no-data-message > span');
            
            if (noSiteMessage.is(':visible') && noSiteMessage.text().includes('No Site data')) {  
                // Click create button for new site
                cy.get('#CustomerFormControl-CustomerForm-SitesGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(1) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);
                cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('exist')
                    .should('be.visible')
                    .clear()
                    .type(uniqueSiteName);
        
                cy.get('.create-new-form-timezone-input-custom > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input')
                    .should('exist')
                    .should('be.visible')
                    .click();
        
                // select the first item from the dropdown
                cy.get('html > body > [data-test-id="lookup_wrapper"] > li:nth-of-type(1) > [data-test-id="lookup_item"]')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.get('.save')
                    .should('exist')
                    .should('be.visible')
                    .click();
                
                cy.wait(1000);

                // Verify the site was created
                cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
                    .should('exist')
                    .should('be.visible')
                    .should('contain', uniqueSiteName);

            } else {
               cy.log('Site already exists, skipping creation');
            }
        });

    });

    it("Should update the site name and timezone", () => {
        // Sites already exist, click on an existing site
        cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
        .should('exist')
        .should('be.visible')
        .first()
        .click();
        cy.wait(1000);
        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);
        cy.get('#Commands > .btn-group > .edit')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);
        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type(uniqueSiteName + " - Updated");

        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsTimezoneVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('exist')
            .should('be.visible')
            .click();

        // select the first item from the dropdown
        cy.get('html > body > [data-test-id="lookup_wrapper"] > li:nth-of-type(3) > [data-test-id="lookup_item"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get(':nth-child(1) > :nth-child(1) > .basicForm > .edit.optionalField > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text')
            .should('exist')
            .should('be.visible')
            .clear()
            .type("Test Address");

        cy.get('.save')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // Verify the site is updated
        cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
            .should('exist')
            .should('be.visible')
            .should('contain', uniqueSiteName + " - Updated");

        // revert the site name to the original name
        cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
        .should('exist')
        .should('be.visible')
        .first()
        .click();
        cy.wait(1000);
        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();
        cy.wait(1000);
        cy.get('#Commands > .btn-group > .edit')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);
        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type(uniqueSiteName);
        
            cy.get('.save')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // Verify the site is updated
        cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
            .should('exist')
            .should('be.visible')
            .should('contain', uniqueSiteName);
    });
        
});
