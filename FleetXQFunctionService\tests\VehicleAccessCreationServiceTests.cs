using NUnit.Framework;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using NSubstitute;
using System.Collections.Generic;
using System.Net.Http;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Reflection;
using System;
using FleetXQFunctionService.Services;
using FleetXQFunctionService.Messages;

namespace FleetXQFunctionService.Tests
{
    [TestFixture]
    public class VehicleAccessCreationServiceTests
    {
        private VehicleAccessCreationService _service;
        private ILogger<VehicleAccessCreationService> _logger;
        private IConfiguration _configuration;

        [SetUp]
        public void Setup()
        {
            _logger = Substitute.For<ILogger<VehicleAccessCreationService>>();
            _configuration = Substitute.For<IConfiguration>();
            _service = new VehicleAccessCreationService(_logger, _configuration);
        }

        [TearDown]
        public void TearDown()
        {
            _service?.Dispose();
        }

        [Test]
        [TestCase("Vehicle not found", false)]
        [TestCase("Vehicle does not exist", false)]
        [TestCase("error occurred", false)]
        [TestCase("failed to process", false)]
        [TestCase("invalid request", false)]
        [TestCase("unauthorized access", false)]
        [TestCase("No site accesses found", true)] // Business scenario - should not retry
        [TestCase("success", true)]
        [TestCase("created successfully", true)]
        [TestCase("completed", true)]
        [TestCase("processed", true)]
        [TestCase("", false)]
        [TestCase("   ", false)]
        [TestCase(null, false)]
        public void IsSuccessfulResponse_ShouldReturnCorrectResult(string responseContent, bool expectedResult)
        {
            // Arrange & Act
            var result = InvokeIsSuccessfulResponse(responseContent);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test]
        [TestCase("{\"success\": true}", true)]
        [TestCase("{\"success\": false}", false)]
        [TestCase("{\"error\": \"some error\"}", false)]
        [TestCase("{\"status\": \"success\"}", true)]
        [TestCase("{\"status\": \"ok\"}", true)]
        [TestCase("{\"status\": \"completed\"}", true)]
        [TestCase("{\"status\": \"failed\"}", false)]
        [TestCase("{\"status\": \"error\"}", false)]
        public void IsSuccessfulResponse_WithJsonResponse_ShouldReturnCorrectResult(string jsonResponse, bool expectedResult)
        {
            // Arrange & Act
            var result = InvokeIsSuccessfulResponse(jsonResponse);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        [Test]
        public void IsSuccessfulResponse_WithInvalidJson_ShouldReturnFalse()
        {
            // Arrange
            var invalidJson = "{invalid json}";

            // Act
            var result = InvokeIsSuccessfulResponse(invalidJson);

            // Assert
            Assert.That(result, Is.False);
        }

        [Test]
        public void IsSuccessfulResponse_WithAmbiguousResponse_ShouldReturnFalse()
        {
            // Arrange
            var ambiguousResponse = "Some response without clear indicators";

            // Act
            var result = InvokeIsSuccessfulResponse(ambiguousResponse);

            // Assert
            // When we can't determine success/failure clearly, treat as failure to be safe
            Assert.That(result, Is.False);
        }

        [Test]
        [TestCase("\"Vehicle not found\"", false)] // JSON string response
        [TestCase("\"success\"", true)] // JSON string response
        public void IsSuccessfulResponse_WithQuotedStrings_ShouldReturnCorrectResult(string quotedResponse, bool expectedResult)
        {
            // Arrange & Act
            var result = InvokeIsSuccessfulResponse(quotedResponse);

            // Assert
            Assert.That(result, Is.EqualTo(expectedResult));
        }

        private bool InvokeIsSuccessfulResponse(string responseContent)
        {
            // Use reflection to invoke the private IsSuccessfulResponse method
            var method = typeof(VehicleAccessCreationService).GetMethod("IsSuccessfulResponse", BindingFlags.NonPublic | BindingFlags.Instance);
            return (bool)method.Invoke(_service, new object[] { responseContent });
        }
    }
}