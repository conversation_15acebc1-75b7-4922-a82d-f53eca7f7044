﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using VDS.RDF;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2019.Presentation;
using FleetXQ.BusinessLayer.Components.Server.Custom;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class PreopChecklistAPITest : TestBase
    {
        private IPreopChecklistAPI _preopChecklistAPI;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"PreopChecklistAPITests-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUp()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _preopChecklistAPI = _serviceProvider.GetRequiredService<IPreopChecklistAPI>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task ActivateQuestion_ValidPreOpChecklistId_ActivatesQuestionAsExpected()
        {
            // Arrange
            var preOpChecklist = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            Assert.That(preOpChecklist, Is.Not.Null, "Test data setup failed: No PreOperationalChecklist found.");

            // Act
            var result = await _preopChecklistAPI.ActivateQuestionAsync(preOpChecklist.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null, "ActivateQuestion should return a result.");
            Assert.That(result.Result.Active, Is.True, "PreOperationalChecklist should be activated.");
        }

        [Test]
        public async Task StoreChecklistDetails_ValidData_StoresSuccessfully()
        {
            // Arrange
            var session = (await _dataFacade.SessionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
            //get card        
            var person = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { vehicle.SiteId }, skipSecurity: true)).FirstOrDefault();
            var card = await (await person.LoadDriverAsync(skipSecurity: true)).LoadCardAsync(skipSecurity: true);

            string validMessage = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",64D8556C OPCKS=64D8556C,64D8556C,0,0\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";

            // Act
            var response = await _preopChecklistAPI.StoreCheclistDetailsAsync(validMessage);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result.Contains("Success"), Is.True);
        }

        [Test]
        public async Task StoreChecklistDetails_InvalidData_ThrowsException()
        {
            // Arrange
            string invalidMessage = "{\"event_type\":\"OPCKS\",\"payload\":\"INVALID_PAYLOAD\"}";

            // Act & Assert
            try
            {
                await _preopChecklistAPI.StoreCheclistDetailsAsync(invalidMessage);
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Is.Not.Null, "Expected GOServerException for invalid payload.");
            }
        }

        [Test]
        public async Task StoreChecklistDetails_DuplicateEntry_IgnoresDuplicate()
        {
            // Arrange
            var session = (await _dataFacade.SessionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
            var person = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { vehicle.SiteId }, skipSecurity: true)).FirstOrDefault();
            var card = await (await person.LoadDriverAsync(skipSecurity: true)).LoadCardAsync(skipSecurity: true);
            var preOpChecklist = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();

            // Create message with a specific question ID
            string message = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",64D8556C OPCKS=64D8556C,64D8556C," + preOpChecklist.Id + ",1\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";

            // Act - First submission
            await _preopChecklistAPI.StoreCheclistDetailsAsync(message);

            // Get count of checklist details before second submission
            var beforeCount = (await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, skipSecurity: true)).Count;

            // Act - Second submission (duplicate)
            await _preopChecklistAPI.StoreCheclistDetailsAsync(message);

            // Get count after second submission
            var afterCount = (await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, skipSecurity: true)).Count;

            // Assert
            Assert.That(afterCount, Is.EqualTo(beforeCount), "Duplicate submission should not create new checklist detail");
        }

        [Test]
        public async Task StoreChecklistDetails_MultipleUniqueEntries_StoresSuccessfully()
        {
            // Arrange
            var session = (await _dataFacade.SessionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
            var person = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { vehicle.SiteId }, skipSecurity: true)).FirstOrDefault();
            var card = await (await person.LoadDriverAsync(skipSecurity: true)).LoadCardAsync(skipSecurity: true);

            // Get initial state and examine existing details
            var initialDetails = await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, skipSecurity: true);
            var initialCount = initialDetails.Count;
            Console.WriteLine($"Initial checklist details count: {initialCount}");
            foreach (var detail in initialDetails)
            {
                Console.WriteLine($"Existing detail - ID: {detail.Id}, Question ID: {detail.PreOperationalChecklistId}, Result ID: {detail.ChecklistResultId}");
            }

            // Get all available questions first
            var allQuestions = await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Console.WriteLine($"Total available questions: {allQuestions.Count}");

            // Get existing question IDs to exclude
            var existingQuestionIds = initialDetails.Select(d => d.PreOperationalChecklistId).ToList();
            Console.WriteLine($"Excluding {existingQuestionIds.Count} existing question IDs");

            // Get two different questions that haven't been answered yet
            var preOpChecklists = allQuestions
                .Where(q => !existingQuestionIds.Contains(q.Id)) // Exclude questions that have already been answered
                .GroupBy(x => x.SiteChecklistId)
                .Where(g => g.Count() > 0)
                .Select(g => g.First())
                .Take(2)
                .ToList();

            Console.WriteLine($"Selected questions count: {preOpChecklists.Count}");
            foreach (var q in preOpChecklists)
            {
                Console.WriteLine($"Question ID: {q.Id}, SiteChecklistId: {q.SiteChecklistId}");
                // Verify this question hasn't been answered
                Assert.That(!existingQuestionIds.Contains(q.Id),
                    $"Selected question {q.Id} has already been answered");
            }

            Assert.That(preOpChecklists.Count, Is.EqualTo(2), "Test requires two different questions");
            Assert.That(preOpChecklists[0].Id, Is.Not.EqualTo(preOpChecklists[1].Id), "Questions must be different");
            Assert.That(preOpChecklists[0].SiteChecklistId, Is.Not.EqualTo(preOpChecklists[1].SiteChecklistId), "Questions must be from different checklists");

            // Submit answers for two different questions
            foreach (var checklist in preOpChecklists)
            {
                string message = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",64D8556C OPCKS=64D8556C,64D8556C," + checklist.Id + ",1\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";
                var response = await _preopChecklistAPI.StoreCheclistDetailsAsync(message);
                Assert.That(response.Result.Contains("Success"), Is.True, $"Failed to store checklist detail for question {checklist.Id}");

                // Verify each submission immediately
                var currentDetails = await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, skipSecurity: true);
                var currentDetail = currentDetails.FirstOrDefault(d => d.PreOperationalChecklistId == checklist.Id && d.ChecklistResultId == session.Id);
                Console.WriteLine($"After submitting question {checklist.Id}: Count = {currentDetails.Count}");
                Console.WriteLine($"Detail found: {(currentDetail != null ? "Yes" : "No")}, Detail ID: {currentDetail?.Id}, Result ID: {currentDetail?.ChecklistResultId}");
            }

            // Get final state and verify details
            var finalDetails = await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, skipSecurity: true);
            var newDetails = finalDetails.Where(d => !initialDetails.Any(i => i.Id == d.Id)).ToList();

            Console.WriteLine($"Final checklist details count: {finalDetails.Count}");
            Console.WriteLine($"New details count: {newDetails.Count}");
            foreach (var detail in newDetails)
            {
                Console.WriteLine($"New detail - ID: {detail.Id}, Question ID: {detail.PreOperationalChecklistId}, Result ID: {detail.ChecklistResultId}");
            }

            // Detailed assertions
            Assert.That(finalDetails.Count, Is.EqualTo(initialCount + 2),
                $"Should have added exactly two new checklist details. Initial: {initialCount}, Final: {finalDetails.Count}");
            Assert.That(newDetails.Count, Is.EqualTo(2), "Should have found two new details");

            var distinctQuestionIds = newDetails.Select(d => d.PreOperationalChecklistId).Distinct().ToList();
            Assert.That(distinctQuestionIds.Count, Is.EqualTo(2),
                $"New details should be for different questions. Found {distinctQuestionIds.Count} distinct questions");

            // Verify the specific questions were saved
            Assert.That(distinctQuestionIds, Contains.Item(preOpChecklists[0].Id), "First question should be saved");
            Assert.That(distinctQuestionIds, Contains.Item(preOpChecklists[1].Id), "Second question should be saved");
        }

        [Test]
        public async Task StoreChecklistDetails_TimeoutScenario_CreatesSeparateBatches()
        {
            // Arrange
            var session = (await _dataFacade.SessionDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
            var person = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null, "SiteId == @0", new object[] { vehicle.SiteId }, skipSecurity: true)).FirstOrDefault();
            var card = await (await person.LoadDriverAsync(skipSecurity: true)).LoadCardAsync(skipSecurity: true);

            // Get two different questions to answer
            var allQuestions = await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, skipSecurity: true);
            var selectedQuestions = allQuestions.Take(2).ToList();
            Assert.That(selectedQuestions.Count, Is.EqualTo(2), "Test requires two different questions");

            // Get initial count of checklist results
            var initialResultsCount = (await _dataFacade.ChecklistResultDataProvider.GetCollectionAsync(null, skipSecurity: true)).Count;

            // First batch - Initial message with first timestamp (68068238) - just start/end time, no answers
            string firstBatchMessage = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",68068238 OPCKS=68068238,68068238,0,0\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";
            var firstResponse = await _preopChecklistAPI.StoreCheclistDetailsAsync(firstBatchMessage);
            Assert.That(firstResponse.Result.Contains("Success"), Is.True, "First batch message failed");

            // Check that first batch was created
            var resultsAfterFirstBatch = await _dataFacade.ChecklistResultDataProvider.GetCollectionAsync(null, skipSecurity: true);
            Assert.That(resultsAfterFirstBatch.Count, Is.EqualTo(initialResultsCount + 1), "First batch should create one new result");

            // Second batch - Later messages with different timestamp (680682C2)
            // Initial message for second batch
            string secondBatchInitialMessage = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",680682C2 OPCKS=680682C2,680682C2,0,0\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";
            var secondInitialResponse = await _preopChecklistAPI.StoreCheclistDetailsAsync(secondBatchInitialMessage);
            Assert.That(secondInitialResponse.Result.Contains("Success"), Is.True, "Second batch initial message failed");

            // Answer first question in second batch
            string secondBatchFirstAnswer = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",680682CC OPCKS=680682CC,680682C2," + selectedQuestions[0].Id + ",1\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";
            var secondFirstAnswerResponse = await _preopChecklistAPI.StoreCheclistDetailsAsync(secondBatchFirstAnswer);
            Assert.That(secondFirstAnswerResponse.Result.Contains("Success"), Is.True, "Second batch first answer failed");

            // Answer second question in second batch
            string secondBatchSecondAnswer = "{\"event_type\":\"OPCKS\",\"payload\":\"AUTH=" + card.Weigand + ",680682CC OPCKS=680682CC,680682C2," + selectedQuestions[1].Id + ",1\",\"session_id\":\"" + session.Id + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\"}";
            var secondSecondAnswerResponse = await _preopChecklistAPI.StoreCheclistDetailsAsync(secondBatchSecondAnswer);
            Assert.That(secondSecondAnswerResponse.Result.Contains("Success"), Is.True, "Second batch second answer failed");

            // Check final state
            var finalResults = await _dataFacade.ChecklistResultDataProvider.GetCollectionAsync(
                null,
                "SessionId1 == @0",
                new object[] { session.Id },
                skipSecurity: true);

            // We should have exactly two results for this session - one for each batch
            Assert.That(finalResults.Count, Is.EqualTo(2), "Should have exactly two checklist results for the session");

            // Check that the timestamps are different between the two batches
            var firstBatchResult = finalResults.FirstOrDefault(r => Math.Abs((r.StartTime - DataUtils.HexToUtcTime("68068238")).TotalSeconds) < 1);
            var secondBatchResult = finalResults.FirstOrDefault(r => Math.Abs((r.StartTime - DataUtils.HexToUtcTime("680682C2")).TotalSeconds) < 1);

            Assert.That(firstBatchResult, Is.Not.Null, "First batch result not found");
            Assert.That(secondBatchResult, Is.Not.Null, "Second batch result not found");
            Assert.That(firstBatchResult.Id, Is.Not.EqualTo(secondBatchResult.Id), "Batch results should have different IDs");

            // Check that the checklist details are associated with the correct batch
            var details = await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, skipSecurity: true);
            var firstBatchDetails = details.Where(d => d.ChecklistResultId == firstBatchResult.Id).ToList();
            var secondBatchDetails = details.Where(d => d.ChecklistResultId == secondBatchResult.Id).ToList();

            Assert.That(firstBatchDetails.Count, Is.EqualTo(0), "First batch should have no checklist details as it was incomplete");
            Assert.That(secondBatchDetails.Count, Is.EqualTo(2), "Second batch should have two checklist details");

            // Verify the second batch details have the expected question IDs
            var secondBatchQuestionIds = secondBatchDetails.Select(d => d.PreOperationalChecklistId).ToList();
            Assert.That(secondBatchQuestionIds, Contains.Item(selectedQuestions[0].Id), "Second batch should contain first question");
            Assert.That(secondBatchQuestionIds, Contains.Item(selectedQuestions[1].Id), "Second batch should contain second question");
        }

        [Test]
        public async Task ActivateQuestion_WithOrderConflict_AssignsMinimumAvailableOrder()
        {
            // Arrange
            // Create a site first
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.Name = "Test Site";
            site.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, skipSecurity: true)).First().Id; // Use existing customer
            site.TimezoneId = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null, skipSecurity: true)).First().Id; // Use existing timezone
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);
            Assert.That(site, Is.Not.Null, "Failed to create test Site.");

            // Create a department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id; // Use the created site's ID
            department.Active = true;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);
            Assert.That(department, Is.Not.Null, "Failed to create test Department.");

            // Create a new site checklist for this test
            var siteChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            siteChecklist.Id = Guid.NewGuid();
            siteChecklist.DepartmentId = department.Id; // Use the created department's ID
            siteChecklist.IsThaiEnabled = false; // Required field
            siteChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(siteChecklist, skipSecurity: true);
            Assert.That(siteChecklist, Is.Not.Null, "Failed to create test DepartmentChecklist.");

            // Create two questions with the same order
            var question1 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question1.Id = Guid.NewGuid();
            question1.SiteChecklistId = siteChecklist.Id;
            question1.Question = "Test Question 1";
            question1.Order = 1;
            question1.Active = true;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question1, skipSecurity: true);

            var question2 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question2.Id = Guid.NewGuid();
            question2.SiteChecklistId = siteChecklist.Id;
            question2.Question = "Test Question 2";
            question2.Order = 1; // Same order as question1
            question2.Active = false;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question2, skipSecurity: true);

            // Act
            var result = await _preopChecklistAPI.ActivateQuestionAsync(question2.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null, "ActivateQuestion should return a result.");
            Assert.That(result.Result.Active, Is.True, "Question should be activated.");
            Assert.That(result.Result.Order, Is.EqualTo(2), "Question should be assigned the next available order number.");
        }

        [Test]
        public async Task ActivateQuestion_WithGapsInOrder_AssignsMinimumAvailableOrder()
        {
            // Arrange
            // Create a site first
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.Name = "Test Site";
            site.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, skipSecurity: true)).First().Id; // Use existing customer
            site.TimezoneId = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null, skipSecurity: true)).First().Id; // Use existing timezone
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);
            Assert.That(site, Is.Not.Null, "Failed to create test Site.");

            // Create a department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id; // Use the created site's ID
            department.Active = true;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);
            Assert.That(department, Is.Not.Null, "Failed to create test Department.");

            // Create a new site checklist for this test
            var siteChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            siteChecklist.Id = Guid.NewGuid();
            siteChecklist.DepartmentId = department.Id; // Use the created department's ID
            siteChecklist.IsThaiEnabled = false; // Required field
            siteChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(siteChecklist, skipSecurity: true);
            Assert.That(siteChecklist, Is.Not.Null, "Failed to create test DepartmentChecklist.");

            // Create questions with gaps in order numbers
            var question1 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question1.Id = Guid.NewGuid();
            question1.SiteChecklistId = siteChecklist.Id;
            question1.Question = "Test Question 1";
            question1.Order = 1;
            question1.Active = true;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question1, skipSecurity: true);

            var question2 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question2.Id = Guid.NewGuid();
            question2.SiteChecklistId = siteChecklist.Id;
            question2.Question = "Test Question 2";
            question2.Order = 3; // Gap in order numbers
            question2.Active = true;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question2, skipSecurity: true);

            var question3 = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            question3.Id = Guid.NewGuid();
            question3.SiteChecklistId = siteChecklist.Id;
            question3.Question = "Test Question 3";
            question3.Order = 1; // Same order as question1
            question3.Active = false;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question3, skipSecurity: true);

            // Act
            var result = await _preopChecklistAPI.ActivateQuestionAsync(question3.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null, "ActivateQuestion should return a result.");
            Assert.That(result.Result.Active, Is.True, "Question should be activated.");
            Assert.That(result.Result.Order, Is.EqualTo(2), "Question should be assigned the minimum available order number.");
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();

            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            //Create IOs
            var iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "0";
            iofields.Description = "ignition";
            iofields.IOType = " ";
            iofields.CANBUS = false;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "SEAT";
            iofields.Description = "Canbus Seat Switch Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "HYDL";
            iofields.Description = "Canbus Hydrolic Raising Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "TRACK";
            iofields.Description = "Canbus Traction/Movement Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);

            var sites = new List<string> { "Site 1", "Site 2", "Site 3" };
            var IoTHubIds1 = new string[] { "test_00000001", "test_00000002", "test_00000003", "test_00000004", "test_00000005", "test_00000006", "test_00000007", "test_00000008", "test_00000009", "test_00000010" };
            var IoTHubIds2 = new string[] { "test_00000011", "test_00000012", "test_00000013", "test_00000014", "test_00000015", "test_00000016", "test_00000017", "test_00000018", "test_00000019", "test_00000020" };
            var IoTHubIds3 = new string[] { "test_00000021", "test_00000022", "test_00000023", "test_00000024", "test_00000025", "test_00000026", "test_00000027", "test_00000028", "test_00000029", "test_00000030" };
            var VehicleHireNos1 = new string[] { "VH1", "VH2", "VH3", "VH4", "VH5", "VH6", "VH7", "VH8", "VH9", "VH10" };
            var VehicleHireNos2 = new string[] { "VH11", "VH12", "VH13", "VH14", "VH15", "VH16", "VH17", "VH18", "VH19", "VH20" };
            var VehicleHireNos3 = new string[] { "VH21", "VH22", "VH23", "VH24", "VH25", "VH26", "VH27", "VH28", "VH29", "VH30" };
            var VehicleSerialNos1 = new string[] { "VS1", "VS2", "VS3", "VS4", "VS5", "VS6", "VS7", "VS8", "VS9", "VS10" };
            var VehicleSerialNos2 = new string[] { "VS11", "VS12", "VS13", "VS14", "VS15", "VS16", "VS17", "VS18", "VS19", "VS20" };
            var VehicleSerialNos3 = new string[] { "VS21", "VS22", "VS23", "VS24", "VS25", "VS26", "VS27", "VS28", "VS29", "VS30" };
            var PersonFirstName1 = new string[] { "John", "Peter", "Paul", "Mark", "Luke", "Matthew", "James", "Jude", "Simon", "Andrew" };
            var PersonFirstName2 = new string[] { "Mary", "Elizabeth", "Anna", "Ruth", "Esther", "Sarah", "Rebecca", "Leah", "Rachel", "Deborah" };
            var PersonFirstName3 = new string[] { "David", "Solomon", "Elijah", "Elisha", "Isaiah", "Jeremiah", "Ezekiel", "Daniel", "Hosea", "Joel" };
            var PersonLastName1 = new string[] { "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor" };
            var PersonLastName2 = new string[] { "Anderson", "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson", "Garcia", "Martinez", "Robinson" };
            var PersonLastName3 = new string[] { "Clark", "Rodriguez", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "Hernandez", "King" };

            foreach (var siteName in sites)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.CustomerId = customer.Id;
                site.Name = siteName;
                site.TimezoneId = timeZone.Id;
                site.Id = Guid.NewGuid();

                await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

                var departmentNames = new List<string> { "Warehouse", "Logistics", "Production" };

                // create 3 departments for each site
                for (int j = 0; j < 3; j++)
                {
                    var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                    department.Id = Guid.NewGuid();
                    department.Name = siteName + " " + departmentNames[j];
                    department.SiteId = site.Id;
                    await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

                    // get only 3 models
                    var Models = new List<ModelDataObject>();
                    for (int i = 0; i < 3; i++)
                    {
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = "Model " + (i + 1).ToString();
                        model.Description = "Description for Model " + (i + 1).ToString();
                        model.DealerId = dealer.Id;
                        // Assigning Model.Type based on the ModelTypesEnum
                        switch (i)
                        {
                            case 0:
                                model.Type = ModelTypesEnum.Electric;
                                break;
                            case 1:
                                model.Type = ModelTypesEnum.ICForklifts;
                                break;
                            case 2:
                                model.Type = ModelTypesEnum.OrderPickers;
                                break;
                            // Additional cases can be added here for other types if necessary
                            default:
                                model.Type = ModelTypesEnum.PalletJack; // Default case if more than 3 models are created
                                break;
                        }
                        // Removed setting the non-existent Active property
                        await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);
                        Models.Add(model);
                    }
                    // create 10 vehicles for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = Guid.NewGuid();
                        vehicle.CustomerId = customer.Id;
                        vehicle.SiteId = site.Id;
                        vehicle.DepartmentId = department.Id;
                        vehicle.IDLETimer = 300;
                        vehicle.OnHire = true;
                        vehicle.ImpactLockout = true;
                        // set random modelId with index rand 0 to 2
                        if (Models.Any())
                        {
                            vehicle.ModelId = Models[k % 3].Id;
                        }
                        else
                        {
                            throw new InvalidOperationException("No models found to assign to vehicle.");
                        }
                        if (j == 0)
                        {
                            vehicle.HireNo = VehicleHireNos1[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos1[k] + department.Id;
                        }
                        else if (j == 1)
                        {
                            vehicle.HireNo = VehicleHireNos2[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos2[k] + department.Id;
                        }
                        else
                        {
                            vehicle.HireNo = VehicleHireNos3[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos3[k] + department.Id;
                        }

                        // create a module for the vehicle
                        var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                        module.Id = Guid.NewGuid();
                        module.Calibration = 100;
                        module.CCID = "CCID" + j + k;
                        // set FSSSBASE random from 100000 to 200000 in increment of 10000
                        Random random = new Random();
                        int randomNumber = random.Next(10, 21);
                        module.FSSSBase = randomNumber * 10000;
                        module.FSSXMulti = 1;
                        if (j == 0)
                            module.IoTDevice = IoTHubIds1[k] + department.Id;
                        else if (j == 1)
                            module.IoTDevice = IoTHubIds2[k] + department.Id;
                        else
                            module.IoTDevice = IoTHubIds3[k] + department.Id;
                        module.IsAllocatedToVehicle = true;
                        await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

                        vehicle.ModuleId1 = module.Id;
                        await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                    }
                    // create 10 persons for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                        person.Id = Guid.NewGuid();
                        person.CustomerId = customer.Id;
                        person.SiteId = site.Id;
                        person.DepartmentId = department.Id;
                        if (j == 0)
                        {
                            person.FirstName = PersonFirstName1[k];
                            person.LastName = PersonLastName1[k];
                        }
                        else if (j == 1)
                        {
                            person.FirstName = PersonFirstName2[k];
                            person.LastName = PersonLastName2[k];
                        }
                        else
                        {
                            person.FirstName = PersonFirstName3[k];
                            person.LastName = PersonLastName3[k];
                        }
                        person.IsDriver = true;
                        person.IsActiveDriver = true;

                        person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true); //Crear person and driver

                        var card = _serviceProvider.GetRequiredService<CardDataObject>();
                        card.Id = Guid.NewGuid();
                        // Facility Code is random between 1 to 254 in string
                        Random random = new Random();
                        card.FacilityCode = random.Next(1, 255).ToString();
                        // Card Number is random between 100001 to 675899 in string
                        card.CardNumber = random.Next(100001, 675900).ToString();
                        card.Active = true;
                        card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
                        card.Type = CardTypeEnum.CardID;

                        card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

                        // get the driver object from person and assign the card ID to the driver
                        var driver = person.Driver;
                        driver.CardDetailsId = card.Id;
                        driver = await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);
                    }
                }
            }

            // get all departments
            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null, skipSecurity: true);
            // get all models
            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(null, skipSecurity: true);

            // create DepartmentChecklist for each department and model
            foreach (var department in departments)
            {
                foreach (var model in models)
                {
                    var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                    departmentChecklist.Id = Guid.NewGuid();
                    departmentChecklist.ModelId = model.Id;
                    departmentChecklist.DepartmentId = department.Id;
                    await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist, skipSecurity: true);
                }
            }

            // get all DepartmentChecklist
            var departmentChecklists = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, skipSecurity: true)).ToArray();

            var questions = new string[] { "Do the brakes work properly ?", "Is the steering operating correctly?" };
            // create 2 PreOperationalChecklist for each DepartmentChecklist
            foreach (var departmentChecklist in departmentChecklists)
            {
                for (int i = 0; i < 2; i++)
                {
                    var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                    preOperationalChecklist.Id = Guid.NewGuid();
                    preOperationalChecklist.SiteChecklistId = departmentChecklist.Id;
                    preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
                    preOperationalChecklist.Question = questions[i];
                    preOperationalChecklist.ExpectedAnswer = true;
                    preOperationalChecklist.Critical = true;
                    // order is i + 1 as short
                    preOperationalChecklist.Order = (short)(i + 1);
                    await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist, skipSecurity: true);
                }
            }

            // get all vehicles
            var vehicles = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).ToArray();
            var IOFieldIgnition = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "0" }, skipSecurity: true)).SingleOrDefault();
            var IOFieldSEAT = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "SEAT" }, skipSecurity: true)).SingleOrDefault();
            var IOFieldHYDR = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "HYDL" }, skipSecurity: true)).SingleOrDefault();
            var IOFieldTRACK = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "TRACK" }, skipSecurity: true)).SingleOrDefault();
            // get drivers where customer Name is Demo Customer
            var drivers = (await _dataFacade.DriverDataProvider.GetCollectionAsync(null, "Person.Customer.CompanyName == @0", new object[] { customer.CompanyName }, skipSecurity: true)).ToArray();
            // for each vehicle create 2 sessions each with 1 impact and 4 sessiondetails and 1 ChecklistResult and 2 ChecklistDetail
            foreach (var vehicle in vehicles)
            {
                var vehicleIndex = Array.IndexOf(vehicles, vehicle);
                for (int i = 0; i < 2; i++)
                {
                    var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                    session.Id = Guid.NewGuid();
                    session.VehicleId = vehicle.Id;
                    // get random drivers index from 0 to 25
                    var randomDriverIndex = new Random().Next(0, 25);
                    session.DriverId = drivers[randomDriverIndex].Id;
                    var random1To30 = new Random().Next(1, 30);
                    var random1To24 = new Random().Next(1, 24);
                    // session StartTime is random from 1 to 30 days ago

                    session.StartTime = DateTime.Now.AddDays(random1To30).AddHours(random1To24);
                    // sessionEndTime is 1 hour after StartTime
                    session.EndTime = session.StartTime.AddHours(1);
                    session.isVOR = false;

                    await _dataFacade.SessionDataProvider.SaveAsync(session, skipSecurity: true);

                    var sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldIgnition.Id;
                    // Usage is random from 2500 to 2800 as string
                    sessionDetail.Usage = new Random().Next(2500, 2800).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail, skipSecurity: true);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldSEAT.Id;
                    // Usage is random from 1500 to 2400 as string 
                    sessionDetail.Usage = new Random().Next(1500, 2400).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail, skipSecurity: true);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldHYDR.Id;
                    // Usage is random from 700 to 1300 as string
                    sessionDetail.Usage = new Random().Next(700, 1300).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail, skipSecurity: true);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldTRACK.Id;
                    // Usage is random from 500 to 1400 as string
                    sessionDetail.Usage = new Random().Next(500, 1400).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail, skipSecurity: true);
                }
            }
        } // End create demo data
    }
}

