using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.IO;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class SpareModuleImportComponentExtension : IImportExportComponentExtension<SpareModuleImportSection0Component, ModuleDataObject>
    {
        private readonly IDataFacade _dataFacade;

        public SpareModuleImportComponentExtension(IDataFacade dataFacade)
        {
            this._dataFacade = dataFacade;
        }

        public void Init(IImportExportComponent<ModuleDataObject> importExportComponent)
        {
            importExportComponent.OnBeforeImportDataRowAsync += ImportExportComponent_OnBeforeImportDataRowAsync;
            importExportComponent.OnAfterImportDataRowAsync += ImportExportComponent_OnAfterImportDataRowAsync;
        }

        private async Task ImportExportComponent_OnBeforeImportDataRowAsync(OnBeforeImportDataRowEventArgs<ModuleDataObject> arg)
        {
            try
            {
                // Validate IoT Device
                var iotDeviceId = arg.DataRow[SpareModuleImportSection0Component.COL_IOTDEVICEID]?.ToString();

                if (string.IsNullOrEmpty(iotDeviceId))
                {
                    throw new Exception("IoT Device ID is required");
                }

                // Get and validate Dealer
                var dealerName = arg.DataRow[SpareModuleImportSection0Component.COL_DEALER]?.ToString();

                if (string.IsNullOrEmpty(dealerName))
                {
                    throw new Exception("Dealer name is required");
                }

                // Look up dealer by name
                var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(
                    null, 
                    "Name == @0", 
                    new object[] { dealerName }
                )).SingleOrDefault();

                if (dealer == null)
                {
                    throw new Exception($"Dealer with name '{dealerName}' not found");
                }

                // Store the dealer ID in the row data
                arg.DataRow[SpareModuleImportSection0Component.COL_DEALER] = dealer.Id.ToString();

                // Check if IoT Device is already assigned to another module
                var existingModule = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(
                    null, 
                    "IoTDevice == @0", 
                    new object[] { iotDeviceId }
                )).SingleOrDefault();

                if (existingModule != null)
                {
                    throw new Exception($"IoT Device ID '{iotDeviceId}' is already assigned to another module");
                }
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private async Task ImportExportComponent_OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<ModuleDataObject> arg)
        {
            try
            {
                var iotDeviceId = arg.DataRow[SpareModuleImportSection0Component.COL_IOTDEVICEID]?.ToString();
                var dealerId = new Guid(arg.DataRow[SpareModuleImportSection0Component.COL_DEALER]?.ToString());

                // Create and save the module
                var module = arg.Entity;  // This should be the created ModuleDataObject
                if (module == null)
                {
                    throw new Exception("Module entity was not created");
                }

                module.IoTDevice = iotDeviceId;
                module.DealerId = dealerId;
                await _dataFacade.ModuleDataProvider.SaveAsync(module);
            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}