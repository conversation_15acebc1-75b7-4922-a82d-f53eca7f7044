describe('Example of using global test data', () => {
  it('demonstrates how to use global test data', () => {
    // Method 1: Using the standard fixture approach
    cy.fixture('testData').then((testData) => {
      cy.log(`Company name from fixture: ${testData.cypressCompanyName}`);
    });

    // Method 2: Using the custom getTestData command
    cy.getTestData().then((testData) => {
      cy.log(`Company name from getTestData: ${testData.cypressCompanyName}`);
    });

    // Method 3: Using the specialized getCompanyData command
    cy.getCompanyData().then((company) => {
      cy.log(`Company name: ${company.name}`);
      cy.log(`First name: ${company.firstName}`);
      cy.log(`Last name: ${company.lastName}`);
    });

    // Method 4: Using the generateUniqueSiteName helper
    cy.generateUniqueSiteName().then((siteName) => {
      cy.log(`Generated unique site name: ${siteName}`);
    });
  });
}); 