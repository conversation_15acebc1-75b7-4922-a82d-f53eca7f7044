<!--
// This is Custom Code - Model Master Access Filter
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter "Person to model vehicle master access view Filter" Internal name : "PersonToModelVehicleMasterAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToModelVehicleMasterAccessViewFilter"
        data-test-id="1730190a-3719-4d87-bbf3-a2595141c89c">
        <form
            data-bind="submit: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.commands.searchCommand">
            <div class="uiSearchContainer" style="margin-top: 8px;">
                <div class="filterFieldSetContent">
                    <div class="row g-2 align-items-end">
                        <!-- Model Name Field -->
                        <div class="col-auto"
                            data-bind="visible: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.statusData.isModelNameVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToModelVehicleMasterAccessView/filters/PersonToModelVehicleMasterAccessViewFilter:filterFields.ModelName.displayName'">Model</span>
                                </label>
                                <input type="text" class="form-control form-control-sm"
                                    style="min-width: 150px; padding-left: 12px;"
                                    data-bind="value: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.filterData.fields.ModelName, enable: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="3a3b3aae-379f-4852-874b-3f0b214f2300" />
                            </div>
                        </div>

                        <!-- Has Access Field -->
                        <div class="col-auto"
                            data-bind="visible: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.statusData.isHasAccessVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToModelVehicleMasterAccessView/filters/PersonToModelVehicleMasterAccessViewFilter:filterFields.HasAccess.displayName'">Has
                                        access</span>
                                </label>
                                <select class="form-control form-control-sm"
                                    style="min-width: 120px; padding-left: 12px;"
                                    data-bind="value: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.HasAccessValues, enable: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"></select>
                            </div>
                        </div>

                        <!-- Search Buttons -->
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="submit" class="btn btn-primary btn-sm"
                                    data-bind="click: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.commands.searchCommand, i18n: 'buttons.search', enable: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="searchCommand">SEARCH</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    data-bind="click: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.PersonToModelVehicleNormalAccessViewFilterViewModel.commands.clearCommand, i18n: 'buttons.clear', enable: SupervisorVehicleAccessFormFormViewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="clearCommand">CLEAR</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter "Person to model vehicle master access view Filter" Internal name : "PersonToModelVehicleMasterAccessViewFilter"-->