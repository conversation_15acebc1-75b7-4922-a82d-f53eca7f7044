﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{
    public class EmailDetail
    {
        public string Alert { get; set; }
        public AlertDetails Details { get; set; }
        public string TimeStamp { get; set; }

        public string DriverName { get; set; }
        public Guid VehicleId { get; set; }
        public class AlertDetails
        {
            public string CriticalQuestion { get; set; }

            public string ExpectedAnswer { get; set; }
            public string Response { get; set; }

            public Guid Vehicle { get; set; }
            public string dueDate { get; set; }
            public double? currentServiceHours { get; set; }
            public double? nextServiceHours { get; set; }
        }
    }

    
}
