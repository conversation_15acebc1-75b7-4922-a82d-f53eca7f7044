using System;
using System.Threading;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;

namespace FleetXQFunctionService.Services
{
    public interface IMessageLockRenewalService
    {
        Task<IDisposable> StartAutoRenewalAsync(ServiceBusReceivedMessage message, ServiceBusMessageActions messageActions, CancellationToken cancellationToken = default);
    }

    public class MessageLockRenewalService : IMessageLockRenewalService
    {
        private readonly ILogger<MessageLockRenewalService> _logger;

        public MessageLockRenewalService(ILogger<MessageLockRenewalService> logger)
        {
            _logger = logger;
        }

        public Task<IDisposable> StartAutoRenewalAsync(ServiceBusReceivedMessage message, ServiceBusMessageActions messageActions, CancellationToken cancellationToken = default)
        {
            var renewal = new MessageLockRenewal(message, messageActions, _logger, cancellationToken);
            renewal.Start();
            return Task.FromResult<IDisposable>(renewal);
        }

        private class MessageLockRenewal : IDisposable
        {
            private readonly ServiceBusReceivedMessage _message;
            private readonly ServiceBusMessageActions _messageActions;
            private readonly ILogger _logger;
            private readonly CancellationTokenSource _cancellationTokenSource;
            private readonly Timer _renewalTimer;
            private bool _disposed;

            public MessageLockRenewal(ServiceBusReceivedMessage message, ServiceBusMessageActions messageActions, ILogger logger, CancellationToken externalCancellationToken)
            {
                _message = message;
                _messageActions = messageActions;
                _logger = logger;
                _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(externalCancellationToken);

                // Renew every 4 minutes (lock duration is typically 5 minutes)
                var renewalInterval = TimeSpan.FromMinutes(4);
                _renewalTimer = new Timer(RenewLockCallback, null, renewalInterval, renewalInterval);
            }

            public void Start()
            {
                _logger.LogDebug("Started automatic lock renewal for message {MessageId}", _message.MessageId);
            }

            private void RenewLockCallback(object state)
            {
                if (_disposed || _cancellationTokenSource.Token.IsCancellationRequested)
                    return;

                // Fire and forget async operation
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await _messageActions.RenewMessageLockAsync(_message, _cancellationTokenSource.Token);
                        _logger.LogDebug("Successfully renewed lock for message {MessageId}", _message.MessageId);
                    }
                    catch (Azure.Messaging.ServiceBus.ServiceBusException sbEx) when (sbEx.Reason == Azure.Messaging.ServiceBus.ServiceBusFailureReason.MessageLockLost)
                    {
                        _logger.LogWarning("Cannot renew lock for message {MessageId} - lock already lost", _message.MessageId);
                        Dispose(); // Stop trying to renew
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to renew lock for message {MessageId}: {Error}", _message.MessageId, ex.Message);
                    }
                });
            }

            public void Dispose()
            {
                if (_disposed)
                    return;

                _disposed = true;
                _renewalTimer?.Dispose();
                _cancellationTokenSource?.Cancel();
                _cancellationTokenSource?.Dispose();

                _logger.LogDebug("Stopped automatic lock renewal for message {MessageId}", _message.MessageId);
            }
        }
    }
}