using System.Text;
using LocaleTranslator.Models;
using LocaleTranslator.Services;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using NUnit.Framework;

namespace LocaleTranslator.Tests;

[TestFixture]
public class CsvExportTests
{
    private Mock<ILogger<FileProcessingService>> _mockLogger;
    private FileProcessingService _service;
    private string _testRoot;

    [SetUp]
    public void SetUp()
    {
        _mockLogger = new Mock<ILogger<FileProcessingService>>();
        _service = new FileProcessingService(null, _mockLogger.Object);
        _testRoot = Path.Combine(TestContext.CurrentContext.TestDirectory, "TestData", "CsvExport");
        if (Directory.Exists(_testRoot)) Directory.Delete(_testRoot, true);
        Directory.CreateDirectory(_testRoot);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_testRoot)) Directory.Delete(_testRoot, true);
    }

    [Test]
    public async Task ExportTranslationsToCsvAsync_CreatesCsvWithCorrectHeaders()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        await File.WriteAllTextAsync(Path.Combine(source, "common.json"), "{\"hello\": \"Hello\"}");
        await File.WriteAllTextAsync(Path.Combine(target, "common.json"), "{\"hello\": \"Bonjour\"}");

        var options = new TranslationOptions
        {
            SourceLocalesPath = source,
            TargetLocalesPath = target,
            TargetLanguage = "French"
        };

        var csvPath = Path.Combine(_testRoot, "exports", "translations_french_test.csv");
        var count = await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        Assert.That(File.Exists(csvPath), Is.True);
        var text = await File.ReadAllTextAsync(csvPath, Encoding.UTF8);
        var firstLine = text.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None)[0];
        Assert.That(firstLine, Is.EqualTo("SourceText,TranslatedText,SourceLanguage,TargetLanguage,FilePath,KeyPath"));
        Assert.That(count, Is.EqualTo(1));
    }

    [Test]
    public async Task ExportTranslationsToCsvAsync_DefaultEncoding_IsUtf8WithBom()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        await File.WriteAllTextAsync(Path.Combine(source, "one.json"), "{\"k\": \"v\"}");
        await File.WriteAllTextAsync(Path.Combine(target, "one.json"), "{\"k\": \"x\"}");

        var options = new TranslationOptions
        {
            SourceLocalesPath = source,
            TargetLocalesPath = target,
            TargetLanguage = "French"
        };

        var csvPath = Path.Combine(_testRoot, "out_bom.csv");
        await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        var bytes = await File.ReadAllBytesAsync(csvPath);
        Assert.That(bytes.Length, Is.GreaterThan(3));
        Assert.That(bytes[0], Is.EqualTo(0xEF));
        Assert.That(bytes[1], Is.EqualTo(0xBB));
        Assert.That(bytes[2], Is.EqualTo(0xBF));
    }

    [Test]
    public async Task ExportTranslationsToCsvAsync_EscapesCommasQuotesNewlinesAndSpaces()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        var sourceJson = new
        {
            common = new
            {
                greet = "Hello, \"World\"\nNew Line",
                note = " spaced "
            },
            items = new[] { "One, Two", "Line\nBreak", "Quote \"Here\"" }
        };
        var targetJson = new
        {
            common = new
            {
                greet = "Bonjour, \"Monde\"\nNouvelle Ligne",
                note = " espacé "
            },
            items = new[] { "Un, Deux", "Ligne\nRupture", "Citation \"Ici\"" }
        };

        await File.WriteAllTextAsync(Path.Combine(source, "common.json"), JsonConvert.SerializeObject(sourceJson));
        await File.WriteAllTextAsync(Path.Combine(target, "common.json"), JsonConvert.SerializeObject(targetJson));

        var options = new TranslationOptions
        {
            SourceLocalesPath = source,
            TargetLocalesPath = target,
            TargetLanguage = "French"
        };

        var csvPath = Path.Combine(_testRoot, "escaped.csv");
        await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        var csv = await File.ReadAllTextAsync(csvPath, Encoding.UTF8);
        Assert.That(csv, Does.Contain("\"Hello, \"\"World\"\"\nNew Line\""));
        Assert.That(csv, Does.Contain("\"Bonjour, \"\"Monde\"\"\nNouvelle Ligne\""));
        Assert.That(csv, Does.Contain("\" spaced \""));
        Assert.That(csv, Does.Contain("\" espacé \""));
        Assert.That(csv, Does.Contain("\"One, Two\""));
        Assert.That(csv, Does.Contain("\"Un, Deux\""));
        Assert.That(csv, Does.Contain("\"Line\nBreak\""));
        Assert.That(csv, Does.Contain("\"Ligne\nRupture\""));
        Assert.That(csv, Does.Contain("\"Quote \"\"Here\"\"\""));
        Assert.That(csv, Does.Contain("\"Citation \"\"Ici\"\"\""));
    }

    [Test]
    public async Task ExportTranslationsToCsvAsync_SkipsNonStringValues()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        var src = "{\"a\":\"x\",\"b\":123,\"c\":true,\"d\":null,\"e\":{\"y\":\"z\"},\"arr\":[\"s\",1]}";
        var tgt = "{\"a\":\"ax\",\"b\":456,\"c\":false,\"d\":null,\"e\":{\"y\":\"zz\"},\"arr\":[\"ss\",2]}";
        await File.WriteAllTextAsync(Path.Combine(source, "file.json"), src);
        await File.WriteAllTextAsync(Path.Combine(target, "file.json"), tgt);

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var csvPath = Path.Combine(_testRoot, "nonstrings.csv");
        var count = await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        Assert.That(count, Is.EqualTo(3));
        var csv = await File.ReadAllTextAsync(csvPath, Encoding.UTF8);
        var lines = csv.Split(new[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries).Skip(1).ToList();
        Assert.That(lines.Any(l => l.EndsWith(",file.json,a")), Is.True);
        Assert.That(lines.Any(l => l.EndsWith(",file.json,e.y")), Is.True);
        Assert.That(lines.Any(l => l.EndsWith(",file.json,arr[0]")), Is.True);
        Assert.That(lines.Any(l => l.EndsWith(",file.json,b")), Is.False);
        Assert.That(lines.Any(l => l.EndsWith(",file.json,c")), Is.False);
        Assert.That(lines.Any(l => l.EndsWith(",file.json,arr[1]")), Is.False);
    }

    [Test]
    public void CsvExportOptions_Defaults_AreExpected()
    {
        var opts = new CsvExportOptions();
        Assert.That(opts.Delimiter, Is.EqualTo(","));
        Assert.That(opts.Encoding, Is.EqualTo(Encoding.UTF8));
    }

    [Test]
    public async Task CsvExportOptions_CustomDelimiterAndEncoding_AreApplied()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        await File.WriteAllTextAsync(Path.Combine(source, "file.json"), "{\"a\":\"x\"}");
        await File.WriteAllTextAsync(Path.Combine(target, "file.json"), "{\"a\":\"y\"}");

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var csvPath = Path.Combine(_testRoot, "custom.csv");
        var exportOpts = new CsvExportOptions { Delimiter = ";", Encoding = new UTF8Encoding(false) };
        await _service.ExportTranslationsToCsvAsync(options, csvPath, exportOpts);

        var bytes = await File.ReadAllBytesAsync(csvPath);
        Assert.That(bytes.Length, Is.GreaterThan(0));
        Assert.That(bytes[0], Is.Not.EqualTo(0xEF));

        var text = await File.ReadAllTextAsync(csvPath, new UTF8Encoding(false));
        Assert.That(text.StartsWith("SourceText;TranslatedText;SourceLanguage;TargetLanguage;FilePath;KeyPath"), Is.True);
    }

    [Test]
    public async Task Export_EndToEnd_AfterMockTranslation_CreatesCsvInExports_WithTimestampName()
    {
        var projectExports = Path.Combine(_testRoot, "exports");
        Directory.CreateDirectory(projectExports);

        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        await File.WriteAllTextAsync(Path.Combine(source, "common.json"), "{\"save\":\"Save\"}");
        await File.WriteAllTextAsync(Path.Combine(target, "common.json"), "{\"save\":\"Enregistrer\"}");

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var fileName = $"translations_{options.TargetLanguage.ToLowerInvariant()}_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
        var csvPath = Path.Combine(projectExports, fileName);
        var rows = await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        Assert.That(File.Exists(csvPath), Is.True);
        Assert.That(Path.GetFileName(csvPath), Does.Match(@"^translations_french_\d{8}_\d{6}\.csv$"));
        Assert.That(rows, Is.EqualTo(1));
    }

    [Test]
    public async Task Export_WhenTargetDirectoryMissing_SucceedsWithZeroRowsAndCreatesCsv()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "missing_target");
        Directory.CreateDirectory(source);

        await File.WriteAllTextAsync(Path.Combine(source, "file.json"), "{\"k\":\"v\"}");

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var csvPath = Path.Combine(_testRoot, "no_target.csv");
        var rows = await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        Assert.That(File.Exists(csvPath), Is.True);
        Assert.That(rows, Is.EqualTo(0));
        var text = await File.ReadAllTextAsync(csvPath, Encoding.UTF8);
        var lines = text.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);
        Assert.That(lines.Length, Is.GreaterThanOrEqualTo(1));
    }

    [Test]
    public void Export_WhenOutputDirectoryInvalid_Throws()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);
        File.WriteAllText(Path.Combine(source, "a.json"), "{\"x\":\"y\"}");
        File.WriteAllText(Path.Combine(target, "a.json"), "{\"x\":\"z\"}");

        // Create a file where a directory is expected to force failure
        var blocked = Path.Combine(_testRoot, "blocked");
        File.WriteAllText(blocked, "content");

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var csvPath = Path.Combine(blocked, "out.csv");

        Assert.ThrowsAsync<IOException>(async () => await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions()));
    }

    [Test]
    public async Task Export_WhenOutputFileLocked_Throws()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);
        await File.WriteAllTextAsync(Path.Combine(source, "a.json"), "{\"x\":\"y\"}");
        await File.WriteAllTextAsync(Path.Combine(target, "a.json"), "{\"x\":\"z\"}");

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var csvPath = Path.Combine(_testRoot, "locked.csv");

        Directory.CreateDirectory(Path.GetDirectoryName(csvPath)!);
        using (var fs = new FileStream(csvPath, FileMode.Create, FileAccess.ReadWrite, FileShare.None))
        {
            Assert.ThrowsAsync<IOException>(async () => await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions()));
        }
    }

    [Test]
    public async Task DataValidation_Mapping_FilePathAndKeyPath_AndRowCount()
    {
        var source = Path.Combine(_testRoot, "english");
        var target = Path.Combine(_testRoot, "french");
        Directory.CreateDirectory(source);
        Directory.CreateDirectory(target);

        var srcObj = new
        {
            user = new { name = "John", profile = new { title = "Mr" } },
            labels = new[] { "A", "B" }
        };
        var tgtObj = new
        {
            user = new { name = "Jean", profile = new { title = "M" } },
            labels = new[] { "Un", "Deux" }
        };

        await File.WriteAllTextAsync(Path.Combine(source, "nested.json"), JsonConvert.SerializeObject(srcObj));
        await File.WriteAllTextAsync(Path.Combine(target, "nested.json"), JsonConvert.SerializeObject(tgtObj));

        var options = new TranslationOptions { SourceLocalesPath = source, TargetLocalesPath = target, TargetLanguage = "French" };
        var csvPath = Path.Combine(_testRoot, "mapping.csv");
        var rows = await _service.ExportTranslationsToCsvAsync(options, csvPath, new CsvExportOptions());

        Assert.That(rows, Is.EqualTo(4));
        var csv = await File.ReadAllTextAsync(csvPath, Encoding.UTF8);
        Assert.That(csv, Does.Contain("nested.json".Replace('\\', '/')));
        Assert.That(csv, Does.Contain("user.name"));
        Assert.That(csv, Does.Contain("user.profile.title"));
        Assert.That(csv, Does.Contain("labels[0]"));
        Assert.That(csv, Does.Contain("labels[1]"));
    }
}


