import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

// Mock the dependencies
global.ko = {
    observable: vi.fn((x) => {
        const obs = function (newValue) {
            if (arguments.length > 0) {
                obs._value = newValue;
                return;
            }
            return obs._value;
        };
        obs._value = x;
        return obs;
    }),
    observableArray: vi.fn((arr = []) => {
        const obsArray = function () {
            return obsArray._array;
        };
        obsArray._array = [...arr];
        obsArray.push = vi.fn((item) => { obsArray._array.push(item); });
        obsArray.remove = vi.fn((itemOrFn) => {
            if (typeof itemOrFn === 'function') {
                obsArray._array = obsArray._array.filter((item) => !itemOrFn(item));
            } else {
                obsArray._array = obsArray._array.filter((item) => item !== itemOrFn);
            }
        });
        obsArray.removeAll = vi.fn(() => { obsArray._array = []; });
        return obsArray;
    }),
    pureComputed: vi.fn()
};

// Mock FleetXQ
global.FleetXQ = {
    Web: {
        ViewModels: {
            SelectDealerCategoryGridViewModelCustom: null
        }
    }
};

// Import the file to test - using require instead of import
// This ensures our mocks are set up before the file is loaded
vi.mock('../../../WebApplicationLayer/wwwroot/ViewModels/Model/SelectDealerCategoryGridViewModel.custom.js', () => {
    return {};
});
require('../../../WebApplicationLayer/wwwroot/ViewModels/Model/SelectDealerCategoryGridViewModel.custom.js');

describe('SelectDealerCategoryGridViewModel', () => {
    let viewmodel;
    let customViewModel;

    beforeEach(() => {
        // Create a mock viewmodel with required properties
        viewmodel = {
            ModelObjectCollection: global.ko.observableArray(),
            checkedStates: global.ko.observableArray(),
            selectedModelIds: global.ko.observableArray(),
            toggleChecked: vi.fn()
        };

        // Initialize custom view model
        customViewModel = new FleetXQ.Web.ViewModels.SelectDealerCategoryGridViewModelCustom(viewmodel);

        // Call initialize to set up the custom functions
        customViewModel.initialize();

        // Set up test data
        const mockModelData = [
            { Data: { Id: vi.fn().mockReturnValue('model1'), Name: vi.fn().mockReturnValue('Model 1') } },
            { Data: { Id: vi.fn().mockReturnValue('model2'), Name: vi.fn().mockReturnValue('Model 2') } },
            { Data: { Id: vi.fn().mockReturnValue('model3'), Name: vi.fn().mockReturnValue('Model 3') } }
        ];

        viewmodel.ModelObjectCollection = global.ko.observableArray(mockModelData);

        // Initialize checkedStates with all unchecked
        viewmodel.updateCheckStates();
    });

    afterEach(() => {
        vi.resetAllMocks();
    });

    it('should have selectAll and deselectAll functions defined', () => {
        expect(typeof viewmodel.selectAll).toBe('function');
        expect(typeof viewmodel.deselectAll).toBe('function');
    });

    it('should call toggleChecked for all unchecked items in selectAll', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Simulate some items are already checked
        viewmodel.checkedStates()[1]._value = true;

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was called twice (for the first and third items that were unchecked)
        expect(toggleCheckedSpy).toHaveBeenCalledTimes(2);
        expect(toggleCheckedSpy).toHaveBeenCalledWith(0, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(2, expect.any(Object));
    });

    it('should call toggleChecked for all checked items in deselectAll', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Simulate some items are checked
        viewmodel.checkedStates()[0]._value = true;
        viewmodel.checkedStates()[2]._value = true;

        // Run deselectAll function
        viewmodel.deselectAll();

        // Verify toggleChecked was called twice (for the first and third items that were checked)
        expect(toggleCheckedSpy).toHaveBeenCalledTimes(2);
        expect(toggleCheckedSpy).toHaveBeenCalledWith(0, expect.any(Object));
        expect(toggleCheckedSpy).toHaveBeenCalledWith(2, expect.any(Object));
    });

    it('should not call toggleChecked for any items if none match the criteria', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // For selectAll, make all items checked already
        viewmodel.checkedStates()[0]._value = true;
        viewmodel.checkedStates()[1]._value = true;
        viewmodel.checkedStates()[2]._value = true;

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was not called
        expect(toggleCheckedSpy).not.toHaveBeenCalled();

        // Reset all to unchecked
        viewmodel.checkedStates()[0]._value = false;
        viewmodel.checkedStates()[1]._value = false;
        viewmodel.checkedStates()[2]._value = false;

        // Reset spy
        toggleCheckedSpy.mockClear();

        // For deselectAll with nothing checked
        viewmodel.deselectAll();

        // Verify toggleChecked was not called
        expect(toggleCheckedSpy).not.toHaveBeenCalled();
    });

    it('should pass an object with stopPropagation to toggleChecked', () => {
        // Create spy on toggleChecked
        const toggleCheckedSpy = vi.spyOn(viewmodel, 'toggleChecked');

        // Run selectAll function
        viewmodel.selectAll();

        // Verify toggleChecked was called with an object that has stopPropagation
        const eventArg = toggleCheckedSpy.mock.calls[0][1];
        expect(typeof eventArg.stopPropagation).toBe('function');

        // Reset spy
        toggleCheckedSpy.mockClear();

        // Setup for deselectAll
        viewmodel.checkedStates()[0]._value = true;

        // Run deselectAll function
        viewmodel.deselectAll();

        // Verify toggleChecked was called with an object that has stopPropagation
        const eventArg2 = toggleCheckedSpy.mock.calls[0][1];
        expect(typeof eventArg2.stopPropagation).toBe('function');
    });
});
