import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('FirmwareSettingsViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console methods
        global.console.log = vi.fn();
        global.console.error = vi.fn();

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                viewModelCustom: {
                    hasAccess: vi.fn(),
                    AccessRules: {
                        HAS_CUSTOMERS_ACCESS: 'CA',
                        CAN_VIEW_FIRMWARE: 'VF'
                    }
                }
            }
        };

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Site/FirmwareSettingsViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create mock site object
        const mockSiteObject = {
            Data: {
                Id: ko.observable('site-123')
            }
        };

        // Create mock VehicleFirmwaresViewModel
        const mockVehicleFirmwaresViewModel = {
            siteId: ko.observable(null)
        };

        // Create base view model with required properties
        viewModel = {
            SiteObject: ko.observable(mockSiteObject),
            VehicleItemsGridViewModel: mockVehicleFirmwaresViewModel,
            subscriptions: [],
            CurrentObject: ko.pureComputed(() => viewModel.SiteObject())
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.FirmwareSettingsViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    afterEach(() => {
        // Cleanup
        if (customViewModel && customViewModel.release) {
            customViewModel.release();
        }
    });

    describe('Initialization', () => {
        it('should initialize custom view model', () => {
            expect(customViewModel).toBeDefined();
            expect(customViewModel.viewmodel).toBe(viewModel);
        });

        it('should set up CurrentObject subscription', () => {
            expect(viewModel.subscriptions.length).toBeGreaterThan(0);
        });
    });

    describe('IsVehicleItemsVisible method', () => {
        it('should return true when user has both required access permissions', () => {
            ApplicationController.viewModel.viewModelCustom.hasAccess
                .mockReturnValueOnce(true)  // HAS_CUSTOMERS_ACCESS (CA)
                .mockReturnValueOnce(true); // CAN_VIEW_FIRMWARE (VF)

            const result = customViewModel.IsVehicleItemsVisible();

            expect(result).toBe(true);
            expect(ApplicationController.viewModel.viewModelCustom.hasAccess).toHaveBeenCalledWith('HAS_CUSTOMERS_ACCESS');
            expect(ApplicationController.viewModel.viewModelCustom.hasAccess).toHaveBeenCalledWith('CAN_VIEW_FIRMWARE');
        });

        it('should return false when user lacks HAS_CUSTOMERS_ACCESS', () => {
            ApplicationController.viewModel.viewModelCustom.hasAccess
                .mockReturnValueOnce(false)  // HAS_CUSTOMERS_ACCESS (CA)
                .mockReturnValueOnce(true);  // CAN_VIEW_FIRMWARE (VF)

            const result = customViewModel.IsVehicleItemsVisible();

            expect(result).toBe(false);
        });

        it('should return false when user lacks CAN_VIEW_FIRMWARE', () => {
            ApplicationController.viewModel.viewModelCustom.hasAccess
                .mockReturnValueOnce(true)   // HAS_CUSTOMERS_ACCESS (CA)
                .mockReturnValueOnce(false); // CAN_VIEW_FIRMWARE (VF)

            const result = customViewModel.IsVehicleItemsVisible();

            expect(result).toBe(false);
        });

        it('should return false when user lacks both permissions', () => {
            ApplicationController.viewModel.viewModelCustom.hasAccess
                .mockReturnValueOnce(false)  // HAS_CUSTOMERS_ACCESS (CA)
                .mockReturnValueOnce(false); // CAN_VIEW_FIRMWARE (VF)

            const result = customViewModel.IsVehicleItemsVisible();

            expect(result).toBe(false);
        });
    });

    describe('Site ID propagation', () => {
        it('should set siteId in VehicleFirmwaresViewModel when CurrentObject changes', () => {
            const newSiteId = 'new-site-456';
            const newSiteObject = {
                Data: {
                    Id: ko.observable(newSiteId)
                }
            };

            // Trigger the CurrentObject change
            viewModel.SiteObject(newSiteObject);

            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(newSiteId);
        });

        it('should set initial siteId when SiteObject already has an ID', () => {
            // The initial setup should have already set the siteId
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe('site-123');
        });


    });

    describe('Subscription management', () => {
        it('should add subscription to CurrentObject', () => {
            expect(viewModel.subscriptions.length).toBeGreaterThan(0);
        });

        it('should handle multiple CurrentObject changes', () => {
            const siteId1 = 'site-1';
            const siteId2 = 'site-2';
            const siteId3 = 'site-3';

            const siteObject1 = { Data: { Id: ko.observable(siteId1) } };
            const siteObject2 = { Data: { Id: ko.observable(siteId2) } };
            const siteObject3 = { Data: { Id: ko.observable(siteId3) } };

            viewModel.SiteObject(siteObject1);
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(siteId1);

            viewModel.SiteObject(siteObject2);
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(siteId2);

            viewModel.SiteObject(siteObject3);
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(siteId3);
        });
    });



    describe('Integration with VehicleFirmwaresViewModel', () => {
        it('should properly propagate siteId to child component', () => {
            const testSiteId = 'test-site-id';
            const testSiteObject = {
                Data: {
                    Id: ko.observable(testSiteId)
                }
            };

            viewModel.SiteObject(testSiteObject);

            // Verify the child component received the siteId
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(testSiteId);
        });

        it('should maintain siteId consistency between parent and child', () => {
            const initialSiteId = 'initial-site';
            const updatedSiteId = 'updated-site';

            // Set initial site
            const initialSiteObject = {
                Data: {
                    Id: ko.observable(initialSiteId)
                }
            };
            viewModel.SiteObject(initialSiteObject);
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(initialSiteId);

            // Update site
            const updatedSiteObject = {
                Data: {
                    Id: ko.observable(updatedSiteId)
                }
            };
            viewModel.SiteObject(updatedSiteObject);
            expect(viewModel.VehicleItemsGridViewModel.siteId()).toBe(updatedSiteId);
        });
    });

    describe('Access control integration', () => {
        it('should check both access rules when determining visibility', () => {
            ApplicationController.viewModel.viewModelCustom.hasAccess
                .mockReturnValueOnce(true)
                .mockReturnValueOnce(true);

            customViewModel.IsVehicleItemsVisible();

            expect(ApplicationController.viewModel.viewModelCustom.hasAccess).toHaveBeenCalledTimes(2);
            expect(ApplicationController.viewModel.viewModelCustom.hasAccess).toHaveBeenNthCalledWith(1, 'HAS_CUSTOMERS_ACCESS');
            expect(ApplicationController.viewModel.viewModelCustom.hasAccess).toHaveBeenNthCalledWith(2, 'CAN_VIEW_FIRMWARE');
        });

        it('should use short-circuit evaluation for access control', () => {
            ApplicationController.viewModel.viewModelCustom.hasAccess
                .mockReturnValueOnce(false); // First check fails

            customViewModel.IsVehicleItemsVisible();

            // Should only call the first access check due to short-circuit evaluation
            expect(ApplicationController.viewModel.viewModelCustom.hasAccess).toHaveBeenCalledTimes(1);
        });
    });
}); 