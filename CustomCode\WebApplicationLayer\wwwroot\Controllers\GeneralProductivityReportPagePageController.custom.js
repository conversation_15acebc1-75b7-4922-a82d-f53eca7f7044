﻿(function () {

    FleetXQ.Web.Controllers.GeneralProductivityReportPagePageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getGeneralProductivityConfiguration = function () {
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];
            var currentData = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject().Data;
        
            var parameterIndex = 0; // Start indexing after the initial three parameters.
        
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }
        
            if (currentData.CustomerId() != null) {
                configuration.filterPredicate += `CustomerId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.CustomerId() });
                parameterIndex += 1;
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate += ` && SiteId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.SiteId() });
                parameterIndex += 1;
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate += ` && DepartmentId == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.Guid", "IsNullable": false, "Value": currentData.DepartmentId() });
                parameterIndex += 1;
            }
        
            // Only add dates to the filter if they are actually set
            if (currentData.StartDate() != null && currentData.StartDate() !== "") {
                configuration.filterPredicate += ` && StartDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.StartDate() });
                parameterIndex += 1;
            }
        
            if (currentData.EndDate() != null && currentData.EndDate() !== "") {
                configuration.filterPredicate += ` && EndDate == @${parameterIndex}`;
                configuration.filterParameters.push({ "TypeName": "System.DateTime", "IsNullable": false, "Value": currentData.EndDate() });
                parameterIndex += 1;
            }
        
            // Convert configuration.filterParameters array to a JSON string.
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);
        
            return configuration;
        };
        

        this.addMultiSearchFilter = function (configuration) {
            var updatedConfiguration = configuration;
            var currentData = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject().Data;
            if (currentData.MultiSearch()) {
                if (updatedConfiguration) {
                    var filterParameters = JSON.parse(updatedConfiguration.filterParameters);
                    updatedConfiguration.filterPredicate = updatedConfiguration.filterPredicate ? 
                        updatedConfiguration.filterPredicate + ' && MultiSearch == @' + filterParameters.length :
                        'MultiSearch == @' + filterParameters.length;
                    filterParameters.push({ "TypeName": "System.String", "IsNullable": true, "Value": currentData.MultiSearch() });
                    updatedConfiguration.filterParameters = JSON.stringify(filterParameters);
                    return updatedConfiguration;
                } else {
                    // Create new configuration if none exists
                    return {
                        filterPredicate: 'MultiSearch == @0',
                        filterParameters: JSON.stringify([{ 
                            "TypeName": "System.String", 
                            "IsNullable": true, 
                            "Value": currentData.MultiSearch() 
                        }])
                    };
                }
            }
            return updatedConfiguration;
        };

        this.loadGeneralProductivityPerVehicleViewData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection(configuration);
        };

        this.loadGeneralProductivityPerDriverViewData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection(configuration);
        };
        
        // Load unit utilization data (utilized units)
        this.loadUnitUtilisationData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.LoadUnitUtilisationStoreProcedureObjectCollection(configuration);
        };

        // Load unit unutilization data (unutilized units)
        this.loadUnitUnutilisationData = function () {
            var configuration = self.getGeneralProductivityConfiguration();
            // add the filter for multi search to the configuration
            configuration = self.addMultiSearchFilter(configuration);
            self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.LoadUnitUnutilisationStoreProcedureObjectCollection(configuration);
        };
        
        this.loadReportData = function () {
            var configuration = this.getGeneralProductivityConfiguration();
            self.controller.LoggedHoursVersusSeatHoursViewReportViewModel.LoadLoggedHoursVersusSeatHoursViewObjectCollection(configuration);
        };

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection(defaultConfig);
                
                var defaultConfig2 = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection(defaultConfig2);
                
                // Load unit utilization and unutilization data with default configuration
                var defaultConfig3 = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.LoadUnitUtilisationStoreProcedureObjectCollection(defaultConfig3);
                
                var defaultConfig4 = self.getDefaultConfiguration();
                self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.LoadUnitUnutilisationStoreProcedureObjectCollection(defaultConfig4);
                
                return;
            }
            
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.LoadGeneralProductivityPerDriverViewLatestObjectCollection();
            }
            
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.LoadGeneralProductivityPerVehicleViewObjectCollection();
            }
            
            // Load unit utilization and unutilization data
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.UnitUtilisationStoreProcedureItemsGridViewModel.LoadUnitUtilisationStoreProcedureObjectCollection();
            }
            
            if (!GO.Filter.hasUrlFilter(self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.FILTER_NAME, self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel)) {
                self.controller.GeneralProductivityViewFormViewModel.UnitUnutilisationStoreProcedureItemsGridViewModel.LoadUnitUnutilisationStoreProcedureObjectCollection();
            }
        };


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter
            self.controller.GeneralProductivityReportFilterFormViewModel.filterData = function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.GeneralProductivityReportFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }

                // The validation is now done in the Commands.FilterDataCommand
                self.loadGeneralProductivityPerDriverViewData();
                self.loadGeneralProductivityPerVehicleViewData();
                self.loadReportData();
                // Also load unit utilization data when filter is applied
                self.loadUnitUtilisationData();
                self.loadUnitUnutilisationData();

                // save filter data to the GeneralProductivityViewFormViewModel so that it can be used by the "Show Sessions" popup.
                var currentData = self.controller.GeneralProductivityReportFilterFormViewModel.CurrentObject().Data;

                // Handle StartDate: set or clear it properly
                if (currentData.StartDate() && currentData.StartDate() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate = currentData.StartDate();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.StartDate = currentData.StartDate();
                } else {
                    // Clear the stored StartDate when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.StartDate = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.StartDate = null;
                }
                
                // Handle EndDate: set or clear it properly
                if (currentData.EndDate() && currentData.EndDate() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate = currentData.EndDate();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.EndDate = currentData.EndDate();
                } else {
                    // Clear the stored EndDate when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.EndDate = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.EndDate = null;
                }
                
                // Handle CustomerId: set or clear it properly
                if (currentData.CustomerId() && currentData.CustomerId() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.CustomerId = currentData.CustomerId();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.CustomerId = currentData.CustomerId();
                } else {
                    // Clear the stored CustomerId when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.CustomerId = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.CustomerId = null;
                }
                
                // Handle SiteId: set or clear it properly
                if (currentData.SiteId() && currentData.SiteId() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.SiteId = currentData.SiteId();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.SiteId = currentData.SiteId();
                } else {
                    // Clear the stored SiteId when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.SiteId = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.SiteId = null;
                }
                
                // Handle DepartmentId: set or clear it properly
                if (currentData.DepartmentId() && currentData.DepartmentId() !== "") {
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.DepartmentId = currentData.DepartmentId();
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.DepartmentId = currentData.DepartmentId();
                } else {
                    // Clear the stored DepartmentId when the field is empty
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerDriverViewLatestItemsGridViewModel.DepartmentId = null;
                    self.controller.GeneralProductivityViewFormViewModel.GeneralProductivityPerVehicleViewItemsGridViewModel.DepartmentId = null;
                }
            };

            // Uncomment if you want to load data on page initialization
            // self.loadInitialGridData();
            // self.loadReportData();
        };
    };

})();