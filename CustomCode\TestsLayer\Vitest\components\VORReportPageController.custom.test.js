import { describe, it, expect, vi, beforeEach } from 'vitest';
import fs from 'fs';
import path from 'path';

// Set up FleetXQ namespace before importing the file
global.FleetXQ = {
    Web: {
        Controllers: {}
    }
};

// Mock window and other globals
global.window = {
    location: {
        reload: vi.fn()
    }
};

global.GO = {
    Filter: {
        hasUrlFilter: vi.fn().mockReturnValue(false)
    }
};

// Mock sessionStorage
global.sessionStorage = {
    getItem: vi.fn().mockReturnValue('true'), // Return true to skip reload
    setItem: vi.fn(),
    removeItem: vi.fn()
};

// Mock ko.observable
function mockObservable(initialValue) {
    const obs = vi.fn();
    obs.subscribe = vi.fn();
    obs.dispose = vi.fn();
    obs.notifySubscribers = vi.fn();
    return obs;
}

// Read and evaluate the actual controller file
const controllerFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/Controllers/VORReportPageController.custom.js');
const controllerFileContent = fs.readFileSync(controllerFilePath, 'utf8');
eval(controllerFileContent);

describe('VORReportPageController', () => {
    let controller;
    let customController;

    beforeEach(() => {
        // Mock the grid view model
        const gridViewModel = {
            Events: {
                CollectionLoaded: {
                    subscribe: vi.fn()
                }
            },
            sortingOptions: vi.fn(),
            filterData: vi.fn(),
            LoadAllVORStatusStoreProcedureObjectCollection: vi.fn(),
            exportFilterPredicate: null,
            exportFilterParameters: null,
            FILTER_NAME: 'vorStatusFilter'
        };

        // Mock the sessions grid view model
        const sessionsGridViewModel = {
            Events: {
                CollectionLoaded: {
                    subscribe: vi.fn()
                }
            },
            LoadAllVORSessionsPerVehicleStoreProcedureObjectCollection: vi.fn(),
            exportFilterPredicate: null,
            exportFilterParameters: null,
            FILTER_NAME: 'vorSessionsFilter',
            StartDate: null,
            EndDate: null
        };

        // Mock the filter form view model
        const filterFormViewModel = {
            CurrentObject: vi.fn().mockReturnValue({
                Data: {
                    CustomerId: vi.fn(),
                    SiteId: vi.fn(),
                    DepartmentId: vi.fn(),
                    StartDate: vi.fn(),
                    EndDate: vi.fn(),
                    MultiSearch: vi.fn()
                }
            }),
            ShowError: vi.fn()
        };

        // Mock the controller with all required properties
        controller = {
            VORReportCombinedViewFormViewModel: {
                AllVORStatusStoreProcedureItemsGridViewModel: gridViewModel,
                AllVORSessionsPerVehicleStoreProcedureItemsGridViewModel: sessionsGridViewModel
            },
            VORReportFilterFormViewModel: filterFormViewModel,
            applicationController: {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn().mockReturnValue({
                            CustomerId: 'test-customer-id',
                            AllowedSiteIds: '{}',
                            role: null
                        })
                    }
                }
            },
            ShowError: vi.fn()
        };

        // Create the custom controller
        customController = new FleetXQ.Web.Controllers.VORReportPageControllerCustom(controller);
    });

    describe('loadPageData', () => {
        it('should set default sorting when grid data loads', () => {
            // Set up subscription callback
            let subscriptionCallback;
            controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.Events.CollectionLoaded.subscribe = vi.fn((callback) => {
                subscriptionCallback = callback;
                return { dispose: vi.fn() };
            });

            // Call loadPageData
            customController.loadPageData();

            // Simulate the CollectionLoaded event
            subscriptionCallback();

            // Verify sorting was set correctly
            expect(controller.VORReportCombinedViewFormViewModel.AllVORStatusStoreProcedureItemsGridViewModel.sortingOptions).toHaveBeenCalledWith({
                columnName: "TimezoneAdjustedStartDateTime",
                order: "desc"
            });
        });
    });
});
