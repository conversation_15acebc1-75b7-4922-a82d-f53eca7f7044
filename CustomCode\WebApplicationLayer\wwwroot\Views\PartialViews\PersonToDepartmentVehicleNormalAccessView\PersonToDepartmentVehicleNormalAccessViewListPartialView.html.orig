﻿
<!--
// This is Generated Code
// You should not modify this code as it may be overwritten
// Generated By Generative Objects 
-->
 
<div class="uiContainer list-container" id="{VIEWNAME}-{DISPLAYMODE}" data-bind="'css': { 'busy': {DATABINDROOT}StatusData.IsBusy(), 'disabled': !{DATABINDROOT}StatusData.IsEnabled() || {DATABINDROOT}StatusData.IsBusy() }" data-test-id="426c483b-d11c-4439-a973-ba480544ce0c">
  <div id="{VIEWNAME}Control-{DISPLAYMODE}">
    <div class="hideElt" data-bind="css: { hideElt : false }">
      <h2 data-bind="visible: {DATABINDROOT}StatusData.ShowTitle(), i18n: {DATABINDROOT}StatusData.Title()"></h2>
      <div class="no-data-message" data-bind="'visible' : {DATABINDROOT}StatusData.IsEmpty() &amp;&amp; !{DATABINDROOT}StatusData.IsBusy(), i18n: 'entities/PersonToDepartmentVehicleNormalAccessView/lists/PersonToDepartmentVehicleNormalAccessViewList:messages.noDataMessage'"></div>
      <div class="loading-content" data-bind="'visible' : {DATABINDROOT}StatusData.IsEmpty() &amp;&amp; {DATABINDROOT}StatusData.IsBusy()">
        <span class="loading-content-image"></span>
        <span data-bind="text: FleetXQ.Web.Messages.loadingMessage"></span>
      </div>
      <div class="uiContainer list-item-container">
        <div class="row" data-bind="'visible' : !{DATABINDROOT}StatusData.IsEmpty(), 'foreach' : { data: {DATABINDROOT}viewModelCollection, as: '{LISTNAME}KOData' }" id="{VIEWNAME}Data-{DISPLAYMODE}">
          <div class="col-4 uiContainer">
            <div>{#false,PersonToDepartmentVehicleNormalAccessViewForm,Form,,PersonToDepartmentVehicleNormalAccessView\PersonToDepartmentVehicleNormalAccessViewFormPartialView.html#}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
 
