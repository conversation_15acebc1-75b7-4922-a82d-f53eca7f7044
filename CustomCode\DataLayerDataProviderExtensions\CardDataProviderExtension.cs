﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.Components.Server.Custom;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class CardDataProviderExtension : IDataProviderExtension<CardDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceTwinHandler _deviceTwinHandler;
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthentication _authentication;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        private readonly ILoggingService _logger;

        // need a var to contain list of vehicles for later use
        private List<String> _iotDevices = new List<String>();
        private bool _isUpdate = false;

        private bool _dealerCardEdited = false;
        public CardDataProviderExtension(IDataFacade dataFacade, IDeviceTwinHandler deviceTwinHandler, IServiceProvider serviceProvider, IAuthentication authentication, IServiceScopeFactory serviceScopeFactory, ILoggingService logger)
        {
            _dataFacade = dataFacade;
            _deviceTwinHandler = deviceTwinHandler;
            _serviceProvider = serviceProvider;
            _authentication = authentication;
            _serviceScopeFactory = serviceScopeFactory;
            _logger = logger;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSet;
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSet;
        }

        private async Task OnBeforeSaveDataSet(OnBeforeSaveDataSetEventArgs e)
        {
            var card = e.Entity as CardDataObject;
            var oldCard = _serviceProvider.GetRequiredService<CardDataObject>();

            if (card == null)
            {
                return;
            }

            // Explicitly load driver if it's not loaded
            if (card.Driver == null && card.Id != Guid.Empty)
            {
                // Attempt to load the driver by querying for drivers with this card
                var drivers = await _dataFacade.DriverDataProvider.GetCollectionAsync(null, "CardDetailsId == @0", new object[] { card.Id });
                var driver = drivers.FirstOrDefault();

                if (driver != null)
                {
                    // Manually associate the driver with the card
                    card.Driver = driver;
                }
            }

            if (card.Driver == null || !card.Driver.Active)
            {
                return;
            }

            // Add PIN range validation
            if (card.Type == CardTypeEnum.PinID && !string.IsNullOrEmpty(card.CardNumber))
            {
                if (!int.TryParse(card.CardNumber, out int pinValue) || pinValue < 0 || pinValue > 65535)
                {
                    throw new GOServerException("PIN must be a number between 0 and 65535");
                }
            }

            if (int.TryParse(card.FacilityCode, out int facilityCode) && facilityCode > 255)
            {
                throw new GOServerException("Facility code should not be greater than 255.");
            }

            var cardAccess = card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true).Result;
            foreach (var access in cardAccess)
            {
                var vehicle = access.LoadVehicleAsync(skipSecurity: true).Result;
                if (vehicle != null)
                {
                    var module = vehicle.LoadModuleAsync(skipSecurity: true).Result;
                    if (module != null)
                    {
                        _iotDevices.Add(module.IoTDevice);
                    }
                }
            }

            var siteId = card.Driver?.Person?.SiteId;

            card.SiteId = siteId;

            if (!string.IsNullOrEmpty(card.CardNumber))
            {
                if (string.IsNullOrEmpty(card.FacilityCode))
                {
                    return; // Exit early if FacilityCode or CardNumber is null or empty
                }

                var isActiveDriver = card.Driver?.Person?.IsActiveDriver ?? false;

                // Handle Weigand generation based on driver status and card type
                if (!isActiveDriver)
                {
                    // Inactive drivers should not have a Weigand code
                    card.Weigand = null;
                }
                else if (card.KeypadReader == KeypadReaderEnum.CaptureCard)
                {
                    // For capture cards, remove leading zeros while preserving the value
                    if (!string.IsNullOrEmpty(card.Weigand))
                    {
                        card.Weigand = Regex.Replace(card.Weigand, "^0+(?!$)", "");
                    }
                }
                else
                {
                    // For non-capture cards, generate a new Weigand code
                    try
                    {
                        card.Weigand = CardToWeigand(card.CardNumber, card.FacilityCode, card.Type, card.KeypadReader);

                        // Validate the generated Weigand
                        if (string.IsNullOrEmpty(card.Weigand))
                        {
                            throw new GOServerException("Failed to generate valid Weigand code");
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException($"Error generating Weigand code: {ex.Message}");
                    }
                }
            }

            if (!string.IsNullOrEmpty(card.Weigand))
            {
                var site = card.Driver?.Person?.SiteId;

                // Check if site is not null before looking for duplicates
                if (site != null)
                {
                    var siteObject = _serviceProvider.GetRequiredService<SiteDataObject>();
                    siteObject.Id = site.Value;
                    siteObject = await _dataFacade.SiteDataProvider.GetAsync(siteObject);

                    var customer = await siteObject.LoadCustomerAsync();
                    var sites = await customer.LoadSitesAsync();

                    var siteIds = sites.Select(x => x.Id).ToList();
                    string siteIdQuery = string.Join(" || ", siteIds.Select((_, index) => $"SiteId == @{index + 1}"));
                    string finalQuery = $"Weigand == @0 && ({siteIdQuery}) && Id != @{siteIds.Count + 1}";

                    // Combine arguments: Weigand, individual SiteIds, and card.Id
                    var arguments = new List<object> { card.Weigand };

                    arguments.AddRange(siteIds.Cast<object>());
                    arguments.Add(card.Id);

                    var duplicateWeigand = (await _dataFacade.CardDataProvider.GetCollectionAsync(
                        null,
                        finalQuery,
                        arguments.ToArray()
                    )).Any();

                    if (duplicateWeigand)
                    {
                        throw new GOServerException("Weigand already in use");
                    }
                }
            }

            // check if the card is a dealer card and the whether weigand or active of the card changed or is new
            oldCard = (await _dataFacade.CardDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { card.Id })).SingleOrDefault();
            var maybeNewDealerDriver = card.DealerDriver;

            if (maybeNewDealerDriver != null && card.IsNew == true)
            {
                _dealerCardEdited = true;
                return;
            }

            if (oldCard != null)
            {
                var oldDealerDriver = await oldCard.LoadDealerDriverAsync();
                if (oldDealerDriver != null)
                {
                    if (oldCard.Weigand != card.Weigand || oldCard.Active != card.Active)
                    {
                        _dealerCardEdited = true;
                    }
                }
            }
            return;
        }

        private async Task OnAfterSaveDataSet(OnAfterSaveDataSetEventArgs e)
        {
            // skip if it is a new card
            if (!e.EntityBeforeSave.IsNew)
            {
                var card = e.EntityBeforeSave as CardDataObject;
                if (card == null)
                {
                    return;
                }
                if (e.EntityBeforeSave.IsMarkedForDeletion)
                {
                    return; // TODO => when deleted, the access to vehicles should be removed
                }
                if (!_isUpdate)
                {
                    // Get current user ID for authorization
                    var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                    var currentUserId = userClaims?.UserId;

                    _ = Task.Run(async () =>
                    {
                        var syncStart = DateTime.UtcNow;
                        try
                        {
                            await using var scope = _serviceScopeFactory.CreateAsyncScope();
                            var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                            if (currentUserId == null)
                            {
                                _logger?.LogInformation($"[CardDataProviderExtension] OnAfterSaveDataSet: currentUserId is null, skipping sync");
                                return;
                            }

                            foreach (var iotDevice in _iotDevices)
                            {
                                try
                                {
                                    await scopedDeviceTwinHandler.SyncDriverToVehicle(iotDevice, currentUserId.Value);
                                }
                                catch (Exception ex)
                                {
                                    _logger?.LogError(ex, $"Failed to sync device {iotDevice}: {ex.Message}");
                                }
                            }

                            var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                            _logger?.LogInformation($"[PERF] CardDataProviderExtension sync completed in {syncDuration}ms for {_iotDevices.Count} devices");
                        }
                        catch (Exception ex)
                        {
                            var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                            _logger?.LogError(ex, $"[PERF] CardDataProviderExtension sync failed after {syncDuration}ms: {ex.Message}");
                        }
                    });

                    _isUpdate = true;
                }
            }

            // if dealer card is edited, get all customer of the dealer and
            // get all vehicles of each customer and for each vehicle, sync the driver to the vehicle
            // WARNING: This is a very heavy operation, depending on number of customers and vehicles in a dealer
            if (_dealerCardEdited)
            {
                var card = e.EntityBeforeSave as CardDataObject;

                // Get current user ID for authorization
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                var currentUserId = userClaims?.UserId;

                _ = Task.Run(async () =>
                {
                    var syncStart = DateTime.UtcNow;
                    try
                    {
                        await using var scope = _serviceScopeFactory.CreateAsyncScope();
                        var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                        var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                        if (currentUserId == null)
                        {
                            _logger?.LogInformation($"[CardDataProviderExtension] Dealer card sync: currentUserId is null, skipping sync");
                            return;
                        }

                        // Reload card in the new scope
                        var scopedCard = scope.ServiceProvider.GetRequiredService<CardDataObject>();
                        scopedCard.Id = card.Id;
                        scopedCard = await scopedDataFacade.CardDataProvider.GetAsync(scopedCard, skipSecurity: true);

                        if (scopedCard == null)
                        {
                            return;
                        }

                        var dealerDriver = await scopedCard.LoadDealerDriverAsync(skipSecurity: true);
                        var dealer = await (await dealerDriver.LoadGOUserAsync(skipSecurity: true)).LoadDealerAsync(skipSecurity: true);
                        var customers = await scopedDataFacade.CustomerDataProvider.GetCollectionAsync(null, "DealerId == @0", new object[] { dealer.Id }, skipSecurity: true);

                        var totalVehicles = 0;
                        foreach (var customer in customers)
                        {
                            var vehicles = await scopedDataFacade.VehicleDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customer.Id }, skipSecurity: true);
                            foreach (var vehicle in vehicles)
                            {
                                var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                                if (module != null)
                                {
                                    try
                                    {
                                        await scopedDeviceTwinHandler.SyncDriverToVehicle(module.IoTDevice, currentUserId.Value);
                                        totalVehicles++;
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger?.LogError(ex, $"Failed to sync dealer card to vehicle {module.IoTDevice}: {ex.Message}");
                                    }
                                }
                            }
                        }

                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogInformation($"[PERF] CardDataProviderExtension dealer card sync completed in {syncDuration}ms for {totalVehicles} vehicles");
                    }
                    catch (Exception ex)
                    {
                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogError(ex, $"[PERF] CardDataProviderExtension dealer card sync failed after {syncDuration}ms: {ex.Message}");
                    }
                });

                _dealerCardEdited = false;
            }

            // NOTE: Card access creation has been moved to DriverDataProviderExtension
            // When a Driver gets a CardDetailsId set, it will create the necessary card access items

            return;
        }

        private string CardToWeigand(string cardNo, string weigandPre, CardTypeEnum cardType, KeypadReaderEnum keypadReader)
        {
            long card_no = long.Parse(cardNo);
            long weigandPreVal = int.Parse(weigandPre);
            long wiegand = 0;
            long m = 0;
            long n = 0;
            long temp = 0;
            int bitLength = 0;

            if (cardType == CardTypeEnum.PinID && keypadReader == KeypadReaderEnum.Smart)
            {
                weigandPreVal = 0xF0000000;
                card_no = Convert.ToInt64(cardNo, 16);
                wiegand = card_no;
                bitLength = Convert.ToString(wiegand, 2).Length;

                while (m == 0)
                {
                    m = weigandPreVal & card_no;
                    if (m == 0)
                    {
                        wiegand = weigandPreVal | wiegand;
                    }
                    weigandPreVal = weigandPreVal / 16;
                }
            }
            else if (cardType == CardTypeEnum.PinID && keypadReader == KeypadReaderEnum.Rosslare)
            {
                weigandPreVal = 0;
                wiegand = (weigandPreVal << 16) | card_no;
                bitLength = Convert.ToString(wiegand, 2).Length;
            }
            else if (cardType == CardTypeEnum.CardID && keypadReader == KeypadReaderEnum._48BitWeigand)
            {
                return CardToWeigand48Bit(card_no, weigandPreVal);
            }
            else
            {
                wiegand = (weigandPreVal << 16) | card_no;
            }

            if (weigandPreVal > 255)
            {
                m = (wiegand & 0xFFFFFFFF);
                wiegand = m * 2;
                n = 0;
                m = 0x10000;

                while (m > 0)
                {
                    if ((m & wiegand) > 0)
                    {
                        n++;
                    }
                    m >>= 1;
                }

                if (n % 2 != 0)
                {
                    temp = wiegand | 1;
                }
                else
                {
                    temp = wiegand;
                }

                temp = temp & 0xFFFFFFFFL;
            }
            else if (bitLength >= 17)
            {
                long i = (wiegand & 0xFFFFFFFFFFL);
                wiegand = i << 1;

                long j = 0;
                long mask = 0x20000000000L;

                while (mask > 0x800000)
                {
                    if ((mask & wiegand) != 0)
                    {
                        j++;
                    }
                    mask >>= 1;
                }

                if (j % 2 != 0)
                {
                    wiegand |= 0x20000000000L;
                }

                j = 0;
                mask = 0x800000;

                while (mask > 0)
                {
                    if ((mask & wiegand) != 0)
                    {
                        j++;
                    }
                    mask >>= 1;
                }

                if (j % 2 != 0)
                {
                    temp = wiegand;
                }
                else
                {
                    temp = wiegand | 1;
                }

                return temp.ToString("X");

            }
            else
            {
                m = (wiegand & 0xFFFFFF);
                wiegand = m * 2;
                m = 0x2000000;

                while (m != 0x1000)
                {
                    if ((m & wiegand) > 0)
                    {
                        n++;
                    }
                    m >>= 1;
                }

                if (n % 2 != 0)
                {
                    wiegand = (wiegand | 0x2000000);
                }

                n = 0;
                m = 0x1000;

                while (m > 0)
                {
                    if ((m & wiegand) > 0)
                    {
                        n++;
                    }
                    m >>= 1;
                }

                if (n % 2 != 0)
                {
                    temp = wiegand;
                }
                else
                {
                    temp = wiegand | 1;
                }
            }


            return temp.ToString("X");
        }

        private String CardToWeigand48Bit(long cardId, long weigandpre)
        {
            long facilityCd = weigandpre;
            string facilityCdBinaryPadded = Convert.ToString(facilityCd, 2).PadLeft(22, '0');
            string cardIdBinaryPadded = Convert.ToString(cardId, 2).PadLeft(23, '0');

            int totalOnesForEvenPar = CountOnes(new int[] { 2, 3, 5, 6, 8, 9, 11, 12, 14, 15, 17, 18, 20, 21 }, facilityCdBinaryPadded)
                    + CountOnes(new int[] { 1, 2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20, 22, 23 }, cardIdBinaryPadded);

            string evenParStr = "0";
            if (totalOnesForEvenPar % 2 == 0)
            {
                evenParStr = "0";
            }
            else
            {
                evenParStr = "1";
            }

            int totalOnesForOddPar = CountOnes(new int[] { 1, 2, 4, 5, 7, 8, 10, 11, 13, 14, 16, 17, 19, 20, 22 }, facilityCdBinaryPadded)
                    + CountOnes(new int[] { 1, 3, 4, 6, 7, 9, 10, 12, 13, 15, 16, 18, 19, 21, 22 }, cardIdBinaryPadded);

            string oddParStr = "0";
            if (totalOnesForOddPar % 2 != 0)
            {
                oddParStr = "0";
            }
            else
            {
                oddParStr = "1";
            }

            int totalOnesForOddPar2 = CountOnes(evenParStr + facilityCdBinaryPadded + cardIdBinaryPadded + oddParStr);

            string oddParity2Str = "0";
            if (totalOnesForOddPar2 % 2 != 0)
            {
                oddParity2Str = "0";
            }
            else
            {
                oddParity2Str = "1";
            }

            string binaryStr = oddParity2Str + evenParStr + facilityCdBinaryPadded + cardIdBinaryPadded + oddParStr;
            string hexStr = Convert.ToInt64(binaryStr, 2).ToString("X");

            return hexStr;
        }

        private static int CountOnes(string binary)
        {
            int count = 0;
            string[] binaryArray = binary.ToCharArray().Select(c => c.ToString()).ToArray();

            for (int i = 0; i < 47; i++)
            {
                if ("1".Equals(binaryArray[i]))
                {
                    count += 1;
                }
            }

            return count;
        }

        private static int CountOnes(int[] indexes, string binary)
        {
            int count = 0;
            string[] binaryArray = binary.ToCharArray().Select(c => c.ToString()).ToArray();

            foreach (int index in indexes)
            {
                if ("1".Equals(binaryArray[index - 1]))
                {
                    count += 1;
                }
            }

            return count;
        }

    }
}
