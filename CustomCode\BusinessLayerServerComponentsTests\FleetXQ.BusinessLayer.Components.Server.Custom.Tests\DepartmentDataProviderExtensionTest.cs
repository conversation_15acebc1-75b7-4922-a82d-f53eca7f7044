using DocumentFormat.OpenXml.EMMA;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class DepartmentDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"DepartmentDataProviderExtensionTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);

            // Run the seed script after database creation
            string sqlFileRootPath = "../../../../../../Scripts/OneOffScripts";
            string seedReportTypesPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "SeedReportTypes.sql"));
            if (!File.Exists(seedReportTypesPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedReportTypesPath}\"");
            }
            _databaseFactory.RunSqlScript(_testDatabaseName, File.ReadAllText(seedReportTypesPath));

            string seedAlertsPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "SeedAlerts.sql"));
            if (!File.Exists(seedAlertsPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedAlertsPath}\"");
            }
            _databaseFactory.RunSqlScript(_testDatabaseName, File.ReadAllText(seedAlertsPath));

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<SiteDataObject> CreateTestSiteAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            return await _dataFacade.SiteDataProvider.SaveAsync(site);
        }

        [Test]
        public async Task OnBeforeGetCollection_ExcludesDeletedDepartments()
        {
            // Arrange
            var site = await CreateTestSiteAsync();

            // Create active department
            var activeDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            activeDepartment.Id = Guid.NewGuid();
            activeDepartment.SiteId = site.Id;
            activeDepartment.Name = "Active Department";
            activeDepartment.DeletedAtUtc = null;
            await _dataFacade.DepartmentDataProvider.SaveAsync(activeDepartment);

            // Create deleted department
            var deletedDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            deletedDepartment.Id = Guid.NewGuid();
            deletedDepartment.SiteId = site.Id;
            deletedDepartment.Name = "Deleted Department";
            deletedDepartment.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.DepartmentDataProvider.SaveAsync(deletedDepartment);

            // Act
            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(
                null, "SiteId == @0", new object[] { site.Id });

            // Assert
            Assert.That(departments.Count(), Is.EqualTo(1), "Should only return active departments");
            Assert.That(departments.First().Id, Is.EqualTo(activeDepartment.Id), "Should return the active department");
        }

        [Test]
        public async Task OnBeforeGetCollection_CombinesWithExistingFilter()
        {
            // Arrange
            var site = await CreateTestSiteAsync();

            // Create departments with different names
            var department1 = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department1.Id = Guid.NewGuid();
            department1.SiteId = site.Id;
            department1.Name = "Test Department 1";
            department1.DeletedAtUtc = null;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department1);

            var department2 = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department2.Id = Guid.NewGuid();
            department2.SiteId = site.Id;
            department2.Name = "Test Department 2";
            department2.DeletedAtUtc = null;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department2);

            var deletedDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            deletedDepartment.Id = Guid.NewGuid();
            deletedDepartment.SiteId = site.Id;
            deletedDepartment.Name = "Test Department 1";
            deletedDepartment.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.DepartmentDataProvider.SaveAsync(deletedDepartment);

            // Act
            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(
                null, "SiteId == @0 && Name == @1", new object[] { site.Id, "Test Department 1" });

            // Assert
            Assert.That(departments.Count(), Is.EqualTo(1), "Should only return active departments matching the filter");
            Assert.That(departments.First().Id, Is.EqualTo(department1.Id), "Should return the active department with matching name");
        }
    }
}