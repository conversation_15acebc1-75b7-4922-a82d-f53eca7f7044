# FXQ-3046: Wiegand ID Column in Impact Report Shows Empty Values for Deleted Users Implementation Plan

## Issue Description
The Wiegand ID column in the Impact Report currently shows empty values for deleted users. When a driver is deleted from the system, their historical Wiegand values are lost from the Impact Report, making it difficult to track and analyze historical impact data. The system needs to retain historical Wiegand values even when users are deleted.

## Root Cause Analysis
Based on codebase analysis:
1. The `GetAllImpacts` stored procedure uses `LEFT JOIN` for Driver and Person tables
2. When a driver is deleted, the `sess.DriverId` becomes orphaned and the JOIN returns NULL
3. The current data flow: `Impact -> Session -> Driver -> Person -> Card -> Weigand`
4. When Driver/Person is deleted, the entire chain breaks and Weigand value is lost
5. The `getImpact_Session_Driver_Card_WeigandValue` function relies on the generated code that follows this broken chain
6. No historical preservation mechanism exists for Wiegand values when users are deleted

## Implementation Plan

### Phase 1: Analysis and Design
**Estimated Duration:** 2-3 hours

- [ ] **Task 1.1: Analyze Current Data Flow**
  - Review the `GetAllImpacts` stored procedure in `Sql/CreateViews.custom.sql`
  - Document the current JOIN structure and data relationships
  - Identify all points where Wiegand data could be lost
  - **Dependencies:** None
  - **Deliverables:** Current data flow documentation

- [ ] **Task 1.2: Design Historical Data Preservation Strategy**
  - Design approach for preserving Wiegand values at the Impact level
  - Evaluate options: denormalization vs. historical tables vs. soft deletes
  - Determine the best approach for maintaining data integrity
  - **Dependencies:** Task 1.1
  - **Deliverables:** Historical data preservation design document

- [ ] **Task 1.3: Database Schema Analysis**
  - Review current Impact table structure
  - Analyze Card table relationships and constraints
  - Identify potential schema changes needed
  - **Dependencies:** None
  - **Deliverables:** Database schema analysis report

- [ ] **Task 1.4: Impact Assessment**
  - Identify all reports and views that depend on Wiegand data
  - Assess impact on existing functionality
  - Document potential breaking changes
  - **Dependencies:** Task 1.1
  - **Deliverables:** Impact assessment document

- [ ] **Task 1.5: Create Test Data Scenarios**
  - Create test scenarios with deleted drivers and historical impacts
  - Set up test data with various deletion scenarios
  - Prepare validation test cases
  - **Dependencies:** None
  - **Deliverables:** Comprehensive test data and scenarios

### Phase 2: Database Schema Changes
**Estimated Duration:** 3-4 hours

- [ ] **Task 2.1: Add Historical Wiegand Column to Impact Table**
  - Add `HistoricalWiegand` column to `dbo.Impact` table
  - Set appropriate data type and constraints
  - Add index for performance optimization
  - **Dependencies:** Task 1.2
  - **Deliverables:** Database migration script

- [ ] **Task 2.2: Create Data Migration Script**
  - Populate `HistoricalWiegand` column with existing data
  - Handle cases where current Wiegand values exist
  - Ensure data consistency and integrity
  - **Dependencies:** Task 2.1
  - **Deliverables:** Data migration script

- [ ] **Task 2.3: Update Impact Creation Logic**
  - Modify `DriverImpactAPI.StoreImpactMessageAsync` method
  - Capture Wiegand value at impact creation time
  - Store historical Wiegand in new column
  - **Dependencies:** Task 2.1
  - **Deliverables:** Updated impact creation logic

- [ ] **Task 2.4: Create Historical Data Cleanup Procedure**
  - Design procedure to clean up orphaned historical data
  - Implement data retention policies
  - Create maintenance scripts
  - **Dependencies:** Task 2.1
  - **Deliverables:** Data cleanup procedures

- [ ] **Task 2.5: Update Database Constraints and Indexes**
  - Add appropriate foreign key constraints
  - Create indexes for performance optimization
  - Update existing constraints if needed
  - **Dependencies:** Task 2.1
  - **Deliverables:** Database constraint and index updates

### Phase 3: Stored Procedure Updates
**Estimated Duration:** 2-3 hours

- [ ] **Task 3.1: Update GetAllImpacts Stored Procedure**
  - Modify the stored procedure to use `HistoricalWiegand` column
  - Update JOIN logic to prioritize historical data
  - Maintain backward compatibility with existing queries
  - **Dependencies:** Task 2.1, Task 2.3
  - **Deliverables:** Updated stored procedure

- [ ] **Task 3.2: Create Fallback Logic**
  - Implement logic to use current Wiegand if historical is not available
  - Handle edge cases and data inconsistencies
  - Ensure robust error handling
  - **Dependencies:** Task 3.1
  - **Deliverables:** Fallback logic implementation

- [ ] **Task 3.3: Update Related Stored Procedures**
  - Review and update other stored procedures that use Wiegand data
  - Ensure consistency across all database operations
  - Update any dependent views or functions
  - **Dependencies:** Task 3.1
  - **Deliverables:** Updated related stored procedures

- [ ] **Task 3.4: Performance Optimization**
  - Analyze query performance with new schema
  - Optimize indexes and query plans
  - Implement caching strategies if needed
  - **Dependencies:** Task 3.1
  - **Deliverables:** Performance optimization results

### Phase 4: Application Layer Updates
**Estimated Duration:** 2-3 hours

- [ ] **Task 4.1: Update Data Provider Layer**
  - Modify `AllImpactsViewDataProvider` to handle historical Wiegand
  - Update data object models if needed
  - Ensure proper data mapping
  - **Dependencies:** Task 3.1
  - **Deliverables:** Updated data provider layer

- [ ] **Task 4.2: Update ViewModel Logic**
  - Modify `getImpact_Session_Driver_Card_WeigandValue` function
  - Implement logic to display historical Wiegand values
  - Handle cases where both historical and current data exist
  - **Dependencies:** Task 4.1
  - **Deliverables:** Updated ViewModel logic

- [ ] **Task 4.3: Update UI Display Logic**
  - Modify the HTML template to handle historical data
  - Update data binding to show appropriate Wiegand values
  - Add visual indicators for historical vs. current data
  - **Dependencies:** Task 4.2
  - **Deliverables:** Updated UI display logic

- [ ] **Task 4.4: Update Export Functionality**
  - Ensure exported reports include historical Wiegand values
  - Update export logic to handle new data structure
  - Maintain compatibility with existing export formats
  - **Dependencies:** Task 4.1
  - **Deliverables:** Updated export functionality

### Phase 5: Integration and Testing
**Estimated Duration:** 3-4 hours

- [ ] **Task 5.1: Create Unit Tests**
  - Write unit tests for new data provider methods
  - Test historical Wiegand retrieval logic
  - Create tests for edge cases and error scenarios
  - **Dependencies:** Phase 4 completion
  - **Deliverables:** Comprehensive unit test suite

- [ ] **Task 5.2: Create Integration Tests**
  - Test end-to-end data flow from impact creation to display
  - Verify historical data preservation
  - Test with deleted user scenarios
  - **Dependencies:** Phase 4 completion
  - **Deliverables:** Integration test suite

- [ ] **Task 5.3: Performance Testing**
  - Test query performance with large datasets
  - Verify impact on report loading times
  - Test concurrent user scenarios
  - **Dependencies:** Task 3.4
  - **Deliverables:** Performance test results

- [ ] **Task 5.4: Data Migration Testing**
  - Test data migration scripts with production-like data
  - Verify data integrity after migration
  - Test rollback procedures
  - **Dependencies:** Task 2.2
  - **Deliverables:** Migration testing results

- [ ] **Task 5.5: User Acceptance Testing**
  - Test with real-world scenarios involving deleted users
  - Verify historical Wiegand values are displayed correctly
  - Test all report functionality
  - **Dependencies:** Phase 4 completion
  - **Deliverables:** UAT test plan and results

### Phase 6: Documentation and Deployment
**Estimated Duration:** 1-2 hours

- [ ] **Task 6.1: Create Technical Documentation**
  - Document database schema changes
  - Create API documentation for new methods
  - Document data migration procedures
  - **Dependencies:** Phase 5 completion
  - **Deliverables:** Technical documentation

- [ ] **Task 6.2: Create User Documentation**
  - Update user guides for Impact Report
  - Document new functionality and features
  - Create troubleshooting guides
  - **Dependencies:** Phase 5 completion
  - **Deliverables:** User documentation

- [ ] **Task 6.3: Prepare Deployment Package**
  - Create deployment scripts and procedures
  - Prepare rollback procedures
  - Create configuration updates
  - **Dependencies:** Phase 5 completion
  - **Deliverables:** Deployment package

- [ ] **Task 6.4: Conduct Code Review**
  - Review all code changes with team members
  - Ensure coding standards and best practices
  - Verify security and performance considerations
  - **Dependencies:** Phase 5 completion
  - **Deliverables:** Code review approval

- [ ] **Task 6.5: Final Testing and Validation**
  - Perform final end-to-end testing
  - Validate all requirements are met
  - Confirm deployment readiness
  - **Dependencies:** All previous tasks
  - **Deliverables:** Final validation report

## Success Criteria
1. Historical Wiegand values are preserved when users are deleted
2. Impact Report displays correct Wiegand values for both active and deleted users
3. No performance degradation in report loading
4. All existing functionality remains intact
5. Data integrity is maintained throughout the system
6. Export functionality includes historical Wiegand values
7. Backward compatibility is maintained
8. Comprehensive test coverage is achieved

## Risk Mitigation
- **Risk:** Data migration issues with large datasets
  - **Mitigation:** Thorough testing with production-like data and rollback procedures
- **Risk:** Performance impact on existing reports
  - **Mitigation:** Performance testing and optimization before deployment
- **Risk:** Breaking existing functionality
  - **Mitigation:** Comprehensive regression testing and gradual rollout
- **Risk:** Data inconsistency during migration
  - **Mitigation:** Data validation scripts and integrity checks

## Dependencies
- Access to development environment and database
- Coordination with database administration team
- Testing environment with representative data
- Code review and approval process
- Deployment coordination with operations team

## Technical Notes
- The solution involves adding a `HistoricalWiegand` column to the `Impact` table
- Historical Wiegand values will be captured at impact creation time
- The stored procedure will prioritize historical data over current data
- Backward compatibility will be maintained for existing functionality
- Performance optimization will be implemented to minimize impact on report loading times 