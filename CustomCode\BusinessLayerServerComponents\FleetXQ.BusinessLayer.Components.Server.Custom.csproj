﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Business Layer Server Components Custom</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" />
    <PackageReference Include="GenerativeObjects.Services" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" />
    <PackageReference Include="Microsoft.Azure.Devices" />
    <PackageReference Include="Azure.Storage.Blobs" />
    <PackageReference Include="Azure.Messaging.ServiceBus" />
    <PackageReference Include="System.IO" />
    <PackageReference Include="Microsoft.CSharp" />
    <PackageReference Include="Microsoft.Extensions.Logging.ApplicationInsights" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="NLog" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\BusinessLayerORMSupportClasses\FleetXQ.BusinessLayer.ORMSupportClasses.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
    <ProjectReference Include="..\DataLayer\FleetXQ.Data.DataObjects.Custom.csproj" />
    <ProjectReference Include="..\DataLayerDataProviders\FleetXQ.Data.DataProviders.Custom.csproj" />
  </ItemGroup>
</Project>