import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';

describe('PedestrianDetectionHistoryGridViewModel', () => {
    let viewModel;
    let customViewModel;
    let mockJQuery;

    beforeEach(() => {
        // Mock jQuery
        mockJQuery = {
            ajax: vi.fn()
        };
        global.$ = mockJQuery;
        
        // Mock FleetXQ global object
        global.FleetXQ = {
            Web: {
                Application: {
                    BaseURL: 'http://test-url/',
                    CSRF_TOKEN: 'test-token',
                    Messages: {
                        componentError: '%OPERATION% failed'
                    }
                },
                ViewModels: {
                    PedestrianDetectionHistoryGridViewModelCustom: function(vm) {
                        this.viewmodel = vm;
                        this.serviceUrl = FleetXQ.Web.Application.BaseURL + "api/exportjobstatus/";
                        
                        this.initialize = function() {
                            this.viewmodel.onExportSuccess = function(data) {
                                var configuration = {
                                    contextId: this.viewmodel.contextId,
                                    successHandler: this.viewmodel.onExport_ChainedCommand0Success,
                                    errorHandler: this.viewmodel.ShowError,
                                    fileName: this.viewmodel.fileName,
                                    fileUrl: this.viewmodel.fileUrl
                                };

                                if (data.Data.TaskStatus() === 5) {
                                    configuration.fileUrl = data.Data.ExportedFileUrl();
                                    configuration.fileName = data.Data.ExportedFile();
                                    ApplicationController.getProxyForComponent("FileDownloader").DownloadFile(configuration);
                                } else if (data.Data.TaskStatus() !== -1) {
                                    setTimeout(() => this.checkExportStatus(data.Data.Id(), configuration), 2000);
                                }
                            }.bind(this);
                        };

                        this.GetExportStatusById = function(configuration) {
                            return $.ajax({
                                url: this.serviceUrl + "byid/" + configuration.id,
                                dataType: "json",
                                type: "GET",
                                headers: {
                                    'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
                                },
                                data: {
                                    dateformat: "ISO8601"
                                },
                                success: function(result) {
                                    if (configuration.successHandler) {
                                        configuration.successHandler(result);
                                    }
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    if (configuration.errorHandler) {
                                        configuration.errorHandler(errorThrown);
                                    }
                                }
                            });
                        };

                        this.checkExportStatus = function(id, configuration) {
                            this.GetExportStatusById({
                                id: id,
                                contextId: configuration.contextId,
                                successHandler: function(result) {
                                    if (result.TaskStatus === 5) {
                                        configuration.fileUrl = FleetXQ.Web.Application.BaseURL + "api/exportjobstatus/file/" + result.Id + "/ExportedFile?t=";
                                        configuration.fileName = result.ExportedFile;
                                        ApplicationController.getProxyForComponent("FileDownloader").DownloadFile(configuration);
                                    } else if (result.TaskStatus !== -1) {
                                        setTimeout(() => this.checkExportStatus(id, configuration), 2000);
                                    } else {
                                        configuration.errorHandler("Export task failed.");
                                    }
                                }.bind(this),
                                errorHandler: configuration.errorHandler
                            });
                        };
                    }
                }
            }
        };

        // Mock the base viewModel
        viewModel = {
            contextId: 'test-context',
            onExport_ChainedCommand0Success: vi.fn(),
            ShowError: vi.fn(),
            fileName: 'test-file',
            fileUrl: 'test-url'
        };

        // Create instance of custom view model
        customViewModel = new FleetXQ.Web.ViewModels.PedestrianDetectionHistoryGridViewModelCustom(viewModel);
    });

    afterEach(() => {
        vi.clearAllMocks();
        delete global.FleetXQ;
        delete global.$;
    });

    describe('initialize', () => {
        it('should set up onExportSuccess handler', () => {
            customViewModel.initialize();
            expect(typeof viewModel.onExportSuccess).toBe('function');
        });
    });

    describe('GetExportStatusById', () => {
        it('should make ajax call to get export status', () => {
            const configuration = {
                id: 'test-id',
                contextId: 'test-context',
                successHandler: vi.fn(),
                errorHandler: vi.fn()
            };

            customViewModel.GetExportStatusById(configuration);

            expect(mockJQuery.ajax).toHaveBeenCalledWith({
                url: 'http://test-url/api/exportjobstatus/byid/test-id',
                dataType: 'json',
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': 'test-token'
                },
                data: {
                    dateformat: 'ISO8601'
                },
                success: expect.any(Function),
                error: expect.any(Function)
            });
        });

        it('should handle successful response', () => {
            const configuration = {
                id: 'test-id',
                successHandler: vi.fn()
            };

            const mockResult = { status: 'success' };

            customViewModel.GetExportStatusById(configuration);

            // Get the success callback from the ajax call
            const successCallback = mockJQuery.ajax.mock.calls[0][0].success;
            successCallback(mockResult);

            expect(configuration.successHandler).toHaveBeenCalledWith(mockResult);
        });

        it('should handle null response', () => {
            const configuration = {
                id: 'test-id',
                successHandler: vi.fn()
            };

            customViewModel.GetExportStatusById(configuration);

            // Get the success callback from the ajax call
            const successCallback = mockJQuery.ajax.mock.calls[0][0].success;
            successCallback(null);

            expect(configuration.successHandler).toHaveBeenCalledWith(null);
        });
    });

    describe('onExportSuccess', () => {
        beforeEach(() => {
            global.ApplicationController = {
                getProxyForComponent: vi.fn().mockReturnValue({
                    DownloadFile: vi.fn()
                })
            };
            customViewModel.initialize();
        });

        it('should handle completed export immediately', () => {
            const mockData = {
                Data: {
                    TaskStatus: vi.fn().mockReturnValue(5),
                    ExportedFileUrl: vi.fn().mockReturnValue('test-url'),
                    ExportedFile: vi.fn().mockReturnValue('test-file')
                }
            };

            viewModel.onExportSuccess(mockData);

            expect(ApplicationController.getProxyForComponent).toHaveBeenCalledWith('FileDownloader');
            expect(ApplicationController.getProxyForComponent().DownloadFile).toHaveBeenCalled();
        });

        it('should poll for export status when not complete', () => {
            vi.useFakeTimers();
            
            const mockData = {
                Data: {
                    TaskStatus: vi.fn().mockReturnValue(1),
                    Id: vi.fn().mockReturnValue('test-id')
                }
            };

            viewModel.onExportSuccess(mockData);

            vi.advanceTimersByTime(2000);
            expect(mockJQuery.ajax).toHaveBeenCalled();

            vi.useRealTimers();
        });
    });
});
