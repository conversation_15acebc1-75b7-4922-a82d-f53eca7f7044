import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock knockout
const ko = {
    observableArray: (array = []) => {
        let currentArray = array;
        const observable = function (newArray) {
            if (arguments.length === 0) {
                return currentArray;
            }
            currentArray = newArray;
            observable.subscribe(currentArray);
        };
        observable.array = currentArray;
        observable.push = (item) => {
            currentArray.push(item);
            observable.subscribe(currentArray);
        };
        observable.removeAll = () => {
            currentArray = [];
            observable.subscribe(currentArray);
        };
        observable.subscribe = vi.fn();
        Object.defineProperty(observable, 'length', {
            get: () => currentArray.length
        });
        observable.get = (index) => currentArray[index];
        observable.includes = (item) => currentArray.includes(item);
        return observable;
    }
};

// Mock FleetXQ namespace
const FleetXQ = {
    Web: {
        ViewModels: {
            ChecklistDetailGridViewModelCustom: function (viewmodel) {
                var self = this;
                this.viewmodel = viewmodel;

                this.initialize = function () {
                    self.viewmodel.hasFailedAnswer = function (item) {
                        if (!item || !item.Data) {
                            return '';
                        }

                        var failed = item.Data.Failed;
                        if (typeof failed === 'function') {
                            failed = failed();
                        }
                        return failed === true ? 'failed-answer-row' : '';
                    };
                };
            }
        }
    }
};

describe('ChecklistDetailGridViewModel', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Mock the viewmodel object
        viewModel = {
            hasFailedAnswer: null,
            ChecklistDetailObjectCollection: ko.observableArray([])
        };

        // Initialize the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.ChecklistDetailGridViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('hasFailedAnswer', () => {
        it('should return empty string when item is null', () => {
            const result = viewModel.hasFailedAnswer(null);
            expect(result).toBe('');
        });

        it('should return empty string when item.Data is null', () => {
            const result = viewModel.hasFailedAnswer({ Data: null });
            expect(result).toBe('');
        });

        it('should return empty string when Failed is false', () => {
            const result = viewModel.hasFailedAnswer({ Data: { Failed: false } });
            expect(result).toBe('');
        });

        it('should return "failed-answer-row" when Failed is true', () => {
            const result = viewModel.hasFailedAnswer({ Data: { Failed: true } });
            expect(result).toBe('failed-answer-row');
        });

        it('should handle Failed as a function returning true', () => {
            const result = viewModel.hasFailedAnswer({
                Data: {
                    Failed: () => true
                }
            });
            expect(result).toBe('failed-answer-row');
        });
    });
});
