import { describe, it, expect, beforeEach, vi } from 'vitest'
import fs from 'fs'
import path from 'path'

describe('PreOperationalChecklistForm1ViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let mockDepartmentChecklist;
    let mockDepartmentChecklistForm;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/PreOperationalChecklist/PreOperationalChecklistForm1ViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create mock department checklist with language enablement methods
        mockDepartmentChecklist = {
            Data: {
                IsThaiEnabled: vi.fn(() => false),
                IsFrenchEnabled: vi.fn(() => false),
                IsFilipinoEnabled: vi.fn(() => false),
                IsSpanishEnabled: vi.fn(() => false),
                IsTraditionalChineseEnabled: vi.fn(() => false),
                IsVietnameseEnabled: vi.fn(() => false)
            }
        };

        // Create mock department checklist form view model
        mockDepartmentChecklistForm = {
            GetDepartmentChecklistObject: vi.fn(() => mockDepartmentChecklist)
        };

        // Create base view model with required properties
        viewModel = {
            parentViewModel: {
                DepartmentChecklistFormViewModel: mockDepartmentChecklistForm
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.PreOperationalChecklistForm1ViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('IsThaiQuestionVisible', () => {
        it('should return false when parentViewModel is null', () => {
            viewModel.parentViewModel = null;
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
        });

        it('should return false when DepartmentChecklistFormViewModel is null', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = null;
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
        });

        it('should return false when department checklist is null', () => {
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(null);
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
        });

        it('should return false when Thai is not enabled', () => {
            mockDepartmentChecklist.Data.IsThaiEnabled.mockReturnValue(false);
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
        });

        it('should return true when Thai is enabled', () => {
            mockDepartmentChecklist.Data.IsThaiEnabled.mockReturnValue(true);
            expect(customViewModel.IsThaiQuestionVisible()).toBe(true);
        });
    });

    describe('IsFrenchQuestionVisible', () => {
        it('should return false when parentViewModel is null', () => {
            viewModel.parentViewModel = null;
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
        });

        it('should return false when DepartmentChecklistFormViewModel is null', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = null;
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
        });

        it('should return false when department checklist is null', () => {
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(null);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
        });

        it('should return false when French is not enabled', () => {
            mockDepartmentChecklist.Data.IsFrenchEnabled.mockReturnValue(false);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
        });

        it('should return true when French is enabled', () => {
            mockDepartmentChecklist.Data.IsFrenchEnabled.mockReturnValue(true);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(true);
        });
    });

    describe('IsFilipinoQuestionVisible', () => {
        it('should return false when parentViewModel is null', () => {
            viewModel.parentViewModel = null;
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
        });

        it('should return false when DepartmentChecklistFormViewModel is null', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = null;
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
        });

        it('should return false when department checklist is null', () => {
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(null);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
        });

        it('should return false when Filipino is not enabled', () => {
            mockDepartmentChecklist.Data.IsFilipinoEnabled.mockReturnValue(false);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
        });

        it('should return true when Filipino is enabled', () => {
            mockDepartmentChecklist.Data.IsFilipinoEnabled.mockReturnValue(true);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(true);
        });
    });

    describe('IsSpanishQuestionVisible', () => {
        it('should return false when parentViewModel is null', () => {
            viewModel.parentViewModel = null;
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
        });

        it('should return false when DepartmentChecklistFormViewModel is null', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = null;
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
        });

        it('should return false when department checklist is null', () => {
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(null);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
        });

        it('should return false when Spanish is not enabled', () => {
            mockDepartmentChecklist.Data.IsSpanishEnabled.mockReturnValue(false);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
        });

        it('should return true when Spanish is enabled', () => {
            mockDepartmentChecklist.Data.IsSpanishEnabled.mockReturnValue(true);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(true);
        });
    });

    describe('IsTraditionalChineseQuestionVisible', () => {
        it('should return false when parentViewModel is null', () => {
            viewModel.parentViewModel = null;
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
        });

        it('should return false when DepartmentChecklistFormViewModel is null', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = null;
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
        });

        it('should return false when department checklist is null', () => {
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(null);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
        });

        it('should return false when Traditional Chinese is not enabled', () => {
            mockDepartmentChecklist.Data.IsTraditionalChineseEnabled.mockReturnValue(false);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
        });

        it('should return true when Traditional Chinese is enabled', () => {
            mockDepartmentChecklist.Data.IsTraditionalChineseEnabled.mockReturnValue(true);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(true);
        });
    });

    describe('IsVietnameseQuestionVisible', () => {
        it('should return false when parentViewModel is null', () => {
            viewModel.parentViewModel = null;
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should return false when DepartmentChecklistFormViewModel is null', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = null;
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should return false when department checklist is null', () => {
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(null);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should return false when Vietnamese is not enabled', () => {
            mockDepartmentChecklist.Data.IsVietnameseEnabled.mockReturnValue(false);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should return true when Vietnamese is enabled', () => {
            mockDepartmentChecklist.Data.IsVietnameseEnabled.mockReturnValue(true);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(true);
        });
    });

    describe('Multiple language scenarios', () => {
        it('should handle multiple languages enabled simultaneously', () => {
            mockDepartmentChecklist.Data.IsThaiEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsFrenchEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsFilipinoEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsSpanishEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsTraditionalChineseEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsVietnameseEnabled.mockReturnValue(true);

            expect(customViewModel.IsThaiQuestionVisible()).toBe(true);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(true);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(true);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(true);
        });

        it('should handle all languages disabled', () => {
            mockDepartmentChecklist.Data.IsThaiEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsFrenchEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsFilipinoEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsSpanishEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsTraditionalChineseEnabled.mockReturnValue(false);
            mockDepartmentChecklist.Data.IsVietnameseEnabled.mockReturnValue(false);

            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should handle all languages enabled', () => {
            mockDepartmentChecklist.Data.IsThaiEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsFrenchEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsFilipinoEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsSpanishEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsTraditionalChineseEnabled.mockReturnValue(true);
            mockDepartmentChecklist.Data.IsVietnameseEnabled.mockReturnValue(true);

            expect(customViewModel.IsThaiQuestionVisible()).toBe(true);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(true);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(true);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(true);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(true);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(true);
        });
    });

    describe('initialize', () => {
        it('should call initialize without errors', () => {
            expect(() => customViewModel.initialize()).not.toThrow();
        });

        it('should have initialize function defined', () => {
            expect(typeof customViewModel.initialize).toBe('function');
        });
    });

    describe('Edge cases', () => {
        it('should handle undefined parentViewModel', () => {
            viewModel.parentViewModel = undefined;
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should handle undefined DepartmentChecklistFormViewModel', () => {
            viewModel.parentViewModel.DepartmentChecklistFormViewModel = undefined;
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });

        it('should handle department checklist without Data property', () => {
            const invalidChecklist = {};
            mockDepartmentChecklistForm.GetDepartmentChecklistObject.mockReturnValue(invalidChecklist);
            expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
            expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
            expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
            expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
            expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
            expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
        });
    });
}); 