graph TD
    %% User Interface Layer
    subgraph "UI Layer"
        A[Normal Access Form] 
        B[Supervisor Access Form]
        C[Site Tab Filter]
        D[Department Tab Filter]
        E[Model Tab Filter]
        F[Vehicle Tab Filter]
    end

    %% ViewModel Layer
    subgraph "ViewModel Layer"
        G[PersonVehicleAccessFormFormViewModel]
        H[SupervisorVehicleAccessFormFormViewModel]
        I[PersonToSiteVehicleNormalAccessViewItemsListViewModel]
        J[PersonToDepartmentVehicleNormalAccessViewItemsListViewModel]
        K[PersonToModelVehicleNormalAccessViewItemsListViewModel]
        L[PersonToPerVehicleNormalAccessViewItemsListViewModel]
        M[Filter ViewModels]
    end

    %% Business Logic Layer
    subgraph "Business Layer"
        N[Access Control Engine]
        O[Permission Validator]
        P[Command Processor]
        Q[Filter Logic]
    end

    %% Data Layer
    subgraph "Data Layer"
        R[Access Data Provider]
        S[Site Data Provider]
        T[Department Data Provider]
        U[Model Data Provider]
        V[Vehicle Data Provider]
    end

    %% Database
    subgraph "Database"
        W[(Person Access Tables)]
        X[(Site Tables)]
        Y[(Department Tables)]
        Z[(Model Tables)]
        AA[(Vehicle Tables)]
    end

    %% Connections
    A --> G
    B --> H
    C --> I
    D --> J
    E --> K
    F --> L
    
    G --> I
    G --> J
    G --> K
    G --> L
    
    H --> I
    H --> J
    H --> K
    H --> L
    
    I --> M
    J --> M
    K --> M
    L --> M
    
    M --> Q
    I --> N
    J --> N
    K --> N
    L --> N
    
    N --> O
    N --> P
    O --> Q
    
    P --> R
    Q --> S
    Q --> T
    Q --> U
    Q --> V
    
    R --> W
    S --> X
    T --> Y
    U --> Z
    V --> AA

    %% Context Isolation
    classDef normal fill:#e1f5fe
    classDef supervisor fill:#fff3e0
    classDef shared fill:#f3e5f5
    
    class A,G normal
    class B,H supervisor
    class I,J,K,L,M,N,O,P,Q,R,S,T,U,V shared
