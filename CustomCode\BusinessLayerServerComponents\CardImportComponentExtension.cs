using DocumentFormat.OpenXml.Office2010.Drawing;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{

    public class CardImportComponentExtension : IImportExportComponentExtension<CardImportSection0Component,
        CardDataObject>
    {
        private readonly IDataFacade dataFacade;
        private readonly IServiceProvider serviceProvider;

        public CardImportComponentExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {
            this.dataFacade = dataFacade;
            this.serviceProvider = serviceProvider;
        }

        public void Init(IImportExportComponent<CardDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<CardDataObject> arg)
        {
            var customerName = arg.DataRow[CardImportSection0Component.COL_CUSTOMER].ToString();
            var siteName = arg.DataRow[CardImportSection0Component.COL_SITE].ToString();
            var departmentName = arg.DataRow[CardImportSection0Component.COL_DEPARTMENT].ToString();
            var personFirstName = arg.DataRow[CardImportSection0Component.COL_FIRSTNAME].ToString();
            var personLastName = arg.DataRow[CardImportSection0Component.COL_LASTNAME].ToString();

            var customer = (await dataFacade.CustomerDataProvider.GetCollectionAsync(
                null, "CompanyName == @0", new object[] { customerName }
            )).SingleOrDefault();

            if (customer == null)
            {
                return;
            }

            await customer.LoadSitesAsync();
            var site = customer.Sites.FirstOrDefault(x => x.Name == siteName);
            if (site == null)
            {
                return;
            }

            await site.LoadDepartmentItemsAsync();
            var department = site.DepartmentItems.FirstOrDefault(x => x.Name == departmentName);
            if (department == null)
            {
                return;
            }

            var person = (await dataFacade.PersonDataProvider.GetCollectionAsync(
                null,
                "CustomerId == @0 AND SiteId == @1 AND DepartmentId == @2 AND FirstName == @3 AND LastName == @4",
                new object[] { customer.Id, site.Id, department.Id, personFirstName, personLastName }
            )).SingleOrDefault();

            if (person == null)
            {
                return;
            }

            // Make sure the card entity is properly initialized
            if (arg.Entity == null)
            {
                return;
            }

            if (arg.Entity.Id == Guid.Empty)
            {
                arg.Entity.Id = Guid.NewGuid();
            }

            // Handle the CardType conversion first
            var cardTypeString = arg.DataRow[CardImportSection0Component.COL_CARDTYPE].ToString();
            if (int.TryParse(cardTypeString, out int cardTypeValue))
            {
                arg.Entity.Type = (FleetXQ.Data.DataObjects.CardTypeEnum)cardTypeValue;
            }
            else
            {
                arg.Entity.Type = FleetXQ.Data.DataObjects.CardTypeEnum.CardID; // Default fallback
            }

            // Now load and update the driver
            await person.LoadDriverAsync();
            var driver = person.Driver;

            if (driver == null)
            {
                // Throw an exception when driver is not found instead of creating a new one
                throw new Exception($"Driver not found for person: {personFirstName} {personLastName}");
            }

            // Update existing driver properties
            driver.Customer = customer;
            driver.CustomerId = customer.Id;
            driver.Site = site;
            driver.SiteId = site.Id;
            driver.Department = department;
            driver.DepartmentId = department.Id;

            driver.CardDetailsId = arg.Entity.Id;
            driver.Card = arg.Entity;
            arg.Entity.SiteId = site.Id;
            arg.Entity.Driver = driver;

            // Don't try to use SetCardValue/SetDriverValue - just set the ID reference
            driver = await dataFacade.DriverDataProvider.SaveAsync(driver);
            arg.Entity = await driver.LoadCardAsync();
            arg.RowProcessed = true; // Will skip another card save
        }
    }
}