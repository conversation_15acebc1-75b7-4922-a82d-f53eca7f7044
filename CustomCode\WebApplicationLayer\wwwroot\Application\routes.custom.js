﻿(function (global) {
    FleetXQ.Web.Routing.Custom = function (controller) {
        var self = this;

        this.parseAdditionnalRoutesForLevel = function (controller, level) {
            return false;
            /*
            if (level == 1 && controller.params.part1 == 'Dashboard') {
                hash = "#!/Dashboard/0DDD6B16-DED6-435A-89AB-909CDB59C945";
                window.location.hash = hash;
                return true;
            }

            return false;
            */
        };
    }
}());