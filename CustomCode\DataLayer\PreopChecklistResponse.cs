﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{
    public class PreopChecklistResponse
    {
        [JsonProperty("qid")]
        public String QId { get; set; }

        [JsonProperty("type")]
        public int Type { get; set; }
        [JsonProperty("len")]
        public int Len { get; set; }
        [JsonProperty("question")]
        public String question { get; set; }
    }
}
