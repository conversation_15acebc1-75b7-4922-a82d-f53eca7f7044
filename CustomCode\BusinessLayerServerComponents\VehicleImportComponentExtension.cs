using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.MicrosoftExcelImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Microsoft.Extensions.DependencyInjection;
using System.IO;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    /// <summary>
    /// Extension for the VehicleImportComponent that handles custom import logic,
    /// including the creation and linking of ChecklistSettings for each imported vehicle.
    /// </summary>
    public class VehicleImportComponentExtension : IImportExportComponentExtension<VehicleImportSection0Component,
        VehicleDataObject>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IDataFacade dataFacade;

        public VehicleImportComponentExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {
            this.dataFacade = dataFacade;
            this._serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Initializes the extension by hooking into the import component's events.
        /// </summary>
        public void Init(IImportExportComponent<VehicleDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        /// <summary>
        /// Called after importing each row. Handles the creation of ChecklistSettings for each vehicle.
        /// </summary>
        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<VehicleDataObject> arg)
        {
            try
            {
                // Step 1: Extract customer, site, and department information
                var customerName = arg.DataRow[PersonImportSection0Component.COL_CUSTOMER].ToString();
                var siteName = arg.DataRow[PersonImportSection0Component.COL_SITE].ToString();
                var departmentName = arg.DataRow[PersonImportSection0Component.COL_DEPARTMENT].ToString();

                // Step 2: Find the customer by name
                var customer = (await dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { customerName })).SingleOrDefault();

                if (customer == null)
                {
                    Console.WriteLine($"Customer not found: {customerName}");
                    return;
                }

                // Step 3: Find the site within the customer
                await customer.LoadSitesAsync();
                var site = customer.Sites.FirstOrDefault(x => x.Name == siteName);

                if (site == null)
                {
                    Console.WriteLine($"Site not found: {siteName}");
                    return;
                }

                // Step 4: Find the department within the site
                await site.LoadDepartmentItemsAsync();
                var department = site.DepartmentItems.FirstOrDefault(x => x.Name == departmentName);

                if (department == null)
                {
                    Console.WriteLine($"Department not found: {departmentName}");
                    return;
                }

                // Step 5: Set the customer, site, and department on the vehicle
                arg.Entity.Customer = customer;
                arg.Entity.Site = site;
                arg.Entity.Department = department;


                // Step 6: Find and link Canrule by name
                var canruleName = arg.DataRow[VehicleImportSection0Component.COL_CANRULE]?.ToString();
                if (!string.IsNullOrEmpty(canruleName))
                {
                    var canrule = (await dataFacade.CanruleDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { canruleName })).SingleOrDefault();
                    if (canrule != null)
                    {
                        arg.Entity.CanruleId = canrule.Id;
                    }
                }

                
                // Step 7: Create and link ChecklistSettings after the vehicle is saved
                try
                {
                    // Create a new ChecklistSettings object using the service provider
                    var checklistSettings = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>();
                    checklistSettings.Id = Guid.NewGuid();
                    
                    // Step 7.1: Set Type based on Checklist Schedule column
                    string checklistSchedule = null;
                    try
                    {
                        checklistSchedule = arg.DataRow["Checklist Schedule"]?.ToString();
                    }
                    catch (Exception ex)
                    { 
                        // Column doesn't exist - use default
                        Console.WriteLine($"Checklist Schedule column not found: {ex.Message}");
                    }
                    
                    // Default to Timebase if not specified or invalid
                    if (string.IsNullOrEmpty(checklistSchedule) || 
                        (!checklistSchedule.Equals("Timebase", StringComparison.OrdinalIgnoreCase) && 
                         !checklistSchedule.Equals("Driver Base", StringComparison.OrdinalIgnoreCase)))
                    {
                        checklistSettings.Type = Type1Enum.Timebase;
                    }
                    else
                    {
                        checklistSettings.Type = checklistSchedule.Equals("Timebase", StringComparison.OrdinalIgnoreCase) 
                            ? Type1Enum.Timebase 
                            : Type1Enum.DriverBase;
                    }
                    
                    // Step 7.2: Parse and set time slots from CSV (all time slots can be null)
                    // Time Slot 1
                    try
                    {
                        string timeSlot1 = arg.DataRow["Time Setting Slot 1"]?.ToString();
                        if (!string.IsNullOrEmpty(timeSlot1) && TimeSpan.TryParse(timeSlot1, out TimeSpan ts1))
                        {
                            checklistSettings.TimeslotOne = ts1;
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Time Slot 1: {ex.Message}"); }
                    
                    // Time Slot 2
                    try
                    {
                        string timeSlot2 = arg.DataRow["Time Setting Slot 2"]?.ToString();
                        if (!string.IsNullOrEmpty(timeSlot2) && TimeSpan.TryParse(timeSlot2, out TimeSpan ts2))
                        {
                            checklistSettings.TimeslotTwo = ts2;
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Time Slot 2: {ex.Message}"); }
                    
                    // Time Slot 3
                    try
                    {
                        string timeSlot3 = arg.DataRow["Time Setting Slot 3"]?.ToString();
                        if (!string.IsNullOrEmpty(timeSlot3) && TimeSpan.TryParse(timeSlot3, out TimeSpan ts3))
                        {
                            checklistSettings.TimeslotThree = ts3;
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Time Slot 3: {ex.Message}"); }
                    
                    // Time Slot 4
                    try
                    {
                        string timeSlot4 = arg.DataRow["Time Setting Slot 4"]?.ToString();
                        if (!string.IsNullOrEmpty(timeSlot4) && TimeSpan.TryParse(timeSlot4, out TimeSpan ts4))
                        {
                            checklistSettings.TimeslotFour = ts4;
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Time Slot 4: {ex.Message}"); }

                    // Handle Question Timeout
                    try
                    {
                        string timeoutStr = arg.DataRow["Timeout in second(s)"]?.ToString();
                        if (!string.IsNullOrEmpty(timeoutStr) && int.TryParse(timeoutStr, out int timeout))
                        {
                            checklistSettings.QuestionTimeout = (short)timeout;
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Question Timeout: {ex.Message}"); }

                    // Handle Show Comment
                    try 
                    {
                        string showCommentStr = arg.DataRow[VehicleImportSection0Component.COL_SHOWCOMMENT]?.ToString();
                        if (!string.IsNullOrEmpty(showCommentStr))
                        {
                            checklistSettings.ShowComment = bool.Parse(showCommentStr);
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Show Comment: {ex.Message}"); }

                    // Handle Randomisation
                    try 
                    {
                        string randomisationStr = arg.DataRow[VehicleImportSection0Component.COL_RANDOMISATION]?.ToString();
                        if (!string.IsNullOrEmpty(randomisationStr))
                        {
                            checklistSettings.Randomisation = bool.Parse(randomisationStr);
                        }
                    }
                    catch (Exception ex) { Console.WriteLine($"Error processing Randomisation: {ex.Message}"); }

                    
                    // Step 7.4: Save the ChecklistSettings
                    var savedChecklistSettings = await dataFacade.ChecklistSettingsDataProvider.SaveAsync(checklistSettings);
                    
                    if (savedChecklistSettings != null)
                    {
                        // Step 7.5: Link the ChecklistSettings to the Vehicle
                        arg.Entity.ChecklistSettingsId = savedChecklistSettings.Id;
                        
                        // Step 7.6: Save the updated Vehicle
                        await dataFacade.VehicleDataProvider.SaveAsync(arg.Entity);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error creating or saving ChecklistSettings: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in vehicle import process: {ex.Message}");
            }
        }
    }
}