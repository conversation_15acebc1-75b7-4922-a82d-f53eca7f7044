﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using VDS.RDF;
using FleetXQ.Data.DataObjects.Custom;
using DocumentFormat.OpenXml.Bibliography;
using NHibernate.Util;
using FleetXQ.Data.DataProviders.Database;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    internal class MachineUnlockAPITest : TestBase
    {
        private IMachineUnlockAPI _machineUnlockAPI;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = "MachineUnlockAPI_Test";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _machineUnlockAPI = _serviceProvider.GetRequiredService<IMachineUnlockAPI>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task TestDataCreationAsync()
        {
            var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, skipSecurity: true);

            Assert.That(customers, Has.Count.EqualTo(2), $"Wrong number of customers is {customers.Count} should be 2");

            var customer = customers.FirstOrDefault(c => c.CompanyName == "Test customer");
            Assert.That(customer, Is.Not.Null, "Test customer not found");
            await customer.LoadSitesAsync(skipSecurity: true);

            // Expect 1 site due to simplified setup
            Assert.That(customer.Sites, Has.Count.EqualTo(1), $"Wrong number of sites is {customer.Sites.Count} should be 1");
        }

        [Test]
        public async Task ProcessMessageFormat_VariousFormats_ShouldDeserialize()
        {
            // This test focuses only on message format parsing, not the full workflow

            // Mock the necessary dependencies
            var mockDataFacade = Substitute.For<IDataFacade>();
            var mockLogger = Substitute.For<ILoggingService>();
            var mockConfig = Substitute.For<IConfiguration>();
            var mockServiceProvider = Substitute.For<IServiceProvider>();

            // Create a subclass of MachineUnlockAPI to expose the message parsing capability
            var machineUnlockTestApi = new MachineUnlockAPITestWrapper(mockServiceProvider, mockConfig, mockDataFacade, mockLogger);

            // Test standard format
            var sessionId = Guid.NewGuid();
            var iotDeviceId = "test_module_001";
            var unlockWeigand = "6073";

            string standardFormat = "{\"IotDeviceId\":\"" + iotDeviceId +
                "\",\"event_type\":\"UNLK\",\"payload\":\"SID=" + sessionId +
                " UNLK=" + unlockWeigand + ",65CD1BC9,FB,25FA3,65CD1BBD,,0\"}";

            var result1 = machineUnlockTestApi.TestExtractPayload(standardFormat);
            Assert.That(result1, Is.Not.Null, "Standard format should be parsed");
            Assert.That(result1.IoTDeviceId, Is.EqualTo(iotDeviceId), "IoTDeviceId should be extracted correctly");
            Assert.That(result1.EventType, Is.EqualTo("UNLK"), "EventType should be UNLK");
            Assert.That(result1.SessionId, Is.EqualTo(sessionId), "SessionId should be extracted correctly");

            // Test new format
            string newFormat = "{\"IotDeviceId\":\"" + iotDeviceId +
                "\",\"event_type\":\"UNLK\",\"session_id\":\"" + sessionId +
                "\",\"payload\":\"UNLK=" + unlockWeigand +
                ",65CD1BC9,FB,25FA3,65CD1BBD,Unlock test,0\"}";

            var result2 = machineUnlockTestApi.TestExtractPayload(newFormat);
            Assert.That(result2, Is.Not.Null, "New format should be parsed");
            Assert.That(result2.IoTDeviceId, Is.EqualTo(iotDeviceId), "IoTDeviceId should be extracted correctly");
            Assert.That(result2.EventType, Is.EqualTo("UNLK"), "EventType should be UNLK");
            Assert.That(result2.SessionId, Is.EqualTo(sessionId), "SessionId should be extracted correctly");

            // Test wrapped format
            string wrappedFormat = "{\"originalMessage\":{\"session_id\":\"" + sessionId +
                "\",\"event_type\":\"UNLK\",\"payload\":\"UNLK=" + unlockWeigand +
                ",65CD1BC9,FB,25FA3,65CD1BBD,Wrapped format test,0\",\"IotDeviceId\":\"" +
                iotDeviceId + "\",\"msg_id\":\"039a61cc-afa5-41f9-959a-af469f2a7c5b\"},\"statusCode\":200}";

            var result3 = machineUnlockTestApi.TestExtractPayload(wrappedFormat);
            Assert.That(result3, Is.Not.Null, "Wrapped format should be parsed");
            Assert.That(result3.IoTDeviceId, Is.EqualTo(iotDeviceId), "IoTDeviceId should be extracted correctly");
            Assert.That(result3.EventType, Is.EqualTo("UNLK"), "EventType should be UNLK");
            Assert.That(result3.SessionId, Is.EqualTo(sessionId), "SessionId should be extracted correctly");
        }

        // Test wrapper class to expose the message parsing functionality
        private class MachineUnlockAPITestWrapper : MachineUnlockAPI
        {
            public MachineUnlockAPITestWrapper(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, ILoggingService logger)
                : base(serviceProvider, configuration, dataFacade, logger)
            {
            }

            // Add a class to return the extracted details
            public class PayloadResult
            {
                public string IoTDeviceId { get; set; }
                public string EventType { get; set; }
                public Guid SessionId { get; set; }
                public string Payload { get; set; }
            }

            // Extract the payload information without trying to process it completely
            public PayloadResult TestExtractPayload(string message)
            {
                // Deserialize the entire message
                dynamic messageObject = JsonConvert.DeserializeObject<dynamic>(message);
                Guid sessionGuid = Guid.Empty;
                string iotDeviceId = null;
                string eventType = null;
                string payload = null;

                // Extract session_id and payload information
                if (messageObject?.originalMessage != null)
                {
                    // This is a wrapper message with originalMessage containing the actual data
                    string sessionId = messageObject.originalMessage.session_id?.ToString();
                    if (!string.IsNullOrEmpty(sessionId) && Guid.TryParse(sessionId, out sessionGuid))
                    {
                        iotDeviceId = messageObject.originalMessage.IotDeviceId?.ToString();
                        eventType = messageObject.originalMessage.event_type?.ToString();
                        payload = messageObject.originalMessage.payload?.ToString();
                    }
                }
                else if (messageObject?.session_id != null)
                {
                    // Direct message with session_id as a top-level field
                    string sessionId = messageObject.session_id.ToString();
                    if (!string.IsNullOrEmpty(sessionId) && Guid.TryParse(sessionId, out sessionGuid))
                    {
                        iotDeviceId = messageObject.IotDeviceId?.ToString();
                        eventType = messageObject.event_type?.ToString();
                        payload = messageObject.payload?.ToString();
                    }
                }
                else
                {
                    // Legacy format - try to extract session ID from payload
                    iotDeviceId = messageObject.IotDeviceId?.ToString();
                    eventType = messageObject.event_type?.ToString();
                    payload = messageObject.payload?.ToString();

                    // Check if payload contains SID
                    if (payload != null)
                    {
                        var payloadParts = payload.Split(' ');
                        string sid = null;

                        foreach (var part in payloadParts)
                        {
                            if (part.StartsWith("SID="))
                            {
                                sid = part.Substring(4);
                                break;
                            }
                        }

                        if (!string.IsNullOrEmpty(sid))
                        {
                            Guid.TryParse(sid, out sessionGuid);
                        }
                    }
                }

                return new PayloadResult
                {
                    IoTDeviceId = iotDeviceId,
                    EventType = eventType,
                    SessionId = sessionGuid,
                    Payload = payload
                };
            }
        }

        [Test]
        public async Task ProcessUnlockMessageAsync_InvalidIoTDeviceId_ThrowsException()
        {
            // Arrange
            string invalidMessage = "{\"IotDeviceId\":\"invalid\",\"event_type\":\"UNLK\",\"payload\":\"SID=" + Guid.NewGuid() + " UNLK=6697,65CD1BC9,FB,25FA3,65CD1BBD,,0\"}";

            // Act & Assert
            Assert.ThrowsAsync<GOServerException>(async () =>
                await _machineUnlockAPI.ProcessUnlockMessageAsync(invalidMessage),
                "Expected exception for invalid IoTDeviceId was not thrown.");
        }

        [Test]
        public async Task ProcessUnlockMessageAsync_InvalidEventType_ThrowsException()
        {
            // Arrange
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            string invalidMessage = "{\"IotDeviceId\":\"" + module.IoTDevice + "\",\"event_type\":\"INVALID\",\"payload\":\"SID=" + Guid.NewGuid() + " UNLK=6697,65CD1BC9,FB,25FA3,65CD1BBD,,0\"}";

            // Act & Assert
            var ex = Assert.ThrowsAsync<Exception>(async () =>
                await _machineUnlockAPI.ProcessUnlockMessageAsync(invalidMessage));
            Assert.That(ex.Message, Does.Contain("Invalid Payload Type"), "Expected exception for invalid EventType.");
        }

        [Test]
        public async Task ProcessUnlockMessageAsync_InvalidPayload_ThrowsException()
        {
            // Arrange
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            string invalidMessage = "{\"IotDeviceId\":\"" + module.IoTDevice + "\",\"event_type\":\"UNLK\",\"payload\":\"SID=" + Guid.NewGuid() + " UNLK=6697,65CD1BC9,FB,25FA3\"}";

            // Act & Assert
            var ex = Assert.ThrowsAsync<Exception>(async () =>
                await _machineUnlockAPI.ProcessUnlockMessageAsync(invalidMessage));
            Assert.That(ex.Message, Does.Contain("Invalid Unlock Payload"), "Expected exception for insufficient payload data.");
        }

        [Test]
        public async Task ProcessUnlockMessageAsync_InvalidSessionId_ThrowsException()
        {
            // Arrange
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            //get card
            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 3 }, skipSecurity: true)).SingleOrDefault();
            var siteAccess = (await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "SiteId == @0  and PermissionId == @1", new object[] { vehicle.SiteId, permissionDriver.Id }, skipSecurity: true)).FirstOrDefault();
            var card = await siteAccess.LoadCardAsync(skipSecurity: true);

            string invalidMessage = "{\"IotDeviceId\":\"" + module.IoTDevice + "\",\"event_type\":\"UNLK\",\"payload\":\"SID=invalid UNLK=" + card.Weigand + ",65CD1BC9,FB,25FA3,65CD1BBD,,0\"}";

            // Act & Assert
            var ex = Assert.ThrowsAsync<Exception>(async () =>
                await _machineUnlockAPI.ProcessUnlockMessageAsync(invalidMessage));
            Assert.That(ex.Message, Does.Contain("Session Id"), "Expected exception for invalid session ID format.");
        }

        [Test]
        public async Task ProcessUnlockMessageAsync_InvalidNewFormatSessionId_ThrowsException()
        {
            // Arrange
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            //get card
            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 3 }, skipSecurity: true)).SingleOrDefault();
            var siteAccess = (await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "SiteId == @0  and PermissionId == @1", new object[] { vehicle.SiteId, permissionDriver.Id }, skipSecurity: true)).FirstOrDefault();
            var card = await siteAccess.LoadCardAsync(skipSecurity: true);

            // New format with invalid session_id
            string invalidMessage = "{\"IotDeviceId\":\"" + module.IoTDevice + "\",\"event_type\":\"UNLK\",\"session_id\":\"invalid-guid\",\"payload\":\"UNLK=" + card.Weigand + ",65CD1BC9,FB,25FA3,65CD1BBD,,0\"}";

            // Act & Assert
            var ex = Assert.ThrowsAsync<Exception>(async () =>
                await _machineUnlockAPI.ProcessUnlockMessageAsync(invalidMessage));
            Assert.That(ex.Message, Does.Contain("Invalid Session Id format"), "Expected exception for invalid session ID in new format.");
        }

        [Test]
        public async Task ProcessUnlockMessageAsync_InvalidDriverCard_ThrowsException()
        {
            // Arrange
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            // Invalid card ID
            string invalidCardMessage = "{\"IotDeviceId\":\"" + module.IoTDevice + "\",\"event_type\":\"UNLK\",\"session_id\":\"" + Guid.NewGuid() + "\",\"payload\":\"UNLK=INVALID_CARD,65CD1BC9,FB,25FA3,65CD1BBD,,0\"}";

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () =>
                await _machineUnlockAPI.ProcessUnlockMessageAsync(invalidCardMessage));
            Assert.That(ex.Message, Does.Contain("Invalid card"), "Expected exception for invalid driver card.");
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            // --- Simplified Test Data Setup ---

            // Fetch the permission for drivers once
            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 3 }, skipSecurity: true)).SingleOrDefault();
            if (permissionDriver == null) throw new InvalidOperationException("Driver permission (LevelName 3) not found.");

            // 1. Create Site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = customer.Id;
            site.Name = "Test Site";
            site.TimezoneId = timeZone.Id;
            site.Id = Guid.NewGuid();
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // 2. Create Department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

            // 3. Create Model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            model.Type = ModelTypesEnum.Electric;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);

            // 4. Create Module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.IoTDevice = "test_module_001";
            module.IsAllocatedToVehicle = true;
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

            // 5. Create Vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = customer.Id;
            vehicle.SiteId = site.Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "VH_TEST";
            vehicle.SerialNo = "VS_TEST";
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);

            // 6. Create Cards & Persons/Drivers & Access
            var cardsToCreate = new List<(string fc, string cn, string wg, string firstName)>
            {
                ("0", "22222", "AD9C", "Lockout"),
                ("0", "12345", "6073", "Unlock") // This driver will be used for the session
            };

            DriverDataObject sessionDriver = null;

            foreach (var cardInfo in cardsToCreate)
            {
                // Create Person
                var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                person.Id = Guid.NewGuid();
                person.CustomerId = customer.Id;
                person.SiteId = site.Id;
                person.DepartmentId = department.Id;
                person.FirstName = cardInfo.firstName;
                person.LastName = "Driver";
                person.IsDriver = true;
                person.IsActiveDriver = true;
                person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

                // Create Card
                var card = _serviceProvider.GetRequiredService<CardDataObject>();
                card.Id = Guid.NewGuid();
                card.FacilityCode = cardInfo.fc;
                card.CardNumber = cardInfo.cn;
                card.Weigand = cardInfo.wg; // Use provided Weigand
                card.Active = true;
                card.Type = CardTypeEnum.CardID;
                card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

                // Link Card to Driver
                var driver = person.Driver;
                driver.CardDetailsId = card.Id; // Use original card ID
                driver = await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);

                // Grant Site Access
                var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                siteAccess.SetIdValue(Guid.NewGuid());
                siteAccess.SetCardIdValue(card.Id); // Use original card ID
                siteAccess.SetSiteIdValue(site.Id);
                siteAccess.SetPermissionIdValue(permissionDriver.Id);
                await _dataFacade.SiteVehicleNormalCardAccessDataProvider.SaveAsync(siteAccess, skipSecurity: true);

                // Keep track of the driver for the session
                if (cardInfo.wg == "6073")
                {
                    sessionDriver = driver;
                }
            }

            if (sessionDriver == null) throw new InvalidOperationException("Session driver (card 6073) was not created.");

            // 7. Create Session for the 'Unlock' driver
            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = sessionDriver.Id;
            session.StartTime = DateTime.Now.AddHours(-1);
            session.EndTime = DateTime.Now.AddHours(1); // Active session
            await _dataFacade.SessionDataProvider.SaveAsync(session, skipSecurity: true);

            // --- End Simplified Setup ---
        } // End create demo data
    }
}