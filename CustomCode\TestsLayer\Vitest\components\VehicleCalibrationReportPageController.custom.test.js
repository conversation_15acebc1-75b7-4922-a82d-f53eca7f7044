import { describe, it, expect, vi, beforeEach } from 'vitest';
import fs from 'fs';
import path from 'path';

// Set up FleetXQ namespace before importing the file
global.FleetXQ = {
    Web: {
        Controllers: {}
    }
};

// Mock window and other globals
global.window = {
    location: {
        reload: vi.fn()
    }
};

global.GO = {
    Filter: {
        hasUrlFilter: vi.fn().mockReturnValue(false)
    }
};

// Mock sessionStorage
global.sessionStorage = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn()
};

// Mock ko.observable
function mockObservable(initialValue) {
    const obs = vi.fn();
    obs.subscribe = vi.fn();
    obs.dispose = vi.fn();
    obs.notifySubscribers = vi.fn();
    return obs;
}

// Read and evaluate the actual controller file
const controllerFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/Controllers/VehicleCalibrationReportPageController.custom.js');
const controllerFileContent = fs.readFileSync(controllerFilePath, 'utf8');
eval(controllerFileContent);

describe('VehicleCalibrationReportPageController', () => {
    let controller;
    let customController;

    beforeEach(() => {
        // Mock the grid view model
        const gridViewModel = {
            Events: {
                CollectionLoaded: {
                    subscribe: vi.fn()
                }
            },
            sortingOptions: vi.fn(),
            StatusData: {
                IsVisible: vi.fn()
            },
            LoadAllVehicleCalibrationStoreProcedureObjectCollection: vi.fn(),
            exportFilterPredicate: null,
            exportFilterParameters: null
        };

        // Mock the customer grid view model
        const customerGridViewModel = {
            Events: {
                CollectionLoaded: {
                    subscribe: vi.fn()
                }
            },
            sortingOptions: vi.fn(),
            StatusData: {
                IsVisible: vi.fn()
            },
            LoadAllVehicleCalibrationStoreProcedureObjectCollection: vi.fn(),
            exportFilterPredicate: null,
            exportFilterParameters: null
        };

        // Mock the filter form view model
        const filterFormViewModel = {
            CurrentObject: vi.fn().mockReturnValue({
                Data: {
                    CustomerId: vi.fn(),
                    SiteId: vi.fn(),
                    DepartmentId: vi.fn(),
                    MultiSearch: vi.fn()
                }
            })
        };

        // Mock the controller with all required properties
        controller = {
            AllVehicleCalibrationStoreProcedureGridViewModel: gridViewModel,
            AllVehicleCalibrationStoreProcedureGridCustomerViewModel: customerGridViewModel,
            AllVehicleCalibrationFilterFormViewModel: filterFormViewModel,
            applicationController: {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn().mockReturnValue({
                            CustomerId: null, // Set to null to simulate admin user
                            AllowedSiteIds: '{}',
                            role: null
                        })
                    }
                }
            },
            ShowError: vi.fn()
        };

        // Create the custom controller
        customController = new FleetXQ.Web.Controllers.VehicleCalibrationReportPageControllerCustom(controller);
    });

    describe('loadPageData', () => {
        it('should set default sorting when admin grid data loads', () => {
            // Set up subscription callback
            let subscriptionCallback;
            controller.AllVehicleCalibrationStoreProcedureGridViewModel.Events.CollectionLoaded.subscribe = vi.fn((callback) => {
                subscriptionCallback = callback;
                return { dispose: vi.fn() };
            });

            // Call loadPageData
            customController.loadPageData();

            // Simulate the CollectionLoaded event
            subscriptionCallback();

            // Verify sorting was set correctly
            expect(controller.AllVehicleCalibrationStoreProcedureGridViewModel.sortingOptions).toHaveBeenCalledWith({
                columnName: "Vehicle.Module.CalibrationDate",
                order: "desc"
            });
        });
    });
});
