describe('Vehicle and Checklist Import', () => {

    beforeEach(() => {
        // Login first
        cy.login()

        // After login, visit the person page
        cy.visit(`/#!/AdminSettings/Import`)


    })

    it('should import license by model', () => {
        // Click the New button
        cy.get('[data-test-id="330870fb-4701-44cb-920a-13c7b067934e"]').click()

        // Upload a file
        // First, you need to have a test file in your fixtures folder
        cy.get('input[type="file"]').selectFile('cypress/fixtures/VEHICLES_IMPORT_TEMPLATE.csv')

        // Click the VehicleImport button
        cy.get('[data-test-id="1828d222-0bf5-49e7-8237-9aa4ad4c817b"]').click()

        cy.visit(`/#!/Vehicles`)

        cy.wait(5000);

        // Search for the vehicle via vehicleId on Vehicle grid
        cy.get('input.filterTextInputCustom.form-control[data-bind*="VehicleFilterViewModel.filterData.groups.MultiSearch"]')
            .click()
            .type('1 - DO NOT UPDATE');

        cy.get('[data-test-id="searchCommand"]').click();

        //First Vehicle after search
        cy.get('a[href*="#!/Vehicle/"]').first().click();

        // Check Customer has a value
        cy.get('[data-test-id="view_5c4fd1c8-17bf-4e95-963b-dfdb48948374"]')
            .should('be.visible')
            .should('not.be.empty');

        // Check Site has a value
        cy.get('[data-test-id="view_8f0daa62-ce92-4ef8-94ef-0a0fb77b6c6e"]')
            .should('be.visible')
            .should('not.be.empty');

        // Check Department has a value
        cy.get('[data-test-id="view_d4f83472-fca5-4960-b066-4778462f0691"]')
            .should('be.visible')
            .should('not.be.empty');

        // Check Model has a value
        cy.get('[data-test-id="view_39335f81-f2e1-4a86-b91b-98fa78aa5a82"]')
            .should('be.visible')
            .should('not.be.empty');

        // Check Vehicle ID has a value
        cy.get('[data-test-id="view_7bd0ee17-21b8-4196-bc80-23d7e4bcde45"]')
            .should('be.visible')
            .should('not.be.empty');

        // Check Serial No has a value
        cy.get('[data-test-id="view_8ce159b6-fabb-4878-bd8b-9fbb98e6e8e5"]')
            .should('be.visible')
            .should('not.be.empty');

        // Check Device ID has a value
        cy.get('[data-test-id="view_daea5c56-9222-4e36-b855-f3a302e0c877"]')
            .should('be.visible')
            .should('not.be.empty');

        // Click on Vehicle tab (tab 2)
        cy.get('[data-id="VehicleFormControl-VehicleForm-tabs-2"]').click();

    })
}) 