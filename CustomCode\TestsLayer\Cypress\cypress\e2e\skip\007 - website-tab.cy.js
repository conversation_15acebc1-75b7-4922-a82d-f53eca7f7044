//import './005 - users.cy';

describe("007 - Website Tab", () => {
    beforeEach(() => {
        // Use the global login function defined in Cypress support file
        cy.login(); // This assumes `cy.login()` is globally set up in Cypress' support file.
    });

    it("tests 006 - Vehicle Tab", () => {
        // Clicking on the relevant navigation after login
        cy.get("[data-test-id='\\32 cdc2ef2-af43-4274-97ea-54e2987e29af']").click();

        // Intercept and wait for the initial API call to load the person list
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/list*')
            .as('getPersonList');

        // Wait for the initial person list load
        // Wait for the initial person list load
        cy.wait('@getPersonList', { timeout: 30000 });

        // Function to search for "Alex_Active_Test"
        function searchForAlexActiveTest() {
            // Initial search for "Alex_Active_Test"
            cy.get('input.filterTextInputCustom.form-control')
                .should('exist')         // Ensure the input exists
                .should('be.visible')     // Ensure the input is visible
                .clear()                  // Clear any existing text
                .type('Alex_Active_Test'); // Type the desired search text

            cy.get("[data-test-id='searchCommand']").click();

            // Intercept the API response for the person list again after the search
            cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/list*')
                .as('getPersonListAfterSearch');

            // Wait for the search result and start table search
            cy.wait('@getPersonListAfterSearch').then(() => {
                performTableSearch();
            });
        }

        // Function to search the table for a valid row
        function performTableSearch() {
            let validRecordFound = false; // Flag to check if a valid record is found

            // Wait for the person list response to ensure the table data is loaded
            cy.wait('@getPersonList', { timeout: 30000 });

            // Loop through each row in the table
            cy.get('table tbody tr').each(($row) => {
                const userNameColumn = $row.find('td:nth-of-type(2)'); // Target the "Username" column (2nd column)

                // Check if the "Username" column is empty
                if (userNameColumn.text().trim() === '') {
                    // If the "Username" column is blank, click the anchor link in the first column
                    cy.wrap($row)
                        .find('a[href^="#!/UserManagement/UserDetail/"]') // Find the anchor in the row with UserDetail in href
                        .should('exist') // Ensure it exists
                        .then(($anchor) => {
                            cy.wrap($anchor).click(); // Click the anchor link
                        });

                    validRecordFound = true; // Mark that a valid row was found
                    return false; // Stop the loop after finding the first valid record
                }
            }).then(() => {
                // If no valid record was found in the table, try the next page
                if (!validRecordFound) {
                    goToNextPageIfExists(); // Check and go to the next page if available
                }
            });
        }


        // Function to handle pagination and go to the next page if available
        function goToNextPageIfExists() {
            cy.get('li.page-numbers.page-controls:not(.invisible)')  // Select all li elements without the 'invisible' class
                .last()                                               // Select the last visible pagination element
                .then($pagination => {
                    if ($pagination.length > 0) {                     // Check if the pagination element exists
                        cy.wrap($pagination)
                            .click()                                  // Click the pagination button
                            .then(() => {
                                // Call the performTableSearch function again after the click
                                performTableSearch();
                            });
                    } else {
                        // No more pages, exit the flow
                        cy.log("No valid records found, and no more pages.");
                    }
                });
        }

        // Start by searching for "Alex_Active_Test" and validate
        searchForAlexActiveTest();

        //Intercept and wait for the API call for person details
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/byid/*')
            .as('getPersonDetails');

        cy.wait('@getPersonDetails', { timeout: 30000 });

        cy.get('span[data-id="PersonFormControl-PersonForm-tabs-3"]')
            .click();              // Perform the click action

        // Ensure the "Modify" button exists, is visible, and enabled, then click it
        cy.get('button[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]')
            .should('exist')             // Ensure the button exists
            .should('be.enabled')         // Ensure the button is enabled
            .click();                     // Click the button

        // Ensure the "New" link exists, is visible, and then click it
        cy.get('a.pointer[data-bind*="createNewGOUser"]')
            .should('exist')             // Ensure the link exists
            .click();                    // Click the link

        //// Intercept the specific GET request and wait for it to complete
        //cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/accessgroup/list*')
        //    .as('getAccessGroupList');

        //// Wait for the GET request to complete
        //cy.wait('@getAccessGroupList', { timeout: 30000 });

        // Generate unique values for the full name, username, email, and password
        const uniqueFullName = `UniqueFullName_${Date.now()}`;
        const uniqueUserName = `UniqueUserName_${Date.now()}`;
        const uniqueEmail = `unique_email_${Date.now()}@example.com`;
        const uniquePassword = `UniquePassword_${Date.now()}`;

        // Type the unique full name into the full name input field
        cy.get('input[data-test-id="edit_fa86c241-9b7c-4445-b3d1-63b9dc58ed8a"]')
            .should('exist')           // Ensure the input exists
            .clear()                   // Clear any existing text
            .type(uniqueFullName);      // Type the unique full name

        // Type the unique username into the username input field
        cy.get('input[data-test-id="edit_87adc914-7760-4474-9e50-0bd8ec14a2a8"]')
            .should('exist')           // Ensure the input exists
            .clear()                   // Clear any existing text
            .type(uniqueUserName);      // Type the unique username

        // Type the unique email into the email input field
        cy.get('input[data-test-id="edit_bcde7da8-065b-4fb5-8d91-74fff6580036"]')
            .should('exist')           // Ensure the input exists
            .clear()                   // Clear any existing text
            .type(uniqueEmail);         // Type the unique email

        // Type the unique password into the password input field
        cy.get('input[data-test-id="edit_be5a4122-65c5-4480-a67a-320d26b5cada"]')
            .should('exist')           // Ensure the input exists
            .clear()                   // Clear any existing text
            .type(uniquePassword);      // Type the unique password

        // After the GET request is completed, find and click the input field
        cy.get('input[data-test-id="lookup_input"]')
            .should('exist')   // Ensure the input field exists
            .click();          // Click the input field

        // Wait for the autocomplete list to be visible
        cy.get('ul[data-test-id="lookup_wrapper"] li')
            .first() // Select the first <li> element
            .click(); // Click the first item

        cy.get('button[data-test-id="bdf5d451-849f-4118-8b6f-55a4dde083ea"]')
            .should('exist') // Ensure the button exists
            .click(); // Click the "Save" button

        // Step 1: Click the Modify button
        cy.get('button[data-test-id="93c69fd3-db0a-4477-a1b6-9f5e3958ac69"]')
            .should('exist')
            .click();



        // Type the unique full name into the full name input field
        cy.get('input[data-test-id="edit_fa86c241-9b7c-4445-b3d1-63b9dc58ed8a"]')
            .should('exist')           // Ensure the input exists
            .clear()                   // Clear any existing text
            .type(uniqueFullName + ' Updated');      // Type the unique full name

        // Type the unique username into the username input field
        cy.get('input[data-test-id="edit_87adc914-7760-4474-9e50-0bd8ec14a2a8"]')
            .should('exist')           // Ensure the input exists
            .clear()                   // Clear any existing text
            .type(uniqueUserName + ' Updated');      // Type the unique username


        // After the GET request is completed, find and click the input field
        cy.get('input[data-test-id="lookup_input"]')
            .should('exist')   // Ensure the input field exists
            .click();          // Click the input field

        // Wait for the autocomplete list to be visible
        cy.get('ul[data-test-id="lookup_wrapper"] li')
            .eq(3) // Select the first <li> element
            .click(); // Click the first item

        cy.get('button[data-test-id="bdf5d451-849f-4118-8b6f-55a4dde083ea"]')
            .should('exist') // Ensure the button exists
            .click(); // Click the "Save" button

    });
});
