<!DOCTYPE html>
<html>
<head>
    <title>Simple ModuleUtilities Search Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Simple ModuleUtilities Search Test</h1>
    
    <div>
        <label for="searchInput">Search Term:</label>
        <input type="text" id="searchInput" placeholder="Enter search term..." value="TEST" />
        <button onclick="testSearch()">Test Search</button>
        <button onclick="testNoSearch()">Test No Search</button>
    </div>
    
    <div id="results" style="margin-top: 20px; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;">
        <h3>Results will appear here...</h3>
    </div>

    <script>
        function logMessage(message) {
            console.log('[TEST] ' + message);
            var resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }

        function testSearch() {
            var searchTerm = document.getElementById('searchInput').value;
            logMessage('Testing search with term: "' + searchTerm + '"');
            
            // Simulate the exact request that would be made by the proxy
            var requestData = {
                dateformat: "ISO8601",
                dealerId: "00000000-0000-0000-0000-000000000000", // Empty GUID for testing
                filterPredicate: "IoTDevice.Contains(@0)",
                filterParameters: JSON.stringify([{
                    "TypeName": "System.String",
                    "IsNullable": false,
                    "Value": searchTerm
                }]),
                parameters: JSON.stringify({
                    filterPredicate: "IoTDevice.Contains(@0)",
                    filterParameters: JSON.stringify([{
                        "TypeName": "System.String",
                        "IsNullable": false,
                        "Value": searchTerm
                    }])
                })
            };

            logMessage('Request data: ' + JSON.stringify(requestData, null, 2));

            $.ajax({
                url: '/dataset/api/moduleutilities/getavailablemodules',
                dataType: "json",
                type: "POST",
                data: requestData,
                success: function (result) {
                    logMessage('SUCCESS: Received ' + (result?.ObjectsDataSet?.ModuleObjectsDataSet?.ModuleObjects ? Object.keys(result.ObjectsDataSet.ModuleObjectsDataSet.ModuleObjects).length : 0) + ' modules');
                    logMessage('Response: ' + JSON.stringify(result, null, 2));
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    logMessage('ERROR: ' + textStatus + ' - ' + errorThrown);
                    logMessage('Response: ' + jqXHR.responseText);
                }
            });
        }

        function testNoSearch() {
            logMessage('Testing without search parameters');
            
            var requestData = {
                dateformat: "ISO8601",
                dealerId: "00000000-0000-0000-0000-000000000000" // Empty GUID for testing
            };

            logMessage('Request data: ' + JSON.stringify(requestData, null, 2));

            $.ajax({
                url: '/dataset/api/moduleutilities/getavailablemodules',
                dataType: "json",
                type: "POST",
                data: requestData,
                success: function (result) {
                    logMessage('SUCCESS: Received ' + (result?.ObjectsDataSet?.ModuleObjectsDataSet?.ModuleObjects ? Object.keys(result.ObjectsDataSet.ModuleObjectsDataSet.ModuleObjects).length : 0) + ' modules');
                    logMessage('Response: ' + JSON.stringify(result, null, 2));
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    logMessage('ERROR: ' + textStatus + ' - ' + errorThrown);
                    logMessage('Response: ' + jqXHR.responseText);
                }
            });
        }

        // Auto-run test on page load
        window.onload = function() {
            logMessage('Page loaded. Ready to test ModuleUtilities search functionality.');
            logMessage('Click "Test Search" to test with search parameters.');
            logMessage('Click "Test No Search" to test without search parameters.');
        };
    </script>
</body>
</html>
