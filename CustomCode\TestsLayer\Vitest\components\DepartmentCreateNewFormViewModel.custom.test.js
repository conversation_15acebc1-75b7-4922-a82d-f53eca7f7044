import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the FleetXQ global object
global.FleetXQ = {
    Web: {
        ViewModels: {
            DepartmentCreateNewFormViewModelCustom: null // Will be assigned by the IIFE
        }
    }
};

// Mock ko (Knockout.js) with proper function behavior
global.ko = {
    observable: vi.fn((x) => {
        const obs = function (newValue) {
            if (arguments.length > 0) {
                obs._value = newValue;
                return;
            }
            return obs._value;
        };
        obs._value = x;
        return obs;
    }),
    observableArray: vi.fn((arr = []) => {
        const obsArray = function () {
            return obsArray._array;
        };
        obsArray._array = [...arr];
        obsArray.push = vi.fn((item) => { obsArray._array.push(item); });
        obsArray.remove = vi.fn((itemOrFn) => {
            if (typeof itemOrFn === 'function') {
                obsArray._array = obsArray._array.filter((item) => !itemOrFn(item));
            } else {
                obsArray._array = obsArray._array.filter((item) => item !== itemOrFn);
            }
        });
        obsArray.removeAll = vi.fn(() => { obsArray._array = []; });
        obsArray.subscribe = vi.fn((callback) => {
            callback(obsArray._array);
            return { dispose: vi.fn() };
        });
        return obsArray;
    }),
    pureComputed: vi.fn()
};

// Execute the IIFE code directly to assign the constructor
(function () {
    FleetXQ.Web.ViewModels.DepartmentCreateNewFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        this.onBeforeSave = function () {
            // Set the CustomerId of the department based on the CustomerId of the linked site
            var departmentObject = self.viewmodel.DepartmentObject();
            if (departmentObject && departmentObject.Data && typeof departmentObject.Data.SiteId === 'function' && departmentObject.Data.SiteId()) {
                // Get the site object from the dataset
                var siteObject = self.viewmodel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK(departmentObject.Data.SiteId());
                if (siteObject && siteObject.Data && typeof siteObject.Data.CustomerId === 'function' && siteObject.Data.CustomerId()) {
                    // Set the CustomerId of the department to match the site's CustomerId
                    if (typeof departmentObject.Data.CustomerId === 'function') {
                        departmentObject.Data.CustomerId(siteObject.Data.CustomerId());
                    }
                }
            }
            return true;
        };

        this.initialize = function () {
            // Any additional initialization code can go here
        };
    }
}());

describe('DepartmentCreateNewFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let mockSiteObject;
    let mockDepartmentObject;

    beforeEach(() => {
        // Reset mocks
        vi.clearAllMocks();

        // Create mock site object with proper observable functions
        mockSiteObject = {
            Data: {
                Id: ko.observable('site-123'),
                CustomerId: ko.observable('customer-456'),
                Name: ko.observable('Test Site')
            }
        };

        // Create mock department object with proper observable functions
        mockDepartmentObject = {
            Data: {
                Id: ko.observable('dept-789'),
                SiteId: ko.observable('site-123'),
                CustomerId: ko.observable(null),
                Name: ko.observable('Test Department')
            }
        };

        // Create mock view model
        viewModel = {
            DepartmentObject: ko.observable(mockDepartmentObject),
            controller: {
                ObjectsDataSet: {
                    getSiteObjectsDataSet: vi.fn().mockReturnValue({
                        GetObjectByPK: vi.fn().mockReturnValue(mockSiteObject)
                    })
                }
            }
        };

        // Create the custom view model instance
        customViewModel = new FleetXQ.Web.ViewModels.DepartmentCreateNewFormViewModelCustom(viewModel);
    });

    describe('onBeforeSave', () => {
        it('should set CustomerId from linked site when site exists and has CustomerId', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe('customer-456');
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet).toHaveBeenCalled();
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK).toHaveBeenCalledWith('site-123');
        });

        it('should not change CustomerId when department has no SiteId', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            departmentObject.Data.SiteId(null);
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe(originalCustomerId);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet).not.toHaveBeenCalled();
        });

        it('should not change CustomerId when site object is not found', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Mock site not found
            viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK.mockReturnValue(null);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe(originalCustomerId);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK).toHaveBeenCalledWith('site-123');
        });

        it('should not change CustomerId when site has no CustomerId', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Mock site without CustomerId
            const siteWithoutCustomerId = {
                Data: {
                    Id: ko.observable('site-123'),
                    CustomerId: ko.observable(null),
                    Name: ko.observable('Test Site')
                }
            };
            viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK.mockReturnValue(siteWithoutCustomerId);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe(originalCustomerId);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK).toHaveBeenCalledWith('site-123');
        });

        it('should handle empty department object gracefully', () => {
            // Arrange
            viewModel.DepartmentObject(null);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet).not.toHaveBeenCalled();
        });

        it('should handle department object without Data property gracefully', () => {
            // Arrange
            const departmentWithoutData = {
                // No Data property
            };
            viewModel.DepartmentObject(departmentWithoutData);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet).not.toHaveBeenCalled();
        });

        it('should handle department object without SiteId property gracefully', () => {
            // Arrange
            const departmentWithoutSiteId = {
                Data: {
                    Id: ko.observable('dept-789'),
                    CustomerId: ko.observable(null),
                    Name: ko.observable('Test Department')
                    // No SiteId property
                }
            };
            viewModel.DepartmentObject(departmentWithoutSiteId);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet).not.toHaveBeenCalled();
        });

        it('should handle department object without CustomerId property gracefully', () => {
            // Arrange
            const departmentWithoutCustomerId = {
                Data: {
                    Id: ko.observable('dept-789'),
                    SiteId: ko.observable('site-123'),
                    Name: ko.observable('Test Department')
                    // No CustomerId property
                }
            };
            viewModel.DepartmentObject(departmentWithoutCustomerId);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet).toHaveBeenCalled();
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK).toHaveBeenCalledWith('site-123');
        });

        it('should handle site object without CustomerId property gracefully', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Mock site without CustomerId property
            const siteWithoutCustomerIdProperty = {
                Data: {
                    Id: ko.observable('site-123'),
                    Name: ko.observable('Test Site')
                    // No CustomerId property
                }
            };
            viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK.mockReturnValue(siteWithoutCustomerIdProperty);

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe(originalCustomerId);
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK).toHaveBeenCalledWith('site-123');
        });

        it('should update CustomerId when department already has a different CustomerId', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            departmentObject.Data.CustomerId('existing-customer-123');

            // Act
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe('customer-456');
            expect(viewModel.controller.ObjectsDataSet.getSiteObjectsDataSet().GetObjectByPK).toHaveBeenCalledWith('site-123');
        });
    });

    describe('initialize', () => {
        it('should not throw any errors when called', () => {
            // Act & Assert
            expect(() => customViewModel.initialize()).not.toThrow();
        });

        it('should be callable multiple times without issues', () => {
            // Act & Assert
            expect(() => {
                customViewModel.initialize();
                customViewModel.initialize();
                customViewModel.initialize();
            }).not.toThrow();
        });
    });

    describe('Integration with view model', () => {
        it('should work correctly when called from the main view model', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Simulate the view model calling onBeforeSave
            const onBeforeSaveMethod = customViewModel.onBeforeSave;

            // Act
            const result = onBeforeSaveMethod();

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe('customer-456');
        });

        it('should maintain proper context when called', () => {
            // Arrange
            const departmentObject = viewModel.DepartmentObject();
            const originalCustomerId = departmentObject.Data.CustomerId();

            // Act
            const result = customViewModel.onBeforeSave.call(customViewModel);

            // Assert
            expect(result).toBe(true);
            expect(departmentObject.Data.CustomerId()).toBe('customer-456');
        });
    });
}); 