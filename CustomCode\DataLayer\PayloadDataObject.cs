﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{
    public class PayloadDataObject
    {
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [JsonProperty("msg_id")]
        public string MessageId { get; set; }

        [JsonProperty("IotDeviceId", NullValueHandling = NullValueHandling.Ignore)]
        public String IoTDeviceId { get; set; }

        [JsonProperty("msg_timestamp", NullValueHandling = NullValueHandling.Ignore)]
        public string MessageTimestamp { get; set; }

        [JsonProperty("event_type", NullValueHandling = NullValueHandling.Ignore)]
        public string EventType { get; set; }

        [JsonProperty("card_id", NullValueHandling = NullValueHandling.Ignore)]
        public string CardId { get; set; }

        [JsonProperty("payload")]
        public string Payload { get; set; }

        [JsonProperty("type", NullValueHandling = NullValueHandling.Ignore)]
        public string Type { get; set; }

        [JsonProperty("longitude")]
        public string Longitude { get; set; }

        [JsonProperty("latitude")]
        public string Latitude { get; set; }
    }
}
