import { describe, it, expect, beforeEach, vi } from 'vitest';
import ko from 'knockout';
import fs from 'fs';
import path from 'path';

describe('SiteForm1ViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Mock window.location
        global.window = {
            location: {
                reload: vi.fn(),
                hash: ''
            }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Messages: {
                    confirmDeleteMessage: 'Are you sure you want to delete %ENTITY%?',
                    confirmDeletePopupTitle: 'Confirm Delete'
                }
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();
        global.console.log = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Site/SiteForm1ViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ApplicationController
        const getProxyForComponentMock = vi.fn().mockReturnValue({
            SoftDelete: vi.fn()
        });
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        HasCustomersAccess: 'True',
                        CanEditSite: 'True'
                    })
                }
            },
            getProxyForComponent: getProxyForComponentMock
        };

        // Create base view model with required properties
        viewModel = {
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            onDeleteSuccess: vi.fn(),
            closePopup: vi.fn(),
            StatusData: {
                DisplayMode: ko.observable('view'),
                IsEmpty: ko.observable(false)
            },
            DataStore: {
                CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
            },
            SiteObject: ko.observable({
                Data: {
                    Id: ko.observable('site123')
                }
            }),
            controller: {
                applicationController: {
                    showConfirmPopup: vi.fn(),
                    getProxyForComponent: getProxyForComponentMock
                }
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.SiteForm1ViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('IsModifyCommandVisible', () => {
        it('should return true when all conditions are met', () => {
            expect(customViewModel.IsModifyCommandVisible()).toBe(true);
        });

        it('should return false if not in view mode', () => {
            viewModel.StatusData.DisplayMode('edit');
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false if status is empty', () => {
            viewModel.StatusData.IsEmpty(true);
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false if DataStore is missing', () => {
            viewModel.DataStore = null;
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false if CheckAuthorizationForEntityAndMethod returns false', () => {
            viewModel.DataStore.CheckAuthorizationForEntityAndMethod = vi.fn().mockReturnValue(false);
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false if user does not have customer access', () => {
            global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                HasCustomersAccess: 'False',
                CanEditSite: 'True'
            });
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });

        it('should return false if user cannot edit site', () => {
            global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                HasCustomersAccess: 'True',
                CanEditSite: 'False'
            });
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });
    });

    describe('Delete functionality', () => {
        it('should show confirmation popup when Delete is called', () => {
            viewModel.Delete();
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                FleetXQ.Web.Messages.confirmDeleteMessage.replace(/%ENTITY%/g, "Site"),
                FleetXQ.Web.Messages.confirmDeletePopupTitle,
                viewModel.onConfirmDelete,
                viewModel.contextId
            );
        });

        it('should call SoftDelete and close popup on successful confirmation', () => {
            const siteAPI = global.ApplicationController.getProxyForComponent("SiteAPI");
            let capturedConfig;
            siteAPI.SoftDelete = vi.fn((config) => {
                capturedConfig = config;
            });
            // Simulate confirmation
            viewModel.onConfirmDelete(true);
            expect(siteAPI.SoftDelete).toHaveBeenCalledWith(expect.objectContaining({
                caller: viewModel,
                contextId: viewModel.contextId,
                errorHandler: viewModel.ShowError,
                siteId: 'site123'
            }));
            // Simulate successful deletion
            capturedConfig.successHandler({ success: true });
            expect(viewModel.onDeleteSuccess).toHaveBeenCalledWith({ success: true });
            expect(viewModel.closePopup).toHaveBeenCalledWith(true);
        });

        it('should set isBusy to false if confirmation is rejected', () => {
            viewModel.onConfirmDelete(false);
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });
    });
}); 