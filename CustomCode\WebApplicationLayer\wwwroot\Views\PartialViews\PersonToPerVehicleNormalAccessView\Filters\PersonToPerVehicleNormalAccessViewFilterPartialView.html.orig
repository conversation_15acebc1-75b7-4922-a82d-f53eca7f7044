﻿
<!--
// This is Generated Code
// You should not modify this code as it may be overwritten
// Generated By Generative Objects 
-->
 
<!--BEGIN MasterFilter "Master Filter Layout" Filter " Person to per vehicle normal access view Filter" Internal name : "PersonToPerVehicleNormalAccessViewFilter"-->
<div>
  <div id="{VIEWNAME}-Filter" class="PersonToPerVehicleNormalAccessViewFilter" data-test-id="1193e334-ab2a-4bde-a89a-e83c79886030">
    <form data-bind="submit: {DATABINDROOT}commands.searchCommand">
      <div class="uiSearchContainer">
        <div class="filterFieldSetContent">
          <div class="filterContentContainer">
            <div class="filterGroupContainer">
              <div class="row row-cols-1 row-cols-lg-2 row-cols-xl-2 row-cols-xxl-2">
                <div class="col" data-bind="visible: {DATABINDROOT}statusData.isHireNoVisible">
                  <span class="filterFieldSelection filterTextField input-group input-group-sm">
                    <label class="input-group-text">
                      <span data-bind="i18n: 'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter:filterFields.HireNo.displayName'">$DISPLAYNAME$</span>
                    </label>
                    <input type="text" class="form-control" data-bind="value: {DATABINDROOT}filterData.fields.HireNo" data-test-id="ab0d0f4d-3d85-4738-9f07-00b53e5c6605" />
                  </span>
                </div>
                <div class="col" data-bind="visible: {DATABINDROOT}statusData.isHasAccessVisible">
                  <span class="filterFieldSelection filterSelectionField input-group input-group-sm">
                    <label class="input-group-text">
                      <span data-bind="i18n: 'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">$DISPLAYNAME$</span>
                    </label>
                    <select class="form-control" data-bind="value: {DATABINDROOT}filterData.fields.HasAccessValue, optionsText: 'text', options: {DATABINDROOT}HasAccessValues"></select>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="searchCommandsContainer">
            <button class="command-button btn btn-primary btn-sm" data-bind="click: {DATABINDROOT}commands.searchCommand, i18n: 'buttons.search'" data-test-id="searchCommand">%BUTTON_FILTERS_SEARCH%</button>
            <button class="command-button btn btn-third stack btn-sm" data-bind="click: {DATABINDROOT}commands.clearCommand, i18n: 'buttons.clear'" data-test-id="clearCommand">%BUTTON_FILTERS_CLEAR%</button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter " Person to per vehicle normal access view Filter" Internal name : "PersonToPerVehicleNormalAccessViewFilter"-->
 
