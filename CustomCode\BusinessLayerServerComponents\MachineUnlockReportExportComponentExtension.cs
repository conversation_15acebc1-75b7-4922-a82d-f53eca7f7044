﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom;

public class MachineUnlockReportExportComponentExtension : IImportExportComponentExtension<MachineUnlockReportExportSection0Component,
        AllVehicleUnlocksViewDataObject>
{
    private readonly IDataFacade dataFacade;
    private readonly IAuthentication authentication;

    public MachineUnlockReportExportComponentExtension(IDataFacade dataFacade, IAuthentication authentication)
    {
        this.dataFacade = dataFacade;
        this.authentication = authentication;
    }

    public void Init(IImportExportComponent<AllVehicleUnlocksViewDataObject> importExportComponent)
    {
        importExportComponent.OnAfterExportDataRowAsync += ImportExportComponent_OnAfterExportDataRowAsync;
    }

    private CultureInfo GetUserCulture(GOUserDataObject user)
    {
        try
        {
            // First try PreferredLocaleString
            if (!string.IsNullOrEmpty(user.PreferredLocaleString))
            {
                return new CultureInfo(user.PreferredLocaleString);
            }

            // Then try PreferredLocale if it has a value
            if (user.PreferredLocale.HasValue)
            {
                var localeString = DataUtils.GetLocaleString(user.PreferredLocale.Value);
                return new CultureInfo(localeString);
            }

            // Try customer's preferred locale
            if (user.Person?.Customer?.PreferredLocale.HasValue == true)
            {
                var customerLocale = DataUtils.GetLocaleString(user.Person.Customer.PreferredLocale.Value);
                return new CultureInfo(customerLocale);
            }

            // Default to invariant culture if no valid locale found
            return CultureInfo.InvariantCulture;
        }
        catch
        {
            return CultureInfo.InvariantCulture;
        }
    }

    private async Task ImportExportComponent_OnAfterExportDataRowAsync(OnAfterExportDataRowEventArgs<AllVehicleUnlocksViewDataObject> arg)
    {
        try 
        {
            if (arg?.Entity?.VehicleLockout == null)
            {
                return;
            }

            try
            {
                var userClaims = await authentication.GetCurrentUserClaimsAsync();
                var callingUser = await dataFacade.GOUserDataProvider.GetAsync(
                    new GOUserDataObject(userClaims.UserId.Value),
                    includes: new List<string> { "Person", "Person.Customer" }
                );

                try
                {
                    var culture = GetUserCulture(callingUser);
                    var dateFormat = $"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss";

                    arg.DataRow[MachineUnlockReportExportSection0Component.COL_LOCKOUTTIME] =
                        arg.Entity.VehicleLockout.LockoutTime.ToString(dateFormat, culture);

                    arg.DataRow[MachineUnlockReportExportSection0Component.COL_UNLOCKDATETIME] =
                        arg.Entity.VehicleLockout.UnlockDateTime.ToString(dateFormat, culture);
                }
                catch
                {
                    // Fallback to standard format if culture formatting fails
                    arg.DataRow[MachineUnlockReportExportSection0Component.COL_LOCKOUTTIME] =
                        arg.Entity.VehicleLockout.LockoutTime.ToString("MM/dd/yyyy HH:mm:ss");

                    arg.DataRow[MachineUnlockReportExportSection0Component.COL_UNLOCKDATETIME] =
                        arg.Entity.VehicleLockout.UnlockDateTime.ToString("MM/dd/yyyy HH:mm:ss");
                }
            }
            catch
            {
                // If anything fails in the user/claims lookup, use standard format
                arg.DataRow[MachineUnlockReportExportSection0Component.COL_LOCKOUTTIME] =
                    arg.Entity.VehicleLockout.LockoutTime.ToString("MM/dd/yyyy HH:mm:ss");

                arg.DataRow[MachineUnlockReportExportSection0Component.COL_UNLOCKDATETIME] =
                    arg.Entity.VehicleLockout.UnlockDateTime.ToString("MM/dd/yyyy HH:mm:ss");
            }
        }
        catch
        {
            throw; // Re-throw to ensure the error is properly handled by the export component
        }
    }
}
