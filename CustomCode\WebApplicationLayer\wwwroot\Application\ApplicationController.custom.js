(function () {
    FleetXQ.Web.Application.ControllerCustom = function (baseController) {
        var self = this;
        this.baseController = baseController;

        // Custom navigation method for Person
        this.customNavigateToPersonDetail = function (personId) {
            if (personId) {
                var path = `/UserManagement/UserDetail/${personId}`;
                window.location.hash = `#!${path}`;
            } else {
                console.error('Invalid person ID for navigation');
            }
        };

        // Custom navigation method for Vehicle
        this.customNavigateToVehicleDetail = function (vehicleId) {
            if (vehicleId) {
                var path = `/Vehicle/${vehicleId}`;
                window.location.hash = `#!${path}`;
            } else {
                console.error('Invalid vehicle ID for navigation');
            }
        };

        // Custom three-option confirmation popup
        this.showThreeOptionConfirmPopup = function (caller, message, title, callback, contextId) {
            // Create a custom popup container
            var popupId = 'threeOptionPopup_' + Date.now();
            var popupHtml = `
                <div id="${popupId}" class="three-option-popup-overlay">
                    <div class="three-option-popup">
                        <div class="three-option-popup-header">
                            <h3>${title}</h3>
                        </div>
                        <div class="three-option-popup-body">
                            <p>${message.replace(/\n/g, '<br />')}</p>
                        </div>
                        <div class="three-option-popup-footer">
                            <button class="btn btn-success three-option-btn-yes">Yes</button>
                            <button class="btn btn-warning three-option-btn-no">No</button>
                            <button class="btn btn-secondary three-option-btn-cancel">Cancel</button>
                        </div>
                    </div>
                </div>
            `;

            // Add popup to body
            $('body').append(popupHtml);

            // Get the popup element
            var $popup = $('#' + popupId);

            // Add event handlers
            $popup.find('.three-option-btn-yes').on('click', function () {
                if (callback) callback('yes');
                $popup.remove();
            });

            $popup.find('.three-option-btn-no').on('click', function () {
                if (callback) callback('no');
                $popup.remove();
            });

            $popup.find('.three-option-btn-cancel').on('click', function () {
                if (callback) callback('cancel');
                $popup.remove();
            });

            // Close on overlay click (optional)
            $popup.find('.three-option-popup-overlay').on('click', function (e) {
                if (e.target === this) {
                    if (callback) callback('cancel');
                    $popup.remove();
                }
            });

            // Add CSS styles
            if (!$('#three-option-popup-styles').length) {
                $('head').append(`
                    <style id="three-option-popup-styles">
                        .three-option-popup-overlay {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0, 0, 0, 0.5);
                            z-index: 9999;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                        }
                        .three-option-popup {
                            background: white;
                            border-radius: 8px;
                            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                            max-width: 500px;
                            width: 90%;
                            max-height: 90vh;
                            overflow-y: auto;
                        }
                        .three-option-popup-header {
                            padding: 20px 20px 0 20px;
                            border-bottom: 1px solid #eee;
                        }
                        .three-option-popup-header h3 {
                            margin: 0 0 15px 0;
                            color: #333;
                            font-size: 18px;
                        }
                        .three-option-popup-body {
                            padding: 20px;
                            line-height: 1.5;
                            color: #555;
                        }
                        .three-option-popup-footer {
                            padding: 0 20px 20px 20px;
                            display: flex;
                            gap: 10px;
                            justify-content: flex-end;
                        }
                        .three-option-btn-yes, .three-option-btn-no, .three-option-btn-cancel {
                            padding: 8px 20px;
                            border: none;
                            border-radius: 4px;
                            cursor: pointer;
                            font-size: 14px;
                            min-width: 80px;
                        }
                        .three-option-btn-yes:hover { background-color: #28a745; }
                        .three-option-btn-no:hover { background-color: #ffc107; }
                        .three-option-btn-cancel:hover { background-color: #6c757d; }
                    </style>
                `);
            }
        };

        // You can add more custom methods here
    };

    // Extend the existing controller with both methods
    var customController = new FleetXQ.Web.Application.ControllerCustom();
    FleetXQ.Web.Application.Controller.prototype.customNavigateToPersonDetail = customController.customNavigateToPersonDetail;
    FleetXQ.Web.Application.Controller.prototype.customNavigateToVehicleDetail = customController.customNavigateToVehicleDetail;
    FleetXQ.Web.Application.Controller.prototype.showThreeOptionConfirmPopup = customController.showThreeOptionConfirmPopup;
}());