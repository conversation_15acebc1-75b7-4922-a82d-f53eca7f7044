using NUnit.Framework;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using System;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using FleetXQ.Tests.Common;
using System.Collections.Generic;
using System.Linq;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class CustomerAPITest : TestBase
    {
        private IDataFacade _dataFacade;
        private ICustomerAPI _customerAPI;
        private readonly string _testDatabaseName = $"CustomerAPITest-{Guid.NewGuid()}";
        
        // Store IDs for reuse
        private Guid _timezoneId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
            base.AddServiceRegistrations(services);
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _customerAPI = _serviceProvider.GetRequiredService<ICustomerAPI>();

            // Create test database with default user
            CreateTestDatabase(_testDatabaseName);
            
            // Add default GOUser with required fields
            var defaultUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            defaultUser.Id = Guid.NewGuid();
            defaultUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Site; // Use the enum value instead of raw integer
            defaultUser.UserName = "TestUser";
            defaultUser.EmailAddress = "<EMAIL>";
            defaultUser.Password = "password";
            await _dataFacade.GOUserDataProvider.SaveAsync(defaultUser);

            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<CustomerDataObject> CreateTestCustomerAsync(string companyName)
        {
            // Create test country first
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            // Create Region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create Dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Create Customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = companyName;
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;  // Add country reference
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            return customer;
        }

        private async Task CreateTestDataAsync()
        {
            // Create test country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            // Create test region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create test dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Create test timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);
            _timezoneId = timeZone.Id;  // Store for reuse
        }

        [Test]
        public async Task SoftDeleteAsync_WithValidCustomerAndNoSitesOrDepartments_ShouldSucceed()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Test Company");

            // Act
            var result = await _customerAPI.SoftDeleteAsync(customer.Id);

            // Assert
            Assert.That(result.Result, Is.True);
            var deletedCustomer = await _dataFacade.CustomerDataProvider.GetAsync(customer);
            Assert.That(deletedCustomer.DeletedAtUtc, Is.Not.Null);
        }

        [Test]
        public async Task SoftDeleteAsync_WithNonExistentCustomer_ShouldThrowException()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () => 
                await _customerAPI.SoftDeleteAsync(nonExistentId));
            Assert.That(ex.Message, Is.EqualTo($"unknown customer id {nonExistentId}"));
        }

        [Test]
        public async Task SoftDeleteAsync_WithExistingSites_ShouldThrowException()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Test Company");

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = _timezoneId;
            site.Name = "Test Site";
            await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () => 
                await _customerAPI.SoftDeleteAsync(customer.Id));
            Assert.That(ex.Message, Is.EqualTo("Cannot delete customer: Customer contains sites. Please delete all sites first."));
        }

        [Test]
        public async Task SoftDeleteAsync_WithExistingDepartments_ShouldThrowException()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Test Company");
            
            // Create site first (required for department)
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = _timezoneId;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // Create department with ALL required references
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.CustomerId = customer.Id;
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

            // Now soft delete the site
            site.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(async () => 
                await _customerAPI.SoftDeleteAsync(customer.Id));
            Assert.That(ex.Message, Is.EqualTo("Cannot delete customer: Customer contains departments. Please delete all departments first."));
        }
    }
}
