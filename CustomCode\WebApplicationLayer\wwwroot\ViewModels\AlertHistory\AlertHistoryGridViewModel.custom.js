(function () {
    FleetXQ.Web.ViewModels.AlertHistoryGridViewModelCustom = function (viewmodel) {
        var self = this;
        self.viewmodel = viewmodel;

        this.initialize = function () {
            // Show/enable commands only when a row is selected
            self.viewmodel.commands.IsAcknowledgeCommandVisible = function () {
                return self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1;
            };
            self.viewmodel.commands.IsAcknowledgeCommandEnabled = function () {
                return self.viewmodel.commands.IsAcknowledgeCommandVisible();
            };

            self.viewmodel.commands.IsResolveCommandVisible = function () {
                return self.viewmodel.selectedObjectId() && self.viewmodel.selectedObjectId() !== -1;
            };
            self.viewmodel.commands.IsResolveCommandEnabled = function () {
                return self.viewmodel.commands.IsResolveCommandVisible();
            };

            // Override actions to pass the correct alertHistoryId
            self.viewmodel.Resolve = function () {
                var selected = self.viewmodel.selectedObject && self.viewmodel.selectedObject();
                if (!selected) return;

                var configuration = {};
                configuration.contextId = self.viewmodel.contextId;
                configuration.successHandler = self.viewmodel.onResolveSuccess;
                configuration.errorHandler = self.viewmodel.ShowError;
                configuration.alertHistoryId = selected.Data.Id();
                configuration.viewmodel = self.viewmodel;
                self.viewmodel.setIsBusy(true);
                ApplicationController.getProxyForComponent("AlertHistoryAPI").Resolve(configuration);
            };

            self.viewmodel.Acknowledge = function () {
                var selected = self.viewmodel.selectedObject && self.viewmodel.selectedObject();
                if (!selected) return;

                var configuration = {};
                configuration.contextId = self.viewmodel.contextId;
                configuration.successHandler = self.viewmodel.onAcknowledgeSuccess;
                configuration.errorHandler = self.viewmodel.ShowError;
                configuration.alertHistoryId = selected.Data.Id();
                configuration.viewmodel = self.viewmodel;
                self.viewmodel.setIsBusy(true);
                ApplicationController.getProxyForComponent("AlertHistoryAPI").Acknowledge(configuration);
            };
        };

        this.release = function () { };

    };

    if (window.ApplicationSourceHandler)
        window.ApplicationSourceHandler.onSourceLoaded("/ViewModels/AlertHistory/AlertHistoryGridViewModel.custom.js");
}());


