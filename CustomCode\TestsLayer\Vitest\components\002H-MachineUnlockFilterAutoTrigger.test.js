import { describe, it, expect, vi, beforeEach } from 'vitest';
import '../../../WebApplicationLayer/wwwroot/ViewModels/MachineUnlockReportFilter/MachineUnlockReportFilterFormViewModel.custom';

describe('MachineUnlockReportFilterFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Mock the view model with all required properties and methods
        viewModel = {
            contextId: 'test-context',
            CustomerContextId: 'test-context',
            Customer_CompanyName: vi.fn(),
            Customer_lookupItem: vi.fn(),
            MachineUnlockReportFilterObject: vi.fn().mockReturnValue({
                Data: {
                    CustomerId: vi.fn()
                }
            }),
            Commands: {
                FilterDataCommand: vi.fn()
            },
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            selectiveLoadDataForSite: vi.fn(),
            ShowError: vi.fn(),
            Modify: vi.fn(),
            subscribeToMessages: vi.fn()
        };

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn()
                }
            }
        };

        // Create the view model instance
        customViewModel = new FleetXQ.Web.ViewModels.MachineUnlockReportFilterFormViewModelCustom(viewModel);
    });

    describe('initialize', () => {
        it('should not trigger filter for non-customer users', () => {
            // Mock non-customer user
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: ['Admin'],
                CustomerId: null
            });

            // Mock setTimeout to execute immediately
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = (fn) => fn();

            customViewModel.initialize();

            // Verify Modify and subscribeToMessages were called
            expect(viewModel.Modify).toHaveBeenCalled();
            expect(viewModel.subscribeToMessages).toHaveBeenCalled();

            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });

        it('should trigger filter for customer users', () => {
            // Mock customer user
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: ['Customer'],
                CustomerId: '123'
            });

            // Mock setTimeout to execute immediately
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = (fn) => fn();

            customViewModel.initialize();

            // Verify LoadObject was called with correct configuration
            expect(viewModel.DataStoreCustomer.LoadObject).toHaveBeenCalledWith(expect.objectContaining({
                contextId: 'test-context',
                pks: {
                    Id: '123'
                }
            }));

            // Verify Modify and subscribeToMessages were called
            expect(viewModel.Modify).toHaveBeenCalled();
            expect(viewModel.subscribeToMessages).toHaveBeenCalled();

            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });
    });
});
