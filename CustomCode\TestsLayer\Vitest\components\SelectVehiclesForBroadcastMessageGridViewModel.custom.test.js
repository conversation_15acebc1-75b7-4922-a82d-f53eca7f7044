import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('SelectVehiclesForBroadcastMessageGridViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Model: {
                    DataObjects: {
                        VehicleBroadcastMessageObjectFactory: {
                            createNew: vi.fn().mockImplementation((dataset, contextId) => ({
                                Data: {
                                    Id: ko.observable(),
                                    VehicleId: ko.observable(),
                                    IsNew: ko.observable(true),
                                    IsMarkedForDeletion: ko.observable(false)
                                },
                                setBroadcastMessage: vi.fn()
                            }))
                        }
                    }
                }
            }
        };

        // Mock Math.uuid
        global.Math.uuid = vi.fn().mockReturnValue('test-uuid');

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/SelectVehiclesForBroadcastMessageGridViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            customerId: ko.observable(null),
            siteId: ko.observable(null),
            departmentId: ko.observable(null),
            checkedStates: ko.observableArray(),
            VehicleObjectCollection: ko.observableArray([
                { Data: { Id: ko.observable('vehicle1') } },
                { Data: { Id: ko.observable('vehicle2') } },
                { Data: { Id: ko.observable('vehicle3') } }
            ]),
            selectedVehicles: ko.observableArray([]),
            broadcastMessage: { id: 'test-message' },
            contextId: ['parent', 'child'],
            controller: {
                ObjectsDataSet: {
                    RemoveObject: vi.fn()
                }
            },
            filterPredicate: '',
            baseFilterPredicate: 'basePredicate',
            filterParameters: '',
            baseFilterParameters: '[]',
            setGridPageNumber: vi.fn(),
            Rebind: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.SelectVehiclesForBroadcastMessageGridViewModelCustom(viewModel);
        customViewModel.initialize();

        // Initialize checkedStates after viewModel is set up
        viewModel.updateCheckStates();
    });

    it('should initialize with correct properties', () => {
        expect(viewModel.customerId).toBeDefined();
        expect(viewModel.siteId).toBeDefined();
        expect(viewModel.departmentId).toBeDefined();
        expect(viewModel.checkedStates).toBeDefined();
        expect(viewModel.toggleChecked).toBeDefined();
        expect(viewModel.selectAll).toBeDefined();
        expect(viewModel.deselectAll).toBeDefined();
    });

    it('should update check states correctly', () => {
        // Setup some selected vehicles
        const selectedVehicle = {
            Data: {
                VehicleId: ko.observable('vehicle1')
            }
        };
        viewModel.selectedVehicles.push(selectedVehicle);

        // Call updateCheckStates
        viewModel.updateCheckStates();

        // Verify check states
        expect(viewModel.checkedStates().length).toBe(3);
        expect(viewModel.checkedStates()[0]()).toBe(true); // vehicle1 should be checked
        expect(viewModel.checkedStates()[1]()).toBe(false);
        expect(viewModel.checkedStates()[2]()).toBe(false);
    });

    it('should toggle vehicle selection correctly', () => {
        const mockEvent = { stopPropagation: vi.fn() };

        // Toggle first vehicle
        viewModel.toggleChecked(0, mockEvent);

        // Verify vehicle was added to selectedVehicles
        expect(viewModel.selectedVehicles().length).toBe(1);
        expect(viewModel.selectedVehicles()[0].Data.VehicleId()).toBe('vehicle1');
        expect(viewModel.checkedStates()[0]()).toBe(true);

        // Toggle the same vehicle again (deselect)
        viewModel.toggleChecked(0, mockEvent);

        // Verify vehicle was removed from selectedVehicles
        expect(viewModel.selectedVehicles().length).toBe(0);
        expect(viewModel.checkedStates()[0]()).toBe(false);
    });

    it('should handle selectAll correctly', () => {
        // Call selectAll
        viewModel.selectAll();

        // Verify all vehicles are selected
        expect(viewModel.selectedVehicles().length).toBe(3);
        expect(viewModel.checkedStates().every(state => state())).toBe(true);
    });

    it('should handle deselectAll correctly', () => {
        // First select all
        viewModel.selectAll();

        // Then deselect all
        viewModel.deselectAll();

        // Verify no vehicles are selected
        expect(viewModel.selectedVehicles().length).toBe(0);
        expect(viewModel.checkedStates().every(state => !state())).toBe(true);
    });

    it('should handle filter predicate and parameters correctly', () => {
        const testPredicate = 'testPredicate';
        const testParameters = [{ type: 'test', value: 'value' }];

        // Call addFilterPredicateAndParameters
        viewModel.addFilterPredicateAndParameters(testPredicate, testParameters);

        // Verify filter was updated correctly
        expect(viewModel.filterPredicate).toBe('basePredicate && (testPredicate)');
        expect(viewModel.filterParameters).toBe(JSON.stringify(testParameters));
        expect(viewModel.setGridPageNumber).toHaveBeenCalledWith(0);
        expect(viewModel.Rebind).toHaveBeenCalledWith(true);
    });

    it('should handle existing filter parameters correctly', () => {
        const testPredicate = 'testPredicate';
        const testParameters = [{ type: 'test', value: 'value' }];
        viewModel.baseFilterParameters = JSON.stringify([{ type: 'base', value: 'baseValue' }]);

        // Call addFilterPredicateAndParameters
        viewModel.addFilterPredicateAndParameters(testPredicate, testParameters);

        // Verify filter parameters were combined correctly
        const expectedParameters = [
            { type: 'base', value: 'baseValue' },
            { type: 'test', value: 'value' }
        ];
        expect(viewModel.filterParameters).toBe(JSON.stringify(expectedParameters));
    });

    it('should handle removal of existing vehicle correctly', () => {
        // Add a vehicle that's not new
        const existingVehicle = {
            Data: {
                VehicleId: ko.observable('vehicle1'),
                IsNew: ko.observable(false),
                IsMarkedForDeletion: ko.observable(false)
            }
        };
        viewModel.selectedVehicles.push(existingVehicle);
        viewModel.updateCheckStates();

        // Toggle the vehicle off
        viewModel.toggleChecked(0, { stopPropagation: vi.fn() });

        // Verify the vehicle was marked for deletion
        expect(existingVehicle.Data.IsMarkedForDeletion()).toBe(true);
        expect(viewModel.selectedVehicles().length).toBe(0);
    });
}); 