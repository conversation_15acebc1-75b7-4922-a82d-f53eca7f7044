using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class AccessGroupDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"AccessGroupDataProviderExtensionTest-{Guid.NewGuid()}";
        private CountryDataObject _testCountry;
        private RegionDataObject _testRegion;
        private DealerDataObject _testDealer;
        private TimezoneDataObject _testTimezone;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add necessary services for testing
            services.AddTransient<AccessGroupDataProviderExtension>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test country
            _testCountry = _serviceProvider.GetRequiredService<CountryDataObject>();
            _testCountry.Id = Guid.NewGuid();
            _testCountry.Name = "Test Country";
            _testCountry = await _dataFacade.CountryDataProvider.SaveAsync(_testCountry, skipSecurity: true);

            // Create test region
            _testRegion = _serviceProvider.GetRequiredService<RegionDataObject>();
            _testRegion.Id = Guid.NewGuid();
            _testRegion.Name = "Test Region";
            _testRegion.Active = true;
            _testRegion = await _dataFacade.RegionDataProvider.SaveAsync(_testRegion, skipSecurity: true);

            // Create test dealer
            _testDealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            _testDealer.Id = Guid.NewGuid();
            _testDealer.Name = "Test Dealer";
            _testDealer.RegionId = _testRegion.Id;
            _testDealer.Active = true;
            _testDealer = await _dataFacade.DealerDataProvider.SaveAsync(_testDealer, skipSecurity: true);

            // Create test timezone
            _testTimezone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            _testTimezone.Id = Guid.NewGuid();
            _testTimezone.TimezoneName = "Test Timezone";
            _testTimezone.UTCOffset = 10;
            _testTimezone = await _dataFacade.TimezoneDataProvider.SaveAsync(_testTimezone, skipSecurity: true);
        }

        private async Task<CustomerDataObject> CreateTestCustomerAsync(string customerName = "Test Customer")
        {
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = customerName;
            customer.Id = Guid.NewGuid();
            customer.CountryId = _testCountry.Id;
            customer.DealerId = _testDealer.Id;
            customer.Active = true;
            return await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);
        }

        private async Task<SiteDataObject> CreateTestSiteAsync(CustomerDataObject customer, string siteName = "Test Site")
        {
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = _testTimezone.Id;
            site.Name = siteName;
            return await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);
        }

        [Test]
        public void AccessRuleMapper_GetAccessRuleCode_ShouldReturnShortCode()
        {
            // Test main access permissions
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewDashboard"), Is.EqualTo("D"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("HasCustomersAccess"), Is.EqualTo("CA"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("HasUsersAccess"), Is.EqualTo("UA"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("HasVehiclesAccess"), Is.EqualTo("VA"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("HasReportsAccess"), Is.EqualTo("RA"));

            // Test customer-related permissions
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewCustomer"), Is.EqualTo("VC"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewCustomerSite"), Is.EqualTo("VS"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanCreateCustomerSite"), Is.EqualTo("CS"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanEditCustomerSite"), Is.EqualTo("ES"));

            // Test user-related permissions
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanCreateUser"), Is.EqualTo("CU"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanEditUser"), Is.EqualTo("EU"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanDeleteUser"), Is.EqualTo("DU"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewUsers"), Is.EqualTo("VU"));

            // Test vehicle-related permissions
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanCreateVehicle"), Is.EqualTo("CV"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanEditVehicle"), Is.EqualTo("EV"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewVehicle"), Is.EqualTo("VV"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewVehicleService"), Is.EqualTo("VVSV"));

            // Test report-related permissions
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewGeneralProductivityReport"), Is.EqualTo("VGPR"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanExportGeneralProductivityReport"), Is.EqualTo("XGPR"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanViewImpactReport"), Is.EqualTo("VIR"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode("CanExportImpactReport"), Is.EqualTo("XIR"));
        }

        [Test]
        public void AccessRuleMapper_GetAccessRuleName_ShouldReturnFullName()
        {
            // Test reverse mapping
            Assert.That(AccessRuleMapper.GetAccessRuleName("D"), Is.EqualTo("CanViewDashboard"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("CA"), Is.EqualTo("HasCustomersAccess"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("UA"), Is.EqualTo("HasUsersAccess"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("VA"), Is.EqualTo("HasVehiclesAccess"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("RA"), Is.EqualTo("HasReportsAccess"));

            // Test customer-related reverse mapping
            Assert.That(AccessRuleMapper.GetAccessRuleName("VC"), Is.EqualTo("CanViewCustomer"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("VS"), Is.EqualTo("CanViewCustomerSite"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("CS"), Is.EqualTo("CanCreateCustomerSite"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("ES"), Is.EqualTo("CanEditCustomerSite"));

            // Test user-related reverse mapping
            Assert.That(AccessRuleMapper.GetAccessRuleName("CU"), Is.EqualTo("CanCreateUser"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("EU"), Is.EqualTo("CanEditUser"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("DU"), Is.EqualTo("CanDeleteUser"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("VU"), Is.EqualTo("CanViewUsers"));

            // Test vehicle-related reverse mapping
            Assert.That(AccessRuleMapper.GetAccessRuleName("CV"), Is.EqualTo("CanCreateVehicle"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("EV"), Is.EqualTo("CanEditVehicle"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("VV"), Is.EqualTo("CanViewVehicle"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("VVSV"), Is.EqualTo("CanViewVehicleService"));

            // Test report-related reverse mapping
            Assert.That(AccessRuleMapper.GetAccessRuleName("VGPR"), Is.EqualTo("CanViewGeneralProductivityReport"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("XGPR"), Is.EqualTo("CanExportGeneralProductivityReport"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("VIR"), Is.EqualTo("CanViewImpactReport"));
            Assert.That(AccessRuleMapper.GetAccessRuleName("XIR"), Is.EqualTo("CanExportImpactReport"));
        }

        [Test]
        public void AccessRuleMapper_GetAccessRuleCode_WithUnknownRule_ShouldReturnOriginal()
        {
            // Test that unknown rules return the original name
            Assert.That(AccessRuleMapper.GetAccessRuleCode("UnknownRule"), Is.EqualTo("UnknownRule"));
            Assert.That(AccessRuleMapper.GetAccessRuleCode(""), Is.EqualTo(""));
        }

        [Test]
        public void AccessRuleMapper_GetAccessRuleName_WithUnknownCode_ShouldReturnOriginal()
        {
            // Test that unknown codes return the original code
            Assert.That(AccessRuleMapper.GetAccessRuleName("UNKNOWN"), Is.EqualTo("UNKNOWN"));
            Assert.That(AccessRuleMapper.GetAccessRuleName(""), Is.EqualTo(""));
        }

        [Test]
        public void AccessRuleMapper_GetAllMappings_ShouldReturnAllMappings()
        {
            var mappings = AccessRuleMapper.GetAllMappings();
            
            // Verify we have the expected number of mappings
            Assert.That(mappings.Count, Is.GreaterThan(50)); // Should have 60+ mappings
            
            // Verify some key mappings exist
            Assert.That(mappings.ContainsKey("CanViewDashboard"), Is.True);
            Assert.That(mappings.ContainsKey("HasCustomersAccess"), Is.True);
            Assert.That(mappings.ContainsKey("HasUsersAccess"), Is.True);
            Assert.That(mappings.ContainsKey("HasVehiclesAccess"), Is.True);
            Assert.That(mappings.ContainsKey("HasReportsAccess"), Is.True);
            
            // Verify values are short codes
            Assert.That(mappings["CanViewDashboard"], Is.EqualTo("D"));
            Assert.That(mappings["HasCustomersAccess"], Is.EqualTo("CA"));
            Assert.That(mappings["HasUsersAccess"], Is.EqualTo("UA"));
            Assert.That(mappings["HasVehiclesAccess"], Is.EqualTo("VA"));
            Assert.That(mappings["HasReportsAccess"], Is.EqualTo("RA"));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithDashboardAccess_ShouldSetAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Dashboard Access Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.CanViewDashboard = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short code
            Assert.That(accessGroup.AccessRules, Is.EqualTo("D"));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithCustomersAccess_ShouldSetAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Customers Access Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.HasCustomersAccess = true;
            accessGroup.CanViewCustomer = true;
            accessGroup.CanCreateCustomerSite = true;
            accessGroup.CanEditCustomerSite = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "CA,VC,CS,ES";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithUsersAccess_ShouldSetAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Users Access Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.HasUsersAccess = true;
            accessGroup.CanCreateUser = true;
            accessGroup.CanEditUser = true;
            accessGroup.CanDeleteUser = true;
            accessGroup.CanViewUsers = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "UA,CU,EU,DU,VU";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithVehiclesAccess_ShouldSetAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Vehicles Access Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.HasVehiclesAccess = true;
            accessGroup.CanCreateVehicle = true;
            accessGroup.CanEditVehicle = true;
            accessGroup.CanViewVehicle = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "VA,CV,EV,VV";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithReportsAccess_ShouldSetAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Reports Access Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.HasReportsAccess = true;
            accessGroup.CanViewGeneralProductivityReport = true;
            accessGroup.CanExportGeneralProductivityReport = true;
            accessGroup.CanViewImpactReport = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "RA,VGPR,XGPR,VIR";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithAllAccessTypes_ShouldSetAllAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("All Access Types Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";

            // Dashboard access
            accessGroup.CanViewDashboard = true;

            // Customers access
            accessGroup.HasCustomersAccess = true;
            accessGroup.CanViewCustomer = true;
            accessGroup.CanCreateCustomerSite = true;

            // Users access
            accessGroup.HasUsersAccess = true;
            accessGroup.CanCreateUser = true;
            accessGroup.CanViewUsers = true;

            // Vehicles access
            accessGroup.HasVehiclesAccess = true;
            accessGroup.CanCreateVehicle = true;
            accessGroup.CanViewVehicle = true;

            // Reports access
            accessGroup.HasReportsAccess = true;
            accessGroup.CanViewGeneralProductivityReport = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "D,CA,VC,CS,UA,CU,VU,VA,CV,VV,RA,VGPR";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithNoAccess_ShouldSetEmptyAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("No Access Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            // All access properties are false by default

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert
            Assert.That(accessGroup.AccessRules, Is.EqualTo(null));
        }

        [Test]
        public async Task DataProvider_OnAfterSaveDataSet_WithNewAccessGroup_ShouldAddSitesToAccessGroup()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("New Access Group Customer");
            var testSite = await CreateTestSiteAsync(customer, "Test Site for New Access Group");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.CanViewDashboard = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should have access rules and sites
            Assert.That(accessGroup.AccessRules, Is.EqualTo("D"));
            await accessGroup.LoadAccessGroupsToSitesAsync();
            Assert.That(accessGroup.AccessGroupsToSites.Any(ags => ags.SiteId == testSite.Id), Is.True, "Access group should have the site");
            Assert.That(accessGroup.AccessGroupsToSites.Count(ags => ags.SiteId == testSite.Id), Is.EqualTo(1), "Should have exactly one entry for the site");
        }

        [Test]
        public async Task DataProvider_OnAfterSaveDataSet_WithMultipleAccessGroups_ShouldAddSitesToAllAccessGroups()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Multiple Access Groups Customer");
            var testSite = await CreateTestSiteAsync(customer, "Test Site for Multiple Access Groups");

            // Create first access group
            var firstAccessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            firstAccessGroup.Id = Guid.NewGuid();
            firstAccessGroup.CustomerId = customer.Id;
            firstAccessGroup.Name = "First Test Access Group";
            firstAccessGroup.CanViewDashboard = true;

            // Create second access group
            var secondAccessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            secondAccessGroup.Id = Guid.NewGuid();
            secondAccessGroup.CustomerId = customer.Id;
            secondAccessGroup.Name = "Second Test Access Group";
            secondAccessGroup.HasCustomersAccess = true;

            // Act
            firstAccessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(firstAccessGroup, skipSecurity: true);
            secondAccessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(secondAccessGroup, skipSecurity: true);

            // Assert - The first access group should have the site
            await firstAccessGroup.LoadAccessGroupsToSitesAsync();
            Assert.That(firstAccessGroup.AccessGroupsToSites.Any(ags => ags.SiteId == testSite.Id), Is.True, "First access group should have the site");
            Assert.That(firstAccessGroup.AccessGroupsToSites.Count(ags => ags.SiteId == testSite.Id), Is.EqualTo(1), "Should have exactly one entry for the site (no duplicates)");

            // Assert - The second access group should also have the site, but no duplicates
            await secondAccessGroup.LoadAccessGroupsToSitesAsync();
            Assert.That(secondAccessGroup.AccessGroupsToSites.Any(ags => ags.SiteId == testSite.Id), Is.True, "Second access group should also have the site");
            Assert.That(secondAccessGroup.AccessGroupsToSites.Count(ags => ags.SiteId == testSite.Id), Is.EqualTo(1), "Should have exactly one entry for the site (no duplicates)");
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithComplexCustomerPermissions_ShouldSetCorrectAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Complex Customer Permissions Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";

            // Set complex customer permissions
            accessGroup.HasCustomersAccess = true;
            accessGroup.CanViewCustomer = true;
            accessGroup.CanViewCustomerSite = true;
            accessGroup.CanCreateCustomerSite = true;
            accessGroup.CanEditCustomerSite = true;
            accessGroup.CanViewCustomerDepartment = true;
            accessGroup.CanCreateCustomerDepartment = true;
            accessGroup.CanEditCustomerDepartment = true;
            accessGroup.CanViewCustomerEmailGroup = true;
            accessGroup.CanCreateCustomerEmailGroup = true;
            accessGroup.CanEditCustomerEmailGroup = true;
            accessGroup.CanDeleteCustomerEmailGroup = true;
            accessGroup.CanCreateCustomerEmailList = true;
            accessGroup.CanDeleteCustomerEmailList = true;
            accessGroup.CanViewCustomerFirmware = true;
            accessGroup.CanEditCustomerFirmware = true;
            accessGroup.CanViewCustomerModel = true;
            accessGroup.CanViewAccessGroups = true;
            accessGroup.CanCreateCustomerAccessGroups = true;
            accessGroup.CanEditCustomerAccessGroups = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "CA,VC,VS,CS,ES,VD,CD,ED,VEG,CEG,EEG,DEG,CEL,DEL,VF,EF,VM,VAG,CAG,EAG";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithComplexUserPermissions_ShouldSetCorrectAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Complex User Permissions Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";

            // Set complex user permissions
            accessGroup.HasUsersAccess = true;
            accessGroup.CanCreateUser = true;
            accessGroup.CanEditUser = true;
            accessGroup.CanDeleteUser = true;
            accessGroup.CanViewUsers = true;
            accessGroup.CanExportUsers = true;
            accessGroup.CanViewUserCard = true;
            accessGroup.CanCreateUserCard = true;
            accessGroup.CanEditUserCard = true;
            accessGroup.CanViewVehicleAccess = true;
            accessGroup.CanEditVehicleAccess = true;
            accessGroup.CanViewUserLicense = true;
            accessGroup.CanCreateUserLicense = true;
            accessGroup.CanEditUserLicense = true;
            accessGroup.CanViewUserWebsiteAccess = true;
            accessGroup.CanCreateUserWebsiteAccess = true;
            accessGroup.CanEditUserWebsiteAccess = true;
            accessGroup.CanViewUserSupervisorAccess = true;
            accessGroup.CanEditUserSupervisorAccess = true;
            accessGroup.CanViewUserReportSubscription = true;
            accessGroup.CanCreateUserReportSubscription = true;
            accessGroup.CanEditUserReportSubscription = true;
            accessGroup.CanDeleteUserReportSubscription = true;
            accessGroup.CanCreateUserAlert = true;
            accessGroup.CanEditUserAlert = true;
            accessGroup.CanDeleteUserAlert = true;
            accessGroup.CanViewUserAlert = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "UA,CU,EU,DU,VU,XU,VUC,CUC,EUC,VVA,EVA,VUL,CUL,EUL,VUWA,CUWA,EUWA,VUSA,EUSA,VURS,CURS,EURS,DURS,CUA,EUA,DUA,VUA";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithComplexVehiclePermissions_ShouldSetCorrectAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Complex Vehicle Permissions Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";

            // Set complex vehicle permissions
            accessGroup.HasVehiclesAccess = true;
            accessGroup.CanCreateVehicle = true;
            accessGroup.CanEditVehicle = true;
            accessGroup.CanViewVehicle = true;
            accessGroup.CanExportVehicle = true;
            accessGroup.CanViewVehicleSynchronization = true;
            accessGroup.CanViewVehicleChecklist = true;
            accessGroup.CanCreateVehicleChecklist = true;
            accessGroup.CanEditVehicleChecklist = true;
            accessGroup.CanDeleteVehicleChecklist = true;
            accessGroup.CanViewVehicleChecklistSetting = true;
            accessGroup.CanCreateVehicleChecklistSetting = true;
            accessGroup.CanEditVehicleChecklistSetting = true;
            accessGroup.CanViewVehicleImpactSetting = true;
            accessGroup.CanEditVehicleImpactSetting = true;
            accessGroup.CanViewVehicleService = true;
            accessGroup.CanCreateVehicleService = true;
            accessGroup.CanEditVehicleService = true;
            accessGroup.CanViewVehicleOtherSettingFullLockout = true;
            accessGroup.CanEditVehicleOtherSettingFullLockout = true;
            accessGroup.CanViewVehicleOtherSettingVorStatus = true;
            accessGroup.CanEditVehicleOtherSettingVorStatus = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "VA,CV,EV,VV,XV,VVS,VVC,CVC,EVC,DVC,VVCS,CVCS,EVCS,VVIS,EVIS,VVSV,CVS,EVS,VVOSFL,EVOSFL,VVOSVS,EVOSVS";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithComplexReportPermissions_ShouldSetCorrectAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Complex Report Permissions Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";

            // Set complex report permissions
            accessGroup.HasReportsAccess = true;
            accessGroup.CanViewGeneralProductivityReport = true;
            accessGroup.CanExportGeneralProductivityReport = true;
            accessGroup.CanViewImpactReport = true;
            accessGroup.CanExportImpactReport = true;
            accessGroup.CanViewPreopChecklistReport = true;
            accessGroup.CanExportPreopChecklistReport = true;
            accessGroup.CanViewMachineUnlockReport = true;
            accessGroup.CanExportMachineUnlockReport = true;
            accessGroup.CanViewCurrentStatusReport = true;
            accessGroup.CanExportCurrentStatusReport = true;
            accessGroup.CanViewProficiencyReport = true;
            accessGroup.CanExportProficiencyReport = true;
            accessGroup.CanViewServiceCheckReport = true;
            accessGroup.CanExportServiceCheckReport = true;

            // Act
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "RA,VGPR,XGPR,VIR,XIR,VPCR,XPCR,VMUR,XMUR,VCSR,XCSR,VPR,XPR,VSCR,XSCR";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithUpdatedPermissions_ShouldUpdateAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Updated Permissions Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.CanViewDashboard = true;

            // Create access group with initial permissions
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);
            Assert.That(accessGroup.AccessRules, Is.EqualTo("D"));

            // Act - Update permissions
            accessGroup.HasCustomersAccess = true;
            accessGroup.CanViewCustomer = true;
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            var expectedRules = "D,CA,VC";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(expectedRules));
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_WithRemovedPermissions_ShouldUpdateAccessRules()
        {
            // Arrange
            var customer = await CreateTestCustomerAsync("Removed Permissions Customer");
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = Guid.NewGuid();
            accessGroup.CustomerId = customer.Id;
            accessGroup.Name = "Test Access Group";
            accessGroup.CanViewDashboard = true;
            accessGroup.HasCustomersAccess = true;
            accessGroup.CanViewCustomer = true;

            // Create access group with initial permissions
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);
            var initialRules = "D,CA,VC";
            Assert.That(accessGroup.AccessRules, Is.EqualTo(initialRules));

            // Act - Remove some permissions
            accessGroup.HasCustomersAccess = false;
            accessGroup.CanViewCustomer = false;
            accessGroup = await _dataFacade.AccessGroupDataProvider.SaveAsync(accessGroup, skipSecurity: true);

            // Assert - Should use short codes
            Assert.That(accessGroup.AccessRules, Is.EqualTo("D"));
        }
    }
}