﻿(function (global) {
    FleetXQ.Web.Application.ViewModelCustom = function (appviewmodel) {
        var self = this;
        this.appViewModel = appviewmodel;

        // Access rule constants (using short codes for JWT efficiency)
        this.AccessRules = {
            // Main access permissions
            HAS_USERS_ACCESS: 'UA',
            HAS_VEHICLES_ACCESS: 'VA',
            HAS_DASHBOARD_ACCESS: 'D',
            HAS_CUSTOMERS_ACCESS: 'CA',
            HAS_REPORTS_ACCESS: 'RA',

            // User-related permissions
            CAN_VIEW_USERS: 'VU',
            CAN_EDIT_USER: 'EU',
            CAN_CREATE_USER: 'CU',
            CAN_EXPORT_USERS: 'XU',
            CAN_VIEW_USER_CARD: 'VUC',
            CAN_EDIT_USER_CARD: 'EUC',
            CAN_VIEW_USER_LICENSE: 'VUL',
            CAN_EDIT_USER_LICENSE: 'EUL',
            CAN_CREATE_USER_LICENSE: 'CUL',
            CAN_VIEW_VEHICLE_ACCESS: 'VVA',
            CAN_EDIT_VEHICLE_ACCESS: 'EVA',
            CAN_VIEW_SUPERVISOR_ACCESS: 'VUSA',
            CAN_EDIT_SUPERVISOR_ACCESS: 'EUSA',
            CAN_VIEW_WEBSITE_ACCESS: 'VUWA',
            CAN_EDIT_WEBSITE_ACCESS: 'EUWA',
            CAN_CREATE_WEBSITE_ACCESS: 'CUWA',
            CAN_VIEW_REPORT_SUBSCRIPTION: 'VURS',
            CAN_EDIT_REPORT_SUBSCRIPTION: 'EURS',
            CAN_CREATE_REPORT_SUBSCRIPTION: 'CURS',
            CAN_DELETE_REPORT_SUBSCRIPTION: 'DURS',
            CAN_VIEW_ALERTS: 'VUA',
            CAN_CREATE_ALERTS: 'CUA',
            CAN_DELETE_ALERTS: 'DUA',

            // Vehicle-related permissions
            CAN_EDIT_VEHICLE: 'EV',
            CAN_CREATE_VEHICLE: 'CV',
            CAN_EXPORT_VEHICLE: 'XV',
            CAN_VIEW_CHECKLIST: 'VVC',
            CAN_VIEW_CHECKLIST_SETTING: 'VVCS',
            CAN_CREATE_CHECKLIST_SETTING: 'CVCS',
            CAN_EDIT_CHECKLIST_SETTING: 'EVCS',
            CAN_DELETE_CHECKLIST_SETTING: 'DVC',
            CAN_VIEW_IMPACT_SETTING: 'VVIS',
            CAN_EDIT_IMPACT_SETTING: 'EVIS',
            CAN_VIEW_SERVICE: 'VVSV',
            CAN_CREATE_SERVICE: 'CVS',
            CAN_EDIT_SERVICE: 'EVS',
            CAN_VIEW_SYNCHRONIZATION: 'VVS',
            CAN_VIEW_VOR_STATUS: 'VVOSVS',
            CAN_VIEW_FULL_IMPACT_LOCKOUT: 'VVOSFL',
            CAN_EDIT_FULL_IMPACT_LOCKOUT: 'EVOSFL',

            // Customer-related permissions
            CAN_VIEW_SITE: 'VS',
            CAN_EDIT_SITE: 'ES',
            CAN_CREATE_SITE: 'CS',
            CAN_VIEW_DEPARTMENT: 'VD',
            CAN_VIEW_EMAIL_GROUP: 'VEG',
            CAN_EDIT_EMAIL_GROUP: 'EEG',
            CAN_CREATE_EMAIL_GROUP: 'CEG',
            CAN_DELETE_EMAIL_GROUP: 'DEG',
            CAN_VIEW_MODELS: 'VM',
            CAN_VIEW_ACCESS_GROUPS: 'VAG',
            CAN_EDIT_ACCESS_GROUP: 'EAG',
            CAN_CREATE_ACCESS_GROUPS: 'CAG',
            CAN_EDIT_DEPARTMENT: 'ED',
            CAN_CREATE_DEPARTMENT: 'CD',
            CAN_VIEW_FIRMWARE: 'VF',
            CAN_EDIT_FIRMWARE: 'EF',
            CAN_CREATE_EMAIL_LIST: 'CEL',
            CAN_DELETE_EMAIL_LIST: 'DEL',

            // Report-related permissions
            CAN_VIEW_GEN_PROD: 'VGPR',
            CAN_VIEW_IMPACT_REPORT: 'VIR',
            CAN_VIEW_PREOP_REPORT: 'VPCR',
            CAN_VIEW_MACHINE_UNLOCK_REPORT: 'VMUR',
            CAN_VIEW_CURRENT_STATUS_REPORT: 'VCSR',
            CAN_VIEW_PROFICIENCY_REPORT: 'VPR',
            CAN_EXPORT_SERVICE_CHECK_REPORT: 'XSCR',
            CAN_EXPORT_GEN_PROD: 'XGPR',
            CAN_EXPORT_IMPACT_REPORT: 'XIR',
            CAN_EXPORT_PREOP_REPORT: 'XPCR',
            CAN_EXPORT_MACHINE_UNLOCK_REPORT: 'XMUR',
            CAN_EXPORT_CURRENT_STATUS_REPORT: 'XCSR',
            CAN_EXPORT_PROFICIENCY_REPORT: 'XPR'
        };

        // Reverse mapping for backward compatibility (short code to full name)
        this.AccessRuleReverseMapping = {
            'D': 'CanViewDashboard',
            'CA': 'HasCustomersAccess',
            'UA': 'HasUsersAccess',
            'VA': 'HasVehiclesAccess',
            'RA': 'HasReportsAccess',
            'VC': 'CanViewCustomer',
            'VS': 'CanViewCustomerSite',
            'CS': 'CanCreateCustomerSite',
            'ES': 'CanEditCustomerSite',
            'VD': 'CanViewCustomerDepartment',
            'CD': 'CanCreateCustomerDepartment',
            'ED': 'CanEditCustomerDepartment',
            'VEG': 'CanViewCustomerEmailGroup',
            'CEG': 'CanCreateCustomerEmailGroup',
            'EEG': 'CanEditCustomerEmailGroup',
            'DEG': 'CanDeleteCustomerEmailGroup',
            'CEL': 'CanCreateCustomerEmailList',
            'DEL': 'CanDeleteCustomerEmailList',
            'VF': 'CanViewCustomerFirmware',
            'EF': 'CanEditCustomerFirmware',
            'VM': 'CanViewCustomerModel',
            'VAG': 'CanViewAccessGroups',
            'CAG': 'CanCreateCustomerAccessGroups',
            'EAG': 'CanEditCustomerAccessGroups',
            'CU': 'CanCreateUser',
            'EU': 'CanEditUser',
            'DU': 'CanDeleteUser',
            'VU': 'CanViewUsers',
            'XU': 'CanExportUsers',
            'VUC': 'CanViewUserCard',
            'CUC': 'CanCreateUserCard',
            'EUC': 'CanEditUserCard',
            'VVA': 'CanViewVehicleAccess',
            'EVA': 'CanEditVehicleAccess',
            'VUL': 'CanViewUserLicense',
            'CUL': 'CanCreateUserLicense',
            'EUL': 'CanEditUserLicense',
            'VUWA': 'CanViewUserWebsiteAccess',
            'CUWA': 'CanCreateUserWebsiteAccess',
            'EUWA': 'CanEditUserWebsiteAccess',
            'VUSA': 'CanViewUserSupervisorAccess',
            'EUSA': 'CanEditUserSupervisorAccess',
            'VURS': 'CanViewUserReportSubscription',
            'CURS': 'CanCreateUserReportSubscription',
            'EURS': 'CanEditUserReportSubscription',
            'DURS': 'CanDeleteUserReportSubscription',
            'CUA': 'CanCreateUserAlert',
            'EUA': 'CanEditUserAlert',
            'DUA': 'CanDeleteUserAlert',
            'VUA': 'CanViewUserAlert',
            'CV': 'CanCreateVehicle',
            'EV': 'CanEditVehicle',
            'VV': 'CanViewVehicle',
            'XV': 'CanExportVehicle',
            'VVS': 'CanViewVehicleSynchronization',
            'VVC': 'CanViewVehicleChecklist',
            'CVC': 'CanCreateVehicleChecklist',
            'EVC': 'CanEditVehicleChecklist',
            'DVC': 'CanDeleteVehicleChecklist',
            'VVCS': 'CanViewVehicleChecklistSetting',
            'CVCS': 'CanCreateVehicleChecklistSetting',
            'EVCS': 'CanEditVehicleChecklistSetting',
            'VVIS': 'CanViewVehicleImpactSetting',
            'EVIS': 'CanEditVehicleImpactSetting',
            'VVSV': 'CanViewVehicleService',
            'CVS': 'CanCreateVehicleService',
            'EVS': 'CanEditVehicleService',
            'VVOSFL': 'CanViewVehicleOtherSettingFullLockout',
            'EVOSFL': 'CanEditVehicleOtherSettingFullLockout',
            'VVOSVS': 'CanViewVehicleOtherSettingVorStatus',
            'EVOSVS': 'CanEditVehicleOtherSettingVorStatus',
            'VGPR': 'CanViewGeneralProductivityReport',
            'XGPR': 'CanExportGeneralProductivityReport',
            'VIR': 'CanViewImpactReport',
            'XIR': 'CanExportImpactReport',
            'VPCR': 'CanViewPreopChecklistReport',
            'XPCR': 'CanExportPreopChecklistReport',
            'VMUR': 'CanViewMachineUnlockReport',
            'XMUR': 'CanExportMachineUnlockReport',
            'VCSR': 'CanViewCurrentStatusReport',
            'XCSR': 'CanExportCurrentStatusReport',
            'VPR': 'CanViewProficiencyReport',
            'XPR': 'CanExportProficiencyReport',
            'VSCR': 'CanViewServiceCheckReport',
            'XSCR': 'CanExportServiceCheckReport'
        };

        // Helper function to check if user has access based on AccessRules
        this.hasAccess = function (rule) {
            const userClaims = self.appViewModel.security.currentUserClaims();
            if (!userClaims) return false;

            // Always grant access to administrators and dealer admins
            if (userClaims.role?.includes('Administrator') || userClaims.role?.includes('DealerAdmin')) return true;

            // Check if AccessRules property exists (new pattern with short codes)
            if (userClaims.AccessRules) {
                const accessRules = userClaims.AccessRules.split(',').map(r => r.trim());

                // First try the rule as-is (for short codes)
                if (accessRules.includes(rule)) {
                    return true;
                }

                // If not found, try to find the short code for this rule
                const shortCode = self.AccessRules[rule];
                if (shortCode && accessRules.includes(shortCode)) {
                    return true;
                }
            }

            // Fallback to old pattern for backward compatibility
            // Check if the rule exists as a direct property
            if (userClaims.hasOwnProperty(rule)) {
                return userClaims[rule] == null || userClaims[rule] == 'True';
            }

            return false;
        };

        this.initialize = function () {
            self.updateLocalePreference();
        };

        this.updateLocalePreference = function () {
            const userClaims = self.appViewModel.security.currentUserClaims();

            // Default fallback locale
            let preferredLocale = 'en-US';

            // Only try to access properties if userClaims exists
            if (userClaims) {
                if (userClaims.UserPreferredLocale) {
                    preferredLocale = userClaims.UserPreferredLocale;
                } else if (userClaims.CustomerPreferredLocale) {
                    preferredLocale = userClaims.CustomerPreferredLocale;
                }
            }

            // Set the locale preference in the GO object
            GO.LocalePreference.set(preferredLocale);
        };

        this.updateIsUserManagementVisible = function () {
            self.appViewModel.navigation.isUserManagementVisible(self.hasAccess(self.AccessRules.HAS_USERS_ACCESS));
        };

        this.updateIsVehiclesVisible = function () {
            self.appViewModel.navigation.isVehiclesVisible(self.hasAccess(self.AccessRules.HAS_VEHICLES_ACCESS));
        };

        this.updateIsDashboardVisible = function () {
            self.appViewModel.navigation.isDashboardVisible(self.hasAccess(self.AccessRules.HAS_DASHBOARD_ACCESS));
        };

        this.updateIsCustomersVisible = function () {
            self.appViewModel.navigation.isCustomersVisible(self.hasAccess(self.AccessRules.HAS_CUSTOMERS_ACCESS));
        };

        this.updateIsReportsVisible = function () {
            self.appViewModel.navigation.isReportsVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS));
        };

        this.updateIsGeneralProductivityReportVisible = function () {
            self.appViewModel.navigation.isGeneralProductivityReportVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && self.hasAccess(self.AccessRules.CAN_VIEW_GEN_PROD));
        };

        this.updateIsImpactReportVisible = function () {
            console.log(self.appViewModel.security.currentUserClaims().CanViewImpactReport);
            self.appViewModel.navigation.isImpactReportVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && self.hasAccess(self.AccessRules.CAN_VIEW_IMPACT_REPORT));
        };

        this.updateIsPreOpCheckReportVisible = function () {
            self.appViewModel.navigation.isPreOpCheckReportVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && self.hasAccess(self.AccessRules.CAN_VIEW_PREOP_REPORT));
        };

        this.updateIsMachineUnlockReportVisible = function () {
            self.appViewModel.navigation.isMachineUnlockReportVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && self.hasAccess(self.AccessRules.CAN_VIEW_MACHINE_UNLOCK_REPORT));
        };

        this.updateIsCurrentStatusReportVisible = function () {
            self.appViewModel.navigation.isCurrentStatusReportVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && self.hasAccess(self.AccessRules.CAN_VIEW_CURRENT_STATUS_REPORT));
        };

        this.updateIsProficiencyReportVisible = function () {
            self.appViewModel.navigation.isProficiencyReportVisible(self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && self.hasAccess(self.AccessRules.CAN_VIEW_PROFICIENCY_REPORT));
        };

        this.updateIsExportVisible = function () {
            self.appViewModel.navigation.isExportVisible(
                self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) && (
                    self.hasAccess(self.AccessRules.CAN_EXPORT_SERVICE_CHECK_REPORT) ||
                    self.hasAccess(self.AccessRules.CAN_EXPORT_GEN_PROD) ||
                    self.hasAccess(self.AccessRules.CAN_EXPORT_IMPACT_REPORT) ||
                    self.hasAccess(self.AccessRules.CAN_EXPORT_PREOP_REPORT) ||
                    self.hasAccess(self.AccessRules.CAN_EXPORT_MACHINE_UNLOCK_REPORT) ||
                    self.hasAccess(self.AccessRules.CAN_EXPORT_CURRENT_STATUS_REPORT) ||
                    self.hasAccess(self.AccessRules.CAN_EXPORT_PROFICIENCY_REPORT)
                )
            );
        };

        this.updateIsOnDemandAuthorisationReportVisible = function () {
            self.appViewModel.navigation.isOnDemandAuthorisationReportVisible(
                // Hide for Customer role, show for others with proper access
                self.hasAccess(self.AccessRules.HAS_REPORTS_ACCESS) &&
                self.appViewModel.security.currentUserClaims().role?.includes('Administrator') == true
            );
        };

    }
})();