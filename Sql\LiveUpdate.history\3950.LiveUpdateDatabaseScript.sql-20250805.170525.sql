﻿-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- BEGIN LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
SET NOCOUNT ON
SET NOEXEC OFF
SET ARITHABORT ON
SET XACT_ABORT ON
SET TRANSACTION ISOLATION LEVEL SERIALIZABLE
GO
BEGIN TRAN
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- TRANSFER SCHEMAS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 1: Drop Unique Constraints U_SlamcoreDeviceSlamcoreAuthenticationDetails 
IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'U_SlamcoreDeviceSlamcoreAuthenticationDetails' AND object_id = OBJECT_ID('[dbo].[SlamcoreDevice]'))
BEGIN
DROP INDEX U_SlamcoreDeviceSlamcoreAuthenticationDetails 
	ON [dbo].[SlamcoreDevice]
END
GO
IF (OBJECT_ID(N'[dbo].[U_SlamcoreDeviceSlamcoreAuthenticationDetails]', 'UQ') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreDevice] DROP CONSTRAINT [U_SlamcoreDeviceSlamcoreAuthenticationDetails]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 1, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 1' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 2: Drop Foreign Key constraint dbo.SlamcoreDevice 
IF (OBJECT_ID(N'[dbo].[FK_SlamcoreDevice_SlamcoreAPIKey_7b8d8d19-9ca4-42d7-9df8-2339518bf0c3]', 'F') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreDevice] DROP CONSTRAINT [FK_SlamcoreDevice_SlamcoreAPIKey_7b8d8d19-9ca4-42d7-9df8-2339518bf0c3]
END
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 2, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 2' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- DROP TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- CREATE TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- RENAME TABLEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (soft)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 3: Soft drop (Rename out of the way) column dbo.SlamcoreDevice.SlamcoreAPIKeyId 
EXEC sp_rename '[dbo].[SlamcoreDevice].[SlamcoreAPIKeyId]', 'SlamcoreAPIKeyId_$@GO.dropped._$@GO', 'COLUMN'
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 3, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 3' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN RENAMEs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN ADDs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 4: add column dbo.SlamcoreDevice.SlamcoreAPIKeyId 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD [SlamcoreAPIKeyId] [uniqueidentifier] NULL 
GO

IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 4, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 4' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN MODIFYs
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COLUMN Drops (hard)
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 5: Drop column dbo.SlamcoreDevice.SlamcoreAPIKeyId_$@GO.dropped._$@GO 
-- first drop any default value constraint
IF (OBJECT_ID(N'[dbo].[DF_field_id_d57902ba-7f1c-4931-b9b2-fd7c1d9e8cdf]', 'D') IS NOT NULL)
BEGIN
   ALTER TABLE [dbo].[SlamcoreDevice] DROP CONSTRAINT [DF_field_id_d57902ba-7f1c-4931-b9b2-fd7c1d9e8cdf]
END
GO
-- drop the column
ALTER TABLE [dbo].[SlamcoreDevice] DROP COLUMN [SlamcoreAPIKeyId_$@GO.dropped._$@GO]
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 5, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 5' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD PK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD FK CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 6: Add Foreign Key Constraint dbo.SlamcoreDevice to dbo.SlamcoreAPIKey 
ALTER TABLE [dbo].[SlamcoreDevice] 
	ADD CONSTRAINT [FK_SlamcoreDevice_SlamcoreAPIKey_3b46f4a6-8511-48a6-9a7a-a2627bf61c88] FOREIGN KEY
	(
		[SlamcoreAPIKeyId] 
	)
	REFERENCES [dbo].[SlamcoreAPIKey]
	(
		[Id] 
	) ON UPDATE NO ACTION ON DELETE NO ACTION 
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 6, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 6' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- ADD UNIQUE CONSTRAINTS
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- step 7: Add/Update Unique constraint U_SlamcoreDeviceSlamcoreAPIKey 
CREATE UNIQUE NONCLUSTERED INDEX [U_SlamcoreDeviceSlamcoreAPIKey]
ON [dbo].[SlamcoreDevice] 	
	([SlamcoreAPIKeyId]) 
WHERE 
	[SlamcoreAPIKeyId] IS NOT NULL  
GO
IF @@ERROR <> 0 AND @@TRANCOUNT > 0 BEGIN PRINT 'error at step 7, transaction will be rolled back' ROLLBACK TRAN END
GO
IF @@TRANCOUNT = 0 BEGIN PRINT 'Error at step 7' SET NOEXEC ON END
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- MODEL TO DATABASE SYNCHRONISATION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
UPDATE [GO.LiveUpdate].[ModelSync] SET [ModelRevisionId] = 3950, [When] = GETUTCDATE() WHERE Id = 'AF3DF4FF-A05A-4969-9796-FAC22A6ED2AF'
GO
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
-- COMMIT LIVE UPDATE DATABASE TRANSACTION
-- ------------------------------------------------------------------------------------------------------------------------------------------------------------------
IF @@TRANCOUNT > 0 
BEGIN 
	COMMIT TRAN PRINT 'Synchronization completed successfully.' 
END
GO
SET NOEXEC OFF
GO
