////////////////////////////////////////////////////////////////////////////////////////////
// ModuleUtilities Component Proxy
// Custom implementation to handle search parameters properly
////////////////////////////////////////////////////////////////////////////////////////////

(function () {
    // ModuleUtilities server Component proxy
    FleetXQ.Web.Model.Components.ModuleUtilitiesProxy = function (rootObjectsDataSet) {
        var self = this;
        this.serviceUrl = FleetXQ.Web.Application.BaseURL + "dataset/api/moduleutilities/";
        this.ObjectsDataSet = rootObjectsDataSet;

        /// GetAvailableModules Method
        /// Get the list of available modules, not used on any vehicle
        this.GetAvailableModulesRequest = null;

        this.isGetAvailableModulesBusy = function () {
            return self.GetAvailableModulesRequest != null && self.GetAvailableModulesRequest.readyState != 4;
        };

        this.GetAvailableModules = function (configuration) {
            if (self.isGetAvailableModulesBusy())
                self.GetAvailableModulesRequest.abort();

            // Prepare the data object
            var requestData = {
                dateformat: "ISO8601"
            };

            // Add dealerId if provided
            if (configuration.dealerId) {
                requestData.dealerId = configuration.dealerId;
            }

            // Add search parameters if provided
            if (configuration.filterPredicate) {
                requestData.filterPredicate = configuration.filterPredicate;
                console.log("[SEARCH DEBUG] Adding filterPredicate:", configuration.filterPredicate);
            }

            if (configuration.filterParameters) {
                requestData.filterParameters = configuration.filterParameters;
                console.log("[SEARCH DEBUG] Adding filterParameters:", configuration.filterParameters);
            }

            // Add other parameters if provided
            if (configuration.parameters) {
                requestData.parameters = configuration.parameters;
                console.log("[SEARCH DEBUG] Adding parameters:", configuration.parameters);
            }

            if (configuration.pageSize) {
                requestData.pageSize = configuration.pageSize;
            }

            if (configuration.pageNumber) {
                requestData.pageNumber = configuration.pageNumber;
            }

            console.log("[SEARCH DEBUG] Full request data:", requestData);

            self.GetAvailableModulesRequest = $.ajax({
                url: self.serviceUrl + "getavailablemodules",
                dataType: "json",
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
                },
                data: requestData,
                success: function (result) {
                    console.log("[SEARCH DEBUG] GetAvailableModules success response:", result);
                    if (configuration.successHandler) {
                        if (result === null) {
                            configuration.successHandler(null);
                        } else {
                            var datatoreturn = FleetXQ.Web.Model.DataStores.MapJSONDataToDataSet(self.ObjectsDataSet, result, configuration.contextId, "module");
                            if (configuration.successHandler)
                                configuration.successHandler(datatoreturn);
                        }
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.error("[SEARCH DEBUG] GetAvailableModules error:", textStatus, errorThrown);
                    ApplicationController.errorHandler(jqXHR, errorThrown, configuration.errorHandler, FleetXQ.Web.Messages.componentError.replace(/%OPERATION%/g, "ModuleUtilities.GetAvailableModules"));
                }
            });
        };

        /// ResetCalibration Method
        this.ResetCalibrationRequest = null;

        this.isResetCalibrationBusy = function () {
            return self.ResetCalibrationRequest != null && self.ResetCalibrationRequest.readyState != 4;
        };

        this.ResetCalibration = function (configuration) {
            if (self.isResetCalibrationBusy())
                self.ResetCalibrationRequest.abort();

            self.ResetCalibrationRequest = $.ajax({
                url: self.serviceUrl + "resetcalibration",
                dataType: "json",
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
                },
                data: {
                    dateformat: "ISO8601",
                    moduleId: configuration.moduleId
                },
                success: function (result) {
                    if (configuration.successHandler) {
                        if (result === null) {
                            configuration.successHandler(null);
                        } else {
                            var datatoreturn = FleetXQ.Web.Model.DataStores.MapJSONDataToDataSet(self.ObjectsDataSet, result, configuration.contextId, "module");
                            if (configuration.successHandler)
                                configuration.successHandler(datatoreturn[0]);
                        }
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    ApplicationController.errorHandler(jqXHR, errorThrown, configuration.errorHandler, FleetXQ.Web.Messages.componentError.replace(/%OPERATION%/g, "ModuleUtilities.ResetCalibration"));
                }
            });
        };

        /// SwapModuleForVehicle Method
        this.SwapModuleForVehicleRequest = null;

        this.isSwapModuleForVehicleBusy = function () {
            return self.SwapModuleForVehicleRequest != null && self.SwapModuleForVehicleRequest.readyState != 4;
        };

        this.SwapModuleForVehicle = function (configuration) {
            if (self.isSwapModuleForVehicleBusy())
                self.SwapModuleForVehicleRequest.abort();

            self.SwapModuleForVehicleRequest = $.ajax({
                url: self.serviceUrl + "swapmoduleforvehicle",
                dataType: "json",
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
                },
                data: {
                    dateformat: "ISO8601",
                    vehicleId: configuration.vehicleId,
                    newModuleId: configuration.newModuleId,
                    note: configuration.note
                },
                success: function (result) {
                    if (configuration.successHandler) {
                        configuration.successHandler(result);
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    ApplicationController.errorHandler(jqXHR, errorThrown, configuration.errorHandler, FleetXQ.Web.Messages.componentError.replace(/%OPERATION%/g, "ModuleUtilities.SwapModuleForVehicle"));
                }
            });
        };
    };

    // Register the component
    FleetXQ.Web.Model.Components["ModuleUtilities"] = FleetXQ.Web.Model.Components.ModuleUtilitiesProxy;

    console.log("[SEARCH DEBUG] ModuleUtilitiesProxy registered successfully");

    if (window.ApplicationSourceHandler)
        window.ApplicationSourceHandler.onSourceLoaded("/Model/Components/ModuleUtilitiesProxy.js");
}());