﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using DocumentFormat.OpenXml.Bibliography;


namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// CustomerUtilities Component
	///  
	/// </summary>
    public partial class CustomerUtilities : BaseServerComponent, ICustomerUtilities
    {
        public CustomerUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
        {
        }

        public async Task<ComponentResponse<DataObjectCollection<CustomerModelDataObject>>> AddCategoriesToCustomerAsync(Guid customerid, Guid[] modelIds, Dictionary<string, object> parameters = null)
        {
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.Id = customerid;

            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer, skipSecurity: true);

            if (customer == null)
            {
                throw new GOServerException("Customer not found.");
            }

            var newCustomerModels = new List<CustomerModelDataObject>();

            foreach (var modelId in modelIds)
            {
                var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                model.Id = modelId;
                model = await _dataFacade.ModelDataProvider.GetAsync(model, skipSecurity: true);

                if (customer == null)
                {
                    continue;
                }

                var customerModels = await model.LoadCustomerModelItemsAsync();

                var existingCustomerModel = customerModels.FirstOrDefault(x => x.CustomerId == customerid);

                if (existingCustomerModel == null)
                {
                    existingCustomerModel = _serviceProvider.GetRequiredService<CustomerModelDataObject>();
                    existingCustomerModel.ModelId = modelId;
                    existingCustomerModel.CustomerId = customer.Id;

                    existingCustomerModel = await _dataFacade.CustomerModelDataProvider.SaveAsync(existingCustomerModel);
                    newCustomerModels.Add(existingCustomerModel);
                }
            }

            return new ComponentResponse<DataObjectCollection<CustomerModelDataObject>>(new DataObjectCollection<CustomerModelDataObject>(newCustomerModels));
        }

        public async Task<ComponentResponse<bool>> ApplyAccessGroupTemplateAsync(Guid accessGroupTemplateId, Guid[] customerIds, Dictionary<string, object> parameters = null)
        {
            var accessGroupTemplate = await GetAccessGroupTemplate(accessGroupTemplateId);

            foreach (var customerId in customerIds)
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = customerId;

                customer = await _dataFacade.CustomerDataProvider.GetAsync(customer, skipSecurity: true);
                await customer.LoadAccessGroupItemsAsync(skipSecurity: true);

                var newAccessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
                newAccessGroup.CustomerId = customerId;
                newAccessGroup.Name = accessGroupTemplate.Name;
                newAccessGroup.Description = accessGroupTemplate.Description;
                newAccessGroup.CanDeleteCustomerEmailList = accessGroupTemplate.CanDeleteCustomerEmailList;
                newAccessGroup.CanEditUserReportSubscription = accessGroupTemplate.CanEditUserReportSubscription;
                newAccessGroup.CanViewPreopChecklistReport = accessGroupTemplate.CanViewPreopChecklistReport;
                newAccessGroup.CanExportCurrentStatusReport = accessGroupTemplate.CanExportCurrentStatusReport;
                newAccessGroup.CanCreateUser = accessGroupTemplate.CanCreateUser;
                newAccessGroup.CanEditVehicleOtherSettingFullLockout = accessGroupTemplate.CanEditVehicleOtherSettingFullLockout;
                newAccessGroup.CanViewUserSupervisorAccess = accessGroupTemplate.CanViewUserSupervisorAccess;
                newAccessGroup.CanViewProficiencyReport = accessGroupTemplate.CanViewProficiencyReport;
                newAccessGroup.CanViewAccessGroups = accessGroupTemplate.CanViewAccessGroups;
                newAccessGroup.CanExportProficiencyReport = accessGroupTemplate.CanExportProficiencyReport;
                newAccessGroup.CanEditCustomerFirmware = accessGroupTemplate.CanEditCustomerFirmware;
                newAccessGroup.CanDeleteUserAlert = accessGroupTemplate.CanDeleteUserAlert;
                newAccessGroup.CanDeleteUserReportSubscription = accessGroupTemplate.CanDeleteUserReportSubscription;
                newAccessGroup.CanCreateCustomerSite = accessGroupTemplate.CanCreateCustomerSite;
                newAccessGroup.CanEditVehicleOtherSettingVorStatus = accessGroupTemplate.CanEditVehicleOtherSettingVorStatus;
                newAccessGroup.CanEditCustomerAccessGroups = accessGroupTemplate.CanEditCustomerAccessGroups;
                newAccessGroup.CanExportPreopChecklistReport = accessGroupTemplate.CanExportPreopChecklistReport;
                newAccessGroup.CanViewMachineUnlockReport = accessGroupTemplate.CanViewMachineUnlockReport;
                newAccessGroup.CanViewVehicleSynchronization = accessGroupTemplate.CanViewVehicleSynchronization;
                newAccessGroup.CanCreateVehicleChecklist = accessGroupTemplate.CanCreateVehicleChecklist;
                newAccessGroup.CanDeleteCustomerEmailGroup = accessGroupTemplate.CanDeleteCustomerEmailGroup;
                newAccessGroup.CanEditVehicle = accessGroupTemplate.CanEditVehicle;
                newAccessGroup.CanViewVehicleImpactSetting = accessGroupTemplate.CanViewVehicleImpactSetting;
                newAccessGroup.HasUsersAccess = accessGroupTemplate.HasUsersAccess;
                newAccessGroup.CanViewUsers = accessGroupTemplate.CanViewUsers;
                newAccessGroup.CanEditVehicleChecklist = accessGroupTemplate.CanEditVehicleChecklist;
                newAccessGroup.CanViewGeneralProductivityReport = accessGroupTemplate.CanViewGeneralProductivityReport;
                newAccessGroup.CanEditUserWebsiteAccess = accessGroupTemplate.CanEditUserWebsiteAccess;
                newAccessGroup.CanViewDashboard = accessGroupTemplate.CanViewDashboard;
                newAccessGroup.CanCreateCustomerEmailGroup = accessGroupTemplate.CanCreateCustomerEmailGroup;
                newAccessGroup.CanCreateUserLicense = accessGroupTemplate.CanCreateUserLicense;
                newAccessGroup.CanViewVehicleService = accessGroupTemplate.CanViewVehicleService;
                newAccessGroup.CanViewServiceCheckReport = accessGroupTemplate.CanViewServiceCheckReport;
                newAccessGroup.CanEditVehicleImpactSetting = accessGroupTemplate.CanEditVehicleImpactSetting;
                newAccessGroup.CanEditCustomerSite = accessGroupTemplate.CanEditCustomerSite;
                newAccessGroup.CanCreateCustomerAccessGroups = accessGroupTemplate.CanCreateCustomerAccessGroups;
                newAccessGroup.CanViewVehicleChecklistSetting = accessGroupTemplate.CanViewVehicleChecklistSetting;
                newAccessGroup.CanViewVehicleAccess = accessGroupTemplate.CanViewVehicleAccess;
                newAccessGroup.CanViewVehicle = accessGroupTemplate.CanViewVehicle;
                newAccessGroup.CanViewVehicleOtherSettingVorStatus = accessGroupTemplate.CanViewVehicleOtherSettingVorStatus;
                newAccessGroup.CanCreateUserCard = accessGroupTemplate.CanCreateUserCard;
                newAccessGroup.CanViewCustomer = accessGroupTemplate.CanViewCustomer;
                newAccessGroup.CanViewImpactReport = accessGroupTemplate.CanViewImpactReport;
                newAccessGroup.CanViewCustomerSite = accessGroupTemplate.CanViewCustomerSite;
                newAccessGroup.CanExportServiceCheckReport = accessGroupTemplate.CanExportServiceCheckReport;
                newAccessGroup.CanCreateUserWebsiteAccess = accessGroupTemplate.CanCreateUserWebsiteAccess;
                newAccessGroup.CanViewCurrentStatusReport = accessGroupTemplate.CanViewCurrentStatusReport;
                newAccessGroup.CanCreateVehicle = accessGroupTemplate.CanCreateVehicle;
                newAccessGroup.CanViewUserReportSubscription = accessGroupTemplate.CanViewUserReportSubscription;
                newAccessGroup.CanEditUserAlert = accessGroupTemplate.CanEditUserAlert;
                newAccessGroup.CanCreateUserAlert = accessGroupTemplate.CanCreateUserAlert;
                newAccessGroup.HasReportsAccess = accessGroupTemplate.HasReportsAccess;
                newAccessGroup.CanViewVehicleOtherSettingFullLockout = accessGroupTemplate.CanViewVehicleOtherSettingFullLockout;
                newAccessGroup.CanViewUserWebsiteAccess = accessGroupTemplate.CanViewUserWebsiteAccess;
                newAccessGroup.CanViewCustomerDepartment = accessGroupTemplate.CanViewCustomerDepartment;
                newAccessGroup.CanCreateCustomerEmailList = accessGroupTemplate.CanCreateCustomerEmailList;
                newAccessGroup.CanExportGeneralProductivityReport = accessGroupTemplate.CanExportGeneralProductivityReport;
                newAccessGroup.CanEditUser = accessGroupTemplate.CanEditUser;
                newAccessGroup.CanViewCustomerEmailGroup = accessGroupTemplate.CanViewCustomerEmailGroup;
                newAccessGroup.CanCreateVehicleService = accessGroupTemplate.CanCreateVehicleService;
                newAccessGroup.CanViewUserLicense = accessGroupTemplate.CanViewUserLicense;
                newAccessGroup.CanEditCustomerEmailGroup = accessGroupTemplate.CanEditCustomerEmailGroup;
                newAccessGroup.CanViewUserCard = accessGroupTemplate.CanViewUserCard;
                newAccessGroup.CanEditVehicleAccess = accessGroupTemplate.CanEditVehicleAccess;
                newAccessGroup.CanCreateCustomerDepartment = accessGroupTemplate.CanCreateCustomerDepartment;
                newAccessGroup.CanViewVehicleChecklist = accessGroupTemplate.CanViewVehicleChecklist;
                newAccessGroup.CanEditCustomerDepartment = accessGroupTemplate.CanEditCustomerDepartment;
                newAccessGroup.CanExportVehicle = accessGroupTemplate.CanExportVehicle;
                newAccessGroup.HasVehiclesAccess = accessGroupTemplate.HasVehiclesAccess;
                newAccessGroup.CanExportMachineUnlockReport = accessGroupTemplate.CanExportMachineUnlockReport;
                newAccessGroup.CanEditUserSupervisorAccess = accessGroupTemplate.CanEditUserSupervisorAccess;
                newAccessGroup.CanEditUserLicense = accessGroupTemplate.CanEditUserLicense;
                newAccessGroup.CanEditUserCard = accessGroupTemplate.CanEditUserCard;
                newAccessGroup.CanCreateUserReportSubscription = accessGroupTemplate.CanCreateUserReportSubscription;
                newAccessGroup.HasCustomersAccess = accessGroupTemplate.HasCustomersAccess;
                newAccessGroup.CanCreateVehicleChecklistSetting = accessGroupTemplate.CanCreateVehicleChecklistSetting;
                newAccessGroup.CanViewUserAlert = accessGroupTemplate.CanViewUserAlert;
                newAccessGroup.CanViewCustomerFirmware = accessGroupTemplate.CanViewCustomerFirmware;
                newAccessGroup.CanExportUsers = accessGroupTemplate.CanExportUsers;
                newAccessGroup.CanEditVehicleChecklistSetting = accessGroupTemplate.CanEditVehicleChecklistSetting;
                newAccessGroup.CanViewCustomerModel = accessGroupTemplate.CanViewCustomerModel;
                newAccessGroup.CanExportImpactReport = accessGroupTemplate.CanExportImpactReport;
                newAccessGroup.CanDeleteVehicleChecklist = accessGroupTemplate.CanDeleteVehicleChecklist;
                newAccessGroup.CanEditVehicleService = accessGroupTemplate.CanEditVehicleService;
                newAccessGroup.CanEditRAModuleSwap = accessGroupTemplate.CanEditRAModuleSwap;

                await _dataFacade.AccessGroupDataProvider.SaveAsync(newAccessGroup);
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> ApplyChecklistTemplatesAsync(Guid checklistTemplateId, Guid customerId, Guid[] modelIds, Dictionary<string, object> parameters = null)
        {
            var checklistTemplate = _serviceProvider.GetRequiredService<CustomerPreOperationalChecklistTemplateDataObject>();
            checklistTemplate.Id = checklistTemplateId;

            checklistTemplate = await _dataFacade.CustomerPreOperationalChecklistTemplateDataProvider.GetAsync(checklistTemplate, skipSecurity: true);

            if (checklistTemplate == null)
            {
                return new ComponentResponse<bool>(false);
            }

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.Id = customerId;

            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer, skipSecurity: true);

            if (customer == null)
            {
                return new ComponentResponse<bool>(false);
            }

            await customer.LoadSitesAsync();

            foreach (var site in customer.Sites)
            {
                await site.LoadDepartmentItemsAsync();

                foreach (var department in site.DepartmentItems)
                {
                    foreach (var modelId in modelIds)
                    {
                        var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "DepartmentId == @0 and ModelId == @1", new object[] { department.Id, modelId })).FirstOrDefault();

                        if (departmentChecklist == null)
                        {
                            continue;
                        }

                        await departmentChecklist.LoadPreOperationalChecklistsAsync();

                        if (!departmentChecklist.PreOperationalChecklists.Any(x => x.Question.Equals(checklistTemplate.Question, StringComparison.InvariantCultureIgnoreCase)))
                        {
                            var question = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                            question.Question = checklistTemplate.Question;
                            question.Id = Guid.NewGuid();
                            question.AnswerType = checklistTemplate.AnswerType;
                            question.ExpectedAnswer = checklistTemplate.ExpectedAnswer;
                            question.Critical = checklistTemplate.Critical;
                            question.SiteChecklistId = departmentChecklist.Id;
                            question.Active = checklistTemplate.Active;

                            // Get the max order from existing active questions and increment it
                            var maxOrder = departmentChecklist.PreOperationalChecklists
                                .Where(q => q.Active)
                                .DefaultIfEmpty()
                                .Max(q => q?.Order ?? 0);
                            question.Order = (short)(maxOrder + 1);

                            question = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question);
                        }
                    }
                }
            }

            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// CopyAccessGroup Method
        /// </summary>
        /// <param name="accessGroupId"></param>
        /// <param name="customerIds"></param>
        /// <returns></returns>
        public async Task<ComponentResponse<bool>> CopyAccessGroupAsync(Guid accessGroupId, Guid[] customerIds, Dictionary<string, object> parameters = null)
        {
            var accessGroup = await GetAccessGroup(accessGroupId);

            foreach (var customerId in customerIds)
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = customerId;

                customer = await _dataFacade.CustomerDataProvider.GetAsync(customer, skipSecurity: true);
                await customer.LoadAccessGroupItemsAsync(skipSecurity: true);

                var newAccessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
                newAccessGroup.CustomerId = customerId;
                newAccessGroup.Name = accessGroup.Name;
                newAccessGroup.Description = accessGroup.Description;
                newAccessGroup.CanDeleteCustomerEmailList = accessGroup.CanDeleteCustomerEmailList;
                newAccessGroup.CanEditUserReportSubscription = accessGroup.CanEditUserReportSubscription;
                newAccessGroup.CanViewPreopChecklistReport = accessGroup.CanViewPreopChecklistReport;
                newAccessGroup.CanExportCurrentStatusReport = accessGroup.CanExportCurrentStatusReport;
                newAccessGroup.CanCreateUser = accessGroup.CanCreateUser;
                newAccessGroup.CanEditVehicleOtherSettingFullLockout = accessGroup.CanEditVehicleOtherSettingFullLockout;
                newAccessGroup.CanViewUserSupervisorAccess = accessGroup.CanViewUserSupervisorAccess;
                newAccessGroup.CanViewProficiencyReport = accessGroup.CanViewProficiencyReport;
                newAccessGroup.CanDeleteCustomerAccessGroups = accessGroup.CanDeleteCustomerAccessGroups;
                newAccessGroup.CanViewAccessGroups = accessGroup.CanViewAccessGroups;
                newAccessGroup.CanExportProficiencyReport = accessGroup.CanExportProficiencyReport;
                newAccessGroup.CanEditCustomerFirmware = accessGroup.CanEditCustomerFirmware;
                newAccessGroup.CanDeleteUserAlert = accessGroup.CanDeleteUserAlert;
                newAccessGroup.CanDeleteUserReportSubscription = accessGroup.CanDeleteUserReportSubscription;
                newAccessGroup.CanCreateCustomerSite = accessGroup.CanCreateCustomerSite;
                newAccessGroup.CanEditVehicleOtherSettingVorStatus = accessGroup.CanEditVehicleOtherSettingVorStatus;
                newAccessGroup.CanEditCustomerAccessGroups = accessGroup.CanEditCustomerAccessGroups;
                newAccessGroup.CanExportPreopChecklistReport = accessGroup.CanExportPreopChecklistReport;
                newAccessGroup.CanViewMachineUnlockReport = accessGroup.CanViewMachineUnlockReport;
                newAccessGroup.CanViewVehicleSynchronization = accessGroup.CanViewVehicleSynchronization;
                newAccessGroup.CanCreateVehicleChecklist = accessGroup.CanCreateVehicleChecklist;
                newAccessGroup.CanDeleteCustomerEmailGroup = accessGroup.CanDeleteCustomerEmailGroup;
                newAccessGroup.CanDeleteUser = accessGroup.CanDeleteUser;
                newAccessGroup.CanEditVehicle = accessGroup.CanEditVehicle;
                newAccessGroup.CanViewVehicleImpactSetting = accessGroup.CanViewVehicleImpactSetting;
                newAccessGroup.HasUsersAccess = accessGroup.HasUsersAccess;
                newAccessGroup.CanViewUsers = accessGroup.CanViewUsers;
                newAccessGroup.CanEditVehicleChecklist = accessGroup.CanEditVehicleChecklist;
                newAccessGroup.CanViewGeneralProductivityReport = accessGroup.CanViewGeneralProductivityReport;
                newAccessGroup.CanEditUserWebsiteAccess = accessGroup.CanEditUserWebsiteAccess;
                newAccessGroup.CanViewDashboard = accessGroup.CanViewDashboard;
                newAccessGroup.CanCreateCustomerEmailGroup = accessGroup.CanCreateCustomerEmailGroup;
                newAccessGroup.CanCreateUserLicense = accessGroup.CanCreateUserLicense;
                newAccessGroup.CanViewVehicleService = accessGroup.CanViewVehicleService;
                newAccessGroup.CanViewServiceCheckReport = accessGroup.CanViewServiceCheckReport;
                newAccessGroup.CanEditVehicleImpactSetting = accessGroup.CanEditVehicleImpactSetting;
                newAccessGroup.CanEditCustomerSite = accessGroup.CanEditCustomerSite;
                newAccessGroup.CanCreateCustomerAccessGroups = accessGroup.CanCreateCustomerAccessGroups;
                newAccessGroup.CanViewVehicleChecklistSetting = accessGroup.CanViewVehicleChecklistSetting;
                newAccessGroup.CanViewVehicleAccess = accessGroup.CanViewVehicleAccess;
                newAccessGroup.CanViewVehicle = accessGroup.CanViewVehicle;
                newAccessGroup.CanViewVehicleOtherSettingVorStatus = accessGroup.CanViewVehicleOtherSettingVorStatus;
                newAccessGroup.CanCreateUserCard = accessGroup.CanCreateUserCard;
                newAccessGroup.CanViewCustomer = accessGroup.CanViewCustomer;
                newAccessGroup.CanViewImpactReport = accessGroup.CanViewImpactReport;
                newAccessGroup.CanViewCustomerSite = accessGroup.CanViewCustomerSite;
                newAccessGroup.CanExportServiceCheckReport = accessGroup.CanExportServiceCheckReport;
                newAccessGroup.CanCreateUserWebsiteAccess = accessGroup.CanCreateUserWebsiteAccess;
                newAccessGroup.CanViewCurrentStatusReport = accessGroup.CanViewCurrentStatusReport;
                newAccessGroup.CanCreateVehicle = accessGroup.CanCreateVehicle;
                newAccessGroup.CanViewUserReportSubscription = accessGroup.CanViewUserReportSubscription;
                newAccessGroup.CanEditUserAlert = accessGroup.CanEditUserAlert;
                newAccessGroup.CanCreateUserAlert = accessGroup.CanCreateUserAlert;
                newAccessGroup.HasReportsAccess = accessGroup.HasReportsAccess;
                newAccessGroup.CanViewVehicleOtherSettingFullLockout = accessGroup.CanViewVehicleOtherSettingFullLockout;
                newAccessGroup.CanViewUserWebsiteAccess = accessGroup.CanViewUserWebsiteAccess;
                newAccessGroup.CanViewCustomerDepartment = accessGroup.CanViewCustomerDepartment;
                newAccessGroup.CanDeleteVehicleService = accessGroup.CanDeleteVehicleService;
                newAccessGroup.CanCreateCustomerEmailList = accessGroup.CanCreateCustomerEmailList;
                newAccessGroup.CanExportGeneralProductivityReport = accessGroup.CanExportGeneralProductivityReport;
                newAccessGroup.CanEditUser = accessGroup.CanEditUser;
                newAccessGroup.CanViewCustomerEmailGroup = accessGroup.CanViewCustomerEmailGroup;
                newAccessGroup.CanCreateVehicleService = accessGroup.CanCreateVehicleService;
                newAccessGroup.CanViewUserLicense = accessGroup.CanViewUserLicense;
                newAccessGroup.CanEditCustomer = accessGroup.CanEditCustomer;
                newAccessGroup.CanEditCustomerEmailGroup = accessGroup.CanEditCustomerEmailGroup;
                newAccessGroup.CanViewUserCard = accessGroup.CanViewUserCard;
                newAccessGroup.CanEditVehicleAccess = accessGroup.CanEditVehicleAccess;
                newAccessGroup.CanCreateCustomerDepartment = accessGroup.CanCreateCustomerDepartment;
                newAccessGroup.CanViewVehicleChecklist = accessGroup.CanViewVehicleChecklist;
                newAccessGroup.CanEditCustomerDepartment = accessGroup.CanEditCustomerDepartment;
                newAccessGroup.CanExportVehicle = accessGroup.CanExportVehicle;
                newAccessGroup.HasVehiclesAccess = accessGroup.HasVehiclesAccess;
                newAccessGroup.CanExportMachineUnlockReport = accessGroup.CanExportMachineUnlockReport;
                newAccessGroup.CanEditUserSupervisorAccess = accessGroup.CanEditUserSupervisorAccess;
                newAccessGroup.CanEditUserLicense = accessGroup.CanEditUserLicense;
                newAccessGroup.CanEditUserCard = accessGroup.CanEditUserCard;
                newAccessGroup.CanCreateUserReportSubscription = accessGroup.CanCreateUserReportSubscription;
                newAccessGroup.HasCustomersAccess = accessGroup.HasCustomersAccess;
                newAccessGroup.CanCreateVehicleChecklistSetting = accessGroup.CanCreateVehicleChecklistSetting;
                newAccessGroup.CanViewUserAlert = accessGroup.CanViewUserAlert;
                newAccessGroup.CanViewCustomerFirmware = accessGroup.CanViewCustomerFirmware;
                newAccessGroup.CanExportUsers = accessGroup.CanExportUsers;
                newAccessGroup.CanEditVehicleChecklistSetting = accessGroup.CanEditVehicleChecklistSetting;
                newAccessGroup.CanViewCustomerModel = accessGroup.CanViewCustomerModel;
                newAccessGroup.CanExportImpactReport = accessGroup.CanExportImpactReport;
                newAccessGroup.CanDeleteVehicleChecklist = accessGroup.CanDeleteVehicleChecklist;
                newAccessGroup.CanEditVehicleService = accessGroup.CanEditVehicleService;

                await _dataFacade.AccessGroupDataProvider.SaveAsync(newAccessGroup);
            }

            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// CopyFeatureSubscription Method
        /// </summary>
        /// <param name="customerFeatureSubscriptionId"></param>
        /// <param name="customerIds"></param>
        /// <returns></returns>
        public async Task<ComponentResponse<bool>> CopyFeatureSubscriptionAsync(Guid customerFeatureSubscriptionId, Guid[] customerIds, Dictionary<string, object> parameters = null)
        {
            var dealerFeatureSubscription = await GetDealerFeatureSubscription(customerFeatureSubscriptionId);

            foreach (var customerId in customerIds)
            {
                var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
                customer.Id = customerId;

                customer = await _dataFacade.CustomerDataProvider.GetAsync(customer, skipSecurity: true);

                if (customer.CustomerFeatureSubscriptionId.HasValue)
                {
                    // Update existing
                    var existingSubscription = await _dataFacade.CustomerFeatureSubscriptionDataProvider.GetAsync(
                        _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize(customer.CustomerFeatureSubscriptionId.Value), 
                        skipSecurity: true);
                    
                    existingSubscription.Name = dealerFeatureSubscription.Name;
                    existingSubscription.Description = dealerFeatureSubscription.Description;
                    existingSubscription.HasAdditionalHardwaresAccess = dealerFeatureSubscription.HasAdditionalHardwaresAccess;
                    existingSubscription.IsEnabled = dealerFeatureSubscription.IsEnab;
                    
                    await _dataFacade.CustomerFeatureSubscriptionDataProvider.SaveAsync(existingSubscription);
                }
                else
                {
                    // Create new (current logic)
                    var newCustomerFeatureSubscription = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>();
                    newCustomerFeatureSubscription.Id = Guid.NewGuid();
                    newCustomerFeatureSubscription.Name = dealerFeatureSubscription.Name;
                    newCustomerFeatureSubscription.Description = dealerFeatureSubscription.Description;
                    newCustomerFeatureSubscription.HasAdditionalHardwaresAccess = dealerFeatureSubscription.HasAdditionalHardwaresAccess;
                    newCustomerFeatureSubscription.IsEnabled = dealerFeatureSubscription.IsEnab;
                    newCustomerFeatureSubscription.IsTagged = false; // Default value for new customer feature subscriptions

                    // Save the new customer feature subscription
                    newCustomerFeatureSubscription = await _dataFacade.CustomerFeatureSubscriptionDataProvider.SaveAsync(newCustomerFeatureSubscription);

                    // Update the customer to reference the new feature subscription
                    customer.CustomerFeatureSubscriptionId = newCustomerFeatureSubscription.Id;
                }

                await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            }

            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// GetHireDehireCustomers Method
        /// GetHireDehireCustomers 
        /// </summary>
        /// <param name="vehicleId"></param>
        /// <returns></returns>		
        public async System.Threading.Tasks.Task<ComponentResponse<DataObjectCollection<CustomerDataObject>>> GetHireDehireCustomersAsync(System.Guid vehicleId, Dictionary<string, object> parameters = null)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            var dealerId = (await vehicle.LoadCustomerAsync()).DealerId;
            var onHire = vehicle.OnHire;
            if (onHire == true)
            {
                // getting dehired so return dealerCustomer
                var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "DealerId == @0 && DealerCustomer == @1", new object[] { dealerId, true });
                if (customers == null)
                {
                    throw new GOServerException("No dealer customer found.");
                }
                return new ComponentResponse<DataObjectCollection<CustomerDataObject>>(new DataObjectCollection<CustomerDataObject>(customers));
            }
            else
            {
                // return all customers of the dealer since getting hired
                var customers = await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "DealerId == @0", new object[] { dealerId });
                if (customers == null)
                {
                    throw new GOServerException("No customers found.");
                }
                return new ComponentResponse<DataObjectCollection<CustomerDataObject>>(new DataObjectCollection<CustomerDataObject>(customers));
            }
        }

        private async Task<AccessGroupDataObject> GetAccessGroup(Guid accessGroupId)
        {
            var accessGroup = _serviceProvider.GetRequiredService<AccessGroupDataObject>();
            accessGroup.Id = accessGroupId;

            accessGroup = await _dataFacade.AccessGroupDataProvider.GetAsync(accessGroup, skipSecurity: true);

            if (accessGroup == null)
            {
                throw new GOServerException($"Access group with id: {accessGroupId} not found");
            }

            return accessGroup;
        }

        private async Task<AccessGroupTemplateDataObject> GetAccessGroupTemplate(Guid accessGroupTemplateId)
        {
            var accessGroupTemplate = _serviceProvider.GetRequiredService<AccessGroupTemplateDataObject>();
            accessGroupTemplate.Id = accessGroupTemplateId;

            accessGroupTemplate = await _dataFacade.AccessGroupTemplateDataProvider.GetAsync(accessGroupTemplate, skipSecurity: true);

            if (accessGroupTemplate == null)
            {
                throw new GOServerException($"Access group template with id: {accessGroupTemplateId} not found");
            }

            return accessGroupTemplate;
        }

        private async Task<DealerFeatureSubscriptionDataObject> GetDealerFeatureSubscription(Guid dealerFeatureSubscriptionId)
        {
            var dealerFeatureSubscription = _serviceProvider.GetRequiredService<DealerFeatureSubscriptionDataObject>();
            dealerFeatureSubscription.Id = dealerFeatureSubscriptionId;

            dealerFeatureSubscription = await _dataFacade.DealerFeatureSubscriptionDataProvider.GetAsync(dealerFeatureSubscription, skipSecurity: true);

            if (dealerFeatureSubscription == null)
            {
                throw new GOServerException($"Dealer feature subscription with id: {dealerFeatureSubscriptionId} not found");
            }

            return dealerFeatureSubscription;
        }
    }
}
