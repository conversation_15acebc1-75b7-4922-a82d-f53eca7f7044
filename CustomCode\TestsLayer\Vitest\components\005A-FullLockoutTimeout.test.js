import { describe, it, expect, beforeEach } from 'vitest';

describe('FullLockoutTimeout Tests', () => {
    // Test 1: Simple read-only logic
    it('should allow modifying FullLockoutTimeout when FullLockout is true', () => {
        // Create a minimal mock of the viewmodel
        const mockViewModel = {
            VehicleOtherSettingsObject: () => ({
                Data: {
                    FullLockout: () => true
                }
            })
        };

        const customViewModel = new FleetXQ.Web.ViewModels.VehicleOtherSettingsFormViewModelCustom(mockViewModel);

        // Verify FullLockoutTimeout is not read-only
        expect(customViewModel.IsFullLockoutTimeoutReadOnly()).toBe(false);
    });

    // Test 2: Simple read-only logic
    it('should make FullLockoutTimeout read-only when FullLockout is false', () => {
        // Create a minimal mock of the viewmodel
        const mockViewModel = {
            VehicleOtherSettingsObject: () => ({
                Data: {
                    FullLockout: () => false
                }
            })
        };

        const customViewModel = new FleetXQ.Web.ViewModels.VehicleOtherSettingsFormViewModelCustom(mockViewModel);

        // Verify FullLockoutTimeout is read-only
        expect(customViewModel.IsFullLockoutTimeoutReadOnly()).toBe(true);
    });

    // Test 3: Full validation test
    describe('Validation Tests', () => {
        let viewModel;
        let customViewModel;
        let mockController;
        let mockApplicationController;

        beforeEach(() => {
            // Mock ApplicationController
            mockApplicationController = {
                viewModel: {
                    security: {
                        currentUserClaims: () => ({
                            HasVehiclesAccess: 'True',
                            CanEditFullImpactLockout: 'True'
                        })
                    }
                },
                getNextContextId: () => 'test-context-id',
                showConfirmPopup: () => { },
                showAlertPopup: () => { },
                closeCurrentPopup: () => { }
            };

            // Mock Controller
            mockController = {
                applicationController: mockApplicationController,
                ObjectsDataSet: {
                    AddContextIdsStatusChangeHandler: () => { },
                    RemoveContextIdsStatusChangeHandler: () => { },
                    cleanContext: () => { },
                    isContextIdDirty: () => false,
                    resetContextIdDirty: () => { },
                    AddOrReplaceObject: () => { },
                    RemoveObject: () => { },
                    GetObject: (obj) => obj
                }
            };

            // Initialize ViewModel
            viewModel = new FleetXQ.Web.ViewModels.VehicleOtherSettingsFormViewModel(
                mockController,
                null,
                null,
                null,
                null,
                { isRootSubForm: true }
            );

            // Initialize Custom ViewModel
            customViewModel = new FleetXQ.Web.ViewModels.VehicleOtherSettingsFormViewModelCustom(viewModel);
        });

        it('should show error when FullLockoutTimeout is negative', () => {
            // Set FullLockout to true
            viewModel.VehicleOtherSettingsObject().Data.FullLockout(true);

            // Set a negative FullLockoutTimeout
            viewModel.VehicleOtherSettingsObject().Data.FullLockoutTimeout(-1);

            // Run validation
            viewModel.runValidation(true);

            // Verify validation fails
            expect(viewModel.StatusData.isValid()).toBe(false);
            expect(viewModel.StatusData.errorSummary().length).toBeGreaterThan(0);
        });
    });
});
