using System;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using NUnit.Framework;
using System.Linq;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;
using Moq;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using Azure.Storage.Blobs;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class VehicleAPITest : TestBase
    {
        private IVehicleAPI _vehicleAPI;
        private IDataFacade _dataFacade;
        private IModuleUtilities _moduleUtilities;
        private IDeviceTwinHandler _deviceTwinHandler;
        private ILoggingService _logger;
        private IVehicleUtils _vehicleUtils;
        private IDeviceMessageHandler _deviceMessageHandler;
        private readonly string _testDatabaseName = $"VehicleAPITest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _departmentId;
        private Guid _modelId;
        private Guid _siteId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            _deviceTwinHandler = Substitute.For<IDeviceTwinHandler>();
            _logger = Substitute.For<ILoggingService>();
            _vehicleUtils = Substitute.For<IVehicleUtils>();
            _deviceMessageHandler = Substitute.For<IDeviceMessageHandler>();

            services.AddTransient<IDeviceTwinHandler>(sp => _deviceTwinHandler);
            services.AddTransient<ILoggingService>(sp => _logger);
            services.AddTransient<IVehicleUtils>(sp => _vehicleUtils);
            services.AddTransient<IDeviceMessageHandler>(sp => _deviceMessageHandler);

            // Mock IWebHostEnvironment using Moq
            var webHostEnvironment = new Mock<IWebHostEnvironment>();
            webHostEnvironment.Setup(x => x.WebRootPath).Returns(@"C:\test\wwwroot");
            services.AddSingleton<IWebHostEnvironment>(webHostEnvironment.Object);

            // Mock IFileService
            var fileService = Substitute.For<IFileService>();
            services.AddSingleton<IFileService>(fileService);


        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _vehicleAPI = _serviceProvider.GetRequiredService<IVehicleAPI>();
            _moduleUtilities = _serviceProvider.GetRequiredService<IModuleUtilities>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [SetUp]
        public void SetUp()
        {
            _deviceTwinHandler.ClearReceivedCalls();
            _logger.ClearReceivedCalls();
            _vehicleUtils.ClearReceivedCalls();
            _deviceMessageHandler.ClearReceivedCalls();

            // Configure device twin handler methods
            _deviceTwinHandler.SyncGeneralSettings(Arg.Any<string>()).Returns(Task.CompletedTask);
            _deviceTwinHandler.SyncLogoAsync(Arg.Any<string>(), Arg.Any<string>()).Returns(Task.CompletedTask);
            _deviceTwinHandler.UpdateFirmware(Arg.Any<string>(), Arg.Any<string>()).Returns(Task.CompletedTask);

            // Configure IFileService mock
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(true);
            fileService.OpenRead(Arg.Any<string>()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes("test content")));

            _vehicleUtils.GetDeviceTwinReportedProperties(Arg.Any<string>()).Returns(new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    LastPreopCheck = **********,
                    ShockParameter = new ShockParameter
                    {
                        RedImpact = 100,
                        Threshold = 50
                    },
                    IdleParameter = new IdleParameter
                    {
                        Timeout = 300
                    },
                    ChecklistTimeout = 60
                },
                System = new FleetXQ.Data.DataObjects.Custom.System
                {
                    ModemIccid = "123456789",
                    ExpansionModuleVersion = "1.0.0",
                    AppVersion = "2.0.0",
                    RootfsVersion = "3.0.0"
                },
                Status = new Status
                {
                    ModemApn = "test.apn",
                    CanConfigCrc = *********,
                    Csq = "20"
                }
            });

            _vehicleUtils.GetLastActivityTime(Arg.Any<string>()).Returns("2024-03-20T10:00:00");
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            //create firmware
            var firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
            firmware.Id = Guid.NewGuid();
            firmware.Version = "6.0.0U";
            firmware.Url = "https://fleetxqfiles.blob.core.windows.net/devicetwin/firmware/FXQ_6.0.0U/FleetIQ360App";
            await _dataFacade.FirmwareDataProvider.SaveAsync(firmware);

            firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
            firmware.Id = Guid.NewGuid();
            firmware.Version = "6.0.0T";
            firmware.Url = "https://fleetxqfiles.blob.core.windows.net/devicetwin/firmware/FXQ_6.0.0T/FleetIQ360App";
            await _dataFacade.FirmwareDataProvider.SaveAsync(firmware);

            //create canrule
            var canrule = _serviceProvider.GetRequiredService<CanruleDataObject>();
            canrule.Id = Guid.NewGuid();
            canrule.CRC = "21D3E387";
            canrule.Name = "388(388C0)";
            canrule = await _dataFacade.CanruleDataProvider.SaveAsync(canrule);

            var canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANATT=1,SEAT,3,0,1,1";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANPGN=3,0,7,0,91,0";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANLIN2=1,5A8,628,4001200500000000,4B01200500000000,FFFFFFFF00000000,1,27,=,1";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANLIN2=2,5A8,628,4001200200000000,4B01200200000000,FFFFFFFF00000000,20,20,>,0";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANSPN=5,3,1,6163,10,18,-,0";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANBYD=1,628,40012005,1,27,=,1";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANCFG=2,500000,0,1";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            canruleDetails = _serviceProvider.GetRequiredService<CanruleDetailsDataObject>();
            canruleDetails.Id = Guid.NewGuid();
            canruleDetails.CanruleId = canrule.Id;
            canruleDetails.Canrules = "CANLIN=1,1C0,0,8,0,>,0";
            await _dataFacade.CanruleDetailsDataProvider.SaveAsync(canruleDetails);

            //Create IOs
            var iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "0";
            iofields.Description = "ignition";
            iofields.IOType = " ";
            iofields.CANBUS = false;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "SEAT";
            iofields.Description = "Canbus Seat Switch Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "HYDL";
            iofields.Description = "Canbus Hydrolic Raising Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "TRACK";
            iofields.Description = "Canbus Traction/Movement Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);
            _siteId = site.Id;

            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.CustomerId = customer.Id;
            department.Name = "Test Department";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);
            _departmentId = department.Id;

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            model.Type = ModelTypesEnum.Electric;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);
            _modelId = model.Id;
        }

        private async Task<(VehicleDataObject, ModuleDataObject)> CreateTestVehicleAsync(string hireNo, string serialNo)
        {
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID1";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = $"test_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}_{_departmentId}";
            module.IsAllocatedToVehicle = true;
            module.Status = ModuleStatusEnum.Assigned;
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = _customerId;
            vehicle.DepartmentId = _departmentId;
            vehicle.ModelId = _modelId;
            vehicle.SiteId = _siteId;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = hireNo;
            vehicle.SerialNo = serialNo;
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            return (vehicle, module);
        }

        [Test]
        public async Task UpdateVehicleDiagnosticAsync_WhenTimezoneMismatch_SetsIsSynchronizedToFalse()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 5", "Test Serial No 5");
            await vehicle.LoadVehicleDiagnosticAsync();

            var vehicleDiagnosticData = vehicle.VehicleDiagnostic ?? _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
            vehicleDiagnosticData.Id = vehicle.VehicleDiagnostic?.Id ?? Guid.NewGuid();
            vehicleDiagnosticData.Timezone = "300"; // 5 hours offset
            await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);

            var deviceTwinProperties = new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    IdleParameter = new IdleParameter { Timeout = 300 }
                }
            };

            _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice)
                .Returns(deviceTwinProperties);

            // Act
            var result = await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            Assert.That(result.Result.IsSynchronized, Is.False);
        }

        [Test]
        public async Task UpdateVehicleDiagnosticAsync_WhenIdleTimeMismatch_SetsIsSynchronizedToFalse()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 6", "Test Serial No 6");
            vehicle.IDLETimer = 600; // 10 minutes
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            await vehicle.LoadVehicleDiagnosticAsync();

            var vehicleDiagnosticData = vehicle.VehicleDiagnostic ?? _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
            vehicleDiagnosticData.Id = vehicle.VehicleDiagnostic?.Id ?? Guid.NewGuid();
            vehicleDiagnosticData.SeatIdles = "300"; // 5 minutes
            await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);

            var deviceTwinProperties = new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    IdleParameter = new IdleParameter { Timeout = 300 }
                }
            };

            _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice)
                .Returns(deviceTwinProperties);

            // Act
            var result = await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            Assert.That(result.Result.IsSynchronized, Is.False);
        }

        [Test]
        public async Task UpdateVehicleDiagnosticAsync_WhenDatabaseRedImpactThresholdMatchesCalculatedValue_SetsIsSynchronizedToTrue()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 7", "Test Serial No 7");
            await vehicle.LoadVehicleDiagnosticAsync();

            var vehicleDiagnosticData = vehicle.VehicleDiagnostic ?? _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
            vehicleDiagnosticData.Id = vehicle.VehicleDiagnostic?.Id ?? Guid.NewGuid();
            vehicleDiagnosticData.DatabaseRedImpactThreshold = 20000; // Matches FSSSBase * FSSXMulti
            vehicleDiagnosticData.VehicleId = vehicle.Id;
            await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);

            var deviceTwinProperties = new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    ShockParameter = new ShockParameter { RedImpact = 20000 }
                }
            };

            _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice)
                .Returns(deviceTwinProperties);

            // Act
            var result = await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            Assert.That(result.Result.IsSynchronized, Is.True);
        }

        [Test]
        public async Task UpdateVehicleDiagnosticAsync_WhenDatabaseRedImpactThresholdIsNullAndDeviceTwinMatchesCalculatedValue_SetsIsSynchronizedToTrue()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 9", "Test Serial No 9");
            await vehicle.LoadVehicleDiagnosticAsync();

            var vehicleDiagnosticData = vehicle.VehicleDiagnostic ?? _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
            vehicleDiagnosticData.Id = vehicle.VehicleDiagnostic?.Id ?? Guid.NewGuid();
            vehicleDiagnosticData.DatabaseRedImpactThreshold = null;
            vehicleDiagnosticData.VehicleId = vehicle.Id;
            await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);

            var threshold = module.FSSSBase * ((module.FSSXMulti / 100) + 1);
            var redImpactThreshold = (int)(threshold * 10);

            var deviceTwinProperties = new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    ShockParameter = new ShockParameter { RedImpact = redImpactThreshold }
                }
            };

            _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice)
                .Returns(deviceTwinProperties);

            // Act
            var result = await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            Assert.That(result.Result.IsSynchronized, Is.True);
        }

        [Test]
        public async Task UpdateVehicleDiagnosticAsync_WhenDatabaseRedImpactThresholdIsNullAndDeviceTwinDoesNotMatchCalculatedValue_SetsIsSynchronizedToFalse()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 10", "Test Serial No 10");
            await vehicle.LoadVehicleDiagnosticAsync();

            var vehicleDiagnosticData = vehicle.VehicleDiagnostic ?? _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
            vehicleDiagnosticData.Id = vehicle.VehicleDiagnostic?.Id ?? Guid.NewGuid();
            vehicleDiagnosticData.DatabaseRedImpactThreshold = null;
            vehicleDiagnosticData.VehicleId = vehicle.Id;
            await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);

            var threshold = module.FSSSBase * ((module.FSSXMulti / 100) + 1);
            var redImpactThreshold = (int)(threshold * 10);

            var deviceTwinProperties = new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    ShockParameter = new ShockParameter { RedImpact = redImpactThreshold + 1000 } // Doesn't match calculated value
                }
            };

            _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice)
                .Returns(deviceTwinProperties);

            // Act
            var result = await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            Assert.That(result.Result.IsSynchronized, Is.False);
        }

        [Test]
        public async Task UpdateVehicleDiagnosticAsync_WhenDatabaseRedImpactThresholdRequiresFSSSBaseUpdate_ShouldUpdateModuleAndVehicle()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 13", "Test Serial No 13");
            await vehicle.LoadVehicleDiagnosticAsync();

            var vehicleDiagnosticData = vehicle.VehicleDiagnostic ?? _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
            vehicleDiagnosticData.Id = vehicle.VehicleDiagnostic?.Id ?? Guid.NewGuid();
            vehicleDiagnosticData.DatabaseRedImpactThreshold = 20000; // This will require updating FSSSBase
            vehicleDiagnosticData.VehicleId = vehicle.Id;
            await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnosticData);

            var deviceTwinProperties = new DeviceTwinReportedProperties
            {
                Configuration = new Configuration
                {
                    ShockParameter = new ShockParameter { RedImpact = 20000 }
                }
            };

            _vehicleUtils.GetDeviceTwinReportedProperties(module.IoTDevice)
                .Returns(deviceTwinProperties);

            // Act
            var result = await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            Assert.That(result.Result.IsSynchronized, Is.True);

            // Verify module was updated
            var updatedModule = await _dataFacade.ModuleDataProvider.GetAsync(module);
            Assert.That(updatedModule.Calibration, Is.EqualTo(100));
            Assert.That(updatedModule.FSSSBase, Is.EqualTo(1980.1980198019801d).Within(0.0000000000001d)); // 20000 / 10 / ((1/100) + 1)

            // Verify vehicle was updated
            var updatedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(updatedVehicle.ImpactLockout, Is.True);

            // Verify vehicle diagnostic was updated
            Assert.That(result.Result.RedImpactThreshold, Is.EqualTo(20000));
        }

        [Test]
        public async Task UnlockAsync_WhenVehicleExists_ShouldSendUnlockMessage()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 4", "Test Serial No 4");
            string actualMessage = null;
            string actualDeviceId = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualDeviceId = x.ArgAt<string>(0);
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.UnlockAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            await _deviceMessageHandler.Received(1).SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>());
            Assert.That(actualDeviceId, Is.EqualTo(module.IoTDevice));
            Assert.That(actualMessage, Is.EqualTo("MAINT=0"));
        }

        [Test]
        public void UnlockAsync_WhenVehicleDoesNotExist_ShouldThrowException()
        {
            // Arrange
            var nonExistentVehicleId = Guid.NewGuid();

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() => _vehicleAPI.UnlockAsync(nonExistentVehicleId));
            Assert.That(ex.Message, Does.Contain($"unknow vehicle id {nonExistentVehicleId}"));
        }

        [Test]
        public async Task RebootAsync_WhenVehicleExists_ShouldSendRebootMessage()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 5", "Test Serial No 5");
            string actualMessage = null;
            string actualDeviceId = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualDeviceId = x.ArgAt<string>(0);
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.RebootAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.True);
            await _deviceMessageHandler.Received(1).SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>());
            Assert.That(actualDeviceId, Is.EqualTo(module.IoTDevice));
            Assert.That(actualMessage, Is.EqualTo("REBOOT"));
        }

        [Test]
        public void RebootAsync_WhenVehicleDoesNotExist_ShouldThrowException()
        {
            // Arrange
            var nonExistentVehicleId = Guid.NewGuid();

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() => _vehicleAPI.RebootAsync(nonExistentVehicleId));
            Assert.That(ex.Message, Does.Contain($"unknow vehicle id {nonExistentVehicleId}"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenValidInput_ShouldSendMessagesToAllVehicles()
        {
            // Arrange
            var (vehicle1, module1) = await CreateTestVehicleAsync("Test Vehicle 11", "Test Serial No 11");
            var (vehicle2, module2) = await CreateTestVehicleAsync("Test Vehicle 12", "Test Serial No 12");

            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var vehicleIds = new[] { vehicle1.Id, vehicle2.Id };
            var sentMessages = new List<(string deviceId, string message)>();

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    sentMessages.Add((x.ArgAt<string>(0), x.ArgAt<string>(1)));
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(sentMessages.Count, Is.EqualTo(2));

            // Verify first vehicle message
            Assert.That(sentMessages[0].deviceId, Is.EqualTo(module1.IoTDevice));
            Assert.That(sentMessages[0].message, Does.StartWith("MSG="));
            Assert.That(sentMessages[0].message, Does.Contain(broadcastMessage.Message));
            Assert.That(sentMessages[0].message, Does.Contain(((int)broadcastMessage.Priority).ToString()));
            Assert.That(sentMessages[0].message, Does.Contain(((int)broadcastMessage.ResponseOptions).ToString()));
            Assert.That(sentMessages[0].message.Split(',')[0], Does.Match(@"^MSG=\d+$")); // Allow any number of digits

            // Verify second vehicle message
            Assert.That(sentMessages[1].deviceId, Is.EqualTo(module2.IoTDevice));
            Assert.That(sentMessages[1].message, Does.StartWith("MSG="));
            Assert.That(sentMessages[1].message, Does.Contain(broadcastMessage.Message));
            Assert.That(sentMessages[1].message, Does.Contain(((int)broadcastMessage.Priority).ToString()));
            Assert.That(sentMessages[1].message, Does.Contain(((int)broadcastMessage.ResponseOptions).ToString()));
            Assert.That(sentMessages[1].message.Split(',')[0], Does.Match(@"^MSG=\d+$")); // Allow any number of digits
        }

        [Test]
        public void BroadcastMessageAsync_WhenBroadcastMessageIsNull_ShouldThrowException()
        {
            // Arrange
            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() =>
                _vehicleAPI.BroadcastMessageAsync(null, vehicleIds));
            Assert.That(ex.ParamName, Is.EqualTo("broadcastMessage"));
        }

        [Test]
        public void BroadcastMessageAsync_WhenVehicleIdsIsNull_ShouldThrowException()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() =>
                _vehicleAPI.BroadcastMessageAsync(broadcastMessage, null));
            Assert.That(ex.ParamName, Is.EqualTo("vehicleIds"));
        }

        [Test]
        public void BroadcastMessageAsync_WhenVehicleIdsIsEmpty_ShouldThrowException()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.BroadcastMessageAsync(broadcastMessage, Array.Empty<Guid>()));
            Assert.That(ex.Message, Does.Contain("vehicleIds"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenVehicleNotFound_ShouldThrowException()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var nonExistentVehicleId = Guid.NewGuid();
            var vehicleIds = new[] { nonExistentVehicleId };

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() =>
                _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds));
            Assert.That(ex.Message, Does.Contain($"unknow vehicle id {nonExistentVehicleId}"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WithNormalPriority_ShouldSendMessageWithCorrectPriority()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.NormalMessage;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 14", "Test Serial No 14");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain(((int)MessagePriorityEnum.NormalMessage).ToString()));
        }

        [Test]
        public async Task BroadcastMessageAsync_WithDifferentResponseOptions_ShouldSendMessageWithCorrectOptions()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.OK;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 15", "Test Serial No 15");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain(((int)ResponseOptionsEnum.OK).ToString()));
        }

        [Test]
        public async Task BroadcastMessageAsync_MessageFormat_ShouldIncludeAllRequiredComponents()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 16", "Test Serial No 16");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.StartWith("MSG="));
            Assert.That(actualMessage, Does.Contain(broadcastMessage.Message));
            Assert.That(actualMessage, Does.Contain(((int)broadcastMessage.Priority).ToString()));
            Assert.That(actualMessage, Does.Contain(((int)broadcastMessage.ResponseOptions).ToString()));
            Assert.That(actualMessage, Does.Contain("60")); // Default timeout

            // Verify message ID is a number
            var messageParts = actualMessage.Split(',');
            Assert.That(messageParts[0], Does.Match(@"^MSG=\d+$")); // Allow any number of digits
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageContainsCommas_ShouldReplaceCommasWithSpaces()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Hello,World,Test";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 17", "Test Serial No 17");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("Hello World Test"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageContainsSpecialChars_ShouldReplaceWithSpaces()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test[message]^with+special,chars";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 18", "Test Serial No 18");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("Test message with special chars"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageContainsMultipleSpaces_ShouldNotHaveExtraSpaces()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Hello,,,World,,,Test";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 19", "Test Serial No 19");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("Hello World Test"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageHasLeadingOrTrailingSpaces_ShouldTrimSpaces()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "  Hello World  ";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 18", "Test Serial No 18");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("Hello World"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageIsEmpty_ShouldThrowArgumentException()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "   ";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 19", "Test Serial No 19");
            var vehicleIds = new[] { vehicle.Id };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds));
            Assert.That(ex.Message, Does.Contain("Message cannot be null or empty"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageIsNull_ShouldThrowArgumentException()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = null;
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 20", "Test Serial No 20");
            var vehicleIds = new[] { vehicle.Id };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds));
            Assert.That(ex.Message, Does.Contain("Message cannot be null or empty"));
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenTimeoutIsZero_ShouldSetDefaultTimeout()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;
            broadcastMessage.Timeout = 0;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 21", "Test Serial No 21");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("60")); // Default timeout should be 60
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenTimeoutIsNegative_ShouldSetDefaultTimeout()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;
            broadcastMessage.Timeout = -10;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 22", "Test Serial No 22");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("60")); // Default timeout should be 60
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenTimeoutIsPositive_ShouldUseSpecifiedTimeout()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;
            broadcastMessage.Timeout = 120;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 23", "Test Serial No 23");
            var vehicleIds = new[] { vehicle.Id };
            string actualMessage = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            Assert.That(result.Result, Is.True);
            Assert.That(actualMessage, Does.Contain("120")); // Should use specified timeout
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenMessageSendingSucceeds_ShouldNotLogError()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 25", "Test Serial No 25");
            var vehicleIds = new[] { vehicle.Id };

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask);

            // Act
            await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            _logger.DidNotReceive().LogError(Arg.Any<Exception>(), Arg.Any<string>());
        }

        [Test]
        public async Task BroadcastMessageAsync_WhenDriverNotFound_ShouldCreateHistoryWithoutDriverId()
        {
            // Arrange
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test message";
            broadcastMessage.Priority = MessagePriorityEnum.HighPriority;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;

            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 27", "Test Serial No 27");
            var vehicleIds = new[] { vehicle.Id };

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask);

            // Act
            await _vehicleAPI.BroadcastMessageAsync(broadcastMessage, vehicleIds);

            // Assert
            var history = (await _dataFacade.BroadcastMessageHistoryDataProvider.GetCollectionAsync(
                null,
                "VehicleId == @0",
                new object[] { vehicle.Id }
            )).FirstOrDefault();

            Assert.That(history, Is.Not.Null);
            Assert.That(history.DriverId, Is.Null);
        }

        [Test]
        public async Task ResetUnitMemoryAsync_WhenVehicleExists_ShouldSendResetMemoryMessage()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 28", "Test Serial No 28");
            string actualMessage = null;
            string actualDeviceId = null;

            _deviceMessageHandler.SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.CompletedTask)
                .AndDoes(x =>
                {
                    actualDeviceId = x.ArgAt<string>(0);
                    actualMessage = x.ArgAt<string>(1);
                });

            // Act
            var result = await _vehicleAPI.ResetUnitMemoryAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.True);
            await _deviceMessageHandler.Received(1).SendCloudToDeviceMessageAsync(Arg.Any<string>(), Arg.Any<string>());
            Assert.That(actualDeviceId, Is.EqualTo(module.IoTDevice));
            Assert.That(actualMessage, Is.EqualTo("CLRMEM"));
        }

        [Test]
        public void ResetUnitMemoryAsync_WhenVehicleDoesNotExist_ShouldThrowException()
        {
            // Arrange
            var nonExistentVehicleId = Guid.NewGuid();

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() => _vehicleAPI.ResetUnitMemoryAsync(nonExistentVehicleId));
            Assert.That(ex.Message, Does.Contain($"unknow vehicle id {nonExistentVehicleId}"));
        }

        [Test]
        public async Task SoftDeleteAsync_UnknownVehicleId_ThrowsException()
        {
            // Arrange
            var unknownVehicleId = Guid.NewGuid();

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _vehicleAPI.SoftDeleteAsync(unknownVehicleId));

            Assert.That(exception.Message, Is.EqualTo($"unknow vehicle id {unknownVehicleId}"));
        }

        [Test]
        public async Task SoftDeleteAsync_VehicleWithModule_SuccessfullySoftDeletes()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("HIRE001", "SERIAL001");
            var oldModuleId = module.Id;

            // Act
            var result = await _vehicleAPI.SoftDeleteAsync(vehicle.Id);

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify vehicle is marked as deleted
            var updatedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(updatedVehicle.DeletedAtUtc, Is.Not.Null);

            // Verify new dummy module was created
            var dummyModule = await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice.Contains(@0)", new object[] { $"Deleted_{vehicle.SerialNo}_" });
            Assert.That(dummyModule, Has.Count.EqualTo(1));
            Assert.That(dummyModule[0].Status, Is.EqualTo((ModuleStatusEnum)2)); // Assigned to vehicle
            Assert.That(dummyModule[0].ModuleType, Is.EqualTo((ModuleTypeEnum)0)); // Mk3

            // Verify old module status was updated to spare
            var oldModule = await _dataFacade.ModuleDataProvider.GetAsync(module);
            Assert.That(oldModule.Status, Is.EqualTo(ModuleStatusEnum.Spare));

            // Verify vehicle's module was updated after swap
            var finalVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(finalVehicle.ModuleId1, Is.EqualTo(dummyModule[0].Id));
        }

        [Test]
        public async Task UploadLogoAsync_WhenValidInput_ShouldUploadLogoAndSyncToVehicles()
        {
            // Arrange
            var (vehicle1, module1) = await CreateTestVehicleAsync("Test Vehicle 30", "Test Serial No 30");
            var (vehicle2, module2) = await CreateTestVehicleAsync("Test Vehicle 31", "Test Serial No 31");

            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { vehicle1.Id, vehicle2.Id };

            // Create test file system
            var testWebRootPath = Path.Combine(Path.GetTempPath(), "test_wwwroot");
            var testFilesPath = Path.Combine(testWebRootPath, "files");
            Directory.CreateDirectory(testFilesPath);
            
            // Get the mocked IWebHostEnvironment from the service provider
            var webHostEnvironment = _serviceProvider.GetRequiredService<IWebHostEnvironment>();

            // Mock IFileService
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(true);
            fileService.OpenRead(Arg.Any<string>()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes("test content")));

            // Create test file
            var filePath = Path.Combine(testFilesPath, uploadLogoRequest.LogoInternalName);
            File.WriteAllText(filePath, "test content");

            // Act
            var result = await _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest);

            // Assert
            Assert.That(result.Result, Is.True);
            var storageClientFactory = _serviceProvider.GetRequiredService<IStorageClientFactory>();
            await _deviceTwinHandler.Received(2).SyncLogoAsync(Arg.Any<string>(), Arg.Any<string>());
        }

        [Test]
        public void UploadLogoAsync_WhenUploadLogoRequestIsNull_ShouldThrowArgumentNullException()
        {
            // Arrange
            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentNullException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, null));
            Assert.That(ex.ParamName, Is.EqualTo("uploadLogoRequest"));
        }

        [Test]
        public void UploadLogoAsync_WhenVehicleIdsIsNull_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(null, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("vehicleIds cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenVehicleIdsIsEmpty_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(Array.Empty<Guid>(), uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("vehicleIds cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenLogoIsNull_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = null;
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Logo data cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenLogoIsEmpty_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Logo data cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenLogoIsWhitespace_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "   ";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Logo data cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenLogoInternalNameIsNull_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = null;

            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Logo internal name cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenLogoInternalNameIsEmpty_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "";

            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Logo internal name cannot be null or empty"));
        }

        [Test]
        public void UploadLogoAsync_WhenLogoInternalNameIsWhitespace_ShouldThrowArgumentException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "   ";

            var vehicleIds = new[] { Guid.NewGuid() };

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Logo internal name cannot be null or empty"));
        }

        [Test]
        public async Task UploadLogoAsync_WhenLocalFileDoesNotExist_ShouldThrowGOServerException()
        {
            // Arrange
            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "nonexistent-logo.png";

            var vehicleIds = new[] { Guid.NewGuid() };

            // Create test file system
            var testWebRootPath = Path.Combine(Path.GetTempPath(), "test_wwwroot");
            var testFilesPath = Path.Combine(testWebRootPath, "files");
            Directory.CreateDirectory(testFilesPath);
            
            // Get the mocked IWebHostEnvironment from the service provider
            var webHostEnvironment = _serviceProvider.GetRequiredService<IWebHostEnvironment>();

            // Mock IFileService
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(false);

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() =>
                _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest));
            Assert.That(ex.Message, Does.Contain("Local file not found"));
        }

        [Test]
        public async Task UploadLogoAsync_WhenVehicleNotFound_ShouldLogWarningAndContinue()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 32", "Test Serial No 32");
            var nonExistentVehicleId = Guid.NewGuid();

            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { vehicle.Id, nonExistentVehicleId };

            // Get the mocked IWebHostEnvironment from the service provider
            var webHostEnvironment = _serviceProvider.GetRequiredService<IWebHostEnvironment>();

            // Create test directory structure
            var testWebRootPath = webHostEnvironment.WebRootPath;
            var testFilesPath = Path.Combine(testWebRootPath, "files");
            Directory.CreateDirectory(testFilesPath);

            // Mock IFileService
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(true);
            fileService.OpenRead(Arg.Any<string>()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes("test content")));

            // Create test file
            var filePath = Path.Combine(testFilesPath, uploadLogoRequest.LogoInternalName);
            File.WriteAllText(filePath, "test content");

            // Act
            var result = await _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest);

            // Assert
            Assert.That(result.Result, Is.True);
            await _deviceTwinHandler.Received(1).SyncLogoAsync(Arg.Any<string>(), Arg.Any<string>());
            _logger.Received(1).LogWarning(Arg.Is<string>(msg => msg.Contains($"Vehicle with ID {nonExistentVehicleId} not found")));
        }

        [Test]
        public async Task UploadLogoAsync_WhenSyncLogoThrowsException_ShouldLogErrorAndContinue()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 34", "Test Serial No 34");

            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { vehicle.Id };

            // Get the mocked IWebHostEnvironment from the service provider
            var webHostEnvironment = _serviceProvider.GetRequiredService<IWebHostEnvironment>();

            // Create test directory structure
            var testWebRootPath = webHostEnvironment.WebRootPath;
            var testFilesPath = Path.Combine(testWebRootPath, "files");
            Directory.CreateDirectory(testFilesPath);

            // Mock IFileService
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(true);
            fileService.OpenRead(Arg.Any<string>()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes("test content")));

            // Create test file
            var filePath = Path.Combine(testFilesPath, uploadLogoRequest.LogoInternalName);
            File.WriteAllText(filePath, "test content");

            // Mock device twin handler to throw exception
            _deviceTwinHandler.SyncLogoAsync(Arg.Any<string>(), Arg.Any<string>())
                .Returns(Task.FromException<Exception>(new Exception("Sync failed")));

            // Act
            var result = await _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest);

            // Assert
            Assert.That(result.Result, Is.True);
            await _deviceTwinHandler.Received(1).SyncLogoAsync(Arg.Any<string>(), Arg.Any<string>());
            _logger.Received(1).LogError(Arg.Any<Exception>(), Arg.Is<string>(msg => msg.Contains($"Failed to sync logo to vehicle {vehicle.Id}")));
        }

        [Test]
        public async Task UploadLogoAsync_ShouldUseCorrectContainerNameFromConfiguration()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 36", "Test Serial No 36");

            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { vehicle.Id };

            // Get the mocked IWebHostEnvironment from the service provider
            var webHostEnvironment = _serviceProvider.GetRequiredService<IWebHostEnvironment>();

            // Create test directory structure
            var testWebRootPath = webHostEnvironment.WebRootPath;
            var testFilesPath = Path.Combine(testWebRootPath, "files");
            Directory.CreateDirectory(testFilesPath);

            // Mock IFileService
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(true);
            fileService.OpenRead(Arg.Any<string>()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes("test content")));

            // Create test file
            var filePath = Path.Combine(testFilesPath, uploadLogoRequest.LogoInternalName);
            File.WriteAllText(filePath, "test content");

            // Act
            var result = await _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest);

            // Assert
            Assert.That(result.Result, Is.True);
        }

        [Test]
        public async Task UploadLogoAsync_ShouldLogSuccessfulUpload()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 37", "Test Serial No 37");

            var uploadLogoRequest = _serviceProvider.GetRequiredService<UploadLogoRequestDataObject>();
            uploadLogoRequest.Logo = "test-logo-data";
            uploadLogoRequest.LogoInternalName = "test-logo.png";

            var vehicleIds = new[] { vehicle.Id };

            // Get the mocked IWebHostEnvironment from the service provider
            var webHostEnvironment = _serviceProvider.GetRequiredService<IWebHostEnvironment>();

            // Create test directory structure
            var testWebRootPath = webHostEnvironment.WebRootPath;
            var testFilesPath = Path.Combine(testWebRootPath, "files");
            Directory.CreateDirectory(testFilesPath);

            // Mock IFileService
            var fileService = _serviceProvider.GetRequiredService<IFileService>();
            fileService.FileExists(Arg.Any<string>()).Returns(true);
            fileService.OpenRead(Arg.Any<string>()).Returns(new MemoryStream(System.Text.Encoding.UTF8.GetBytes("test content")));

            // Create test file
            var filePath = Path.Combine(testFilesPath, uploadLogoRequest.LogoInternalName);
            File.WriteAllText(filePath, "test content");

            // Act
            var result = await _vehicleAPI.UploadLogoAsync(vehicleIds, uploadLogoRequest);

            // Assert
            Assert.That(result.Result, Is.True);
            _logger.Received(1).LogInformation(Arg.Is<string>(msg => msg.Contains("Logo uploaded successfully")));
            _logger.Received(1).LogInformation(Arg.Is<string>(msg => msg.Contains($"Logo synced to vehicle {vehicle.Id}")));
        }

        [Test]
        public async Task UpdateFirmwareAsync_WhenValidInput_ShouldUpdateFirmwareAndSyncToVehicles()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 38", "Test Serial No 38");
            var firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
            firmware.Id = Guid.NewGuid();
            firmware.Version = "7.0.0";
            firmware.Url = "https://fleetxqfiles.blob.core.windows.net/devicetwin/firmware/FXQ_7.0.0/FleetIQ360App";
            await _dataFacade.FirmwareDataProvider.SaveAsync(firmware);

            var vehicleIds = new[] { vehicle.Id };
            var firmwareId = firmware.Id;

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = firmwareId;

            // Act
            var result = await _vehicleAPI.UpdateFirmwareAsync(vehicleIds, updateFirmwareRequest);

            // Assert
            Assert.That(result.Result, Is.True);
            
            // Verify vehicle was updated with firmware ID
            var updatedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(updatedVehicle.FirmwareId, Is.EqualTo(firmwareId));
            
            // Verify firmware update was called with correct parameters
            await _deviceTwinHandler.Received(1).UpdateFirmware(
                Arg.Is<string>(deviceId => deviceId == module.IoTDevice),
                Arg.Is<string>(version => version == firmware.Version));
        }

        [Test]
        public void UpdateFirmwareAsync_WhenVehicleIdsIsNull_ShouldThrowArgumentException()
        {
            // Arrange
            var firmwareId = Guid.NewGuid();

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = firmwareId;

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UpdateFirmwareAsync(null, updateFirmwareRequest));
            Assert.That(ex.Message, Does.Contain("vehicleIds cannot be null or empty"));
        }

        [Test]
        public void UpdateFirmwareAsync_WhenVehicleIdsIsEmpty_ShouldThrowArgumentException()
        {
            // Arrange
            var vehicleIds = new Guid[] { };
            var firmwareId = Guid.NewGuid();

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = firmwareId;

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UpdateFirmwareAsync(vehicleIds, updateFirmwareRequest));
            Assert.That(ex.Message, Does.Contain("vehicleIds cannot be null or empty"));
        }

        [Test]
        public void UpdateFirmwareAsync_WhenFirmwareIdIsEmpty_ShouldThrowArgumentException()
        {
            // Arrange
            var vehicleIds = new[] { Guid.NewGuid() };
            var firmwareId = Guid.Empty;

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = firmwareId;

            // Act & Assert
            var ex = Assert.ThrowsAsync<ArgumentException>(() =>
                _vehicleAPI.UpdateFirmwareAsync(vehicleIds, updateFirmwareRequest));
            Assert.That(ex.Message, Does.Contain("firmwareId cannot be empty"));
        }

        [Test]
        public async Task UpdateFirmwareAsync_WhenFirmwareNotFound_ShouldThrowGOServerException()
        {
            // Arrange
            var (vehicle, module) = await CreateTestVehicleAsync("Test Vehicle 39", "Test Serial No 39");
            var vehicleIds = new[] { vehicle.Id };
            var nonExistentFirmwareId = Guid.NewGuid();

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = nonExistentFirmwareId;

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() =>
                _vehicleAPI.UpdateFirmwareAsync(vehicleIds, updateFirmwareRequest));
            Assert.That(ex.Message, Does.Contain($"Firmware with ID {nonExistentFirmwareId} not found"));
        }

        [Test]
        public async Task UpdateFirmwareAsync_WhenVehicleNotFound_ShouldThrowGOServerException()
        {
            // Arrange
            var firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
            firmware.Id = Guid.NewGuid();
            firmware.Version = "8.0.0";
            firmware.Url = "https://fleetxqfiles.blob.core.windows.net/devicetwin/firmware/FXQ_8.0.0/FleetIQ360App";
            await _dataFacade.FirmwareDataProvider.SaveAsync(firmware);

            var nonExistentVehicleId = Guid.NewGuid();
            var vehicleIds = new[] { nonExistentVehicleId };
            var firmwareId = firmware.Id;

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = firmwareId;

            // Act & Assert
            var ex = Assert.ThrowsAsync<GOServerException>(() =>
                _vehicleAPI.UpdateFirmwareAsync(vehicleIds, updateFirmwareRequest));
            Assert.That(ex.Message, Does.Contain($"Unknown vehicle id {nonExistentVehicleId}"));
        }



        [Test]
        public async Task UpdateFirmwareAsync_WhenMultipleVehicles_ShouldUpdateAllVehiclesAndSyncToAllDevices()
        {
            // Arrange
            var (vehicle1, module1) = await CreateTestVehicleAsync("Test Vehicle 41", "Test Serial No 41");
            var (vehicle2, module2) = await CreateTestVehicleAsync("Test Vehicle 42", "Test Serial No 42");
            var (vehicle3, module3) = await CreateTestVehicleAsync("Test Vehicle 43", "Test Serial No 43");

            var firmware = _serviceProvider.GetRequiredService<FirmwareDataObject>();
            firmware.Id = Guid.NewGuid();
            firmware.Version = "10.0.0";
            firmware.Url = "https://fleetxqfiles.blob.core.windows.net/devicetwin/firmware/FXQ_10.0.0/FleetIQ360App";
            await _dataFacade.FirmwareDataProvider.SaveAsync(firmware);

            var vehicleIds = new[] { vehicle1.Id, vehicle2.Id, vehicle3.Id };
            var firmwareId = firmware.Id;

            // Create UpdateFirmwareRequestDataObject
            var updateFirmwareRequest = _serviceProvider.GetRequiredService<UpdateFirmwareRequestDataObject>();
            updateFirmwareRequest.FirmwareId = firmwareId;

            // Act
            var result = await _vehicleAPI.UpdateFirmwareAsync(vehicleIds, updateFirmwareRequest);

            // Assert
            Assert.That(result.Result, Is.True);
            
            // Verify all vehicles were updated with firmware ID
            var updatedVehicle1 = await _dataFacade.VehicleDataProvider.GetAsync(vehicle1);
            var updatedVehicle2 = await _dataFacade.VehicleDataProvider.GetAsync(vehicle2);
            var updatedVehicle3 = await _dataFacade.VehicleDataProvider.GetAsync(vehicle3);
            
            Assert.That(updatedVehicle1.FirmwareId, Is.EqualTo(firmwareId));
            Assert.That(updatedVehicle2.FirmwareId, Is.EqualTo(firmwareId));
            Assert.That(updatedVehicle3.FirmwareId, Is.EqualTo(firmwareId));
            
            // Verify firmware update was called with all device IDs
            await _deviceTwinHandler.Received(3).UpdateFirmware(
                Arg.Any<string>(),
                Arg.Is<string>(version => version == firmware.Version));
            
            // Verify each device was called individually
            await _deviceTwinHandler.Received(1).UpdateFirmware(module1.IoTDevice, firmware.Version);
            await _deviceTwinHandler.Received(1).UpdateFirmware(module2.IoTDevice, firmware.Version);
            await _deviceTwinHandler.Received(1).UpdateFirmware(module3.IoTDevice, firmware.Version);
        }


    }
}