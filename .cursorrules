when asking "end session" please run the following prompt: 

Could you provide a structured summary of our work session today? Please include:

1. CURSOR Session Title: [Relevant title for the session]

2. Task Overview:
   - Main tasks/problems worked on
   - Initial complexity assessment (Low/Medium/High)

3. Time & Value Analysis:
   - Time spent with AI assistance: [X] minutes/hours
   - Estimated traditional development time: [X] minutes/hours
   - Key factors in time savings:
     • [Specific ways AI accelerated the work]
     • [Tools or approaches that were particularly effective]
  - can you tell me what is your confidence level from 0 to  100% on the time estimation. If lower than 80% , what is missing to have more relevant answers ? 

4. Process Details:
   - Context/input data used
   - Solutions and approaches developed
   - Code or documentation improvements

5. Value Delivered:
   - Concrete deliverables produced
   - Quality improvements achieved
   - Unexpected benefits discovered

6. Learning Points:
   - Notable AI techniques used
   - Challenges overcome
   - Areas where AI was particularly effective/ineffective

7. Next Steps:
   - Remaining items
   - Recommendations for similar future tasks

End of the summary triggered by "end session"

when asking "create implementation plan: {task title and task details}" please run the following prompt:

Create a comprehensive implementation plan for the following task: {task title and task details}

Use the structure below and fill in all relevant details. Ensure the output is ready for review and inclusion in the /TechDoc folder as part of the design review process.

---

# Implementation Plan

## 1. Executive Summary

- **Feature Overview**:  
  [Brief summary of what this task aims to implement.]

- **Timeline**:  
  [Expected delivery timeline or sprint scope.]

- **Resource Requirements**:  
  [Who needs to be involved, including roles or specific skills.]

---

## 2. Requirements and Context

- **Functional Requirements**:  
  [Specific functions or behavior the feature must support.]

- **Non-Functional Requirements**:  
  [Performance, security, scalability, reliability, etc.]

- **Business Context**:  
  [Connection to broader product or business objectives.]

- **User Stories**:  
  [Include 1–3 stories with acceptance criteria.]

- **Dependencies**:  
  [Other systems, APIs, teams, or features this relies on.]

---

## 3. Technical Design

> Note: Documentation for this section must be attached to the JIRA ticket and reviewed during the design review process.

- **Architecture Overview**:  
  - Components impacted  
  - System layers affected  
  - Data flow  
  - External integration points

- **Database Design** (if applicable):  
  - New tables or fields  
  - Migrations  
  - Performance concerns

- **API Design** (if applicable):  
  - New/updated endpoints  
  - Data models  
  - Error handling

- **Frontend Implementation** (if applicable):  
  - UI/UX flow  
  - Components affected or added  
  - State management

---

## 4. Implementation Approach

- **Development Strategy**:  
  [Phases, flags, rollout strategy, compatibility concerns.]

- **Code Organization**:  
  [Where the code will live, reusable components, conventions.]

---

## 5. Risk Assessment and Mitigation

- **Technical Risks**:  
  [List possible risks related to performance, integrations, security, etc.]

- **Mitigation Strategies**:  
  [Fallback plans, validations, monitoring, etc.]

---

## 6. Testing and Quality Assurance

- **Testing Strategy**:  
  [Unit, integration, E2E, performance tests.]

- **Quality Gates**:  
  [Required reviewers, coverage levels, documentation updates.]

---

## 7. Deployment and Rollout

- **Deployment Strategy**:  
  [Environment flow, migration steps, toggles.]

- **Monitoring and Success Metrics**:  
  [How success will be measured.]

---

## 8. Project Tracking and Links

- **JIRA Links**:  
  [Link any related tickets or epics.]

- **Pull Request Links**:  
  [Add after code is available.]

- **Design Documents**:  
  [Link to TechDoc PR or file.]

- **Meeting Notes**:  
  [Link to relevant discussion or DR session notes.]

---

## 9. Additional Notes (Optional)

[Any additional thoughts, assumptions, or open questions.]

---

End of the implementation plan triggered by "create implementation plan: {task title and task details}"

when asking "create lightweight implementation plan: {task title and task details}" please run the following prompt:

Create a lightweight implementation plan for the following task: {task title and task details}

This task is expected to be small in scope, low risk, and does not require full design review. Use the simplified format below to provide just enough structure and traceability for internal visibility and documentation.

---

# Lightweight Implementation Plan

## 1. Objective

[Brief statement of what the task is and why it’s being done.]

## 2. Problem or Challenge

[What issue, gap, or improvement is being addressed? Include relevant context.]

## 3. Solution Summary

[A high-level overview of what changes will be made to solve the problem.]

## 4. Files Created or Modified

[List relevant files with short notes. Example:  
- `scripts/fix-build.cmd` – adjusted npm call for error handling  
- `docs/deployment-guide.md` – updated with new environment setup steps]

## 5. Usage Instructions (if applicable)

[Include usage commands or setup steps if this introduces a tool, script, or config.]

## 6. Technical Insights or Notes

[Any quirks, lessons learned, or non-obvious decisions made.]

## 7. Outcome

[What this change will achieve, including any user or system value.]

## 8. Related Links

- JIRA ticket(s):  
- PR(s):  
- Design or doc links (if applicable):  

---

End of the plan triggered by "create lightweight implementation plan: {task title and task details}"

when asking "generate technical documentation: {ticket title and summary}" please run the following prompt:

Based on the following context, generate technical documentation that accurately describes the change implemented. The output should be suitable for inclusion in the `/TechDoc` folder. Please cover functional, architectural, and integration-level implications.

---

## Input

**Ticket Title:** {ticket title and summary}

**Context:**  
Summarize the relevant changes based on the codebase modifications, JIRA task description, and AI prompts/interactions that guided implementation. Use the interaction history, test cases, and architecture references to infer technical design choices.

---

## Output Format

# Technical Documentation – {ticket title or feature name}

## Overview

[What was built, changed, or fixed. Why it was necessary.]

## Scope of Changes

- [ ] Files and components affected  
- [ ] Services, APIs, or modules involved  
- [ ] Frontend/backend/database layers touched

## Behavior Summary

[Describe the new or changed behavior. Include how it behaves under typical scenarios.]

## Architecture Notes

- [Key architecture or design choices]
- [Data flow, integration points, or schema changes]
- [Relevant diagrams if applicable — placeholder for Mermaid]

## Edge Cases & Limitations

[List any known constraints, limitations, or edge conditions.]

## Developer Notes

- Key lessons learned or workarounds used  
- Important notes for future maintainers  

## Related Artifacts

- JIRA Ticket(s):  
- Implementation Plan:  
- Pull Request(s):  
- Design Review Notes (if any):  


## Output
- Create a markdown file at ..\fleetxq\TechDoc\docs\technical-documentations\ using kebab-case naming convention (e.g., 'tech-doc-feature-name-plan.md')
- Automatically update the VuePress navigation by modifying ..\fleetxq\TechDoc\docs\.vuepress\config.js:
  - If '/technical-documentations/': section doesn't exist in the sidebar object, add it after the '/development/' section:
    ```javascript
    '/technical-documentations/': [
        {
            title: 'Technical Documentations',
            children: [
                '',
                'new-techdoc-filename'
            ]
        }
    ]
    ```
  - If the section already exists, add the new file (without .md extension) to the children array
  - Use kebab-case naming for the file reference (e.g., 'techdoc-user-settings-update')
  - Ensure proper JSON syntax and maintain existing formatting patterns

---

End of the documentation triggered by "generate technical documentation: {ticket title and summary}"
