@echo off
echo ========================================
echo Local Pipeline Test for VehicleAccessProcessor
echo ========================================

REM Set variables (similar to pipeline variables)
set buildConfiguration=Release
set outputPath=bin\publish

echo.
echo [Step 1] Checking .NET SDK version...
dotnet --version

echo.
echo [Step 2] Restoring NuGet packages...
dotnet restore VehicleAccessProcessor.csproj
if %errorlevel% neq 0 (
    echo ERROR: NuGet restore failed
    exit /b 1
)

echo.
echo [Step 3] Building Azure Function...
dotnet build VehicleAccessProcessor.csproj --configuration %buildConfiguration% --no-restore
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    exit /b 1
)

echo.
echo [Step 4] Publishing Azure Function...
dotnet publish VehicleAccessProcessor.csproj --configuration %buildConfiguration% --output %outputPath%
if %errorlevel% neq 0 (
    echo ERROR: Publish failed
    exit /b 1
)

echo.
echo [Step 5] Checking published files...
if exist %outputPath% (
    echo ✅ Published files:
    dir %outputPath% /b
) else (
    echo ❌ Output directory not found
    exit /b 1
)

echo.
echo [Step 6] Testing function locally (optional)...
echo You can now test the function locally using:
echo   func start --port 7071
echo   curl http://localhost:7071/api/HealthCheck

echo.
echo ========================================
echo ✅ Local pipeline test completed successfully!
echo ======================================== 