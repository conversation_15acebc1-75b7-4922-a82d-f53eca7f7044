(function () {
    // Custom extension for PersonDetailPagePageController
    FleetXQ.Web.Controllers.PersonDetailPagePageControllerCustom = function (baseController) {
        this.baseController = baseController;

        this.initialize = function() {
            
           // Simplified IsInEditMode that just checks display mode
            FleetXQ.Web.Controllers.IsInEditMode = function() {
                var personFormVM = window?.ApplicationController?.viewModel?.pageController?.PersonFormViewModel;
                return personFormVM?.StatusData?.DisplayMode() === 'edit';
            };
        };
    };
})();
