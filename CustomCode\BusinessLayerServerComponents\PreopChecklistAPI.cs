﻿using System;
using System.Collections.Generic;
using System.Linq;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using FleetXQ.Data.DataObjects.Custom;
using Newtonsoft.Json;
using GenerativeObjects.Practices.ExceptionHandling;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using Microsoft.Extensions.DependencyInjection;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using static FleetXQ.Data.DataObjects.Custom.EmailDetail;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// PreopChecklistAPI Component
	/// Manage Pre Operational Checklist questions and answers 
	/// </summary>
    public partial class PreopChecklistAPI : BaseServerComponent, IPreopChecklistAPI
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IDataFacade _dataFacade;
        private readonly ILoggingService _logger;

        public PreopChecklistAPI(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade, ILoggingService logger) : base(provider, configuration, dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = provider;
            _logger = logger;

        }

        public async System.Threading.Tasks.Task<ComponentResponse<PreOperationalChecklistDataObject>> ActivateQuestionAsync(Guid preOpChecklistId, Dictionary<string, object> parameters = null)
        {
            PreOperationalChecklistDataObject preOpChecklist = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { preOpChecklistId })).SingleOrDefault();

            if (preOpChecklist == null)
            {
                _logger.LogError(new GOServerException("PreOperational Checklist not found."));
                throw new GOServerException("PreOperational Checklist not found.");
            }

            preOpChecklist.Active = true;

            // Get all active questions for the same site checklist, excluding the current question
            var activeChecklists = await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(
                null,
                "SiteChecklistId == @0 AND Active == @1 AND Id != @2",
                new object[] { preOpChecklist.SiteChecklistId.Value, true, preOpChecklist.Id }
            );

            // Find the minimum available order number by checking for gaps in active checklists
            var usedOrders = activeChecklists.Select(q => q.Order).OrderBy(o => o).ToList();
            short minOrder = 1;

            // If there are no active checklists, use 1
            if (!usedOrders.Any())
            {
                preOpChecklist.Order = minOrder;
            }
            else
            {
                // Find the first gap in the sequence
                for (int i = 0; i < usedOrders.Count; i++)
                {
                    if (usedOrders[i] != i + 1)
                    {
                        minOrder = (short)(i + 1);
                        break;
                    }
                    minOrder = (short)(usedOrders[i] + 1);
                }
                preOpChecklist.Order = minOrder;
            }

            preOpChecklist = await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOpChecklist);

            return new ComponentResponse<PreOperationalChecklistDataObject>(preOpChecklist);
        }

        /// <summary>
        /// StoreCheclistDetails Method
        /// Store checklist results and answers 
        /// </summary>
        /// <param name="Message">JSON Message</param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> StoreCheclistDetailsAsync(System.String Message, Dictionary<string, object> parameters = null)
        {
            PayloadDataObject payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);

            PreopPayloadDataObject payloadData = handlePayload(payloadObject.Payload);
            if (payloadData == null)
            {
                _logger.LogError(new GOServerException("Invalid PAYLOAD or OPCKS format."));
                throw new GOServerException("Invalid PAYLOAD or OPCKS format.");
            }

            ChecklistResultDataObject chkResult;
            // Get all checklist results for this session
            try
            {
                var sessionId = Guid.Parse(payloadObject.SessionId);
                var startTimeHex = payloadData.StartTime;

                // First, try to find an existing batch with the same start time
                var results = await _dataFacade.ChecklistResultDataProvider.GetCollectionAsync(
                    null,
                    "SessionId1 == @0",
                    new object[] { sessionId });

                // Find batch by exact UTC time instead
                var incomingTime = DataUtils.HexToUtcTime(startTimeHex);

                var existingBatches = results
                    .Where(r => Math.Abs((r.StartTime - incomingTime).TotalSeconds) < 1) // Match within 1 second
                    .ToList();

                if (existingBatches.Any())
                {
                    // Use the first matching batch
                    chkResult = existingBatches.First();
                    _logger.LogInformation($"Using existing batch ID: {chkResult.Id} for session {payloadObject.SessionId} with start time {incomingTime}");
                }
                else
                {
                    // Create a new batch
                    _logger.LogInformation($"Creating new batch for session {payloadObject.SessionId} with start time {incomingTime}");

                    chkResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
                    chkResult.SessionId1 = sessionId;
                    chkResult.StartTime = DataUtils.HexToUtcTime(startTimeHex);
                    chkResult = await _dataFacade.ChecklistResultDataProvider.SaveAsync(chkResult);
                }

                // Update EndTime if provided and chkResult.EndTime is null
                if (!string.IsNullOrEmpty(payloadData.EndTime) && (chkResult.EndTime == null || chkResult.EndTime == chkResult.StartTime))
                {
                    chkResult.EndTime = DataUtils.HexToUtcTime(payloadData.EndTime);
                    chkResult = await _dataFacade.ChecklistResultDataProvider.SaveAsync(chkResult);
                }

                if (chkResult != null && payloadData.QuestionId != null && !payloadData.QuestionId.Equals("0"))
                {
                    PreOperationalChecklistDataObject question = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, "Id == @0",
                        new object[] { Guid.Parse(payloadData.QuestionId) })).SingleOrDefault();
                    if (question == null)
                    {
                        _logger.LogError(new GOServerException("Invalid Question Id."));
                        throw new GOServerException("Invalid Question Id.");
                    }

                    // Check if a checklist detail already exists for this combination
                    var existingDetail = (await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(
                        null,
                        "ChecklistResultId == @0 AND PreOperationalChecklistId == @1",
                        new object[] { chkResult.Id, question.Id }
                    )).FirstOrDefault();

                    // Only create new record if no existing record found
                    if (existingDetail == null)
                    {
                        ChecklistDetailDataObject checklistDetailDataObject = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
                        checklistDetailDataObject.ChecklistResultId = chkResult.Id;
                        checklistDetailDataObject.PreOperationalChecklistId = question.Id;
                        checklistDetailDataObject.Answer = payloadData.Response.Equals("1");

                        if (question.Critical && question.ExpectedAnswer != payloadData.Response.Equals("1"))
                        {
                            IEmailService emailService = _serviceProvider.GetRequiredService<IEmailService>();
                            EmailDetail emailDetail = new EmailDetail();
                            emailDetail.TimeStamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") + " - UTC";
                            emailDetail.Alert = "Failed Critical Question Lockout Alert";
                            emailDetail.VehicleId = (await chkResult.LoadSessionAsync()).VehicleId;
                            var person = await chkResult.LoadSessionAsync().Result.LoadDriverAsync().Result.LoadPersonAsync();
                            if (person != null)
                            {
                                emailDetail.DriverName = person.FirstName + " " + person.LastName;
                            }
                            AlertDetails alertDetails = new AlertDetails();
                            alertDetails.CriticalQuestion = question.Question;
                            alertDetails.ExpectedAnswer = question.ExpectedAnswer ? "Yes" : "No";
                            alertDetails.Response = payloadData.Response.Equals("1") ? "Yes" : "No";
                            emailDetail.Details = alertDetails;
                            await emailService.SendEmailAsync(JsonConvert.SerializeObject(emailDetail));
                        }
                        await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetailDataObject);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException("Error getting checklist result." + ex.Message));
                throw new GOServerException(ex.Message);
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

        private void CleanupBatchTracker()
        {
            // Method no longer needed but kept to avoid compilation errors
        }

        private string UtcTimeToHex(DateTime time)
        {
            return DataUtils.UtcTimeToHex(time);
        }

        private PreopPayloadDataObject handlePayload(String payloadObj)
        {
            PreopPayloadDataObject payloadData = new();

            string[] parts = payloadObj.Split(' ');

            foreach (var part in parts)
            {
                if (part.StartsWith("OPCKS="))
                {
                    string opcksValue = part.Substring("OPCKS=".Length);
                    string[] opcksValues = opcksValue.Split(',');

                    if (opcksValues.Length == 4)
                    {
                        payloadData.EndTime = opcksValues[0];
                        payloadData.StartTime = opcksValues[1];
                        payloadData.QuestionId = opcksValues[2];
                        payloadData.Response = opcksValues[3];
                    }
                    else
                    {
                        Console.WriteLine("Invalid OPCKS format.");
                    }
                    break; // Assuming there's only one OPCKS entry
                }
                else if (part.StartsWith("AUTH="))
                {
                    string authValue = part.Substring("AUTH=".Length);
                    string[] authValues = authValue.Split(',');

                    if (authValues.Length == 2)
                    {
                        payloadData.CardId = authValues[0];
                        payloadData.ModuleHexTimeStamp = authValues[1];
                    }
                    else
                    {
                        Console.WriteLine("Invalid AUTH format.");
                    }
                }
            }

            return payloadData;
        }
    }
}
