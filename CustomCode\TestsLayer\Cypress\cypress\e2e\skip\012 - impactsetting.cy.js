describe("012 - Impact Settings-Tab", () => {
    // Use beforeEach to ensure login is handled before each test
    beforeEach(() => {
        cy.login(); // Assuming you have already implemented cy.login() globally
    });

    it("tests 012 - Impact Settings Tab", () => {
        // Access Vehicles
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

        // Search for a vehicle
        cy.get("input").first().type("Test");
        cy.get("[data-test-id='searchCommand']").click();
        cy.get("tr:nth-of-type(1) a").click();

        // Intercept the vehicle by ID API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/vehicle/byid/*')
            .as('getVehicleById');


        cy.wait('@getVehicleById', { timeout: 30000 });

        cy.get("[data-test-id='tab_link_4428b832-8680-4ac9-8463-e006f813d708'] > a > span:nth-of-type(1)").click();
        cy.get("[data-test-id='fa575ace-682c-4f85-be2c-8b13abf9f558']").click();
        cy.get("[data-test-id='edit_4c318022-7fa4-45a1-8d13-a1e8c6a17bb4']").type("-38");
        cy.get("[data-test-id='edit_4c318022-7fa4-45a1-8d13-a1e8c6a17bb4']").click();
        cy.get("[data-test-id='edit_56fda4f8-29c5-44e5-9032-7c69fdc96227']").click();
        cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']").click();
        //cy.get("[data-test-id='okButton']").click();
        //cy.get("[data-test-id='edit_4c318022-7fa4-45a1-8d13-a1e8c6a17bb4']").type("-13");
        //cy.get("[data-test-id='edit_4c318022-7fa4-45a1-8d13-a1e8c6a17bb4']").click();
        //cy.get("[data-test-id='edit_56fda4f8-29c5-44e5-9032-7c69fdc96227']").click();
        //cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']").click();
        //cy.get("a.overlayClose > svg").click();
        //cy.get("html > body > #form > [data-test-id='main-page-content'] > div.main-wrapper > div.content-wrapper > div > div > div > div > [data-test-id='contentZone_MainZone'] > [data-test-id='abb67ca7-9416-4ec1-81cf-200c48955f88'] > div > div:nth-of-type(1) > [data-test-id='abb67ca7-9416-4ec1-81cf-200c48955f88'] > #VehicleFormControl-VehicleFormData > #VehicleFormControl-VehicleForm-tabs > div.tabs-container > [data-test-id='\\34 428b832-8680-4ac9-8463-e006f813d708'] > div > div > [data-test-id='\\38 42237dd-4fdb-47cf-8541-23691a2d66e0'] [data-test-id='bee74040-c359-4697-b370-eb4898fb0ad2']").click();
        //cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']").click();
        //cy.get("[data-test-id='okButton']").click();
    });
});
