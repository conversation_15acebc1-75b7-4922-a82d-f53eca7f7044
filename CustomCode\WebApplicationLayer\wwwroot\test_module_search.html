<!DOCTYPE html>
<html>
<head>
    <title>ModuleUtilities Search Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>ModuleUtilities Search Functionality Test</h1>
    
    <div>
        <label for="searchInput">Search Term:</label>
        <input type="text" id="searchInput" placeholder="Enter module search term..." />
        <button onclick="testSearch()">Test Search</button>
        <button onclick="testAllModules()">Get All Modules</button>
    </div>
    
    <div id="results" style="margin-top: 20px;">
        <h3>Results will appear here...</h3>
    </div>
    
    <div id="debug" style="margin-top: 20px; background-color: #f0f0f0; padding: 10px;">
        <h3>Debug Information</h3>
        <div id="debugLog"></div>
    </div>

    <script>
        // Mock FleetXQ application objects for testing
        window.FleetXQ = window.FleetXQ || {};
        FleetXQ.Web = FleetXQ.Web || {};
        FleetXQ.Web.Application = FleetXQ.Web.Application || {};
        FleetXQ.Web.Application.BaseURL = window.location.origin + '/';
        FleetXQ.Web.Application.CSRF_TOKEN = 'test-token';
        FleetXQ.Web.Model = FleetXQ.Web.Model || {};
        FleetXQ.Web.Model.Components = FleetXQ.Web.Model.Components || {};
        FleetXQ.Web.Model.DataStores = FleetXQ.Web.Model.DataStores || {};
        
        // Mock data mapping functions
        FleetXQ.Web.Model.DataStores.MapJSONDataToDataSet = function(dataSet, result, contextId, type) {
            return result || [];
        };

        function logDebug(message) {
            const debugLog = document.getElementById('debugLog');
            debugLog.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
            console.log('[DEBUG]', message);
        }

        function testSearch() {
            const searchTerm = document.getElementById('searchInput').value;
            if (!searchTerm) {
                alert('Please enter a search term');
                return;
            }

            logDebug('Testing search with term: "' + searchTerm + '"');

            // Simulate the configuration object that would be created by the frontend
            const configuration = {
                contextId: 'test-context',
                filterPredicate: 'IoTDevice.Contains(@0)',
                filterParameters: JSON.stringify([{
                    "TypeName": "System.String",
                    "IsNullable": false,
                    "Value": searchTerm
                }]),
                parameters: JSON.stringify({
                    filterPredicate: 'IoTDevice.Contains(@0)',
                    filterParameters: JSON.stringify([{
                        "TypeName": "System.String",
                        "IsNullable": false,
                        "Value": searchTerm
                    }])
                }),
                pageSize: 50,
                pageNumber: 1,
                successHandler: function(data) {
                    logDebug('Search completed successfully');
                    displayResults(data, 'Search Results for "' + searchTerm + '"');
                },
                errorHandler: function(xhr, status, error) {
                    logDebug('Search failed: ' + status + ' - ' + error);
                    displayError('Search failed: ' + error);
                }
            };

            logDebug('Configuration object: ' + JSON.stringify(configuration, null, 2));

            // Test the actual AJAX call that would be made
            testAjaxCall('/dataset/api/moduleutilitiesapi/getavailablemodules', configuration, searchTerm);
        }

        function testAllModules() {
            logDebug('Testing get all modules (no search)');

            const configuration = {
                contextId: 'test-context',
                filterPredicate: "",
                successHandler: function(data) {
                    logDebug('Get all modules completed successfully');
                    displayResults(data, 'All Available Modules');
                },
                errorHandler: function(xhr, status, error) {
                    logDebug('Get all modules failed: ' + status + ' - ' + error);
                    displayError('Get all modules failed: ' + error);
                }
            };

            logDebug('Configuration object: ' + JSON.stringify(configuration, null, 2));

            // Test the actual AJAX call that would be made
            testAjaxCall('/dataset/api/moduleutilitiesapi/getavailablemodules', configuration, null);
        }

        function testAjaxCall(url, configuration, searchTerm) {
            const requestData = {
                dateformat: "ISO8601"
            };

            // Add search parameters if provided
            if (configuration.filterPredicate) {
                requestData.filterPredicate = configuration.filterPredicate;
                logDebug('Adding filterPredicate: ' + configuration.filterPredicate);
            }

            if (configuration.filterParameters) {
                requestData.filterParameters = configuration.filterParameters;
                logDebug('Adding filterParameters: ' + configuration.filterParameters);
            }

            if (configuration.parameters) {
                requestData.parameters = configuration.parameters;
                logDebug('Adding parameters: ' + configuration.parameters);
            }

            if (configuration.pageSize) {
                requestData.pageSize = configuration.pageSize;
            }

            if (configuration.pageNumber) {
                requestData.pageNumber = configuration.pageNumber;
            }

            logDebug('Making AJAX call to: ' + url);
            logDebug('Request data: ' + JSON.stringify(requestData, null, 2));

            $.ajax({
                url: url,
                dataType: "json",
                type: "POST",
                headers: {
                    'X-CSRF-TOKEN': FleetXQ.Web.Application.CSRF_TOKEN,
                },
                data: requestData,
                success: function (result) {
                    logDebug('AJAX call successful');
                    logDebug('Response: ' + JSON.stringify(result, null, 2));
                    if (configuration.successHandler) {
                        configuration.successHandler(result);
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    logDebug('AJAX call failed: ' + textStatus + ' - ' + errorThrown);
                    logDebug('Response status: ' + jqXHR.status);
                    logDebug('Response text: ' + jqXHR.responseText);
                    if (configuration.errorHandler) {
                        configuration.errorHandler(jqXHR, textStatus, errorThrown);
                    }
                }
            });
        }

        function displayResults(data, title) {
            const resultsDiv = document.getElementById('results');
            let html = '<h3>' + title + '</h3>';
            
            if (data && data.length > 0) {
                html += '<p>Found ' + data.length + ' modules:</p>';
                html += '<ul>';
                for (let i = 0; i < Math.min(data.length, 10); i++) {
                    const module = data[i];
                    html += '<li>' + (module.IoTDevice || 'N/A') + ' - ' + (module.Status || 'Unknown') + '</li>';
                }
                if (data.length > 10) {
                    html += '<li>... and ' + (data.length - 10) + ' more</li>';
                }
                html += '</ul>';
            } else {
                html += '<p>No modules found</p>';
            }
            
            resultsDiv.innerHTML = html;
        }

        function displayError(message) {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h3 style="color: red;">Error</h3><p>' + message + '</p>';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            logDebug('ModuleUtilities Search Test page loaded');
            logDebug('Base URL: ' + FleetXQ.Web.Application.BaseURL);
        });
    </script>
</body>
</html>