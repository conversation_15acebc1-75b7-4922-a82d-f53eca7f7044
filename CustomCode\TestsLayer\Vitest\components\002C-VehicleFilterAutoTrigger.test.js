import { describe, it, expect, vi, beforeEach } from 'vitest';

import '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehicleFilterViewModel.custom';

describe('VehicleFilterViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Mock the view model
        viewModel = {
            contextId: 'test-context',
            CustomerContextId: 'test-context',
            commands: {
                searchCommand: vi.fn()
            },
            getCustomerCollectionData: vi.fn(),
            filterData: {
                fields: {
                    CustomerValue: vi.fn(),
                    SiteValue: vi.fn()
                }
            }
        };

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn()
                }
            }
        };

        // Create the view model instance
        customViewModel = new FleetXQ.Web.ViewModels.Filters.VehicleFilterViewModelCustom(viewModel);
    });

    describe('initialize', () => {
        it('should not trigger filter for non-customer users', () => {
            // Mock non-customer user
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: ['Admin'],
                CustomerId: null
            });

            // Mock setTimeout to execute immediately
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = (fn) => fn();

            customViewModel.initialize();

            // Verify customer collection data was loaded
            expect(viewModel.getCustomerCollectionData).toHaveBeenCalled();

            // Verify search command was triggered
            expect(viewModel.commands.searchCommand).toHaveBeenCalled();

            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });

        it('should trigger filter for customer users', () => {
            // Mock customer user
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: ['Customer'],
                CustomerId: '123'
            });

            // Mock setTimeout to execute immediately
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = (fn) => fn();

            customViewModel.initialize();

            // Verify customer collection data was loaded
            expect(viewModel.getCustomerCollectionData).toHaveBeenCalled();

            // Verify search command was triggered
            expect(viewModel.commands.searchCommand).toHaveBeenCalled();

            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });
    });
});