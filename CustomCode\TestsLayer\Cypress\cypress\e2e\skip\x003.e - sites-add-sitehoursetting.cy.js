describe("003 - Sites Flow (Record Hour Setting)", () => {

    beforeEach(() => {
        // Perform the login using the login command
        cy.login();

        // Intercept the API request for the timezone list
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/timezone/list*').as('getTimezoneList');
    });

    it("tests Sites-Record Hour Setting", () => {
        // Check and ensure the navigation menu is visible before clicking
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(2) span")
            .should('exist')
            .should('be.visible')
            .click();

        // Ensure the first row link is visible and click
        cy.get("tr:nth-of-type(1) > td:nth-of-type(1) > a")
            .should('exist')
            .should('be.visible')
            .click();

        // Click on the specific tab
        cy.get("[data-test-id='tab_link_941e8b48-2441-4501-b057-2597f5143ad2'] > a > span:nth-of-type(1)")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the first row to open the site details
        cy.get("[data-test-id='\\39 41e8b48-2441-4501-b057-2597f5143ad2'] tr:nth-of-type(1) > td:nth-of-type(1)")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the edit button for the site
        cy.get("[data-test-id='d878e927-dec4-4268-8448-e40773bad533']")
            .should('exist')
            .should('be.visible')
            .click();

        // Click to open the "Record Hour Settings"
        cy.get("[data-test-id='view_b3556c4a-ff6d-46df-8f27-dfa393be184c']")
            .should('exist')
            .should('be.visible')
            .click();

        // Click the "Modify" button before making changes
        cy.get("[data-test-id='1b691d6f-877a-4d06-9ed9-5b92263b74b6']")
            .should('exist')
            .should('be.visible')
            .click();

        // Wait for the timezone list API to finish before interacting with the dropdown
        cy.wait('@getTimezoneList').then((interception) => {
            cy.log('Timezone list loaded', interception);

            // Interact with the dropdown for hour selection
            cy.get("#popupContainer [data-test-id='lookup_input']")
                .should('exist')
                .should('be.visible')
                .click();

            // Get the currently selected item and select a different one
            cy.get("li > [data-test-id='lookup_item']")
                .should('have.length.greaterThan', 1)  // Ensure there are multiple items
                .then((items) => {
                    // Extract the text of the current selection
                    cy.get("[data-test-id='lookup_input']").invoke('text').then((currentSelected) => {
                        // Find and click the first item that is not the current selection
                        const filteredItems = Cypress.$(items).filter((index, item) => {
                            return Cypress.$(item).text().trim() !== currentSelected.trim();
                        });
                        cy.wrap(filteredItems).first().click(); // Select the first non-matching item
                    });
                });
        });

        // Click the "Save" button to save the changes
        cy.get("[data-test-id='2a2a3b0d-97a5-4ad5-a331-895f91d9cd3d']")
            .should('exist')
            .should('be.visible')
            .click();
    });
});
