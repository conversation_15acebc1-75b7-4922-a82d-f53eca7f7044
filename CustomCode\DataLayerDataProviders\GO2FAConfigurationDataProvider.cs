﻿using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class GO2FAConfigurationDataProvider : IDataProvider<GO2FAConfigurationDataObject>
    {
        public Task<int> CountAsync(LambdaExpression securityFilterExpression = null, string filterPredicate = null, object[] filterArguments = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException();
        }

        public Task DeleteAsync(GO2FAConfigurationDataObject entity, LambdaExpression securityFilterExpression = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException();
        }

        public Task<GO2FAConfigurationDataObject> GetAsync(GO2FAConfigurationDataObject entity, LambdaExpression securityFilterExpression = null, List<string> includes = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException();
        }

        public Task<DataObjectCollection<GO2FAConfigurationDataObject>> GetCollectionAsync(LambdaExpression securityFilterExpression = null, string filterPredicate = null, object[] filterArguments = null, string orderByPredicate = null, int pageNumber = 0, int pageSize = 0, List<string> includes = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException();
        }

        public Task<GO2FAConfigurationDataObject> SaveAsync(GO2FAConfigurationDataObject entity, LambdaExpression securityFilterExpression = null, List<string> includes = null, IObjectsDataSet context = null, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException();
        }

        public Task<GO2FAConfigurationDataObject> SaveOutOfTransactionAsync(GO2FAConfigurationDataObject entity, Dictionary<string, object> parameters = null, bool skipSecurity = false)
        {
            throw new NotImplementedException();
        }
    }
}
