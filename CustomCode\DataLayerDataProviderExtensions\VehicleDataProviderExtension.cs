﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Collections.Generic;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using System.Globalization;
using Microsoft.IdentityModel.Tokens;
using FleetXQ.Feature.Security.Common;



namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class VehicleDataProviderExtension : IDataProviderExtension<VehicleDataObject>
    {

        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IFirmwareUpdate _firmwareUpdate;
        private readonly IVehicleAPI _vehicleAPI;
        private readonly IVehicleAccessQueueService _vehicleAccessQueueService;

        private readonly IDeviceTwinHandler _deviceTwinHandler;
        private readonly IAuthentication _authentication;
        private bool _firmwareVersionUpdated = false;

        // New class-level variables to track department and site changes
        private Guid? _oldDepartmentId;
        private Guid? _oldSiteId;

        public VehicleDataProviderExtension(IServiceProvider serviceProvider, IAuthentication authentication, IDataFacade dataFacade, IFirmwareUpdate firmwareUpdate, IDeviceTwinHandler deviceTwinHandler, IVehicleAPI vehicleAPI, IVehicleAccessQueueService vehicleAccessQueueService)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _firmwareUpdate = firmwareUpdate;
            _deviceTwinHandler = deviceTwinHandler;
            _vehicleAPI = vehicleAPI;
            _authentication = authentication;
            _vehicleAccessQueueService = vehicleAccessQueueService;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSetAsync;
            dataProvider.OnAfterSave += DataProvider_OnAfterSave;
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSetAsync;
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
            dataProvider.OnBeforeGet += DataProvider_OnBeforeGet;
        }

        private async Task DataProvider_OnBeforeGet(OnBeforeGetEventArgs arg)
        {
            if (arg.Entity is VehicleDataObject vehicle && arg.Includes.Contains("VehicleDiagnostic"))
            {
                await _vehicleAPI.UpdateVehicleDiagnosticAsync(vehicle.Id);
            }
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            // Only run this code if the result is a collection of VehicleDataObject
            if (arg.Result is DataObjectCollection<VehicleDataObject> vehicles && arg.PageNumber > 0 && arg.PageSize > 0)
            {
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();

                if (userClaims == null || userClaims.UserId == null)
                {
                    return;
                }

                var appUserClaims = userClaims as AppUserClaims;

                var preferredLocale = appUserClaims.UserPreferredLocale != null ? appUserClaims.UserPreferredLocale : appUserClaims.CustomerPreferredLocale;

                foreach (var vehicle in vehicles)
                {
                    // Check if SimCardDate has a value before using it
                    if (vehicle.Module != null && vehicle.Module.SimCardDate.HasValue && vehicle.Module.SimCardDate.Value.AddYears(5) < DateTime.UtcNow)
                    {
                        vehicle.Module.IsSimCardExpired = true;
                    }

                    try
                    {
                        var culture = !string.IsNullOrEmpty(preferredLocale) ? new CultureInfo(preferredLocale) : new CultureInfo("en-US");

                        if (vehicle.LastSessionDateTzAdjusted.HasValue)
                        {
                            vehicle.LastSessionDateTzAdjustedDisplay = vehicle.LastSessionDateTzAdjusted.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss", culture);
                        }

                        var gpsLastLocation = await vehicle.LoadVehicleLastGPSLocationViewAsync();
                        if (gpsLastLocation == null)
                        {
                            continue;
                        }

                        vehicle.GPSDateTimeDisplay = gpsLastLocation.GPSDateTime.ToString($"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss", culture);
                    }
                    catch (CultureNotFoundException)
                    {
                        // If the culture is invalid, just return without modifying the datetime
                        return;
                    }
                }
            }

            await Task.CompletedTask;
        }

        private async Task OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
        {
            var vehicle = e.Entity as VehicleDataObject;
            if (vehicle.IsNew)
            {
                return;
            }
            var oldVehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            oldVehicle.Id = vehicle.Id;
            oldVehicle = await _dataFacade.VehicleDataProvider.GetAsync(oldVehicle);

            // Store old values if there's a change
            if (vehicle.SiteId != oldVehicle.SiteId || vehicle.DepartmentId != oldVehicle.DepartmentId || vehicle.ModelId != oldVehicle.ModelId)
            {
                _oldDepartmentId = oldVehicle.DepartmentId;
                _oldSiteId = oldVehicle.SiteId;

                // If department or site changed, reassign the department checklist
                await AssignDepartmentChecklistAsync(vehicle);
            }

            var firmware = await vehicle.LoadFirmwareAsync();
            var oldFirmware = await oldVehicle.LoadFirmwareAsync();

            // set _firmwareVersionUpdated to true if the entity is not new and firmware is not null and the firmware version is different
            if (firmware != null && oldFirmware != null && firmware.Version != oldFirmware.Version)
            {
                _firmwareVersionUpdated = true;
                return;
            }

            // set _firmwareVersionUpdated to true if oldFirmware is null and firmware is not null
            if (oldFirmware == null && firmware != null)
            {
                _firmwareVersionUpdated = true;
                return;
            }

            return;
        }

        private async Task DataProvider_OnAfterSave(OnAfterSaveEventArgs arg)
        {
            var vehicle = arg.Entity as VehicleDataObject;
            if (vehicle == null)
            {
                return;
            }

            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            if (module == null)
            {
                return;
            }

            var customer = await vehicle.LoadCustomerAsync();

            if (customer != null)
            {
                var customerModels = await customer.LoadCustomerModelItemsAsync();

                if (customerModels == null || !customerModels.Any(cm => cm.CustomerId == vehicle.CustomerId && cm.ModelId == vehicle.ModelId))
                {
                    var customerModel = _serviceProvider.GetRequiredService<CustomerModelDataObject>();
                    customerModel.ModelId = vehicle.ModelId;
                    customerModel.CustomerId = vehicle.CustomerId;
                    customerModel.Polarity = PolarityEnum.ActiveHigh;

                    await _dataFacade.CustomerModelDataProvider.SaveAsync(customerModel, skipSecurity: true);
                }
            }

            var firmware = await vehicle.LoadFirmwareAsync();

            if (firmware != null && _firmwareVersionUpdated)
            {
                await _firmwareUpdate.SendFirmwareUpdateToDevicesAsync(new[] { module.IoTDevice }, firmware.Version);
            }
        }

        private async Task OnAfterSaveDataSetAsync(OnAfterSaveDataSetEventArgs e)
        {
            if (!e.EntityBeforeSave.IsNew)
            {
                var vehicle = e.EntityRefetched as VehicleDataObject;

                if (vehicle != null)
                {
                    // Check if we need to update vehicle access due to department change
                    if (_oldDepartmentId.HasValue || _oldSiteId.HasValue)
                    {
                        await UpdateVehicleAccessForDepartmentChangeAsync(vehicle);
                        // Clear the stored values after processing
                        _oldDepartmentId = null;
                        _oldSiteId = null;
                    }

                    var module = await vehicle.LoadModuleAsync(skipSecurity: true);

                    if (module == null)
                    {
                        return;
                    }

                    // sync all setting
                    await _deviceTwinHandler.SyncGeneralSettings(module.IoTDevice);
                }

                return;
            }

            if (e.EntityBeforeSave.IsNew)
            {
                var vehicle = e.EntityRefetched as VehicleDataObject;
                var module = await vehicle.LoadModuleAsync(skipSecurity: true);

                // Send message to queue for asynchronous vehicle access creation
                var accessCreationMessage = new VehicleAccessCreationMessage
                {
                    VehicleId = vehicle.Id,
                    CustomerId = vehicle.CustomerId,
                    ModelId = vehicle.ModelId,
                    DepartmentId = vehicle.DepartmentId,
                    SiteId = vehicle.SiteId,
                    IsNewVehicle = true,
                    IoTDevice = module?.IoTDevice
                };

                // create a default Checklist Setting for Vehicle with Driver Base as option if vehicle.ChecklistSettingsId is null
                if (vehicle.ChecklistSettingsId == null)
                {
                    var newCheckListSetting = _serviceProvider.GetRequiredService<ChecklistSettingsDataObject>();
                    newCheckListSetting.Type = Type1Enum.DriverBase;
                    // default 5 mins time out for questions
                    newCheckListSetting.QuestionTimeout = 300;
                    // save checklist setting
                    var savedChecklistSetting = await _dataFacade.ChecklistSettingsDataProvider.SaveAsync(newCheckListSetting, skipSecurity: true);
                    vehicle.ChecklistSettingsId = savedChecklistSetting.Id;
                }
                // set Hire Time of the vehicle to the current time
                vehicle.HireTime = DateTime.UtcNow;

                // if module.DealerId is null, set it to the dealerId of the vehicle and set module.Status to Active
                if (module.DealerId == null)
                {
                    module.DealerId = (await vehicle.LoadCustomerAsync()).DealerId;
                }

                // set module.Status to Assigned
                module.Status = ModuleStatusEnum.Assigned;

                // save vehicle
                await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);

                var firmware = await vehicle.LoadFirmwareAsync();

                if (firmware != null)
                {
                    await _firmwareUpdate.SendFirmwareUpdateToDevicesAsync(new[] { module.IoTDevice }, firmware.Version);
                }

                var vehicleDiagnostic = await vehicle.LoadVehicleDiagnosticAsync();
                var vehicleUpdated = false;

                if (vehicleDiagnostic == null)
                {
                    vehicleDiagnostic = _serviceProvider.GetRequiredService<VehicleDiagnosticDataObject>();
                    vehicleDiagnostic.VehicleId = vehicle.Id;
                    vehicleDiagnostic = await _dataFacade.VehicleDiagnosticDataProvider.SaveAsync(vehicleDiagnostic, skipSecurity: true);

                    vehicle.VehicleDiagnostic = vehicleDiagnostic;

                    vehicleUpdated = true;
                }

                if (vehicle.DepartmentChecklistId == null)
                {
                    await AssignDepartmentChecklistAsync(vehicle);
                    vehicleUpdated = true;
                }

                if (vehicle.VehicleOtherSettingsId == null)
                {
                    var vehicleOtherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>();
                    vehicleOtherSettings.VORStatusConfirmed = true;

                    vehicleOtherSettings = await _dataFacade.VehicleOtherSettingsDataProvider.SaveAsync(vehicleOtherSettings, skipSecurity: true);

                    vehicle.VehicleOtherSettingsId = vehicleOtherSettings.Id;
                    vehicle.VehicleOtherSettings = vehicleOtherSettings;

                    vehicleUpdated = true;
                }

                if (vehicleUpdated)
                {
                    vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                }
                // send message to queue for asynchronous vehicle settings sync
                await _vehicleAccessQueueService.SendVehicleAccessCreationMessageAsync(accessCreationMessage);

                // Note: Device sync will be triggered after access creation is completed by the queue processor
            }
        }

        // Helper method to assign appropriate department checklist
        private async Task AssignDepartmentChecklistAsync(VehicleDataObject vehicle)
        {
            var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null,
                "DepartmentId == @0 and ModelId == @1",
                new object[] { vehicle.DepartmentId, vehicle.ModelId })).FirstOrDefault();

            if (departmentChecklist == null)
            {
                // No department checklist: create it 
                departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                departmentChecklist.DepartmentId = vehicle.DepartmentId;
                departmentChecklist.ModelId = vehicle.ModelId;
                departmentChecklist.Id = Guid.NewGuid();
                departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);
            }

            // Assign the department checklist to the vehicle
            vehicle.DepartmentChecklistId = departmentChecklist.Id;
            vehicle.DepartmentChecklist = departmentChecklist;
        }

        // Helper method to update vehicle access when department changes using queue-based approach
        private async Task UpdateVehicleAccessForDepartmentChangeAsync(VehicleDataObject vehicle)
        {
            // Send message to queue for asynchronous vehicle access update processing
            var accessCreationMessage = new VehicleAccessCreationMessage
            {
                VehicleId = vehicle.Id,
                CustomerId = vehicle.CustomerId,
                ModelId = vehicle.ModelId,
                DepartmentId = vehicle.DepartmentId,
                SiteId = vehicle.SiteId,
                IsNewVehicle = false,
                IsDepartmentChange = true,
                OldDepartmentId = _oldDepartmentId,
                OldSiteId = _oldSiteId,
                IoTDevice = (await vehicle.LoadModuleAsync(skipSecurity: true))?.IoTDevice
            };

            await _vehicleAccessQueueService.SendVehicleAccessCreationMessageAsync(accessCreationMessage);
        }
    }
}