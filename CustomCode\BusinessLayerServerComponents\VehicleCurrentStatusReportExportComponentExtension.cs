﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom;

public class VehicleCurrentStatusReportExportComponentExtension : IImportExportComponentExtension<VehicleCurrentStatusReportExportSection0Component,
        CurrentStatusVehicleViewDataObject>
{
    private readonly IDataFacade dataFacade;
    private readonly IAuthentication authentication;

    public VehicleCurrentStatusReportExportComponentExtension(IDataFacade dataFacade, IAuthentication authentication)
    {
        this.dataFacade = dataFacade;
        this.authentication = authentication;
    }

    public void Init(IImportExportComponent<CurrentStatusVehicleViewDataObject> importExportComponent)
    {
        importExportComponent.OnAfterExportDataRowAsync += ImportExportComponent_OnAfterExportDataRowAsync;
    }

    private async Task ImportExportComponent_OnAfterExportDataRowAsync(OnAfterExportDataRowEventArgs<CurrentStatusVehicleViewDataObject> arg)
    {
        if (!arg.Entity.LastReportedTime.HasValue)
        {
            return;
        }
        
        var userClaims = await authentication.GetCurrentUserClaimsAsync();

        var callingUser = await dataFacade.GOUserDataProvider.GetAsync(new GOUserDataObject(userClaims.UserId.Value), includes: new List<string> { "Person", "Person.Customer" });

        var preferredLocale = callingUser.PreferredLocale != null ? callingUser.PreferredLocale.Value : callingUser.Person?.Customer?.PreferredLocale;

        if (!preferredLocale.HasValue)
        {
            return;
        }

        // Validate if the locale is valid
        try
        {
            var culture = new CultureInfo(DataUtils.GetLocaleString(preferredLocale.Value));

            // Format the datetime using the preferred locale
            arg.DataRow[VehicleCurrentStatusReportExportSection0Component.COL_LASTREPORTEDTIME] =
                arg.Entity.LastReportedTime.Value.ToString(culture);
        }
        catch (CultureNotFoundException)
        {
            // If the culture is invalid, just return without modifying the datetime
            return;
        }
    }
}
