using System.Net;
using System.Text;
using LocaleTranslator.Models;
using LocaleTranslator.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;

namespace LocaleTranslator.Tests;

[TestFixture]
public class IntegrationTests
{
    private Mock<HttpMessageHandler> _mockHttpHandler;
    private Mock<ILogger<ClaudeTranslationService>> _mockTranslationLogger;
    private Mock<ILogger<FileProcessingService>> _mockFileProcessingLogger;
    private ServiceProvider _serviceProvider;
    private string _testDataPath;

    [SetUp]
    public void Setup()
    {
        _mockHttpHandler = new Mock<HttpMessageHandler>();
        _mockTranslationLogger = new Mock<ILogger<ClaudeTranslationService>>();
        _mockFileProcessingLogger = new Mock<ILogger<FileProcessingService>>();

        // Use real configuration for robust testing
        var configData = new Dictionary<string, string>
        {
            ["Translation:CopyInsteadOfTranslate:0"] = "config.json",
            ["Translation:CopyInsteadOfTranslate:1"] = "metadata.json"
        };
        var config = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

        // Setup DI container
        var services = new ServiceCollection();

        // Add HttpClient with mocked handler
        services.AddHttpClient<ClaudeTranslationService>()
            .ConfigurePrimaryHttpMessageHandler(() => _mockHttpHandler.Object);

        // Add services
        services.AddSingleton<FileProcessingService>(provider =>
            new FileProcessingService(
                provider.GetService<ClaudeTranslationService>(),
                _mockFileProcessingLogger.Object
            ));

        _serviceProvider = services.BuildServiceProvider();
        _testDataPath = Path.Combine(TestContext.CurrentContext.TestDirectory, "TestData");
        Directory.CreateDirectory(_testDataPath);
    }

    [TearDown]
    public void TearDown()
    {
        _serviceProvider?.Dispose();

        // Clean up test files
        if (Directory.Exists(_testDataPath))
        {
            Directory.Delete(_testDataPath, true);
        }
    }

    [Test]
    public async Task CompleteTranslationWorkflow_WithValidFiles_ShouldSucceed()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataPath, "source");
        var targetDir = Path.Combine(_testDataPath, "target");
        Directory.CreateDirectory(sourceDir);

        // Create source files
        var file1Content = "{\"common\": {\"save\": \"Save\", \"cancel\": \"Cancel\"}}";
        var file2Content = "{\"vehicle\": {\"title\": \"Vehicle Management\", \"addVehicle\": \"Add Vehicle\"}}";

        await File.WriteAllTextAsync(Path.Combine(sourceDir, "common.json"), file1Content);
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "vehicle.json"), file2Content);

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French",
            ClaudeApiKey = "test-key",
            DryRun = false,
            MaxConcurrency = 2,
            DelayBetweenRequests = 0
        };

        // Setup mock responses
        SetupMockTranslationResponseWithCallback(requestContent =>
        {
            if (requestContent.Contains("save") || requestContent.Contains("cancel"))
            {
                return "{\"common\": {\"save\": \"Enregistrer\", \"cancel\": \"Annuler\"}}";
            }
            else if (requestContent.Contains("title") || requestContent.Contains("addVehicle"))
            {
                return "{\"vehicle\": {\"title\": \"Gestion des Véhicules\", \"addVehicle\": \"Ajouter un Véhicule\"}}";
            }
            else
            {
                return "{\"default\": \"default\"}";
            }
        });

        var fileProcessingService = _serviceProvider.GetService<FileProcessingService>()!;

        // Act
        var result = await fileProcessingService.ProcessFilesAsync(options);

        // Assert
        Assert.That(result.TotalFiles, Is.EqualTo(2));
        Assert.That(result.SuccessfulFiles, Is.EqualTo(2));
        Assert.That(result.FailedFiles, Is.EqualTo(0));
        Assert.That(result.SkippedFiles, Is.EqualTo(0));

        // Verify target files were created
        var targetFile1 = Path.Combine(targetDir, "common.json");
        var targetFile2 = Path.Combine(targetDir, "vehicle.json");

        Assert.That(File.Exists(targetFile1), Is.True);
        Assert.That(File.Exists(targetFile2), Is.True);

        var translatedContent1 = await File.ReadAllTextAsync(targetFile1);
        var translatedContent2 = await File.ReadAllTextAsync(targetFile2);

        Assert.That(translatedContent1, Does.Contain("Enregistrer"));
        Assert.That(translatedContent1, Does.Contain("Annuler"));
        Assert.That(translatedContent2, Does.Contain("Gestion des Véhicules"));
        Assert.That(translatedContent2, Does.Contain("Ajouter un Véhicule"));
    }

    [Test]
    public async Task CompleteTranslationWorkflow_WithMixedFileTypes_ShouldHandleCorrectly()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataPath, "source");
        var targetDir = Path.Combine(_testDataPath, "target");
        Directory.CreateDirectory(sourceDir);

        // Create different types of files
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "common.json"), "{\"hello\": \"world\"}");
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "config.json"), "{\"config\": \"value\"}"); // Should be copied
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "large.json"), CreateLargeJsonFile());

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French",
            ClaudeApiKey = "test-key",
            DryRun = false,
            MaxConcurrency = 2,
            DelayBetweenRequests = 0
        };

        // Setup mock responses for translatable files
        SetupMockTranslationResponseWithCallback(requestContent =>
        {
            if (requestContent.Contains("hello"))
            {
                return "{\"hello\": \"bonjour\"}";
            }
            else if (requestContent.Contains("section1"))
            {
                return "{\"section1\": {\"title\": \"Section 1 Title\"}}";
            }
            else
            {
                return "{\"default\": \"default\"}";
            }
        });

        var fileProcessingService = _serviceProvider.GetService<FileProcessingService>()!;

        // Act
        var result = await fileProcessingService.ProcessFilesAsync(options);

        // Assert
        Assert.That(result.TotalFiles, Is.EqualTo(3));
        Assert.That(result.SuccessfulFiles, Is.EqualTo(3));
        Assert.That(result.FailedFiles, Is.EqualTo(0));

        // Verify config.json was copied (not translated)
        var configFile = Path.Combine(targetDir, "config.json");
        Assert.That(File.Exists(configFile), Is.True);
        var configContent = await File.ReadAllTextAsync(configFile);
        Assert.That(configContent, Is.EqualTo("{\"config\": \"value\"}"));

        // Verify other files were translated
        var commonFile = Path.Combine(targetDir, "common.json");
        Assert.That(File.Exists(commonFile), Is.True);
        var commonContent = await File.ReadAllTextAsync(commonFile);
        Assert.That(commonContent, Does.Contain("bonjour"));
    }

    [Test]
    public async Task CompleteTranslationWorkflow_WithTranslationFailures_ShouldHandleGracefully()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataPath, "source");
        var targetDir = Path.Combine(_testDataPath, "target");
        Directory.CreateDirectory(sourceDir);

        await File.WriteAllTextAsync(Path.Combine(sourceDir, "file1.json"), "{\"hello\": \"world\"}");
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "file2.json"), "{\"goodbye\": \"world\"}");

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French",
            ClaudeApiKey = "test-key",
            DryRun = false,
            MaxConcurrency = 2,
            DelayBetweenRequests = 0
        };

        // Setup mixed responses - one success, one failure
        _mockHttpHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Returns<HttpRequestMessage, CancellationToken>(async (request, token) =>
            {
                var requestContent = await request.Content!.ReadAsStringAsync();

                if (requestContent.Contains("hello"))
                {
                    var response = new ClaudeResponse
                    {
                        Content = new List<ClaudeContent>
                        {
                            new() { Type = "text", Text = "{\"hello\": \"bonjour\"}" }
                        },
                        Usage = new ClaudeUsage { InputTokens = 100, OutputTokens = 50 }
                    };

                    return new HttpResponseMessage(HttpStatusCode.OK)
                    {
                        Content = new StringContent(JsonConvert.SerializeObject(response), Encoding.UTF8, "application/json")
                    };
                }
                else
                {
                    // Return error for other requests
                    return new HttpResponseMessage(HttpStatusCode.TooManyRequests)
                    {
                        Content = new StringContent("Rate limit exceeded", Encoding.UTF8, "text/plain")
                    };
                }
            });

        var fileProcessingService = _serviceProvider.GetService<FileProcessingService>()!;

        // Act
        var result = await fileProcessingService.ProcessFilesAsync(options);

        // Assert
        Assert.That(result.TotalFiles, Is.EqualTo(2));
        Assert.That(result.SuccessfulFiles, Is.EqualTo(1));
        Assert.That(result.FailedFiles, Is.EqualTo(1));

        // Verify successful translation
        var successFile = Path.Combine(targetDir, "file1.json");
        Assert.That(File.Exists(successFile), Is.True);
        var successContent = await File.ReadAllTextAsync(successFile);
        Assert.That(successContent, Does.Contain("bonjour"));

        // Verify failed translation was not created
        var failedFile = Path.Combine(targetDir, "file2.json");
        Assert.That(File.Exists(failedFile), Is.False);
    }

    [Test]
    public async Task CompleteTranslationWorkflow_WithDryRun_ShouldNotCreateFiles()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataPath, "source");
        var targetDir = Path.Combine(_testDataPath, "target");
        Directory.CreateDirectory(sourceDir);

        await File.WriteAllTextAsync(Path.Combine(sourceDir, "test.json"), "{\"hello\": \"world\"}");

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French",
            ClaudeApiKey = "test-key",
            DryRun = true,
            MaxConcurrency = 1,
            DelayBetweenRequests = 0
        };

        var fileProcessingService = _serviceProvider.GetService<FileProcessingService>()!;

        // Act
        var result = await fileProcessingService.ProcessFilesAsync(options);

        // Assert
        Assert.That(result.TotalFiles, Is.EqualTo(1));
        Assert.That(result.SuccessfulFiles, Is.EqualTo(1));
        Assert.That(result.FailedFiles, Is.EqualTo(0));

        // Verify no files were created
        Assert.That(Directory.Exists(targetDir), Is.False);

        // Verify translation service was not called
        _mockHttpHandler.Protected().Verify(
            "SendAsync",
            Times.Never(),
            ItExpr.IsAny<HttpRequestMessage>(),
            ItExpr.IsAny<CancellationToken>()
        );
    }

    [Test]
    public async Task CompleteTranslationWorkflow_WithStatusCheck_ShouldReturnCorrectStatus()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataPath, "source");
        var targetDir = Path.Combine(_testDataPath, "target");
        Directory.CreateDirectory(sourceDir);
        Directory.CreateDirectory(targetDir);

        // Create source files
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "common.json"), "{\"save\": \"Save\", \"cancel\": \"Cancel\"}");
        await File.WriteAllTextAsync(Path.Combine(sourceDir, "vehicle.json"), "{\"title\": \"Vehicle Management\"}");

        // Create target files (one missing, one with different keys)
        await File.WriteAllTextAsync(Path.Combine(targetDir, "common.json"), "{\"save\": \"Enregistrer\"}");
        // vehicle.json is missing

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French",
            ClaudeApiKey = "test-key"
        };

        var fileProcessingService = _serviceProvider.GetService<FileProcessingService>()!;

        // Act
        var status = await fileProcessingService.CheckTranslationStatusAsync(options);

        // Assert
        Assert.That(status.TotalSourceFiles, Is.EqualTo(2));
        Assert.That(status.TotalTargetFiles, Is.EqualTo(1));
        Assert.That(status.MissingFiles, Has.Count.EqualTo(1));
        Assert.That(status.MissingFiles.First(), Is.EqualTo("vehicle.json"));
        Assert.That(status.ExtraFiles, Has.Count.EqualTo(0));
        Assert.That(status.FilesWithKeyDifferences, Has.Count.EqualTo(1));
        Assert.That(status.TotalMissingKeys, Is.EqualTo(1)); // "cancel" key is missing
        Assert.That(status.TotalExtraKeys, Is.EqualTo(0));
    }

    [Test]
    public async Task CompleteTranslationWorkflow_WithConcurrencyControl_ShouldRespectLimits()
    {
        // Arrange
        var sourceDir = Path.Combine(_testDataPath, "source");
        var targetDir = Path.Combine(_testDataPath, "target");
        Directory.CreateDirectory(sourceDir);

        // Create multiple files
        for (int i = 1; i <= 5; i++)
        {
            await File.WriteAllTextAsync(Path.Combine(sourceDir, $"file{i}.json"), $"{{\"key{i}\": \"value{i}\"}}");
        }

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French",
            ClaudeApiKey = "test-key",
            DryRun = false,
            MaxConcurrency = 2, // Limit to 2 concurrent operations
            DelayBetweenRequests = 100 // Add delay between requests
        };

        // Setup mock responses with callback to handle different requests
        SetupMockTranslationResponsesForMultipleFiles();

        var fileProcessingService = _serviceProvider.GetService<FileProcessingService>()!;

        // Act
        var result = await fileProcessingService.ProcessFilesAsync(options);

        // Assert
        Assert.That(result.TotalFiles, Is.EqualTo(5));
        Assert.That(result.SuccessfulFiles, Is.EqualTo(5));
        Assert.That(result.FailedFiles, Is.EqualTo(0));

        // Verify all files were processed
        for (int i = 1; i <= 5; i++)
        {
            var targetFile = Path.Combine(targetDir, $"file{i}.json");
            Assert.That(File.Exists(targetFile), Is.True);
            var content = await File.ReadAllTextAsync(targetFile);
            Assert.That(content, Does.Contain($"valeur{i}"));
        }
    }

    private void SetupMockTranslationResponse(string translatedContent)
    {
        var response = new ClaudeResponse
        {
            Content = new List<ClaudeContent>
            {
                new() { Type = "text", Text = translatedContent }
            },
            Usage = new ClaudeUsage { InputTokens = 100, OutputTokens = 50 }
        };

        _mockHttpHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(JsonConvert.SerializeObject(response), Encoding.UTF8, "application/json")
            });
    }

    private void SetupMockTranslationResponsesForMultipleFiles()
    {
        // Create a dictionary to map request content to response content
        var responseMap = new Dictionary<string, string>();
        for (int i = 1; i <= 5; i++)
        {
            responseMap[$"key{i}"] = $"{{\"key{i}\": \"valeur{i}\"}}";
        }

        _mockHttpHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Returns<HttpRequestMessage, CancellationToken>(async (request, token) =>
            {
                // Read the request content to determine which response to return
                var requestContent = await request.Content!.ReadAsStringAsync();

                // Find the matching response based on the key in the request
                string translatedContent = "{\"default\": \"default\"}";
                foreach (var kvp in responseMap)
                {
                    if (requestContent.Contains(kvp.Key))
                    {
                        translatedContent = kvp.Value;
                        break;
                    }
                }

                var response = new ClaudeResponse
                {
                    Content = new List<ClaudeContent>
                    {
                        new() { Type = "text", Text = translatedContent }
                    },
                    Usage = new ClaudeUsage { InputTokens = 100, OutputTokens = 50 }
                };

                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent(JsonConvert.SerializeObject(response), Encoding.UTF8, "application/json")
                };
            });
    }

    private void SetupMockTranslationResponseWithCallback(Func<string, string> responseSelector)
    {
        _mockHttpHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .Returns<HttpRequestMessage, CancellationToken>(async (request, token) =>
            {
                // Read the request content
                var requestContent = await request.Content!.ReadAsStringAsync();

                // Use the callback to determine the response
                var translatedContent = responseSelector(requestContent);

                var response = new ClaudeResponse
                {
                    Content = new List<ClaudeContent>
                    {
                        new() { Type = "text", Text = translatedContent }
                    },
                    Usage = new ClaudeUsage { InputTokens = 100, OutputTokens = 50 }
                };

                return new HttpResponseMessage(HttpStatusCode.OK)
                {
                    Content = new StringContent(JsonConvert.SerializeObject(response), Encoding.UTF8, "application/json")
                };
            });
    }

    private void SetupMockHttpError(HttpStatusCode statusCode, string errorMessage)
    {
        _mockHttpHandler
            .Protected()
            .Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage(statusCode)
            {
                Content = new StringContent(errorMessage, Encoding.UTF8, "text/plain")
            });
    }

    private string CreateLargeJsonFile()
    {
        var data = new Dictionary<string, object>();
        for (int i = 1; i <= 20; i++)
        {
            data[$"section{i}"] = new Dictionary<string, object>
            {
                ["title"] = $"Section {i} Title",
                ["description"] = $"This is a detailed description for section {i} that contains a lot of text to make the file larger and more realistic for testing purposes."
            };
        }
        return JsonConvert.SerializeObject(data, Formatting.Indented);
    }
}