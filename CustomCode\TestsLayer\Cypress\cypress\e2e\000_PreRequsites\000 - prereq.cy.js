/*
Use this file to create a customer, site and department with the name "Cypress Test"
Where all the data that has delete restriction will be handled by this set of data

This file will should be run first before any other test file
*/

describe("000 - Prerequisites", () => {
    let cypressCompanyName, cypressFirstName, cypressLastName, uniqueSiteName, uniqueDepartmentName;

    before(() => {
        // Load test data from fixture
        cy.fixture('testData').then((testData) => {
            cypressCompanyName = testData.cypressCompanyName;
            cypressFirstName = testData.cypressFirstName;
            cypressLastName = testData.cypressLastName;
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            uniqueDepartmentName = testData.uniqueDepartmentName;
        });
    });

    beforeEach(() => {
        // Use the centralized login function from the support file
        cy.login();

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/dealer/list*').as('getDealerList');

        // Intercept the API call for country list before interacting with the second dropdown
        cy.intercept('/dataset/api/country/list*').as('getCountryList');

        // Wait for the page to load by checking a key element
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(2) span").should('exist').should('be.visible');
    });

    it("Should create a customer,site,department with Cypress company name", () => {
        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company else if not found continue and create
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(cypressCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // CHECK IF THE COMPANY IS FOUND OR NOT
        cy.get('body').then($body => {
            // Check if the no-data-message exists and is visible
            if ($body.find('.no-data-message > span:visible').length > 0) {
                // No customer data found, create a new one
                cy.get('.no-data-message > span')
                    .should('exist')
                    .should('be.visible')
                    .and('contain', 'No Customer data available');

                // Create a new company
                cy.get('.topGridCommands > :nth-child(1) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();

                // **Wait for the API call to complete before interacting with the dropdown**
                cy.wait('@getDealerList').then((interception) => {
                    cy.log('Dealer list API completed:', interception);
                });
                        // Select items from the first lookup dropdown
                cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsDealerVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper`)
                    .should('exist')
                    .should('be.visible')
                    .click();

                // Select the first item from the dropdown
                cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']")
                    .should('exist')
                    .should('be.visible')
                    .click();

                // **Wait for the country list API before interacting with the second dropdown**
                        cy.wait('@getCountryList').then((interception) => {
                            cy.log('Country list API completed:', interception);
                        });
                        // Interact with the second lookup dropdown
                        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsCountryVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper`)
                    .should('exist')
                    .should('be.visible')
                    .click();

                // Select the first item from the second dropdown
                cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']")
                    .should('exist')
                    .should('be.visible')
                    .eq(1)
                    .click();

                // Fill in the fields for the customer creation form
                cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsAddessVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('exist')
                    .should('be.visible')
                    .type("Test Address");

                cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsCompanyNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('exist')
                    .should('be.visible')
                            .type(cypressCompanyName);

                cy.get('.save')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // Search for the company
                cy.get('.filterTextInputCustom')
                    .should('exist')
                    .should('be.visible')
                    .clear()
                    .type(cypressCompanyName);

                cy.wait(1000);

                cy.get('.filterTextInputCustom').type('{enter}');
                cy.wait(1000);

                cy.get('td[data-bind="jqStopBubble: \'a\'"]')
                    .should('exist')
                    .should('be.visible')
                    .first()  // Select the first matching element if multiple exist
                    .click();

                cy.get(':nth-child(2) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();
            } else {
                // Customer data found, select the customer
                cy.get('td[data-bind="jqStopBubble: \'a\'"]')
                    .should('exist')
                    .should('be.visible')
                    .first()  // Select the first matching element if multiple exist
                    .click();

                cy.get(':nth-child(2) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();
            }
        });

        // Create a new site
        cy.get('[data-id="CustomerFormControl-CustomerForm-tabs-2"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // Check if the site already exists
        cy.get('body').then($body => {
            const noSiteMessage = $body.find('#CustomerFormControl-CustomerForm-SitesGrid > [data-bind="css: { hideElt : false }"] > :nth-child(3) > .no-data-message > span');
            
            if (noSiteMessage.is(':visible') && noSiteMessage.text().includes('No Site data')) {  
                // Click create button for new site
                cy.get('#CustomerFormControl-CustomerForm-SitesGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(1) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);
                cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('exist')
                    .should('be.visible')
                    .clear()
                    .type(uniqueSiteName);
        
                cy.get('.create-new-form-timezone-input-custom > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input')
                    .should('exist')
                    .should('be.visible')
                    .click();
        
                // select the first item from the dropdown
                cy.get('html > body > [data-test-id="lookup_wrapper"] > li:nth-of-type(1) > [data-test-id="lookup_item"]')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.get('.save')
                    .should('exist')
                    .should('be.visible')
                    .click();
                
                cy.wait(1000);

                // Verify the site was created
                cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
                    .should('exist')
                    .should('be.visible')
                    .should('contain', uniqueSiteName);
                    
                // OPEN SITE
                cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
                .should('exist')
                .should('be.visible')
                .first()
                .click();
                cy.wait(1000);
                cy.get(':nth-child(2) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();
                cy.wait(1000);
            } else {
                // Sites already exist, click on an existing site
                cy.get('#-SitesGridViewModel-grid-widget-SiteGrid1- > .data-grid-container > .data-grid > .model-tbody-custom > .pointer > [data-bind=" safeHtml: Data.Name"]')
                .should('exist')
                .should('be.visible')
                .first()
                .click();
                cy.wait(1000);
                cy.get(':nth-child(2) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();
                cy.wait(1000);
            }
        });

        // Create a new department
        // CHECK IF THE DEPARTMENT ALREADY EXISTS
        cy.get('body').then($body => {
            if( $body.find('.no-data-message > span:visible').length > 0) {
                cy.get(`#SiteForm1-SiteFormForm-DepartmentItemsGrid > [data-bind="css: { hideElt : false }"] > .gridCommandContainer > .d-flex > .gridCommands > :nth-child(1) > .command-button`)
                .should('exist')
                .should('be.visible')
                .click();
        
                cy.wait(1000);
        
                cy.get(`#DepartmentCreateNewFormData > :nth-child(1) > .basicForm > .edit > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                .should('exist')
                .should('be.visible')
                .clear()
                .type(uniqueDepartmentName);
                
                cy.get('.save')
                .should('exist')
                .should('be.visible')
                .click();
        
                cy.wait(1000);
                
                // Verify the department is added to the table
                cy.get(`[data-bind=" safeHtml: Data.Name, jqStopBubble: 'a'"]`)
                .should('exist')
                .should('be.visible')
                .should('contain', uniqueDepartmentName);
                
                // Log the unique department name for debugging purposes
                cy.log(`Unique Department Name created: ${uniqueDepartmentName}`);
            } else {
                cy.log('Department already exists, skipping department creation');
            }
        });
    });
});
