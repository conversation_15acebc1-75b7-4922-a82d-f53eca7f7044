import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehicleBroadcastMessageFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/VehicleBroadcastMessage/VehicleBroadcastMessageFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create mock broadcast message object
        const mockBroadcastMessage = {
            getBroadcastMessage: vi.fn().mockReturnValue({ id: 'test-message' })
        };

        // Create base view model with required properties
        viewModel = {
            subscriptions: [],
            CurrentObject: ko.observable(mockBroadcastMessage),
            SelectVehiclesForBroadcastMessageGridGridViewModel: {
                broadcastMessage: null,
                customerId: ko.observable(),
                siteId: ko.observable(),
                departmentId: ko.observable(),
                Events: {
                    CollectionLoaded: ko.observable(false),
                    CollectionSorted: ko.observable(false)
                },
                selectedVehicles: null,
                updateCheckStates: vi.fn()
            },
            popupCaller: {
                customerId: ko.observable('customer1'),
                siteId: ko.observable('site1'),
                departmentId: ko.observable('department1'),
                VehicleBroadcastMessageObjectCollection: []
            },
            closePopup: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.VehicleBroadcastMessageFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    it('should initialize with correct subscriptions', () => {
        expect(viewModel.subscriptions.length).toBe(3);
    });

    it('should update grid properties when CurrentObject changes', () => {
        // Trigger CurrentObject subscription
        const newBroadcastMessage = {
            getBroadcastMessage: vi.fn().mockReturnValue({ id: 'new-message' })
        };
        viewModel.CurrentObject(newBroadcastMessage);

        // Verify grid properties were updated
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.broadcastMessage).toEqual({ id: 'new-message' });
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.customerId()).toBe('customer1');
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.siteId()).toBe('site1');
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.departmentId()).toBe('department1');
    });

    it('should update selected vehicles when collection is loaded', () => {
        // Setup mock selected vehicles
        const mockSelectedVehicles = [
            { id: 'vehicle1' },
            { id: 'vehicle2' }
        ];
        viewModel.popupCaller.VehicleBroadcastMessageObjectCollection = mockSelectedVehicles;

        // Trigger CollectionLoaded event
        viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.Events.CollectionLoaded(!viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.Events.CollectionLoaded());

        // Verify selected vehicles were updated
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.selectedVehicles).toBe(mockSelectedVehicles);
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.updateCheckStates).toHaveBeenCalled();
    });

    it('should update check states when collection is sorted', () => {
        // Trigger CollectionSorted event
        viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.Events.CollectionSorted(!viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.Events.CollectionSorted());

        // Verify updateCheckStates was called
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.updateCheckStates).toHaveBeenCalled();
    });

    it('should handle onBeforeSave correctly', () => {
        // Call onBeforeSave
        const result = customViewModel.onBeforeSave();

        // Verify popup was closed and false was returned
        expect(viewModel.closePopup).toHaveBeenCalledWith(true);
        expect(result).toBe(false);
    });

    it('should clean up subscriptions when viewmodel is released', () => {
        // Mock subscription dispose functions
        viewModel.subscriptions.forEach(subscription => {
            subscription.dispose = vi.fn();
        });

        // Check if viewmodel has a release function that cleans up subscriptions
        if (customViewModel.release) {
            customViewModel.release();
            viewModel.subscriptions.forEach(subscription => {
                expect(subscription.dispose).toHaveBeenCalled();
            });
        }
    });

    it('should handle null values in popupCaller properties', () => {
        // Set popupCaller properties to null
        viewModel.popupCaller.customerId(null);
        viewModel.popupCaller.siteId(null);
        viewModel.popupCaller.departmentId(null);

        // Trigger CurrentObject subscription
        viewModel.CurrentObject(viewModel.CurrentObject());

        // Verify grid properties handle null values
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.customerId()).toBeNull();
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.siteId()).toBeNull();
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.departmentId()).toBeNull();
    });

    it('should handle empty VehicleBroadcastMessageObjectCollection', () => {
        // Set empty collection
        viewModel.popupCaller.VehicleBroadcastMessageObjectCollection = [];

        // Trigger CollectionLoaded event
        viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.Events.CollectionLoaded(!viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.Events.CollectionLoaded());

        // Verify updateCheckStates is still called with empty collection
        expect(viewModel.SelectVehiclesForBroadcastMessageGridGridViewModel.updateCheckStates).toHaveBeenCalled();
    });
}); 