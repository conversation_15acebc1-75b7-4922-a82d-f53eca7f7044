describe("006.c - Vehicle Service Hours Intervals Test", () => {
    beforeEach(() => {
        // Prevent uncaught exceptions from failing tests
        Cypress.on('uncaught:exception', (err, runnable) => {
            console.log('Uncaught exception:', err.message);
            return false;
        });
        cy.login();
    });

    it("verifies service hours intervals dropdown functionality", () => {
        // Wait after login
        cy.wait(5000);

        // Navigate to Vehicles
        cy.get("[data-test-id='3fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']")
            .first()
            .click({ force: true });

        // Wait for vehicle list to load
        cy.wait(2000);

        // Click on the first vehicle link
        cy.get('tr.pointer a[href*="#!/Vehicle/"]')
            .first()
            .click({ force: true });

        // Wait after clicking vehicle link
        cy.wait(5000);

        // Navigate to SERVICE tab
        cy.get("[data-test-id='tab_link_1c3a90d5-6f63-449e-ae2d-bcae55b5778e']")
            .first()
            .click({ force: true });

        // Wait for page to load
        cy.wait(2000);

        // Click Modify button
        cy.get("[data-test-id='fa575ace-682c-4f85-be2c-8b13abf9f558']")
            .first()
            .click({ force: true });

        // Wait for form to load
        cy.wait(2000);

        // Verify Service Hours Intervals dropdown
        const dropdownSelector = "[data-test-id='edit_932ef885-c3ab-49a2-aeb1-f0eb2cd6e5ee']";

        cy.get(dropdownSelector)
            .should('be.visible')
            .should('not.be.disabled')
            .first()
            .then($select => {
                // Get all options
                cy.wrap($select)
                    .find('option')
                    .then($options => {
                        // Log all options for debugging
                        const options = $options.map((i, el) => ({
                            value: el.value,
                            text: el.text
                        })).get();
                        cy.log('Available options:', options);

                        // Verify the required options exist
                        const expectedOptions = [
                            { value: '', text: '-' },
                            { value: '250', text: '250 hours' },
                            { value: '500', text: '500 hours' },
                            { value: '1000', text: '1000 hours' }
                        ];

                        expectedOptions.forEach(expected => {
                            const found = $options.filter((i, el) =>
                                el.value === expected.value &&
                                el.text.trim() === expected.text
                            );
                            expect(found.length).to.equal(1,
                                `Option with value "${expected.value}" and text "${expected.text}" should exist`
                            );
                        });
                    });
            });
    });
}); 