<!--
// This is Custom Code - Per Normal Access Filter
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter "Person to per vehicle normal access view Filter" Internal name : "PersonToPerVehicleNormalAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToPerVehicleNormalAccessViewFilter"
        data-test-id="1730190a-3719-4d87-bbf3-a2595141c89c">
        <form
            data-bind="submit: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.commands.searchCommand">
            <div class="uiSearchContainer" style="margin-top: 8px;">
                <div class="filterFieldSetContent">
                    <div class="row g-2 align-items-end">
                        <!-- Hire No Field -->
                        <div class="col-auto"
                            data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.statusData.isHireNoVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter:filterFields.HireNo.displayName'">Hire
                                        No</span>
                                </label>
                                <input type="text" class="form-control form-control-sm"
                                    style="min-width: 150px; padding-left: 12px;"
                                    data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.filterData.fields.HireNo, enable: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="4ee436ee-ffb4-4ec2-b238-65127d45f271" />
                            </div>
                        </div>

                        <!-- Has Access Field -->
                        <div class="col-auto"
                            data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.statusData.isHasAccessVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToPerVehicleNormalAccessView/filters/PersonToPerVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">Has
                                        access</span>
                                </label>
                                <select class="form-control form-control-sm"
                                    style="min-width: 120px; padding-left: 12px;"
                                    data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.HasAccessValues, enable: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"></select>
                            </div>
                        </div>

                        <!-- Search Buttons -->
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="submit" class="btn btn-primary btn-sm"
                                    data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.commands.searchCommand, i18n: 'buttons.search', enable: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="searchCommand">SEARCH</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.commands.clearCommand, i18n: 'buttons.clear', enable: PersonVehicleAccessFormFormViewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="clearCommand">CLEAR</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter "Person to per vehicle normal access view Filter" Internal name : "PersonToPerVehicleNormalAccessViewFilter"-->