﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.Commands;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// PersonFilter Component
	///  
	/// </summary>
	public interface IPersonFilter : IModelBase 
    {
		/// <summary>
		/// Clear Method
		///  
      /// </summary>
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> ClearAsync(Dictionary<string, object> parameters = null);
		
		/// <summary>
		/// Search Method
		///  
      /// </summary>
		System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> SearchAsync(Dictionary<string, object> parameters = null);
		
	}
}
