import { describe, it, expect, vi, beforeEach } from 'vitest';

describe('MachineUnlockReportPageControllerCustom', () => {
    let controller;
    let customController;
    let applicationControllerMock;
    let viewModelMock;
    let filterFormViewModel;
    let gridViewModel;

    beforeEach(() => {
        // Create mocks
        filterFormViewModel = {
            CurrentObject: vi.fn().mockReturnValue({
                Data: {
                    CustomerId: vi.fn()
                }
            }),
            filterData: vi.fn()
        };

        gridViewModel = {
            LoadAllVehicleUnlocksViewObjectCollection: vi.fn(),
            FILTER_NAME: 'machineUnlockFilter',
            exportFilterPredicate: '',
            exportFilterParameters: ''
        };

        controller = {
            MachineUnlockReportFilterFormViewModel: filterFormViewModel,
            AllVehicleUnlocksViewGridViewModel: gridViewModel,
            ShowError: vi.fn()
        };

        viewModelMock = {
            security: {
                currentUserClaims: vi.fn().mockReturnValue({
                    role: '',
                    CustomerId: null,
                    AllowedSiteIds: '{}'
                })
            }
        };

        applicationControllerMock = {
            viewModel: viewModelMock
        };

        controller.applicationController = applicationControllerMock;

        // Create the global object
        global.GO = {
            Filter: {
                hasUrlFilter: vi.fn().mockReturnValue(false)
            }
        };

        // Create the FleetXQ namespace
        global.FleetXQ = {
            Web: {
                Controllers: {}
            }
        };

        // Import the custom controller
        require('../../../WebApplicationLayer/wwwroot/Controllers/MachineUnlockReportPageController.custom.js');

        // Create an instance of the custom controller
        customController = new FleetXQ.Web.Controllers.MachineUnlockReportPageControllerCustom(controller);
        customController.initialize();
    });

    describe('DealerAdmin validation', () => {
        it('should show error when no customer is selected for DealerAdmin', () => {
            // Set user role to DealerAdmin
            viewModelMock.security.currentUserClaims.mockReturnValue({
                role: 'DealerAdmin',
                CustomerId: null,
                AllowedSiteIds: '{}'
            });

            // Set customer ID to null
            filterFormViewModel.CurrentObject().Data.CustomerId.mockReturnValue(null);

            // Call filterData function
            controller.MachineUnlockReportFilterFormViewModel.filterData();

            // Verify error is shown
            expect(controller.ShowError).toHaveBeenCalledWith('Please select a customer');
            expect(filterFormViewModel.filterData).not.toHaveBeenCalled();
        });

        it('should allow filtering when customer is selected for DealerAdmin', () => {
            // Set user role to DealerAdmin
            viewModelMock.security.currentUserClaims.mockReturnValue({
                role: 'DealerAdmin',
                CustomerId: null,
                AllowedSiteIds: '{}'
            });

            // Set customer ID to a valid ID
            filterFormViewModel.CurrentObject().Data.CustomerId.mockReturnValue('some-customer-id');

            // Call filterData function
            controller.MachineUnlockReportFilterFormViewModel.filterData();

            // Verify error is not shown and original filter function is called
            expect(controller.ShowError).not.toHaveBeenCalled();
            expect(filterFormViewModel.filterData).toHaveBeenCalled();
        });

        it('should not require customer selection for non-DealerAdmin roles', () => {
            // Set user role to something other than DealerAdmin
            viewModelMock.security.currentUserClaims.mockReturnValue({
                role: 'Administrator',
                CustomerId: null,
                AllowedSiteIds: '{}'
            });

            // Set customer ID to null
            filterFormViewModel.CurrentObject().Data.CustomerId.mockReturnValue(null);

            // Call filterData function
            controller.MachineUnlockReportFilterFormViewModel.filterData();

            // Verify error is not shown and original filter function is called
            expect(controller.ShowError).not.toHaveBeenCalled();
            expect(filterFormViewModel.filterData).toHaveBeenCalled();
        });
    });

    describe('Configuration generation', () => {
        it('should add customer filter when customerId is available', () => {
            // Set customerId
            const customerId = '12345678-1234-1234-1234-123456789012';
            viewModelMock.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                CustomerId: customerId,
                AllowedSiteIds: '{}'
            });

            // Get configuration
            const config = customController.getDefaultConfiguration();

            // Verify customer filter is added
            expect(config.filterPredicate).toContain('CustomerId == @0');
            expect(config.filterParameters).toContain(customerId);
        });

        it('should add site filter when siteId is available', () => {
            // Set siteId
            const siteId = '87654321-4321-4321-4321-210987654321';
            viewModelMock.security.currentUserClaims.mockReturnValue({
                role: 'SiteAdmin',
                CustomerId: null,
                AllowedSiteIds: `{${siteId}}`
            });

            // Get configuration
            const config = customController.getDefaultConfiguration();

            // Verify site filter is added
            expect(config.filterPredicate).toContain('SiteId == @0');
            expect(config.filterParameters).toContain(siteId);
        });

        it('should add both filters when customerId and siteId are available', () => {
            // Set customerId and siteId
            const customerId = '12345678-1234-1234-1234-123456789012';
            const siteId = '87654321-4321-4321-4321-210987654321';
            viewModelMock.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                CustomerId: customerId,
                AllowedSiteIds: `{${siteId}}`
            });

            // Get configuration
            const config = customController.getDefaultConfiguration();

            // Verify both filters are added
            expect(config.filterPredicate).toContain('CustomerId == @0');
            expect(config.filterPredicate).toContain('SiteId == @1');
            expect(config.filterParameters).toContain(customerId);
            expect(config.filterParameters).toContain(siteId);
        });
    });
}); 