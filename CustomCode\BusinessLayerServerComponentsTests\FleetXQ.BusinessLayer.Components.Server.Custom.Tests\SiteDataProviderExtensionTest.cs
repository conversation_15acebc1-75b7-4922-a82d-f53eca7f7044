using DocumentFormat.OpenXml.Office2010.Excel;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class SiteDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"SiteDataProviderExtensionTest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<SiteDataObject> CreateTestSiteAsync(bool isDeleted = false)
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            if (isDeleted)
            {
                site.DeletedAtUtc = DateTime.UtcNow;
            }
            return await _dataFacade.SiteDataProvider.SaveAsync(site);
        }

        [Test]
        public async Task OnBeforeGetCollection_ExcludesDeletedSites()
        {
            // Arrange
            var activeSite = await CreateTestSiteAsync(isDeleted: false);
            var deletedSite = await CreateTestSiteAsync(isDeleted: true);

            // Act
            var sites = await _dataFacade.SiteDataProvider.GetCollectionAsync(
                null, "CustomerId == @0", new object[] { activeSite.CustomerId });

            // Assert
            Assert.That(sites.Count(), Is.EqualTo(1), "Should only return active sites");
            Assert.That(sites.First().Id, Is.EqualTo(activeSite.Id), "Should return the active site");
        }

        [Test]
        public async Task OnBeforeGetCollection_CombinesWithExistingFilter()
        {
            // Arrange
            var site1 = await CreateTestSiteAsync(isDeleted: false);
            site1.Name = "Test Site 1";
            await _dataFacade.SiteDataProvider.SaveAsync(site1);

            var site2 = await CreateTestSiteAsync(isDeleted: false);
            site2.Name = "Test Site 2";
            await _dataFacade.SiteDataProvider.SaveAsync(site2);

            var deletedSite = await CreateTestSiteAsync(isDeleted: true);
            deletedSite.Name = "Test Site 1";
            await _dataFacade.SiteDataProvider.SaveAsync(deletedSite);

            // Act
            var sites = await _dataFacade.SiteDataProvider.GetCollectionAsync(
                null, "CustomerId == @0 && Name == @1", new object[] { site1.CustomerId, "Test Site 1" });

            // Assert
            Assert.That(sites.Count(), Is.EqualTo(1), "Should only return active sites matching the filter");
            Assert.That(sites.First().Id, Is.EqualTo(site1.Id), "Should return the active site with matching name");
        }
    }
}