<!--
// This is Custom Code - Site Normal Access Filter
// Override of the generated filter to use correct binding context
-->
<!--BEGIN MasterFilter "Master Filter Layout" Filter "Person to site vehicle normal access view Filter" Internal name : "PersonToSiteVehicleNormalAccessViewFilter"-->
<div>
    <div id="{VIEWNAME}-Filter" class="PersonToSiteVehicleNormalAccessViewFilter"
        data-test-id="1730190a-3719-4d87-bbf3-a2595141c89c">
        <form
            data-bind="submit: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.commands.searchCommand">
            <div class="uiSearchContainer" style="margin-top: 8px;">
                <div class="filterFieldSetContent">
                    <div class="row g-2 align-items-end">
                        <!-- Site Name Field -->
                        <div class="col-auto"
                            data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.statusData.isSiteNameVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToSiteVehicleNormalAccessView/filters/PersonToSiteVehicleNormalAccessViewFilter:filterFields.SiteName.displayName'">Site</span>
                                </label>
                                <input type="text" class="form-control form-control-sm"
                                    style="min-width: 150px; padding-left: 12px;"
                                    data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.filterData.fields.SiteName, enable: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="3a3b3aae-379f-4852-874b-3f0b214f2300" />
                            </div>
                        </div>

                        <!-- Has Access Field -->
                        <div class="col-auto"
                            data-bind="visible: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.statusData.isHasAccessVisible">
                            <div class="d-flex align-items-center">
                                <label class="form-label me-2 mb-0" style="white-space: nowrap;">
                                    <span
                                        data-bind="i18n: 'entities/PersonToSiteVehicleNormalAccessView/filters/PersonToSiteVehicleNormalAccessViewFilter:filterFields.HasAccess.displayName'">Has
                                        access</span>
                                </label>
                                <select class="form-control form-control-sm"
                                    style="min-width: 120px; padding-left: 12px;"
                                    data-bind="value: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.filterData.fields.HasAccessValue, optionsText: 'text', options: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.HasAccessValues, enable: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"></select>
                            </div>
                        </div>

                        <!-- Search Buttons -->
                        <div class="col-auto">
                            <div class="btn-group" role="group">
                                <button type="submit" class="btn btn-primary btn-sm"
                                    data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.commands.searchCommand, i18n: 'buttons.search', enable: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="searchCommand">SEARCH</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    data-bind="click: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.commands.clearCommand, i18n: 'buttons.clear', enable: PersonVehicleAccessFormFormViewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.IsFilterEnabled"
                                    data-test-id="clearCommand">CLEAR</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<!--END MasterFilter "Master Filter Layout" Filter "Person to site vehicle normal access view Filter" Internal name : "PersonToSiteVehicleNormalAccessViewFilter"-->