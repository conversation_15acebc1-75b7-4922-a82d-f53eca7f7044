﻿using DocumentFormat.OpenXml.Drawing.Charts;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using NHibernate.Engine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class SessionDataProviderExtension : IDataProviderExtension<SessionDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;

        public SessionDataProviderExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSaveDataSet += OnAfterSaveDataSetAsync;
        }

        private async Task OnAfterSaveDataSetAsync(OnAfterSaveDataSetEventArgs e)
        {
            if (!e.EntityBeforeSave.IsNew) return;
            // onafter save of Session : Get all the last session for the vehicle of the session being saved. Then Get vehicle information and compare the LastSessionDate stored in vehicle with  the real last session date. If different : update the LastSessionDate on vehicle
            var session = e.EntityRefetched as SessionDataObject;
            var vehicle = await session.LoadVehicleAsync();
            var site = await vehicle.LoadSiteAsync();
            var timezone = (await site.LoadTimezoneAsync()).UTCOffset;
            // get last session from Vechile.LastSessionDate, if it is null, then insert this Session.StartTime
            var lastSession = vehicle.LastSessionDate;

            var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync();

            if (otherSettings == null)
            {
                otherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>();
            }

            otherSettings.VORStatus = session.isVOR == true;
            await _dataFacade.VehicleOtherSettingsDataProvider.SaveAsync(otherSettings);

            // Also update VORStatus of Vehicle
            vehicle.LastSessionId = session.Id.ToString();
            if (lastSession == null)
            {
                vehicle.DriverId = session.DriverId;
                vehicle.LastSessionDate = session.StartTime;
                // adjust LastSessionDate by timezone of the vehicle and save 
                vehicle.LastSessionDateTzAdjusted = vehicle.LastSessionDate.Value.AddHours(timezone);
                await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            }
            else
            {
                vehicle.DriverId = null;
                // compare lastSession with Session.StartTime, if lastSession < Session.StartTime, then update Vehicle.LastSessionDate with Session.StartTime
                if (lastSession < session.StartTime)
                {
                    vehicle.LastSessionDate = session.StartTime;
                    vehicle.LastSessionDateTzAdjusted = vehicle.LastSessionDate.Value.AddHours(timezone);
                }

                await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            }

            // do the same thing for driver
            var driver = await session.LoadDriverAsync();
            // get last session from Driver.LastSessionDate, if it is null, then insert this Session.StartTime
            var lastSessionDriver = driver.LastSessionDate;
            // set driver.LastSessionId = session.Id and convert session.Id to string
            driver.LastSessionId = session.Id.ToString();
            if (lastSessionDriver == null)
            {
                driver.LastSessionDate = session.StartTime;
                // adjust LastSessionDate by timezone of the vehicle and save 
                driver.LastSessionDateTzAdjusted = vehicle.LastSessionDate.Value.AddHours(timezone);
                await _dataFacade.DriverDataProvider.SaveAsync(driver);
            }
            else
            {
                // compare lastSession with Session.StartTime, if lastSession < Session.StartTime, then update Vehicle.LastSessionDate with Session.StartTime
                if (lastSessionDriver < session.StartTime)
                {
                    driver.LastSessionDate = session.StartTime;
                    // adjust LastSessionDate by timezone of the vehicle and save 
                    driver.LastSessionDateTzAdjusted = vehicle.LastSessionDate.Value.AddHours(timezone);
                    await _dataFacade.DriverDataProvider.SaveAsync(driver);
                }
            }

        }
    }
}
