﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class SiteDataProviderExtension : IDataProviderExtension<SiteDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDeviceMessageHandler _deviceMessageHandler;

        public SiteDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _deviceMessageHandler = deviceMessageHandler;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterSave += DataProvider_OnAfterSaveAsync;
        }

        private async Task DataProvider_OnAfterSaveAsync(OnAfterSaveEventArgs e)
        {
            var site = e.Result as SiteDataObject;

            if (site == null)
            {
                return;
            }

            var departments = (await site.LoadDepartmentItemsAsync());
            foreach (var department in departments)
            {
                var vehicles = (await department.LoadVehiclesAsync());
                foreach (var vehicle in vehicles)
                {
                    var module = await vehicle.LoadModuleAsync();
                    if (module.IoTDevice != null)
                    {

                        var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
                        try
                        {
                            await deviceTwinHandler.SyncTimezone(module.IoTDevice);
                        }
                        catch (Exception ex)
                        {
                            throw new GOServerException($"Error synchronizing vehicle: {ex.Message}");
                        }
                    }
                }
            }

            var customer = await site.LoadCustomerAsync();

            if (customer == null) 
            {
                return;
            }

            var accessGroups = await customer.LoadAccessGroupItemsAsync();

            foreach (var accessGroup in accessGroups)
            {
                var accessGroupToSites = await accessGroup.LoadAccessGroupsToSitesAsync();

                var existingAccessGroupToSite = accessGroupToSites.FirstOrDefault(x => x.SiteId == site.Id);

                if (existingAccessGroupToSite == null)
                {
                    var accessGroupToSite = _serviceProvider.GetRequiredService<AccessGroupToSiteDataObject>();
                    accessGroupToSite.AccessGroupId = accessGroup.Id;
                    accessGroupToSite.SiteId = site.Id;

                    await _dataFacade.AccessGroupToSiteDataProvider.SaveAsync(accessGroupToSite);
                }
            }
        }
    }
}
