# PowerShell script to generate CAN rules SQL following canrule_template.sql pattern
# This script uses IF NOT EXISTS checks without any deletion logic
# V2: Adds 'Linde ' prefix to vehicle model names that start with numbers

param(
    [string]$CsvPath = "canrules.csv",
    [string]$OutputPath = "canrules_generated_v2.sql"
)

Write-Host "Reading CSV file: $CsvPath"

# Function to check if a string starts with a number (no letters at the beginning)
function Should-AddLindePrefix {
    param([string]$name)
    
    if ([string]::IsNullOrWhiteSpace($name)) {
        return $false
    }
    
    # Check if the first character is a digit
    return $name[0] -match '\d'
}

# Function to apply Linde prefix if needed
function Get-ProcessedVehicleModelName {
    param([string]$originalName)
    
    if (Should-AddLindePrefix $originalName) {
        return "Linde $originalName"
    }
    
    return $originalName
}

# Read and process CSV data
$csvData = Import-Csv -Path $CsvPath
Write-Host "Total CSV records: $($csvData.Count)"

# Remove duplicates and group by vehicle model
$processedData = @{}
$duplicateCount = 0
$lindeModifiedCount = 0

foreach ($row in $csvData) {
    $originalVehicleModel = $row.can_mod.Trim()
    $canRule = $row.can_rules.Trim()
    $crc = $row.crc.Trim()
    
    # Skip if essential data is missing
    if ([string]::IsNullOrWhiteSpace($originalVehicleModel) -or [string]::IsNullOrWhiteSpace($canRule)) {
        continue
    }
    
    # Process vehicle model name (add Linde prefix if starts with number)
    $vehicleModel = Get-ProcessedVehicleModelName $originalVehicleModel
    
    # Track if we modified the name
    if ($vehicleModel -ne $originalVehicleModel) {
        $lindeModifiedCount++
    }
    
    # Convert \N to NULL
    if ($crc -eq '\N') { $crc = $null }
    
    # Group by vehicle model
    if (-not $processedData.ContainsKey($vehicleModel)) {
        $processedData[$vehicleModel] = @{
            VehicleModel = $vehicleModel
            OriginalName = $originalVehicleModel
            CRC = $crc
            CanRules = @()
            Guid = [System.Guid]::NewGuid().ToString().ToUpper()
        }
    }
    
    # Add unique CAN rules only
    if ($processedData[$vehicleModel].CanRules -notcontains $canRule) {
        $processedData[$vehicleModel].CanRules += $canRule
    } else {
        $duplicateCount++
    }
}

Write-Host "Unique vehicle models: $($processedData.Count)"
Write-Host "Duplicate CAN rules removed: $duplicateCount"
Write-Host "Vehicle models with 'Linde ' prefix added: $lindeModifiedCount"

# Generate SQL content
$sqlContent = @"
-- Generated CAN Rules SQL following canrule_template.sql pattern (V2)
-- Generated on: $(Get-Date)
-- Source: $CsvPath
-- Vehicle Models: $($processedData.Count)
-- Total CAN Rules: $(($processedData.Values | ForEach-Object { $_.CanRules.Count } | Measure-Object -Sum).Sum)
-- Vehicle models with 'Linde ' prefix added: $lindeModifiedCount

-- V2 Changes:
-- - Adds 'Linde ' prefix to vehicle model names that start with numbers
-- - Preserves original behavior for other vehicle models

-- This script follows the canrule_template.sql pattern with IF NOT EXISTS checks
-- No deletion logic - only inserts new records if they don't already exist

-- ============================================
-- EXISTING CANRULES CHECK
-- ============================================
-- The following vehicle models already exist in the database and will be skipped:
-- (Run this script to see which models are skipped)

"@

# Generate SQL for each vehicle model
foreach ($vehicleModel in $processedData.Keys | Sort-Object) {
    $data = $processedData[$vehicleModel]
    $crcValue = if ($data.CRC -eq $null) { "NULL" } else { "'$($data.CRC)'" }
    $crcDisplay = if ($data.CRC -eq $null) { "NULL" } else { $data.CRC }
    $escapedVehicleModel = $vehicleModel.Replace("'", "''")
    
    # Show if this was modified from original
    $nameComment = if ($data.OriginalName -ne $data.VehicleModel) {
        " (Original: $($data.OriginalName) -> Modified: $($data.VehicleModel))"
    } else {
        ""
    }
    
    $sqlContent += @"

-- ============================================
-- Model: $($data.VehicleModel)$nameComment
-- Rules: $($data.CanRules.Count)
-- CRC: $crcDisplay
-- GUID: $($data.Guid)
-- ============================================

IF NOT EXISTS (SELECT 1 FROM [dbo].[Canrule] WHERE [Name] = '$escapedVehicleModel')
BEGIN
    INSERT INTO [dbo].[Canrule] ([VehicleSerial], [Id], [CRC], [Name], [Description])
        VALUES (NULL, '$($data.Guid)', $crcValue, '$escapedVehicleModel', NULL)

"@
    
    # Insert CanruleDetails for this vehicle model (nested inside the main IF NOT EXISTS)
    foreach ($canRule in $data.CanRules) {
        # Escape single quotes in CAN rules
        $escapedCanRule = $canRule.Replace("'", "''")
        
        $sqlContent += @"
    -- Insert $canRule
    IF NOT EXISTS (SELECT 1 FROM [dbo].[CanruleDetails] WHERE [Canrules] = '$escapedCanRule' AND [CanruleId] = '$($data.Guid)')
    BEGIN
        INSERT INTO [dbo].[CanruleDetails] ([Id], [Canrules], [CanruleId])
            VALUES (NEWID(), '$escapedCanRule', '$($data.Guid)')
    END

"@
    }
    
    # Close the main IF NOT EXISTS block
    $sqlContent += @"
END
ELSE
BEGIN
    PRINT 'Vehicle model ''$escapedVehicleModel'' already exists in database - skipping'
END

"@
}

# Add comprehensive summary at the end
$sqlContent += @"

-- ============================================
-- EXECUTION SUMMARY
-- ============================================
-- Vehicle models processed: $($processedData.Count)
-- Total CAN rule details inserted: $(($processedData.Values | ForEach-Object { $_.CanRules.Count } | Measure-Object -Sum).Sum)
-- Duplicates removed during processing: $duplicateCount
-- Vehicle models with 'Linde ' prefix added: $lindeModifiedCount
-- 
-- Generated from: $CsvPath
-- Generated on: $(Get-Date)
-- Output file: $OutputPath

-- ============================================
-- VERIFICATION QUERIES
-- ============================================

-- Check total record counts
SELECT 'Record Counts' as Info;
SELECT 'Canrule Records' as TableName, COUNT(*) as RecordCount FROM [dbo].[Canrule]
UNION ALL
SELECT 'CanruleDetails Records' as TableName, COUNT(*) as RecordCount FROM [dbo].[CanruleDetails];

-- Show breakdown by vehicle model
SELECT 'Vehicle Model Summary' as Info;
SELECT 
    c.Name as VehicleModel,
    c.CRC,
    COUNT(cd.Id) as RuleCount,
    c.Id as CanruleId
FROM [dbo].[Canrule] c
LEFT JOIN [dbo].[CanruleDetails] cd ON c.Id = cd.CanruleId
GROUP BY c.Name, c.CRC, c.Id
ORDER BY c.Name;

-- Show the newly inserted models (from this script)
SELECT 'Newly Processed Models' as Info;
"@

# Add a query to show the specific models we just processed
$modelsList = ($processedData.Keys | Sort-Object | ForEach-Object { "'$($_.Replace("'", "''"))'" }) -join ","
$sqlContent += @"
SELECT 
    c.Name as VehicleModel,
    c.CRC,
    COUNT(cd.Id) as RuleCount
FROM [dbo].[Canrule] c
LEFT JOIN [dbo].[CanruleDetails] cd ON c.Id = cd.CanruleId
WHERE c.Name IN ($modelsList)
GROUP BY c.Name, c.CRC
ORDER BY c.Name;

-- Show models that received 'Linde ' prefix
SELECT 'Models with Linde Prefix Added' as Info;
SELECT 
    c.Name as VehicleModel,
    c.CRC,
    COUNT(cd.Id) as RuleCount
FROM [dbo].[Canrule] c
LEFT JOIN [dbo].[CanruleDetails] cd ON c.Id = cd.CanruleId
WHERE c.Name LIKE 'Linde %' AND c.Name IN ($modelsList)
GROUP BY c.Name, c.CRC
ORDER BY c.Name;
"@

# Write to output file
$sqlContent | Out-File -FilePath $OutputPath -Encoding UTF8
Write-Host "SQL file generated: $OutputPath"
Write-Host ""
Write-Host "="*60
Write-Host "GENERATION SUMMARY"
Write-Host "="*60
Write-Host "Vehicle models: $($processedData.Count)"
Write-Host "Total CAN rule details: $(($processedData.Values | ForEach-Object { $_.CanRules.Count } | Measure-Object -Sum).Sum)"
Write-Host "Duplicates removed: $duplicateCount"
Write-Host "Models with 'Linde ' prefix added: $lindeModifiedCount"
Write-Host "Output file: $OutputPath"
Write-Host "File size: $((Get-Item $OutputPath).Length / 1KB) KB"
Write-Host ""
Write-Host "VEHICLE MODELS BREAKDOWN:"
Write-Host "="*60
foreach ($vehicleModel in $processedData.Keys | Sort-Object) {
    $data = $processedData[$vehicleModel]
    $crcDisplay = if ($data.CRC -eq $null) { "NULL" } else { $data.CRC }
    $prefixNote = if ($data.OriginalName -ne $data.VehicleModel) { " [+Linde prefix]" } else { "" }
    Write-Host "  $($data.VehicleModel): $($data.CanRules.Count) rules, CRC: $crcDisplay$prefixNote"
}
Write-Host ""
Write-Host "Generated SQL features:"
Write-Host "- Uses nested IF NOT EXISTS structure (following updated template)"
Write-Host "- CanruleDetails inserts are nested inside main Canrule IF NOT EXISTS block"
Write-Host "- Includes PRINT statements to log which vehicle models already exist"
Write-Host "- No deletion logic - safe for existing data"
Write-Host "- Uses NEWID() for CanruleDetails IDs"
Write-Host "- Adds 'Linde ' prefix to vehicle models starting with numbers"
Write-Host "- Detailed summary and verification queries included"
Write-Host ""
Write-Host "Run this SQL in your database to add new CAN rules data."
Write-Host "The script will print messages showing which vehicle models already exist." 