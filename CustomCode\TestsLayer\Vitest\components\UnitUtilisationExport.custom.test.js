import { describe, it, expect, vi, beforeEach } from 'vitest';

// Create global ApplicationController
global.ApplicationController = {
    getProxyForComponent: vi.fn(),
    errorHandler: vi.fn(),
    viewModel: {
        security: {
            currentUserClaims: vi.fn()
        }
    }
};

// Mock jQuery globally
global.$ = vi.fn();
global.jQuery = vi.fn();

// Create the custom viewmodel constructor
class UnitUtilisationStoreProcedureGridViewModelCustom {
    constructor(viewmodel) {
        this.viewmodel = viewmodel;
        this.GetByIdRequest = null;
        this.serviceUrl = 'api/exportjobstatus/';
    }

    initialize() {
        this.viewmodel.onExportSuccess = (data) => {
            const configuration = {
                contextId: this.viewmodel.contextId,
                successHandler: this.viewmodel.onExport_ChainedCommand0Success,
                errorHandler: this.viewmodel.ShowError,
                fileName: this.viewmodel.fileName,
                fileUrl: this.viewmodel.fileUrl
            };

            const checkExportStatus = () => {
                this.GetExportStatusById({
                    id: data.Data.Id(),
                    contextId: configuration.contextId,
                    successHandler: (result) => {
                        if (result.TaskStatus === 5) {
                            configuration.fileUrl = 'api/exportjobstatus/file/' + result.Id + '/ExportedFile?t=';
                            configuration.fileName = result.ExportedFile;
                            ApplicationController.getProxyForComponent('FileDownloader').DownloadFile(configuration);
                        } else if (result.TaskStatus !== -1) {
                            setTimeout(checkExportStatus, 2000);
                        } else {
                            configuration.errorHandler('Export task failed.');
                        }
                    },
                    errorHandler: configuration.errorHandler
                });
            };

            checkExportStatus();
        };
    }

    isGetByIdBusy() {
        return this.GetByIdRequest != null && this.GetByIdRequest.readyState != 4;
    }

    GetExportStatusById(configuration) {
        if (this.isGetByIdBusy())
            this.GetByIdRequest.abort();

        this.GetByIdRequest = $.ajax({
            url: this.serviceUrl + 'byid/' + configuration.id,
            dataType: 'json',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': 'test-token'
            },
            data: {
                dateformat: 'ISO8601'
            },
            success: (result) => {
                if (configuration.successHandler) {
                    if (result === null) {
                        configuration.successHandler(null);
                    } else {
                        configuration.successHandler(result);
                    }
                }
            },
            error: (jqXHR, textStatus, errorThrown) => {
                ApplicationController.errorHandler(jqXHR, errorThrown, configuration.errorHandler, 'GetExportStatusById error');
            }
        });
    }
}

// Make the constructor available globally
global.FleetXQ = {
    Web: {
        ViewModels: {
            UnitUtilisationStoreProcedureGridViewModelCustom
        }
    }
};

describe('UnitUtilisationExport', () => {
    let viewmodel;
    let customViewModel;
    let fileDownloader;

    beforeEach(() => {
        // Reset all mocks
        vi.clearAllMocks();

        // Setup basic viewmodel structure
        viewmodel = {
            contextId: 'test-context',
            onExport_ChainedCommand0Success: vi.fn(),
            ShowError: vi.fn(),
            fileName: '',
            fileUrl: ''
        };

        // Create FileDownloader mock
        fileDownloader = {
            DownloadFile: vi.fn()
        };

        // Mock the FileDownloader component
        ApplicationController.getProxyForComponent.mockImplementation((component) => {
            if (component === 'FileDownloader') {
                return fileDownloader;
            }
            return null;
        });

        // Mock jQuery ajax
        $.ajax = vi.fn();

        // Create instance of our custom viewmodel
        customViewModel = new UnitUtilisationStoreProcedureGridViewModelCustom(viewmodel);
        customViewModel.initialize();
    });

    describe('Export Process', () => {
        it('should start export process when onExportSuccess is called', () => {
            // Mock export success data
            const exportData = {
                Data: {
                    Id: () => 'test-export-id',
                    TaskStatus: () => 1 // In progress
                }
            };

            // Mock the GetExportStatusById function
            customViewModel.GetExportStatusById = vi.fn();

            // Trigger export success
            viewmodel.onExportSuccess(exportData);

            // Verify GetExportStatusById was called
            expect(customViewModel.GetExportStatusById).toHaveBeenCalled();
        });

        it('should handle export completion and trigger file download', () => {
            // Mock export success data
            const exportData = {
                Data: {
                    Id: () => 'test-export-id',
                    TaskStatus: () => 5 // Complete
                }
            };

            // Mock the GetExportStatusById function
            customViewModel.GetExportStatusById = vi.fn((config) => {
                config.successHandler({
                    TaskStatus: 5,
                    Id: 'test-export-id',
                    ExportedFile: 'test-file.xlsx'
                });
            });

            // Trigger export success
            viewmodel.onExportSuccess(exportData);

            // Verify FileDownloader was called
            expect(fileDownloader.DownloadFile).toHaveBeenCalled();
        });

        it('should handle export failure', () => {
            // Mock export success data
            const exportData = {
                Data: {
                    Id: () => 'test-export-id',
                    TaskStatus: () => -1 // Failed
                }
            };

            // Mock the GetExportStatusById function
            customViewModel.GetExportStatusById = vi.fn((config) => {
                config.successHandler({
                    TaskStatus: -1
                });
            });

            // Trigger export success
            viewmodel.onExportSuccess(exportData);

            // Verify error handler was called
            expect(viewmodel.ShowError).toHaveBeenCalledWith('Export task failed.');
        });
    });

    describe('GetExportStatusById', () => {
        it('should make correct AJAX call to check export status', () => {
            const configuration = {
                id: 'test-export-id',
                contextId: 'test-context',
                successHandler: vi.fn(),
                errorHandler: vi.fn()
            };

            // Call GetExportStatusById
            customViewModel.GetExportStatusById(configuration);

            // Verify AJAX call was made with correct parameters
            expect($.ajax).toHaveBeenCalledWith(expect.objectContaining({
                url: expect.stringContaining('api/exportjobstatus/byid/test-export-id'),
                type: 'GET',
                dataType: 'json'
            }));
        });

        it('should handle AJAX errors correctly', () => {
            const configuration = {
                id: 'test-export-id',
                contextId: 'test-context',
                successHandler: vi.fn(),
                errorHandler: vi.fn()
            };

            // Mock jQuery ajax to simulate error
            $.ajax = vi.fn((options) => {
                options.error({ status: 500 }, 'error', 'test error');
            });

            // Call GetExportStatusById
            customViewModel.GetExportStatusById(configuration);

            // Verify error handler was called
            expect(ApplicationController.errorHandler).toHaveBeenCalled();
        });
    });
});
