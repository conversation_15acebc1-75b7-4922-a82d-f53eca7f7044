﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// SupervisorVehicleAccess Component
	///  
	/// </summary>
    public partial class SupervisorVehicleAccess : ISupervisorVehicleAccess 
    {
		public void Dispose()
		{
		}

        public Task<ComponentResponse<bool>> DeselectAllAsync(Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }

        public Task<ComponentResponse<bool>> SelectAllAsync(Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }
    }
}
