import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('BulkUpdateFirmwareFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console methods
        global.console.log = vi.fn();
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/UpdateFirmwareRequest/BulkUpdateFirmwareFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create mock selected vehicles
        const mockSelectedVehicles = [
            {
                Data: {
                    Id: ko.observable('uuid1'),
                    VehicleId: ko.observable('vehicle1'),
                    UpdateFirmwareRequestId: ko.observable('firmware-request-1'),
                    IsNew: ko.observable(true),
                    IsMarkedForDeletion: ko.observable(false),
                    IsDirty: ko.observable(true)
                }
            },
            {
                Data: {
                    Id: ko.observable('uuid2'),
                    VehicleId: ko.observable('vehicle2'),
                    UpdateFirmwareRequestId: ko.observable('firmware-request-1'),
                    IsNew: ko.observable(true),
                    IsMarkedForDeletion: ko.observable(false),
                    IsDirty: ko.observable(true)
                }
            }
        ];

        // Create mock grid view model
        const mockSelectVehiclesGrid = {
            selectedVehicles: ko.observableArray(mockSelectedVehicles),
            checkedStates: ko.observableArray([
                ko.observable(true),
                ko.observable(true)
            ])
        };

        // Create base view model with required properties
        viewModel = {
            SelectVehiclesForFirmwareUpdateGridViewModel: mockSelectVehiclesGrid,
            UpdateFirmwareRequestObject: ko.observable({
                Data: {
                    Id: ko.observable('firmware-request-1'),
                    FirmwareId: ko.observable('firmware-123') // Add FirmwareId for validation
                }
            }),
            contextId: ['context1'],
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            onUpdateSuccess: vi.fn(),
            closePopup: vi.fn(),
            popupCaller: {
                Rebind: vi.fn()
            },
            popupParameter: {
                siteId: 'site-123'
            },
            controller: {
                applicationController: {
                    getProxyForComponent: vi.fn().mockReturnValue({
                        UpdateFirmware: vi.fn()
                    }),
                    showInfoMessageBox: vi.fn()
                }
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.BulkUpdateFirmwareFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    afterEach(() => {
        // Cleanup
        if (customViewModel && customViewModel.release) {
            customViewModel.release();
        }
    });

    describe('Initialization', () => {
        it('should override the Update method', () => {
            expect(viewModel.Update).toBeDefined();
            expect(typeof viewModel.Update).toBe('function');
        });

        it('should override the onUpdateSuccess method', () => {
            expect(viewModel.onUpdateSuccess).toBeDefined();
            expect(typeof viewModel.onUpdateSuccess).toBe('function');
        });

        it('should apply site filter when popupParameter contains siteId', () => {
            // The site filter should be applied in the setTimeout
            // We'll test this by checking if the grid view model exists
            expect(viewModel.SelectVehiclesForFirmwareUpdateGridViewModel).toBeDefined();
        });
    });

    describe('Update method', () => {
        it('should show error when no firmware is selected', () => {
            // Clear firmware selection
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId(null);

            viewModel.Update();

            expect(viewModel.ShowError).toHaveBeenCalledWith(
                "Please select a firmware version before proceeding with the update.",
                "No Firmware Selected"
            );
        });

        it('should show error when firmware ID is empty string', () => {
            // Set empty firmware ID
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('');

            viewModel.Update();

            expect(viewModel.ShowError).toHaveBeenCalledWith(
                "Please select a firmware version before proceeding with the update.",
                "No Firmware Selected"
            );
        });

        it('should extract vehicle IDs from selected vehicles when firmware is selected', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            const mockProxy = {
                UpdateFirmware: vi.fn()
            };
            viewModel.controller.applicationController.getProxyForComponent.mockReturnValue(mockProxy);

            viewModel.Update();

            // Verify that UpdateFirmware was called with correct configuration
            expect(mockProxy.UpdateFirmware).toHaveBeenCalledWith(
                expect.objectContaining({
                    vehicleIds: ['vehicle1', 'vehicle2'],
                    updateFirmwareRequest: viewModel.UpdateFirmwareRequestObject()
                })
            );
        });

        it('should show error when no vehicles are selected', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            // Clear selected vehicles
            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.selectedVehicles([]);

            viewModel.Update();

            expect(viewModel.ShowError).toHaveBeenCalledWith(
                "Please select at least one vehicle for firmware update.",
                "No Vehicles Selected"
            );
        });

        it('should handle controller not available', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            viewModel.controller = null;

            viewModel.Update();

            expect(viewModel.ShowError).toHaveBeenCalledWith(
                "Controller not available. Please refresh the page and try again.",
                "System Error"
            );
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });

        it('should set busy state and call API when controller is available', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            const mockProxy = {
                UpdateFirmware: vi.fn()
            };
            viewModel.controller.applicationController.getProxyForComponent.mockReturnValue(mockProxy);

            viewModel.Update();

            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
            expect(mockProxy.UpdateFirmware).toHaveBeenCalled();
        });

        it('should prevent duplicate vehicle IDs', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            // Add a duplicate vehicle
            const duplicateVehicle = {
                Data: {
                    Id: ko.observable('uuid3'),
                    VehicleId: ko.observable('vehicle1'), // Duplicate ID
                    UpdateFirmwareRequestId: ko.observable('firmware-request-1'),
                    IsNew: ko.observable(true),
                    IsMarkedForDeletion: ko.observable(false),
                    IsDirty: ko.observable(true)
                }
            };
            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.selectedVehicles.push(duplicateVehicle);

            const mockProxy = {
                UpdateFirmware: vi.fn()
            };
            viewModel.controller.applicationController.getProxyForComponent.mockReturnValue(mockProxy);

            viewModel.Update();

            // Should only have unique vehicle IDs
            expect(mockProxy.UpdateFirmware).toHaveBeenCalledWith(
                expect.objectContaining({
                    vehicleIds: ['vehicle1', 'vehicle2'] // No duplicates
                })
            );
        });
    });

    describe('onUpdateSuccess method', () => {
        it('should show success message with vehicle count', () => {
            const result = { success: true };

            viewModel.onUpdateSuccess(result);

            expect(viewModel.controller.applicationController.showInfoMessageBox).toHaveBeenCalledWith(
                "Firmware update request has been successfully submitted for 2 vehicle(s).",
                "Firmware Update Success"
            );
        });

        it('should trigger parent grid rebind', () => {
            const result = { success: true };

            viewModel.onUpdateSuccess(result);

            expect(viewModel.popupCaller.Rebind).toHaveBeenCalled();
        });

        it('should close popup after success', () => {
            const result = { success: true };

            viewModel.onUpdateSuccess(result);

            expect(viewModel.closePopup).toHaveBeenCalledWith(false);
        });

        it('should handle missing controller gracefully', () => {
            viewModel.controller = null;
            const result = { success: true };

            viewModel.onUpdateSuccess(result);

            // Should not throw error and should still close popup
            expect(viewModel.closePopup).toHaveBeenCalledWith(false);
        });
    });

    describe('Site filtering', () => {
        it('should apply site filter when popupParameter contains siteId', () => {
            // Mock the setSiteFilter method
            const mockSetSiteFilter = vi.fn();
            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.setSiteFilter = mockSetSiteFilter;

            // Re-initialize to trigger the setTimeout
            customViewModel.initialize();

            // Wait for setTimeout to execute
            setTimeout(() => {
                expect(mockSetSiteFilter).toHaveBeenCalledWith('site-123');
            }, 150);
        });

        it('should not apply site filter when popupParameter is missing', () => {
            viewModel.popupParameter = null;
            const mockSetSiteFilter = vi.fn();
            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.setSiteFilter = mockSetSiteFilter;

            // Re-initialize
            customViewModel.initialize();

            setTimeout(() => {
                expect(mockSetSiteFilter).not.toHaveBeenCalled();
            }, 150);
        });

        it('should not apply site filter when siteId is missing', () => {
            viewModel.popupParameter = {};
            const mockSetSiteFilter = vi.fn();
            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.setSiteFilter = mockSetSiteFilter;

            // Re-initialize
            customViewModel.initialize();

            setTimeout(() => {
                expect(mockSetSiteFilter).not.toHaveBeenCalled();
            }, 150);
        });
    });

    describe('Error handling', () => {
        it('should handle invalid vehicle objects gracefully', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            // Add an invalid vehicle object
            const invalidVehicle = {
                Data: {
                    // Missing VehicleId
                    Id: ko.observable('uuid4')
                }
            };
            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.selectedVehicles.push(invalidVehicle);

            const mockProxy = {
                UpdateFirmware: vi.fn()
            };
            viewModel.controller.applicationController.getProxyForComponent.mockReturnValue(mockProxy);

            viewModel.Update();

            // Should still process valid vehicles
            expect(mockProxy.UpdateFirmware).toHaveBeenCalledWith(
                expect.objectContaining({
                    vehicleIds: ['vehicle1', 'vehicle2'] // Only valid IDs
                })
            );
        });

        it('should handle empty vehicle collection', () => {
            // Ensure firmware is selected
            viewModel.UpdateFirmwareRequestObject().Data.FirmwareId('firmware-123');

            viewModel.SelectVehiclesForFirmwareUpdateGridViewModel.selectedVehicles([]);

            viewModel.Update();

            expect(viewModel.ShowError).toHaveBeenCalledWith(
                "Please select at least one vehicle for firmware update.",
                "No Vehicles Selected"
            );
        });
    });
}); 