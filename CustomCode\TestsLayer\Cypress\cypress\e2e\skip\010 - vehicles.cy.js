describe("010 - Vehicles", () => {
    // Use beforeEach to ensure login is handled before each test
    beforeEach(() => {
        cy.login(); // Assuming you have already implemented cy.login() globally
    });

    it("tests 010 - Vehicles", () => {
        // Access Vehicles
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

        // Search for a vehicle
        cy.get("input").first().type("Test");
        cy.get("[data-test-id='searchCommand']").click();
        cy.get("tr:nth-of-type(1) a").click();

        // Intercept the vehicle by ID API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/vehicle/byid/*')
            .as('getVehicleById');


        cy.wait('@getVehicleById', { timeout: 30000 });

        // Modify details
        cy.get("[data-test-id='fa575ace-682c-4f85-be2c-8b13abf9f558']").click();
        //cy.get("[data-test-id='\\32 98eec6f-028b-48c2-82c8-c6b414683de1'] > span.form-field-control-container > div > div").click();
        //cy.get("[data-test-id='\\32 98eec6f-028b-48c2-82c8-c6b414683de1'] [data-test-id='lookup_input']").click();
        //cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']").click();

        // Update vehicle name, limit to 50 characters
        cy.get("[data-test-id='edit_7bd0ee17-21b8-4196-bc80-23d7e4bcde45']")
            .invoke('val')  // Get the current value of the input
            .then((currentName) => {
                const updatedName = `${currentName} (Updated)`;
                cy.get("[data-test-id='edit_7bd0ee17-21b8-4196-bc80-23d7e4bcde45']")
                    .clear()  // Clear the field first (optional based on your needs)
                    .type(updatedName.substring(0, 50));  // Limit the string to 50 characters
            });

        // Update equipment ID, limit to 50 characters
        cy.get("[data-test-id='edit_8ce159b6-fabb-4878-bd8b-9fbb98e6e8e5']")
            .invoke('val')  // Get the current value of the input
            .then((currentValue) => {
                const updatedValue = `${currentValue} 223344 55`;
                cy.get("[data-test-id='edit_8ce159b6-fabb-4878-bd8b-9fbb98e6e8e5']")
                    .clear()  // Clear the field first (optional based on your needs)
                    .type(updatedValue.substring(0, 50));  // Limit the string to 50 characters
            });


        //// Update description
        cy.get("[data-test-id='edit_07ffd7bb-e24a-4318-986b-d8ba31c6a07e']").type("Updated Description");

        // Save changes
        cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']").click();
        cy.get("[data-test-id='bdf5ba7a-da84-40f7-841b-76cb876539af']").click();

        // Search and select another vehicle
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(4) span").click();
        //cy.get("input").first().type("Test{enter}");
        //cy.get("tr:nth-of-type(1) > td:nth-of-type(2)").click();

        // Interact with fields
        cy.get("div:nth-of-type(8) > button").click();
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

        // Perform other actions if needed

        cy.get("[data-test-id='\\30 a7fb6bf-e6c6-4022-b139-65e16f899b35']").click();

        // Intercept the customer list API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/customer/list*')
            .as('getCustomerList');

        // Wait for the customer list API call
        cy.wait('@getCustomerList', { timeout: 30000 });


        cy.get("[data-test-id='a01d32d8-5a7d-4f85-92ce-1cf6bf8f0c15'] [data-test-id='lookup_input']").click();
        cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']").click();

        //cy.get("[data-test-id='e14ecfc4-5850-4f2a-a1f8-993984d68f9b'] [data-test-id='lookup_input']").click();
        //cy.get("html > body > [data-test-id='lookup_wrapper'] [data-test-id='lookup_item']").eq(1).click();

        //cy.get("[data-test-id='edit_ac595b08-0635-464e-aff1-20b8c8104d44']").click();
        //cy.get("div.active").click();
        cy.get("[data-test-id='edit_ab52d53b-cbc8-4bd4-95e8-2f7b4580210e']").click();
        cy.get("[data-test-id='edit_ab52d53b-cbc8-4bd4-95e8-2f7b4580210e']").type("123");
        cy.get("[data-test-id='edit_2b87d5c0-8312-4dac-adb5-fc00992a6f2a']").click();
        cy.get("[data-test-id='edit_2b87d5c0-8312-4dac-adb5-fc00992a6f2a']").type("ALEX_TEST_Vehicle");
        cy.get("[data-test-id='edit_bce86411-6c48-45cf-9431-23283d5c6f01']").click();
        cy.get("[data-test-id='edit_bce86411-6c48-45cf-9431-23283d5c6f01']").type("1234");
        //cy.get("[data-test-id='da535013-6f04-4b9e-bb1a-85bf09b91019'] [data-test-id='lookup_input']").click();
        //cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']").click();
        //cy.get("[data-test-id='\\39 00ebdf7-78a1-4be6-acd6-14fa030d4b01'] [data-test-id='lookup_input']").click();
        //cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']").click();
        //cy.get("[data-test-id='bdabb126-9e5a-4e87-af04-2c0eac714872'] [data-test-id='lookup_input']").click();
        //cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']").click();
        //cy.get("[data-test-id='e14ecfc4-5850-4f2a-a1f8-993984d68f9b'] [data-test-id='lookup_input']").click();
        //cy.get("[data-test-id='\\32 f5b966d-c1f3-4452-a6d1-fa2cf2371b27'] [data-test-id='lookup_input']").click();
        //cy.get("html > body > [data-test-id='lookup_wrapper'] [data-test-id='lookup_item']").click();
        //cy.get("[data-test-id='\\31 ef337cd-f22d-4c97-90d2-7269f5c9f576']").click();
    });
});
