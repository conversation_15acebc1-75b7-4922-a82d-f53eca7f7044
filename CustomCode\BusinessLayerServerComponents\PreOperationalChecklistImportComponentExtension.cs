using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Policy;
using System.Threading.Tasks;
using System.IO;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.MicrosoftExcelImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Microsoft.Extensions.DependencyInjection;
using NLog;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class PreOperationalChecklistImportComponentExtension : IImportExportComponentExtension<PreOperationalChecklistImportSection0Component,
        PreOperationalChecklistDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;

        public PreOperationalChecklistImportComponentExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {
            this._dataFacade = dataFacade;
            this._serviceProvider = serviceProvider;
        }

        public void Init(IImportExportComponent<PreOperationalChecklistDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
            importExportComponent.OnBeforeImportDataRowAsync += ImportExportComponent_OnBeforeImportDataRowAsync;
        }

        private async Task ImportExportComponent_OnBeforeImportDataRowAsync(OnBeforeImportDataRowEventArgs<PreOperationalChecklistDataObject> arg)
        {
            var customerName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_CUSTOMER].ToString();
            var siteName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_SITE].ToString();
            var departmentName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_DEPARTMENT].ToString();
            var modelName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_MODEL].ToString();

            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { customerName })).SingleOrDefault();

            if (customer == null)
            {
                return;
            }

            await customer.LoadSitesAsync();

            var site = customer.Sites.FirstOrDefault(x => x.Name == siteName);

            if (site == null)
            {
                return;
            }

            await site.LoadDepartmentItemsAsync();

            var department = site.DepartmentItems.FirstOrDefault(x => x.Name == departmentName);

            if (department == null)
            {
                return;
            }

            var model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { modelName })).SingleOrDefault();

            if (model == null)
            {
                return;
            }

            var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "ModelId == @0 && DepartmentId == @1", new object[] { model.Id, department.Id })).SingleOrDefault();

            if (departmentChecklist == null)
            {
                departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                departmentChecklist.ModelId = model.Id;
                departmentChecklist.DepartmentId = department.Id;
                
                // Get IsThaiEnabled from the import file
                var isThaiEnabledStr = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_ISTHAIENABLED]?.ToString();
                if (bool.TryParse(isThaiEnabledStr, out bool isThaiEnabled))
                {
                    departmentChecklist.IsThaiEnabled = isThaiEnabled;
                }
                else
                {
                    departmentChecklist.IsThaiEnabled = false; // 
                }
                
                await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);
            }

            var questions = await departmentChecklist.LoadPreOperationalChecklistsAsync();

            var newQuestion = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_QUESTION].ToString();

            if (questions?.Any(q => q.Question.Equals(newQuestion, StringComparison.OrdinalIgnoreCase)) == true)
            {
                throw new Exception($"Question already exists for DepartmentChecklist with Id: {departmentChecklist.Id}");
            }
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<PreOperationalChecklistDataObject> arg)
        {
            var customerName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_CUSTOMER].ToString();
            var siteName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_SITE].ToString();
            var departmentName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_DEPARTMENT].ToString();
            var modelName = arg.DataRow[PreOperationalChecklistImportSection0Component.COL_MODEL].ToString();

            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { customerName })).SingleOrDefault();

            if (customer == null)
            {
                return;
            }

            await customer.LoadSitesAsync();

            var site = customer.Sites.FirstOrDefault(x => x.Name == siteName);

            if (site == null)
            {
                return;
            }

            await site.LoadDepartmentItemsAsync();

            var department = site.DepartmentItems.FirstOrDefault(x => x.Name == departmentName);

            var model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { modelName })).SingleOrDefault();

            if (model == null)
            {
                return;
            }

            var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "ModelId == @0 && DepartmentId == @1", new object[] { model.Id, department.Id })).SingleOrDefault();

            if (departmentChecklist != null)
            {
                arg.Entity.SiteChecklistId = departmentChecklist.Id;
            }
        }
    }
}