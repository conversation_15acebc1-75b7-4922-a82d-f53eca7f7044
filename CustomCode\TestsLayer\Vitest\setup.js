import { vi } from 'vitest';

// Mock knockout
global.ko = {
    observable: (value) => {
        let currentValue = value;
        const observable = function (newValue) {
            if (arguments.length === 0) {
                return currentValue;
            }
            currentValue = newValue;
            // Call subscribers
            if (observable.subscribers) {
                observable.subscribers.forEach(fn => fn(newValue));
            }
        };
        observable.value = currentValue;
        observable.set = (newValue) => {
            currentValue = newValue;
            observable.subscribe(currentValue);
        };
        observable.get = () => currentValue;
        observable.subscribe = (fn) => {
            if (!observable.subscribers) {
                observable.subscribers = [];
            }
            observable.subscribers.push(fn);
            return {
                dispose: () => {
                    const index = observable.subscribers.indexOf(fn);
                    if (index > -1) {
                        observable.subscribers.splice(index, 1);
                    }
                }
            };
        };
        return observable;
    },
    observableArray: (array = []) => {
        let currentArray = array;
        const observable = function (newArray) {
            if (arguments.length === 0) {
                return currentArray;
            }
            currentArray = newArray;
            observable.subscribe(currentArray);
        };
        observable.array = currentArray;
        observable.push = (item) => {
            currentArray.push(item);
            observable.subscribe(currentArray);
        };
        observable.removeAll = () => {
            currentArray = [];
            observable.subscribe(currentArray);
        };
        observable.subscribe = vi.fn();
        Object.defineProperty(observable, 'length', {
            get: () => currentArray.length
        });
        observable.get = (index) => currentArray[index];
        observable.includes = (item) => currentArray.includes(item);
        return observable;
    },
    computed: (fn) => {
        const computed = function (newValue) {
            if (arguments.length === 0) {
                return fn();
            }
            fn(newValue);
        };
        computed.set = (newValue) => fn(newValue);
        computed.get = () => fn();
        computed.subscribe = vi.fn();
        return computed;
    },
    pureComputed: (fn) => {
        const computed = function (newValue) {
            if (arguments.length === 0) {
                return fn();
            }
            fn(newValue);
        };
        computed.set = (newValue) => fn(newValue);
        computed.get = () => fn();
        computed.subscribe = vi.fn();
        return computed;
    },
    postbox: {
        subscribe: vi.fn(),
        publish: vi.fn()
    },
    applyBindings: () => { },
    removeNode: () => { }
};

// Mock global namespace
global.FleetXQ = {
    Web: {
        ViewModels: {
            VehicleOtherSettingsFormViewModel: function (controller, $formContainer, sDataBindRoot, $popupContainer, parentContextId, options) {
                var self = this;
                this.controller = controller;
                this.subscriptions = [];

                this.DataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'vehicleothersettings');

                const vehicleObject = new FleetXQ.Web.Model.DataObjects.VehicleOtherSettingsObject();
                this.VehicleOtherSettingsObject = ko.observable(vehicleObject);
                this.VehicleOtherSettingsObject().ObjectsDataSet = this.controller.ObjectsDataSet;
                this.CurrentObject = ko.pureComputed(function () { return this.VehicleOtherSettingsObject() }, this);

                this.StatusData = {
                    IsUIDirty: ko.observable(false),
                    IsBusy: ko.observable(false),
                    IsEnabled: ko.observable(true),
                    IsVisible: ko.observable(true),
                    DisplayMode: ko.observable('view'),
                    ShowTitle: ko.observable(true),
                    PreviousIsEmpty: true,
                    IsEmpty: ko.observable(true),
                    isPopup: ko.observable(false),
                    isValid: ko.observable(true),
                    errorSummary: ko.observableArray()
                };

                this.runValidation = function (partialvalidation) {
                    if (partialvalidation === true) {
                        self.VehicleOtherSettingsObject().runValidation(["VORStatus", "FullLockout", "FullLockoutTimeout", "PedestrianSafety"]);
                    } else {
                        self.VehicleOtherSettingsObject().runValidation();
                    }
                    self.StatusData.isValid(self.VehicleOtherSettingsObject().StatusData.isValid());
                    // Clear existing errors and add new ones
                    self.StatusData.errorSummary.removeAll();
                    const errors = self.VehicleOtherSettingsObject().StatusData.errorSummary();
                    errors.forEach(error => self.StatusData.errorSummary.push(error));
                };
            },
            VehicleOtherSettingsFormViewModelCustom: function (viewmodel) {
                var self = this;
                this.viewmodel = viewmodel;

                this.IsFullLockoutTimeoutReadOnly = function () {
                    return !viewmodel.VehicleOtherSettingsObject().Data.FullLockout();
                };
            },
            Filters: {},
            AllChecklistResultViewGrid1ViewModel: function (controller, $gridContainer, sDataBindRoot, $popupContainer, parentContextId, options) {
                var self = this;
                this.subscriptions = [];
                this.controller = controller;
                this.sDataBindRoot = sDataBindRoot || "";
                this.sDatabindRootWithDot = this.sDataBindRoot !== "" ? this.sDataBindRoot + "." : this.sDataBindRoot;
                this.$gridContainer = $gridContainer;
                this.$popupContainer = $popupContainer;
                this.contextId = parentContextId ? parentContextId.concat([this.controller.applicationController.getNextContextId()]) : [this.controller.applicationController.getNextContextId()];
                this.DataStore = new FleetXQ.Web.Model.DataStores.DataStore(controller.applicationController.ObjectsDataSet, 'allchecklistresultview');
                this.AllChecklistResultViewObjectCollection = ko.observableArray([]);
                this.selectedId = ko.observable(null);
                this.gridSettings = {
                    selectedId: ko.observable(null)
                };
                this.commands = {
                    showShowAnswersPopupCommand: vi.fn()
                };
                this.selectedObject = ko.computed(() => {
                    if (this.selectedId() == -1) return null;
                    return this.controller.ObjectsDataSet.GetObjectByInternalId("AllChecklistResultView", this.selectedId());
                });
                this.criticalRows = ko.observableArray([]);
            },
            AllChecklistResultViewGrid1ViewModelCustom: function (viewmodel) {
                this.viewmodel = viewmodel;
                this.hasCriticalFailedQuestions = function (item) {
                    if (!item || !item.Data) return false;
                    var value = item.Data.HasCriticalQuestions;
                    return (typeof value === 'function' ? value() : value) === true;
                };
                this.getRowClasses = function (data) {
                    var isCritical = this.hasCriticalFailedQuestions(data);
                    var isSelected = this.viewmodel.selectedId() === data.Data.InternalObjectId();
                    return {
                        'currenttr': isSelected,
                        'critical-failed-row': isCritical
                    };
                };
            }
        },
        Model: {
            DataObjects: {
                VehicleOtherSettingsObject: function () {
                    return {
                        Data: {
                            FullLockout: ko.observable(false),
                            FullLockoutTimeout: ko.observable(0),
                            Id: ko.observable(0),
                            IsNew: () => true
                        },
                        StatusData: {
                            isValid: ko.observable(true),
                            errorSummary: ko.observableArray()
                        },
                        CopyValuesFrom: function () { },
                        runValidation: function (fields) {
                            const self = this;
                            self.StatusData.isValid(true);
                            self.StatusData.errorSummary.removeAll();
                        },
                        resetValidation: function () {
                            this.StatusData.isValid(true);
                            this.StatusData.errorSummary.removeAll();
                        }
                    };
                },
                VehicleOtherSettingsObjectFactory: {
                    createNew: () => new FleetXQ.Web.Model.DataObjects.VehicleOtherSettingsObject()
                },
                DashboardFilterObject: vi.fn(),
                AllChecklistResultViewObject: function () {
                    const obj = {
                        Data: {
                            HasCriticalQuestions: ko.observable(false),
                            InternalObjectId: ko.observable(0),
                            TimezoneAdjustedChecklistStartTime: ko.observable('2024-01-01T00:00:00')
                        }
                    };
                    return obj;
                }
            },
            DataStores: {
                DataStore: class {
                    constructor() {
                        this.getShowAllQuestionsLink = vi.fn();
                    }
                }
            },
            DataSets: {
                ObjectsDataSet: {
                    GetObjectByInternalId: vi.fn()
                }
            }
        },
        Application: {
            BaseURL: '/',
            CSRF_TOKEN: 'test-token'
        },
        Messages: {
            confirmDeleteMessage: 'Delete %ENTITY%?',
            confirmDeletePopupTitle: 'Confirm Delete'
        }
    }
};

// Mock ApplicationController
global.ApplicationController = {
    viewModel: {
        security: {
            currentUserClaims: () => ({
                HasVehiclesAccess: 'True',
                CanEditFullImpactLockout: 'True',
                CanViewVORStatus: 'True',
                CanViewFullImpactLockout: 'True'
            })
        }
    }
};

// Mock window.location
global.window = {
    location: {
        href: ''
    }
};
