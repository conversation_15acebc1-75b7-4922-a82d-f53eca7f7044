import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('IncompletedChecklistViewReportViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/IncompletedChecklistView/IncompletedChecklistViewReportViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            IncompletedChecklistViewObjectCollection: ko.observableArray([]),
            sortColumnName: ko.observable('Order'),
            getDataNumberOfIncompletedChecklistsForChart: vi.fn(),
            getDataNumberOfCompletedChecklistsForChart: vi.fn(),
            getDataNumberOfCriticalFailedChecklistsForChart: vi.fn(),
            getLabelsForChart: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.IncompletedChecklistViewReportViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('initialize', () => {
        it('should set initial sort column to Order', () => {
            expect(viewModel.sortColumnName()).toBe('Order');
        });

        it('should override chart data methods with aggregated versions', () => {
            expect(typeof viewModel.getDataNumberOfIncompletedChecklistsForChart).toBe('function');
            expect(typeof viewModel.getDataNumberOfCompletedChecklistsForChart).toBe('function');
            expect(typeof viewModel.getDataNumberOfCriticalFailedChecklistsForChart).toBe('function');
            expect(typeof viewModel.getLabelsForChart).toBe('function');
        });
    });

    describe('parseDate', () => {
        it('should correctly parse valid date components', () => {
            const date = customViewModel.parseDate(15, 3, 2024);
            expect(date.getDate()).toBe(15);
            expect(date.getMonth()).toBe(2); // 0-based month
            expect(date.getFullYear()).toBe(2024);
        });

        it('should handle invalid date components gracefully', () => {
            const date = customViewModel.parseDate('invalid', 'invalid', 'invalid');
            expect(date.getTime()).toBe(NaN);
        });
    });

    describe('getAggregatedDataForChart', () => {
        it('should aggregate data correctly for a given metric', () => {
            // Setup test data
            const testData = [
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024',
                        NumberOfIncompletedChecklists: () => 5
                    }
                },
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024',
                        NumberOfIncompletedChecklists: () => 3
                    }
                }
            ];
            viewModel.IncompletedChecklistViewObjectCollection(testData);

            const aggregatedData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
            expect(aggregatedData).toEqual([8]); // 5 + 3 = 8
        });

        it('should handle empty collection', () => {
            viewModel.IncompletedChecklistViewObjectCollection([]);
            const aggregatedData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
            expect(aggregatedData).toEqual([]);
        });

        it('should handle invalid data gracefully', () => {
            const testData = [
                {
                    Data: {
                        Day: () => null,
                        Month: () => null,
                        Year: () => null,
                        DateDisplay: () => null,
                        NumberOfIncompletedChecklists: () => 'invalid'
                    }
                }
            ];
            viewModel.IncompletedChecklistViewObjectCollection(testData);

            const aggregatedData = customViewModel.getAggregatedDataForChart('NumberOfIncompletedChecklists')();
            expect(aggregatedData).toEqual([]);
        });
    });

    describe('getAggregatedLabelsForChart', () => {
        it('should return chronologically sorted unique dates', () => {
            const testData = [
                {
                    Data: {
                        Day: () => 2,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '02/01/2024'
                    }
                },
                {
                    Data: {
                        Day: () => 1,
                        Month: () => 1,
                        Year: () => 2024,
                        DateDisplay: () => '01/01/2024'
                    }
                }
            ];
            viewModel.IncompletedChecklistViewObjectCollection(testData);

            const labels = customViewModel.getAggregatedLabelsForChart();
            expect(labels).toEqual(['01/01/2024', '02/01/2024']);
        });

        it('should handle empty collection', () => {
            viewModel.IncompletedChecklistViewObjectCollection([]);
            const labels = customViewModel.getAggregatedLabelsForChart();
            expect(labels).toEqual([]);
        });

        it('should handle invalid data gracefully', () => {
            const testData = [
                {
                    Data: {
                        Day: () => null,
                        Month: () => null,
                        Year: () => null,
                        DateDisplay: () => null
                    }
                }
            ];
            viewModel.IncompletedChecklistViewObjectCollection(testData);

            const labels = customViewModel.getAggregatedLabelsForChart();
            expect(labels).toEqual([]);
        });
    });
}); 