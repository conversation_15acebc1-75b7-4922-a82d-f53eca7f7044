import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('PersonVehicleAccessFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Model: {
                    DataObjects: {
                        PersonToSiteVehicleNormalAccessViewObject: vi.fn().mockImplementation(() => ({
                            Data: {
                                IsDirty: vi.fn().mockReturnValue(false),
                                HasAccess: ko.observable(false)
                            },
                            Clone: vi.fn().mockReturnThis(),
                            contextIds: []
                        })),
                        PersonToDepartmentVehicleNormalAccessViewObject: vi.fn().mockImplementation(() => ({
                            Data: {
                                IsDirty: vi.fn().mockReturnValue(false),
                                HasAccess: ko.observable(false)
                            },
                            Clone: vi.fn().mockReturnThis(),
                            contextIds: []
                        })),
                        PersonToModelVehicleNormalAccessViewObject: vi.fn().mockImplementation(() => ({
                            Data: {
                                IsDirty: vi.fn().mockReturnValue(false),
                                HasAccess: ko.observable(false)
                            },
                            Clone: vi.fn().mockReturnThis(),
                            contextIds: []
                        })),
                        PersonToPerVehicleNormalAccessViewObject: vi.fn().mockImplementation(() => ({
                            Data: {
                                IsDirty: vi.fn().mockReturnValue(false),
                                HasAccess: ko.observable(false)
                            },
                            Clone: vi.fn().mockReturnThis(),
                            contextIds: []
                        }))
                    },
                    DataSets: {
                        ObjectsDataSet: vi.fn().mockImplementation(() => ({
                            AddObject: vi.fn()
                        }))
                    }
                }
            }
        };

        // Mock ApplicationController for security checks with the correct structure
        global.ApplicationController = {
            viewModel: {
                viewModelCustom: {
                    hasAccess: vi.fn().mockReturnValue(true),
                    AccessRules: {
                        HAS_USERS_ACCESS: 'HAS_USERS_ACCESS',
                        CAN_EDIT_VEHICLE_ACCESS: 'CAN_EDIT_VEHICLE_ACCESS'
                    }
                }
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Person/PersonVehicleAccessFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            StatusData: {
                DisplayMode: ko.observable('view'),
                IsEmpty: ko.observable(false),
                CurrentTabIndex: ko.observable(1),
                IsBusy: vi.fn()
            },
            Commands: {
                IsSelectAllCommandVisible: ko.observable(false),
                IsDeselectAllCommandVisible: ko.observable(false),
                IsNewSaveCommandVisible: ko.observable(false)
            },
            PersonObject: ko.observable({
                Data: {
                    Id: ko.observable('test-id'),
                    IsNew: vi.fn().mockReturnValue(false)
                }
            }),
            CurrentObject: ko.observable({
                Data: {
                    Id: ko.observable('test-id')
                }
            }),
            PersonToSiteVehicleNormalAccessViewItemsListViewModel: {
                viewModelCollection: ko.observableArray([]),
                SetPersonToSiteVehicleNormalAccessViewObjectCollection: vi.fn(),
                LoadPersonToSiteVehicleNormalAccessViewObjectCollection: vi.fn(),
                getPersonToSiteVehicleNormalAccessViewItems: vi.fn().mockReturnValue([])
            },
            PersonToDepartmentVehicleNormalAccessViewItemsListViewModel: {
                viewModelCollection: ko.observableArray([]),
                SetPersonToDepartmentVehicleNormalAccessViewObjectCollection: vi.fn(),
                LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection: vi.fn(),
                getPersonToDepartmentVehicleNormalAccessViewItems: vi.fn().mockReturnValue([])
            },
            PersonToModelVehicleNormalAccessViewItemsListViewModel: {
                viewModelCollection: ko.observableArray([]),
                SetPersonToModelVehicleNormalAccessViewObjectCollection: vi.fn(),
                LoadPersonToModelVehicleNormalAccessViewObjectCollection: vi.fn(),
                getPersonToModelVehicleNormalAccessViewItems: vi.fn().mockReturnValue([])
            },
            PersonToPerVehicleNormalAccessViewItemsListViewModel: {
                viewModelCollection: ko.observableArray([]),
                SetPersonToPerVehicleNormalAccessViewObjectCollection: vi.fn(),
                LoadPersonToPerVehicleNormalAccessViewObjectCollection: vi.fn(),
                getPersonToPerVehicleNormalAccessViewItems: vi.fn().mockReturnValue([])
            },
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            EndEdit: vi.fn(),
            DataStore: {
                CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
            },
            controller: {
                applicationController: {
                    getProxyForComponent: vi.fn().mockReturnValue({
                        UpdateAccessesForPerson: vi.fn(),
                        GetAccessesForDepartments: vi.fn(),
                        GetAccessesForModels: vi.fn(),
                        GetAccessesForVehicles: vi.fn()
                    })
                }
            },
            contextId: 'test-context-id',
            ObjectsDataSet: new FleetXQ.Web.Model.DataSets.ObjectsDataSet()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.PersonVehicleAccessFormViewModelCustom(viewModel);

        // Add the vehicleAccessUtilitiesProxy to the custom view model
        customViewModel.vehicleAccessUtilitiesProxy = {
            UpdateVehicleSiteAccessesForPerson: vi.fn(),
            UpdateVehicleDepartmentAccessesForPerson: vi.fn(),
            UpdateVehicleModelAccessesForPerson: vi.fn(),
            UpdateVehiclePerVehicleAccessesForPerson: vi.fn()
        };

        customViewModel.initialize();
    });

    describe('initialize', () => {
        it('should set up command visibility based on display mode', () => {
            viewModel.StatusData.DisplayMode('edit');
            expect(viewModel.Commands.IsSelectAllCommandVisible()).toBe(true);
            expect(viewModel.Commands.IsDeselectAllCommandVisible()).toBe(true);
            expect(viewModel.Commands.IsNewSaveCommandVisible()).toBe(true);

            viewModel.StatusData.DisplayMode('view');
            expect(viewModel.Commands.IsSelectAllCommandVisible()).toBe(false);
            expect(viewModel.Commands.IsDeselectAllCommandVisible()).toBe(false);
            expect(viewModel.Commands.IsNewSaveCommandVisible()).toBe(false);
        });

        it('should call RefreshAccessCalculations when tab changes in edit mode', () => {
            const refreshSpy = vi.spyOn(customViewModel, 'RefreshAccessCalculations');
            viewModel.StatusData.DisplayMode('edit');
            viewModel.StatusData.CurrentTabIndex(2);
            expect(refreshSpy).toHaveBeenCalled();
        });
    });

    describe('RefreshCalculations', () => {
        it('should call RefreshAccessCalculations', () => {
            const refreshSpy = vi.spyOn(customViewModel, 'RefreshAccessCalculations');
            customViewModel.RefreshCalculations();
            expect(refreshSpy).toHaveBeenCalled();
        });
    });

    describe('RefreshAccessCalculations', () => {
        it('should create a promise and start async chain', () => {
            const getAccessesSpy = vi.spyOn(customViewModel, 'GetAccessesForDepartments');
            customViewModel.RefreshAccessCalculations();
            expect(getAccessesSpy).toHaveBeenCalled();
        });
    });

    describe('NewSave', () => {
        it('should handle sites tab correctly', () => {
            const getAccessesSpy = vi.spyOn(customViewModel, 'GetAccessesForDepartments');
            viewModel.StatusData.CurrentTabIndex(1);
            viewModel.NewSave();
            expect(getAccessesSpy).toHaveBeenCalled();
            expect(customViewModel.isSaving).toBe(true);
        });

        it('should handle departments tab correctly', () => {
            const getModelsSpy = vi.spyOn(customViewModel, 'GetAccessesForModelsAsync');
            viewModel.StatusData.CurrentTabIndex(2);
            viewModel.NewSave();
            expect(getModelsSpy).toHaveBeenCalled();
            expect(customViewModel.isSaving).toBe(true);
        });

        it('should handle models tab correctly', () => {
            const getVehiclesSpy = vi.spyOn(customViewModel, 'GetAccessesForVehiclesAsync');
            viewModel.StatusData.CurrentTabIndex(3);
            viewModel.NewSave();
            expect(getVehiclesSpy).toHaveBeenCalled();
            expect(customViewModel.isSaving).toBe(true);
        });

        it('should handle vehicles tab correctly', () => {
            const performSaveSpy = vi.spyOn(customViewModel, 'PerformSave');
            viewModel.StatusData.CurrentTabIndex(4);
            viewModel.NewSave();
            expect(performSaveSpy).toHaveBeenCalled();
        });
    });

    describe('PerformSave', () => {
        it('should call UpdateAccessesForPerson with correct configuration', () => {
            const updateAccessesSpy = vi.spyOn(viewModel.controller.applicationController.getProxyForComponent('VehicleAccessUtilities'), 'UpdateAccessesForPerson');

            // Setup some mock data with proper Clone method
            viewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);

            customViewModel.PerformSave();
            expect(updateAccessesSpy).toHaveBeenCalledWith(expect.objectContaining({
                personId: 'test-id',
                contextId: 'test-context-id'
            }));
        });
    });

    describe('GetAccessesForDepartments', () => {
        it('should call GetAccessesForDepartments with correct configuration', () => {
            const getAccessesSpy = vi.spyOn(viewModel.controller.applicationController.getProxyForComponent('VehicleAccessUtilities'), 'GetAccessesForDepartments');

            // Setup mock data with proper Clone method
            viewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);

            customViewModel.GetAccessesForDepartments();
            expect(getAccessesSpy).toHaveBeenCalledWith(expect.objectContaining({
                personId: 'test-id',
                permissionLevel: 3
            }));
        });

        it('should store current department selections', () => {
            // Setup mock data with existing selections
            viewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            DepartmentId: ko.observable('dept1'),
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    DepartmentId: ko.observable('dept1'),
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);
            viewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);

            customViewModel.GetAccessesForDepartments();
            expect(customViewModel.currentDepartmentSelections).toEqual({ 'dept1': true });
        });
    });

    describe('GetAccessesForModelsAsync', () => {
        it('should call GetAccessesForModels with correct configuration', () => {
            const getModelsSpy = vi.spyOn(viewModel.controller.applicationController.getProxyForComponent('VehicleAccessUtilities'), 'GetAccessesForModels');

            // Setup mock data with proper Clone method
            viewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);

            customViewModel.GetAccessesForModelsAsync();
            expect(getModelsSpy).toHaveBeenCalledWith(expect.objectContaining({
                personId: 'test-id',
                permissionLevel: 3
            }));
        });
    });

    describe('GetAccessesForVehiclesAsync', () => {
        it('should call GetAccessesForVehicles with correct configuration', () => {
            const getVehiclesSpy = vi.spyOn(viewModel.controller.applicationController.getProxyForComponent('VehicleAccessUtilities'), 'GetAccessesForVehicles');

            // Setup mock data with proper Clone method
            viewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);
            viewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel.viewModelCollection([
                {
                    CurrentObject: () => ({
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        Clone: vi.fn().mockImplementation(function () {
                            return {
                                Data: {
                                    HasAccess: ko.observable(true),
                                    IsDirty: vi.fn().mockReturnValue(true)
                                },
                                contextIds: []
                            };
                        }),
                        contextIds: []
                    })
                }
            ]);

            customViewModel.GetAccessesForVehiclesAsync();
            expect(getVehiclesSpy).toHaveBeenCalledWith(expect.objectContaining({
                personId: 'test-id',
                permissionLevel: 3
            }));
        });
    });

    describe('onGetAccessesForDepartmentsSuccess', () => {
        it('should update department collection and call GetAccessesForModelsAsync', () => {
            const getModelsSpy = vi.spyOn(customViewModel, 'GetAccessesForModelsAsync');
            const mockData = [{
                Data: {
                    DepartmentId: ko.observable('dept1'),
                    HasAccess: ko.observable(true)
                }
            }];

            // Setup existing selections
            customViewModel.currentDepartmentSelections = { 'dept1': false };

            customViewModel.onGetAccessesForDepartmentsSuccess(mockData);
            expect(getModelsSpy).toHaveBeenCalled();
        });

        it('should preserve existing selections when updating', () => {
            const mockData = [{
                Data: {
                    DepartmentId: ko.observable('dept1'),
                    HasAccess: ko.observable(true)
                }
            }];

            // Setup existing selections
            customViewModel.currentDepartmentSelections = { 'dept1': false };

            customViewModel.onGetAccessesForDepartmentsSuccess(mockData);
            expect(mockData[0].Data.HasAccess()).toBe(false); // Should preserve the stored value
        });
    });

    describe('onGetAccessesForModelsAsyncSuccess', () => {
        it('should update model collection and call GetAccessesForVehiclesAsync', () => {
            const getVehiclesSpy = vi.spyOn(customViewModel, 'GetAccessesForVehiclesAsync');
            const mockData = [{
                Data: {
                    ModelId: ko.observable('model1'),
                    DepartmentId: ko.observable('dept1'),
                    HasAccess: ko.observable(true)
                }
            }];

            // Setup existing selections
            customViewModel.currentModelSelections = { 'model1_dept1': false };

            customViewModel.onGetAccessesForModelsAsyncSuccess(mockData);
            expect(getVehiclesSpy).toHaveBeenCalled();
        });

        it('should preserve existing selections when updating', () => {
            const mockData = [{
                Data: {
                    ModelId: ko.observable('model1'),
                    DepartmentId: ko.observable('dept1'),
                    HasAccess: ko.observable(true)
                }
            }];

            // Setup existing selections
            customViewModel.currentModelSelections = { 'model1_dept1': false };

            customViewModel.onGetAccessesForModelsAsyncSuccess(mockData);
            expect(mockData[0].Data.HasAccess()).toBe(false); // Should preserve the stored value
        });
    });

    describe('onGetAccessesForVehiclesAsyncSuccess', () => {
        it('should update vehicle collection and resolve promise', () => {
            const mockData = [{
                Data: {
                    VehicleId: ko.observable('vehicle1'),
                    HasAccess: ko.observable(true)
                }
            }];

            // Setup existing selections
            customViewModel.currentVehicleSelections = { 'vehicle1': false };

            customViewModel.onGetAccessesForVehiclesAsyncSuccess(mockData);
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });

        it('should preserve existing selections when updating', () => {
            const mockData = [{
                Data: {
                    VehicleId: ko.observable('vehicle1'),
                    HasAccess: ko.observable(true)
                }
            }];

            // Setup existing selections
            customViewModel.currentVehicleSelections = { 'vehicle1': false };

            customViewModel.onGetAccessesForVehiclesAsyncSuccess(mockData);
            expect(mockData[0].Data.HasAccess()).toBe(false); // Should preserve the stored value
        });

        it('should call PerformSave when in save mode', () => {
            const performSaveSpy = vi.spyOn(customViewModel, 'PerformSave');
            customViewModel.isSaving = true;
            customViewModel.isInChain = false;

            customViewModel.onGetAccessesForVehiclesAsyncSuccess([]);
            expect(performSaveSpy).toHaveBeenCalled();
            expect(customViewModel.isSaving).toBe(false);
            expect(customViewModel.isInChain).toBe(false);
        });
    });

    describe('IsModifyCommandVisible', () => {
        it('should return true when user has appropriate permissions', () => {
            viewModel.StatusData.DisplayMode('view');
            viewModel.StatusData.IsEmpty(false);
            ApplicationController.viewModel.viewModelCustom.hasAccess.mockReturnValue(true);
            expect(customViewModel.IsModifyCommandVisible()).toBe(true);
        });

        it('should return false when user lacks permissions', () => {
            viewModel.StatusData.DisplayMode('view');
            viewModel.StatusData.IsEmpty(false);
            ApplicationController.viewModel.viewModelCustom.hasAccess.mockReturnValue(false);
            expect(customViewModel.IsModifyCommandVisible()).toBe(false);
        });
    });

    describe('IsSaveCommandVisible', () => {
        it('should always return false', () => {
            expect(customViewModel.IsSaveCommandVisible()).toBe(false);
        });
    });

    describe('getObjectsToSave', () => {
        it('should return undefined when no changes', () => {
            const accesses = [{
                Data: { IsDirty: vi.fn().mockReturnValue(false) }
            }];

            const result = customViewModel.getObjectsToSave(accesses);
            expect(result).toBeUndefined();
        });

        it('should return object when changes exist', () => {
            const accesses = [{
                Data: { IsDirty: vi.fn().mockReturnValue(true) },
                Clone: vi.fn().mockReturnThis()
            }];

            const result = customViewModel.getObjectsToSave(accesses);
            expect(result).toBeDefined();
        });
    });

    describe('getObjectsForSiteAccess', () => {
        it('should create objects for site access', () => {
            const accesses = [{
                Data: {
                    HasAccess: ko.observable(true),
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: []
                    };
                }),
                contextIds: []
            }];

            const result = customViewModel.getObjectsForSiteAccess(accesses);
            expect(result).toBeDefined();
        });

        it('should create dummy object when no accesses', () => {
            const result = customViewModel.getObjectsForSiteAccess([]);
            expect(result.Data.HasAccess()).toBe(false);
        });
    });

    describe('getObjectsForDepartmentAccess', () => {
        it('should create objects for department access', () => {
            const accesses = [{
                Data: {
                    HasAccess: ko.observable(true),
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: []
                    };
                }),
                contextIds: []
            }];

            const result = customViewModel.getObjectsForDepartmentAccess(accesses);
            expect(result).toBeDefined();
        });
    });

    describe('getObjectsForModelAccess', () => {
        it('should create objects for model access', () => {
            const accesses = [{
                Data: {
                    HasAccess: ko.observable(true),
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: []
                    };
                }),
                contextIds: []
            }];

            const result = customViewModel.getObjectsForModelAccess(accesses);
            expect(result).toBeDefined();
        });
    });

    describe('getObjectsForVehicleAccess', () => {
        it('should create objects for vehicle access', () => {
            const accesses = [{
                Data: {
                    HasAccess: ko.observable(true),
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            HasAccess: ko.observable(true),
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: []
                    };
                }),
                contextIds: []
            }];

            const result = customViewModel.getObjectsForVehicleAccess(accesses);
            expect(result).toBeDefined();
        });
    });

    describe('onBeforeSave', () => {
        it('should handle sites tab', () => {
            viewModel.StatusData.CurrentTabIndex(1);
            viewModel.PersonObject().getPersonToSiteVehicleNormalAccessViewItems = vi.fn().mockReturnValue([{
                Data: {
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: ['test-context']
                    };
                }),
                contextIds: ['test-context']
            }]);

            customViewModel.onBeforeSave();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
        });

        it('should handle departments tab', () => {
            viewModel.StatusData.CurrentTabIndex(2);
            viewModel.PersonObject().getPersonToDepartmentVehicleNormalAccessViewItems = vi.fn().mockReturnValue([{
                Data: {
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: ['test-context']
                    };
                }),
                contextIds: ['test-context']
            }]);

            customViewModel.onBeforeSave();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
        });

        it('should handle models tab', () => {
            viewModel.StatusData.CurrentTabIndex(3);
            viewModel.PersonObject().getPersonToModelVehicleNormalAccessViewItems = vi.fn().mockReturnValue([{
                Data: {
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: ['test-context']
                    };
                }),
                contextIds: ['test-context']
            }]);

            customViewModel.onBeforeSave();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
        });

        it('should handle vehicles tab', () => {
            viewModel.StatusData.CurrentTabIndex(4);
            viewModel.PersonObject().getPersonToPerVehicleNormalAccessViewItems = vi.fn().mockReturnValue([{
                Data: {
                    IsDirty: vi.fn().mockReturnValue(true)
                },
                Clone: vi.fn().mockImplementation(function () {
                    return {
                        Data: {
                            IsDirty: vi.fn().mockReturnValue(true)
                        },
                        contextIds: ['test-context']
                    };
                }),
                contextIds: ['test-context']
            }]);

            customViewModel.onBeforeSave();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
        });
    });

    describe('onAccessChangedSuccess', () => {
        it('should reload all data collections', () => {
            const loadSiteSpy = vi.spyOn(viewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToSiteVehicleNormalAccessViewObjectCollection');
            const loadDeptSpy = vi.spyOn(viewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection');
            const loadModelSpy = vi.spyOn(viewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToModelVehicleNormalAccessViewObjectCollection');
            const loadVehicleSpy = vi.spyOn(viewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToPerVehicleNormalAccessViewObjectCollection');

            customViewModel.onAccessChangedSuccess();

            expect(loadSiteSpy).toHaveBeenCalled();
            expect(loadDeptSpy).toHaveBeenCalled();
            expect(loadModelSpy).toHaveBeenCalled();
            expect(loadVehicleSpy).toHaveBeenCalled();
            expect(viewModel.EndEdit).toHaveBeenCalled();
            expect(viewModel.StatusData.IsBusy).toHaveBeenCalledWith(false);
        });
    });

    describe('onAccessChangedError', () => {
        it('should show error and set busy to false', () => {
            viewModel.StatusData.IsBusy = vi.fn();
            customViewModel.onAccessChangedError();
            expect(viewModel.ShowError).toHaveBeenCalledWith('Failed to change vehicle access', 'Error');
            expect(viewModel.StatusData.IsBusy).toHaveBeenCalledWith(false);
        });
    });

    describe('onNewSaveSuccess', () => {
        it('should reload all data collections', () => {
            const loadSiteSpy = vi.spyOn(viewModel.PersonToSiteVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToSiteVehicleNormalAccessViewObjectCollection');
            const loadDeptSpy = vi.spyOn(viewModel.PersonToDepartmentVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToDepartmentVehicleNormalAccessViewObjectCollection');
            const loadModelSpy = vi.spyOn(viewModel.PersonToModelVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToModelVehicleNormalAccessViewObjectCollection');
            const loadVehicleSpy = vi.spyOn(viewModel.PersonToPerVehicleNormalAccessViewItemsListViewModel, 'LoadPersonToPerVehicleNormalAccessViewObjectCollection');

            customViewModel.onNewSaveSuccess();

            expect(loadSiteSpy).toHaveBeenCalled();
            expect(loadDeptSpy).toHaveBeenCalled();
            expect(loadModelSpy).toHaveBeenCalled();
            expect(loadVehicleSpy).toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
            expect(viewModel.EndEdit).toHaveBeenCalled();
        });
    });

    describe('onNewSaveError', () => {
        it('should show error and set busy to false', () => {
            customViewModel.onNewSaveError('test error');
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
            expect(viewModel.ShowError).toHaveBeenCalledWith('Failed to save vehicle access', 'Error');
        });
    });
}); 