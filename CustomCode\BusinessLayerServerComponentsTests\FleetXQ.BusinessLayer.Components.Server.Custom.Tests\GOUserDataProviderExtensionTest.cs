using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class GOUserDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"GOUserDataProviderExtensionTest-{Guid.NewGuid()}";
        private GOUserDataProviderExtension _goUserDataProviderExtension;
        private Mock<IDataProviderExtensionProvider> _mockDataProvider;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add necessary services for testing
            services.AddTransient<GOUserDataProviderExtension>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            _goUserDataProviderExtension = _serviceProvider.GetRequiredService<GOUserDataProviderExtension>();
            _mockDataProvider = new Mock<IDataProviderExtensionProvider>();
            _goUserDataProviderExtension.Init(_mockDataProvider.Object);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            // Create test region (required for dealer)
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            // Create test dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test Dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            // Create test customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            // Create test timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.CustomerId = customer.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_SetsWebsiteAccessLevelValue_WhenWebsiteAccessLevelIsDepartment()
        {
            // Arrange
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser1";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "TestPassword123!";
            goUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Department;
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.FirstName = "Test";
            goUser.LastName = "User";

            // Act - Save the GOUser which will trigger the OnBeforeSave event
            var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            // Assert
            Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)0), "WebsiteAccessLevelValue should be set to 0 for Department level");
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_SetsWebsiteAccessLevelValue_WhenWebsiteAccessLevelIsSite()
        {
            // Arrange
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser2";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "TestPassword123!";
            goUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Site;
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.FirstName = "Test";
            goUser.LastName = "User";

            // Act - Save the GOUser which will trigger the OnBeforeSave event
            var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            // Assert
            Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)1), "WebsiteAccessLevelValue should be set to 1 for Site level");
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_SetsWebsiteAccessLevelValue_WhenWebsiteAccessLevelIsCustomer()
        {
            // Arrange
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser3";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "TestPassword123!";
            goUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Customer;
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.FirstName = "Test";
            goUser.LastName = "User";

            // Act - Save the GOUser which will trigger the OnBeforeSave event
            var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            // Assert
            Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)2), "WebsiteAccessLevelValue should be set to 2 for Customer level");
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_UpdatesWebsiteAccessLevelValue_WhenWebsiteAccessLevelChanges()
        {
            // Arrange
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser4";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "TestPassword123!";
            goUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Department;
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.FirstName = "Test";
            goUser.LastName = "User";

            // Act - First save with Department level
            var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            // Assert - Should be set to Department level (0)
            Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)0), "WebsiteAccessLevelValue should be set to 0 for Department level");

            // Act - Change to Customer level and save again
            savedGoUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Customer;
            var updatedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(savedGoUser, skipSecurity: true);

            // Assert - Should be updated to Customer level (2)
            Assert.That(updatedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)2), "WebsiteAccessLevelValue should be updated to 2 for Customer level");
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_HandlesAllWebsiteAccessLevelValues()
        {
            // Arrange
            var testCases = new[]
            {
                new { Level = WebsiteAccessLevelEnum.Department, ExpectedValue = (short)0 },
                new { Level = WebsiteAccessLevelEnum.Site, ExpectedValue = (short)1 },
                new { Level = WebsiteAccessLevelEnum.Customer, ExpectedValue = (short)2 }
            };

            foreach (var testCase in testCases)
            {
                var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
                goUser.Id = Guid.NewGuid();
                goUser.UserName = $"testuser_{testCase.Level}";
                goUser.EmailAddress = $"test.user.{testCase.Level}@example.com";
                goUser.Password = "TestPassword123!";
                goUser.WebsiteAccessLevel = testCase.Level;
                goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
                goUser.FirstName = "Test";
                goUser.LastName = "User";

                // Act - Save the GOUser which will trigger the OnBeforeSave event
                var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

                // Assert
                Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo(testCase.ExpectedValue),
                    $"WebsiteAccessLevelValue should be set to {testCase.ExpectedValue} for {testCase.Level} level");
            }
        }

        [Test]
        public async Task DataProvider_OnBeforeSave_DoesNotModifyOtherProperties()
        {
            // Arrange
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser5";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "TestPassword123!";
            goUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Site;
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.FirstName = "Test";
            goUser.LastName = "User";

            var originalUserName = goUser.UserName;
            var originalEmailAddress = goUser.EmailAddress;
            var originalFirstName = goUser.FirstName;
            var originalLastName = goUser.LastName;

            // Act - Save the GOUser which will trigger the OnBeforeSave event
            var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            // Assert
            Assert.That(savedGoUser.UserName, Is.EqualTo(originalUserName), "UserName should not be modified");
            Assert.That(savedGoUser.EmailAddress, Is.EqualTo(originalEmailAddress), "EmailAddress should not be modified");
            Assert.That(savedGoUser.FirstName, Is.EqualTo(originalFirstName), "FirstName should not be modified");
            Assert.That(savedGoUser.LastName, Is.EqualTo(originalLastName), "LastName should not be modified");
            Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)1), "Only WebsiteAccessLevelValue should be modified");
            // Note: Password is intentionally not checked as it gets hashed during save for security reasons
        }

        [Test]
        public async Task DataProvider_OnBeforeSaveDataSet_SetsWebsiteAccessLevelValue_WhenWebsiteAccessLevelIsSet()
        {
            // Arrange
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser6";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "TestPassword123!";
            goUser.WebsiteAccessLevel = WebsiteAccessLevelEnum.Customer;
            goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
            goUser.FirstName = "Test";
            goUser.LastName = "User";

            // Act - Save the GOUser which will trigger the OnBeforeSaveDataSet event
            var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

            // Assert
            Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo((short)2), "WebsiteAccessLevelValue should be set to 2 for Customer level in OnBeforeSaveDataSet");
        }

        [Test]
        public async Task DataProvider_OnBeforeSaveDataSet_HandlesAllWebsiteAccessLevelValues()
        {
            // Arrange
            var testCases = new[]
            {
                new { Level = WebsiteAccessLevelEnum.Department, ExpectedValue = (short)0 },
                new { Level = WebsiteAccessLevelEnum.Site, ExpectedValue = (short)1 },
                new { Level = WebsiteAccessLevelEnum.Customer, ExpectedValue = (short)2 }
            };

            foreach (var testCase in testCases)
            {
                var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
                goUser.Id = Guid.NewGuid();
                goUser.UserName = $"testuser_dataset_{testCase.Level}";
                goUser.EmailAddress = $"test.user.dataset.{testCase.Level}@example.com";
                goUser.Password = "TestPassword123!";
                goUser.WebsiteAccessLevel = testCase.Level;
                goUser.PreferredLocale = LocaleEnum.EnglishUnitedStates;
                goUser.FirstName = "Test";
                goUser.LastName = "User";

                // Act - Save the GOUser which will trigger the OnBeforeSaveDataSet event
                var savedGoUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser, skipSecurity: true);

                // Assert
                Assert.That(savedGoUser.WebsiteAccessLevelValue, Is.EqualTo(testCase.ExpectedValue),
                    $"WebsiteAccessLevelValue should be set to {testCase.ExpectedValue} for {testCase.Level} level in OnBeforeSaveDataSet");
            }
        }
    }
}