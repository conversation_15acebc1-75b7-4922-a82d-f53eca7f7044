using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class VehicleToPreOpChecklistViewDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"VehicleToPreOpChecklistViewDataProviderExtensionTest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _siteId;
        private Guid _timeZoneId;
        private Guid _dealerId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // No need to register the extension as it will be triggered automatically
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateCommonTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task<VehicleDataObject> CreateTestVehicleAsync(string vehicleName, Guid departmentId, Guid modelId)
        {
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID1";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = $"test_{module.Id.ToString("N")}";
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = _customerId;
            vehicle.DepartmentId = departmentId;
            vehicle.ModelId = modelId;
            vehicle.SiteId = _siteId;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = vehicleName;
            vehicle.SerialNo = $"Test Serial No {vehicleName}";
            return await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
        }

        private async Task CreateCommonTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);
            _dealerId = dealer.Id;

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);
            _timeZoneId = timeZone.Id;

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);
            _siteId = site.Id;
        }

        [Test]
        public async Task OnBeforeSave_NewVehicleToPreOp_CreatesDepartmentChecklistAndUpdatesOrder()
        {
            // Arrange
            // Create department and model for this test
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = _siteId;
            department.Name = "Test Department 1";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model 1";
            model.DealerId = _dealerId;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            var vehicle = await CreateTestVehicleAsync("Test Vehicle 1", department.Id, model.Id);
            Assert.That(vehicle, Is.Not.Null, "Test data setup failed: No vehicle found.");

            var preOpChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preOpChecklist.Initialize(Guid.NewGuid());
            preOpChecklist.Order = 1;
            preOpChecklist.Active = true;
            preOpChecklist.Question = "Question";
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOpChecklist);

            var vehicleToPreOp = _serviceProvider.GetRequiredService<VehicleToPreOpChecklistViewDataObject>();
            vehicleToPreOp.VehicleId = vehicle.Id;
            vehicleToPreOp.PreOperationalChecklistId = preOpChecklist.Id;

            // Act
            await _dataFacade.VehicleToPreOpChecklistViewDataProvider.SaveAsync(vehicleToPreOp);

            // Assert
            var savedVehicleToPreOp = await _dataFacade.VehicleToPreOpChecklistViewDataProvider.GetAsync(vehicleToPreOp);
            Assert.That(savedVehicleToPreOp, Is.Not.Null);

            var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null,
                "DepartmentId == @0 and ModelId == @1",
                new object[] { vehicle.DepartmentId, vehicle.ModelId },
                skipSecurity: true)).FirstOrDefault();
            Assert.That(departmentChecklist, Is.Not.Null, "Department checklist should be created");

            var updatedPreOpChecklist = await _dataFacade.PreOperationalChecklistDataProvider.GetAsync(preOpChecklist);
            Assert.That(updatedPreOpChecklist.SiteChecklistId, Is.EqualTo(departmentChecklist.Id));
        }

        [Test]
        public async Task OnBeforeSave_NewVehicleToPreOp_WithExistingDepartmentChecklist_UsesExistingChecklist()
        {
            // Arrange
            // Create department and model for this test
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = _siteId;
            department.Name = "Test Department 2";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model 2";
            model.DealerId = _dealerId;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create department checklist first
            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.DepartmentId = department.Id;
            departmentChecklist.ModelId = model.Id;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            var vehicle = await CreateTestVehicleAsync("Test Vehicle 2", department.Id, model.Id);
            Assert.That(vehicle, Is.Not.Null, "Test data setup failed: No vehicle found.");

            // Update vehicle with the department checklist ID
            vehicle.DepartmentChecklistId = departmentChecklist.Id;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            var preOpChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preOpChecklist.Initialize(Guid.NewGuid());
            preOpChecklist.Order = 1;
            preOpChecklist.Active = true;
            preOpChecklist.Question = "Question";
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOpChecklist);

            var vehicleToPreOp = _serviceProvider.GetRequiredService<VehicleToPreOpChecklistViewDataObject>();
            vehicleToPreOp.VehicleId = vehicle.Id;
            vehicleToPreOp.PreOperationalChecklistId = preOpChecklist.Id;

            // Act
            await _dataFacade.VehicleToPreOpChecklistViewDataProvider.SaveAsync(vehicleToPreOp);

            // Assert
            var savedVehicleToPreOp = await _dataFacade.VehicleToPreOpChecklistViewDataProvider.GetAsync(vehicleToPreOp);
            Assert.That(savedVehicleToPreOp, Is.Not.Null);

            var updatedPreOpChecklist = await _dataFacade.PreOperationalChecklistDataProvider.GetAsync(preOpChecklist);
            Assert.That(updatedPreOpChecklist.SiteChecklistId, Is.EqualTo(vehicle.DepartmentChecklistId));

            // Verify no new department checklist was created
            var departmentChecklists = await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null,
                "DepartmentId == @0 and ModelId == @1",
                new object[] { vehicle.DepartmentId, vehicle.ModelId },
                skipSecurity: true);
            Assert.That(departmentChecklists.Count(), Is.EqualTo(1), "No new department checklist should be created");
            Assert.That(departmentChecklists.First().Id, Is.EqualTo(vehicle.DepartmentChecklistId), "The existing department checklist should be used");
        }

        [Test]
        public async Task OnBeforeSave_WithOrderConflict_AssignsNextAvailableOrder()
        {
            // Arrange
            // Create department and model for this test
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = _siteId;
            department.Name = "Test Department 3";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model 3";
            model.DealerId = _dealerId;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create department checklist first
            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.DepartmentId = department.Id;
            departmentChecklist.ModelId = model.Id;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            var vehicle = await CreateTestVehicleAsync("Test Vehicle 3", department.Id, model.Id);
            Assert.That(vehicle, Is.Not.Null, "Test data setup failed: No vehicle found.");

            // Update vehicle with the department checklist ID
            vehicle.DepartmentChecklistId = departmentChecklist.Id;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Create first question with order 1
            var firstPreOpChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            firstPreOpChecklist.Initialize(Guid.NewGuid());
            firstPreOpChecklist.Order = 1;
            firstPreOpChecklist.Active = true;
            firstPreOpChecklist.Question = "First Question";
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(firstPreOpChecklist);

            // Create first vehicle to pre-op relationship
            var firstVehicleToPreOp = _serviceProvider.GetRequiredService<VehicleToPreOpChecklistViewDataObject>();
            firstVehicleToPreOp.VehicleId = vehicle.Id;
            firstVehicleToPreOp.PreOperationalChecklistId = firstPreOpChecklist.Id;
            await _dataFacade.VehicleToPreOpChecklistViewDataProvider.SaveAsync(firstVehicleToPreOp);

            // Create second question with same order
            var secondPreOpChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            secondPreOpChecklist.Initialize(Guid.NewGuid());
            secondPreOpChecklist.Order = 1; // Same order as first question
            secondPreOpChecklist.Active = true;
            secondPreOpChecklist.Question = "Second Question";
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(secondPreOpChecklist);

            // Create second vehicle to pre-op relationship
            var secondVehicleToPreOp = _serviceProvider.GetRequiredService<VehicleToPreOpChecklistViewDataObject>();
            secondVehicleToPreOp.VehicleId = vehicle.Id;
            secondVehicleToPreOp.PreOperationalChecklistId = secondPreOpChecklist.Id;

            // Act
            await _dataFacade.VehicleToPreOpChecklistViewDataProvider.SaveAsync(secondVehicleToPreOp);

            // Assert
            var updatedSecondPreOpChecklist = await _dataFacade.PreOperationalChecklistDataProvider.GetAsync(secondPreOpChecklist);
            Assert.That(updatedSecondPreOpChecklist.Order, Is.EqualTo(2), "Second question should be assigned order 2 due to conflict");
        }

        [Test]
        public async Task OnBeforeSave_WithInactiveQuestion_ReusesOrder()
        {
            // Arrange
            // Create department and model for this test
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = _siteId;
            department.Name = "Test Department 4";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model 4";
            model.DealerId = _dealerId;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create department checklist first
            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.DepartmentId = department.Id;
            departmentChecklist.ModelId = model.Id;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            var vehicle = await CreateTestVehicleAsync("Test Vehicle 4", department.Id, model.Id);
            Assert.That(vehicle, Is.Not.Null, "Test data setup failed: No vehicle found.");

            // Update vehicle with the department checklist ID
            vehicle.DepartmentChecklistId = departmentChecklist.Id;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Create first question with order 1 and mark it as inactive
            var firstPreOpChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            firstPreOpChecklist.Initialize(Guid.NewGuid());
            firstPreOpChecklist.Order = 1;
            firstPreOpChecklist.Active = false; // Mark as inactive
            firstPreOpChecklist.Question = "First Question (Inactive)";
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(firstPreOpChecklist);

            // Create first vehicle to pre-op relationship
            var firstVehicleToPreOp = _serviceProvider.GetRequiredService<VehicleToPreOpChecklistViewDataObject>();
            firstVehicleToPreOp.VehicleId = vehicle.Id;
            firstVehicleToPreOp.PreOperationalChecklistId = firstPreOpChecklist.Id;
            await _dataFacade.VehicleToPreOpChecklistViewDataProvider.SaveAsync(firstVehicleToPreOp);

            // Create second question with same order but active
            var secondPreOpChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            secondPreOpChecklist.Initialize(Guid.NewGuid());
            secondPreOpChecklist.Order = 1; // Same order as first (inactive) question
            secondPreOpChecklist.Active = true;
            secondPreOpChecklist.Question = "Second Question (Active)";
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(secondPreOpChecklist);

            // Create second vehicle to pre-op relationship
            var secondVehicleToPreOp = _serviceProvider.GetRequiredService<VehicleToPreOpChecklistViewDataObject>();
            secondVehicleToPreOp.VehicleId = vehicle.Id;
            secondVehicleToPreOp.PreOperationalChecklistId = secondPreOpChecklist.Id;

            // Act
            await _dataFacade.VehicleToPreOpChecklistViewDataProvider.SaveAsync(secondVehicleToPreOp);

            // Assert
            var updatedSecondPreOpChecklist = await _dataFacade.PreOperationalChecklistDataProvider.GetAsync(secondPreOpChecklist);
            Assert.That(updatedSecondPreOpChecklist.Order, Is.EqualTo(1), "Second question should keep order 1 since first question is inactive");
        }
    }
}