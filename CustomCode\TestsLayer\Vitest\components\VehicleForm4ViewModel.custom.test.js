import { describe, it, expect, vi, beforeEach } from 'vitest';

// Import the custom ViewModel (assume global FleetXQ.Web.ViewModels is available in test env)
// If not, you may need to import or require the file directly.

// Helper to create a mock viewmodel structure
function createMockViewModel() {
    return {
        Commands: {
            ResetUnitMemoryCommand: vi.fn(),
        },
        ResetUnitMemory: vi.fn(),
        onResetUnitMemorySuccess: vi.fn(),
        setIsBusy: vi.fn(),
        contextId: 'test-context',
        controller: {
            applicationController: {
                showConfirmPopup: vi.fn(),
                showInfoMessageBox: vi.fn(),
            },
        },
    };
}

describe('VehicleForm4ViewModelCustom', () => {
    let viewmodel;
    let custom;

    beforeEach(() => {
        viewmodel = createMockViewModel();
        // Attach the custom class to the global object if not already
        if (!global.FleetXQ) global.FleetXQ = { Web: { ViewModels: {} } };
        if (!global.FleetXQ.Web.ViewModels.VehicleForm4ViewModelCustom) {
            require('../../../../CustomCode/WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehicleForm4ViewModel.custom.js');
        }
        custom = new global.FleetXQ.Web.ViewModels.VehicleForm4ViewModelCustom(viewmodel);
        if (custom.initialize) custom.initialize();
    });

    it('should override ResetUnitMemoryCommand in initialize', () => {
        expect(typeof viewmodel.Commands.ResetUnitMemoryCommand).toBe('function');
    });

    it('should show confirmation popup when ResetUnitMemoryCommand is called', () => {
        viewmodel.controller.applicationController.showConfirmPopup.mockClear();
        viewmodel.Commands.ResetUnitMemoryCommand();
        expect(viewmodel.controller.applicationController.showConfirmPopup).toHaveBeenCalled();
        const args = viewmodel.controller.applicationController.showConfirmPopup.mock.calls[0];
        expect(args[1]).toContain('reset unit memory');
        expect(args[2]).toContain('Reset Unit Memory');
        expect(typeof args[3]).toBe('function');
        expect(args[4]).toBe(viewmodel.contextId);
    });

    it('should call ResetUnitMemory if confirmed', () => {
        viewmodel.ResetUnitMemory.mockClear();
        viewmodel.controller.applicationController.showConfirmPopup.mockImplementation((_, __, ___, cb) => cb(true));

        viewmodel.Commands.ResetUnitMemoryCommand();

        expect(viewmodel.ResetUnitMemory).toHaveBeenCalled();
    });

    it('should NOT call ResetUnitMemory if not confirmed', () => {
        viewmodel.ResetUnitMemory.mockClear();
        viewmodel.controller.applicationController.showConfirmPopup.mockImplementation((_, __, ___, cb) => cb(false));

        viewmodel.Commands.ResetUnitMemoryCommand();

        expect(viewmodel.ResetUnitMemory).not.toHaveBeenCalled();
    });

    it('should show success message and set busy state in onResetUnitMemorySuccess', () => {
        viewmodel.controller.applicationController.showInfoMessageBox.mockClear();
        viewmodel.setIsBusy.mockClear();
        viewmodel.onResetUnitMemorySuccess({});

        expect(viewmodel.controller.applicationController.showInfoMessageBox).toHaveBeenCalledWith(
            "Unit memory has been successfully reset.",
            "Success",
            false
        );
        expect(viewmodel.setIsBusy).toHaveBeenCalledWith(false);
    });
}); 