﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{
    public class DefaultResponse
    {
        public DefaultResponse(int status, string detail)
        {
            this.status = status;
            this.detail = detail;
        }

        public int status  { get; set; }
        public String detail { get; set; }
    }
}
