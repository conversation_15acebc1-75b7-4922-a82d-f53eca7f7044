﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>
  <PropertyGroup>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Service Layer Custom</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices" />
    <PackageReference Include="GenerativeObjects.Services" />
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" />
    <PackageReference Include="Microsoft.CSharp" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
    <ProjectReference Include="..\DataLayer\FleetXQ.Data.DataObjects.Custom.csproj" />
    <ProjectReference Include="..\DataLayerDataProviderExtensions\FleetXQ.Data.DataProvidersExtensions.Custom.csproj" />
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
  </ItemGroup>
</Project>

