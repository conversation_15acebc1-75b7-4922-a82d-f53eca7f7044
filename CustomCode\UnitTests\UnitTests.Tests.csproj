<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="NUnit.Analyzers" />
    <PackageReference Include="coverlet.collector" />
    <PackageReference Include="NSubstitute" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Logging" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
    <ProjectReference Include="..\DataLayer\FleetXQ.Data.DataObjects.Custom.csproj" />
    <ProjectReference Include="..\DataLayerDataProviderExtensions\FleetXQ.Data.DataProvidersExtensions.Custom.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="FleetXQ.Data.DataObjects">
      <HintPath>..\..\GeneratedCode\DataLayer\bin\Debug\net8.0\FleetXQ.Data.DataObjects.dll</HintPath>
    </Reference>
    <Reference Include="FleetXQ.BusinessLayer.ORMSupportClasses">
      <HintPath>..\..\GeneratedCode\BusinessLayerORMSupportClasses\bin\Debug\net8.0\FleetXQ.BusinessLayer.ORMSupportClasses.dll</HintPath>
    </Reference>
    <Reference Include="GenerativeObjects.Practices.ORMSupportClasses">
      <HintPath>..\..\GeneratedCode\BusinessLayerORMSupportClasses\bin\Debug\net8.0\GenerativeObjects.Practices.ORMSupportClasses.dll</HintPath>
    </Reference>
    <Reference Include="GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components">
      <HintPath>..\..\GeneratedCode\BusinessLayerORMSupportClasses\bin\Debug\net8.0\GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project> 