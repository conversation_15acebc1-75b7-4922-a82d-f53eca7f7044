﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class TodaysImpactViewDataProvider : DataProvider<TodaysImpactViewDataObject>
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TodaysImpactViewDataProvider> _logger;

        public TodaysImpactViewDataProvider(
            IServiceProvider serviceProvider,
            IDataProviderTransaction transaction,
            IEntityDataProvider entityDataProvider,
            IDataProviderDispatcher<TodaysImpactViewDataObject> dispatcher,
            IDataProviderDeleteStrategy dataProviderDeleteStrategy,
            IAutoInclude autoInclude,
            IThreadContext threadContext,
            IDataProviderTransaction dataProviderTransaction,
            IConfiguration configuration,
            ILogger<TodaysImpactViewDataProvider> logger)
            : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
            _logger = logger;
        }

        protected override async Task<DataObjectCollection<TodaysImpactViewDataObject>> DoGetCollectionAsync(
            LambdaExpression securityFilterExpression,
            string filterPredicate,
            object[] filterArguments,
            string orderByPredicate,
            int pageNumber,
            int pageSize,
            List<string> includes,
            IObjectsDataSet context,
            Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<TodaysImpactViewDataObject>();
            result.ObjectsDataSet = context;

            try
            {
                _logger.LogInformation("[TodaysImpactViewDataProvider] Starting DoGetCollectionAsync with filter arguments: {FilterArguments}",
                    filterArguments != null ? string.Join(", ", filterArguments.Select(a => a?.ToString() ?? "null")) : "null");
                _logger.LogInformation("[TodaysImpactViewDataProvider] Filter predicate: {FilterPredicate}", filterPredicate ?? "null");

                // Log all parameters
                if (parameters != null)
                {
                    _logger.LogInformation("[TodaysImpactViewDataProvider] Parameters dictionary contains {Count} items", parameters.Count);
                    foreach (var param in parameters)
                    {
                        _logger.LogInformation("[TodaysImpactViewDataProvider] Parameter Key: {Key}, Value Type: {Type}, Raw Value: {Value}",
                            param.Key,
                            param.Value?.GetType().FullName ?? "null",
                            param.Value);
                    }
                }
                else
                {
                    _logger.LogWarning("[TodaysImpactViewDataProvider] Parameters dictionary is null");
                }

                // Get parameters from filter arguments
                DateTime? referenceDate = null;
                Guid? customerId = null;
                Guid? siteId = null;
                Guid? departmentId = null;

                if (filterArguments != null)
                {
                    _logger.LogInformation("[TodaysImpactViewDataProvider] Filter arguments length: {Length}", filterArguments.Length);

                    // Log all filter arguments for debugging
                    for (int i = 0; i < filterArguments.Length; i++)
                    {
                        _logger.LogInformation("[TodaysImpactViewDataProvider] Filter argument {Index}: Type = {Type}, Value = {Value}",
                            i,
                            filterArguments[i]?.GetType().FullName ?? "null",
                            filterArguments[i]?.ToString() ?? "null");
                    }

                    // When only one parameter is passed and it's a DateTime, it's the reference date
                    if (filterArguments.Length == 1 && filterArguments[0] is DateTime)
                    {
                        referenceDate = (DateTime)filterArguments[0];
                        _logger.LogInformation("[TodaysImpactViewDataProvider] Using single date parameter: {Date}", referenceDate);
                    }
                    else
                    {
                        // Process each parameter based on its type
                        for (int i = 0; i < filterArguments.Length; i++)
                        {
                            if (filterArguments[i] == null) continue;

                            // If it's a DateTime, it's our reference date
                            if (filterArguments[i] is DateTime dateValue)
                            {
                                referenceDate = dateValue;
                                _logger.LogInformation("[TodaysImpactViewDataProvider] Found date at index {Index}: {Date}", i, referenceDate);
                            }
                            // If it's a Guid, assign it to the appropriate ID based on order
                            else if (filterArguments[i] is Guid guidValue)
                            {
                                if (customerId == null)
                                {
                                    customerId = guidValue;
                                    _logger.LogInformation("[TodaysImpactViewDataProvider] Found CustomerId at index {Index}: {Id}", i, customerId);
                                }
                                else if (siteId == null)
                                {
                                    siteId = guidValue;
                                    _logger.LogInformation("[TodaysImpactViewDataProvider] Found SiteId at index {Index}: {Id}", i, siteId);
                                }
                                else if (departmentId == null)
                                {
                                    departmentId = guidValue;
                                    _logger.LogInformation("[TodaysImpactViewDataProvider] Found DepartmentId at index {Index}: {Id}", i, departmentId);
                                }
                            }
                            else
                            {
                                _logger.LogWarning("[TodaysImpactViewDataProvider] Unexpected parameter type at index {Index}: {Type}", 
                                    i, 
                                    filterArguments[i].GetType().FullName);
                            }
                        }
                    }

                    _logger.LogInformation("[TodaysImpactViewDataProvider] Final parameter values: CustomerId = {CustomerId}, SiteId = {SiteId}, DepartmentId = {DepartmentId}, ReferenceDate = {ReferenceDate}",
                        customerId,
                        siteId,
                        departmentId,
                        referenceDate);
                }

                using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
                {
                    _logger.LogDebug("[TodaysImpactViewDataProvider] Opening SQL connection");
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("GetTodaysImpact", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters in the order expected by the stored procedure
                        command.Parameters.AddWithValue("@CustomerId", customerId.HasValue ? (object)customerId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@SiteId", siteId.HasValue ? (object)siteId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@DepartmentId", departmentId.HasValue ? (object)departmentId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@ReferenceDate", referenceDate.HasValue ? (object)referenceDate.Value : DBNull.Value);

                        // Log final parameter collection for debugging
                        foreach (SqlParameter param in command.Parameters)
                        {
                            _logger.LogDebug("[TodaysImpactViewDataProvider] Parameter {Name}: Type = {Type}, Value = {Value}, SqlDbType = {SqlDbType}", 
                                param.ParameterName,
                                param.Value?.GetType().FullName ?? "null",
                                param.Value?.ToString() ?? "null",
                                param.SqlDbType);
                        }

                        _logger.LogDebug("[TodaysImpactViewDataProvider] Executing stored procedure");
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    try
                                    {
                                        var entity = _serviceProvider.GetRequiredService<TodaysImpactViewDataObject>();
                                        entity.ObjectsDataSet = context;

                                        // Log column ordinals for debugging
                                        var columnOrdinals = new Dictionary<string, int>
                                        {
                                            { "Id", reader.GetOrdinal("Id") },
                                            { "DealerId", reader.GetOrdinal("DealerId") },
                                            { "CustomerId", reader.GetOrdinal("CustomerId") },
                                            { "SiteId", reader.GetOrdinal("SiteId") },
                                            { "DepartmentId", reader.GetOrdinal("DepartmentId") },
                                            { "ImpactLevel", reader.GetOrdinal("ImpactLevel") },
                                            { "ImpactType", reader.GetOrdinal("ImpactType") },
                                            { "NumberOfImpacts", reader.GetOrdinal("NumberOfImpacts") }
                                        };
                                        _logger.LogDebug("[TodaysImpactViewDataProvider] Column ordinals: {Ordinals}", columnOrdinals);

                                        // Set Id first
                                        entity.Id = reader.GetGuid(columnOrdinals["Id"]);

                                        // Now mark as not new after Id is set
                                        entity.IsNew = false;

                                        if (!reader.IsDBNull(columnOrdinals["DealerId"]))
                                            entity.DealerId = reader.GetGuid(columnOrdinals["DealerId"]);

                                        if (!reader.IsDBNull(columnOrdinals["CustomerId"]))
                                            entity.CustomerId = reader.GetGuid(columnOrdinals["CustomerId"]);

                                        if (!reader.IsDBNull(columnOrdinals["SiteId"]))
                                            entity.SiteId = reader.GetGuid(columnOrdinals["SiteId"]);

                                        if (!reader.IsDBNull(columnOrdinals["DepartmentId"]))
                                            entity.DepartmentId = reader.GetGuid(columnOrdinals["DepartmentId"]);

                                        if (!reader.IsDBNull(columnOrdinals["ImpactLevel"]))
                                            entity.ImpactLevel = reader.GetString(columnOrdinals["ImpactLevel"]);

                                        if (!reader.IsDBNull(columnOrdinals["ImpactType"]))
                                            entity.ImpactType = (ImpactTypeEnum)reader.GetInt32(columnOrdinals["ImpactType"]);

                                        if (!reader.IsDBNull(columnOrdinals["NumberOfImpacts"]))
                                        {
                                            var rawValue = reader.GetValue(columnOrdinals["NumberOfImpacts"]);
                                            _logger.LogDebug("[TodaysImpactViewDataProvider] NumberOfImpacts raw value type: {Type}, value: {Value}", 
                                                rawValue?.GetType().FullName ?? "null", 
                                                rawValue);
                                            var value = Convert.ToInt32(rawValue);
                                            entity.NumberOfImpacts = value <= Int16.MaxValue ? (Int16)value : Int16.MaxValue;
                                        }

                                        result.Add(entity);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, "[TodaysImpactViewDataProvider] Error mapping data reader to entity");
                                        throw new GOServerException("Error mapping data reader to entity", "Error mapping data reader to entity", ex);
                                    }
                                }
                            }
                            else
                            {
                                _logger.LogInformation("[TodaysImpactViewDataProvider] No rows returned from stored procedure");
                            }
                        }
                    }
                }

                _logger.LogInformation("[TodaysImpactViewDataProvider] Successfully completed DoGetCollectionAsync. Returning {Count} items", result.Count);
                return result;
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "[TodaysImpactViewDataProvider] SQL error in DoGetCollectionAsync");
                throw new GOServerException("Database error in GetTodaysImpact", ex.Message, ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[TodaysImpactViewDataProvider] Unexpected error in DoGetCollectionAsync");
                throw new GOServerException("Error in GetTodaysImpact", "An unexpected error occurred", ex);
            }
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task DoDeleteAsync(TodaysImpactViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<TodaysImpactViewDataObject> DoGetAsync(TodaysImpactViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<TodaysImpactViewDataObject> DoSaveAsync(TodaysImpactViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
