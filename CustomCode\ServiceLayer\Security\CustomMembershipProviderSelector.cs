using System;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;

namespace CustomCode.ServiceLayer.Security
{
    public class CustomMembershipProviderSelector : IMembershipProviderSelector
    {
        private readonly IDataFacade _dataFacade;

        public CustomMembershipProviderSelector(IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
        }

        public async Task<string> GetMembershipProviderTypeAsync(string username)
        {
            try
            {
                if (string.IsNullOrEmpty(username))
                {
                    return "application"; // Default to application if no username provided
                }

                var existingUser = (await _dataFacade.GOUserDataProvider.GetCollectionAsync(
                    null, 
                    "(UserName == @0 || EmailAddress == @0)", // Simplified query
                    new object[] { username }, 
                    null, 
                    0, 
                    0, 
                    skipSecurity: true
                ))?.SingleOrDefault();

                if (existingUser != null)
                {
                    try
                    {
                        var person = await existingUser.LoadPersonAsync(skipSecurity: true);
                        if (person != null)
                        {
                            var customer = await person.LoadCustomerAsync(skipSecurity: true);
                            if (customer != null)
                            {
                                var ssoDetails = await customer.LoadCustomerSSODetailItemsAsync(skipSecurity: true);
                                if (ssoDetails != null && ssoDetails.Any())
                                {
                                    return "oauth";
                                }
                            }
                        }

                        // If we get here, the user is either Dealer or Super Admin user
                        return "application";
                    }
                    catch
                    {
                        // If any of the relationship loading fails, default to application
                        return "application";
                    }
                }

                // If user doesn't exist, default to oauth - they might be a new SSO user
                return "oauth";
            }
            catch (Exception ex)
            {
                throw new GOServerException("error", ex.Message);
                // If anything fails, default to application login
                return "application";
            }
        }
    }
}