describe("013 - Service-Tab", () => {
    // Use beforeEach to ensure login is handled before each test
    beforeEach(() => {
        cy.login(); // Assuming you have already implemented cy.login() globally
    });

    it("tests 013 - Service Tab", () => {
        // Access Vehicles
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

        // Search for a vehicle
        cy.get("input").first().type("Test");
        cy.get("[data-test-id='searchCommand']").click();
        cy.get("tr:nth-of-type(1) a").click();

        // Intercept the vehicle by ID API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/vehicle/byid/*')
            .as('getVehicleById');


        cy.wait('@getVehicleById', { timeout: 30000 });


        cy.get("[data-test-id='tab_link_1c3a90d5-6f63-449e-ae2d-bcae55b5778e'] > a > span:nth-of-type(1)").click();
        //cy.get("[data-test-id='edit_4918096e-fba8-45dd-90be-bb0058213159']").click();
        // Click the MODIFY button
        cy.get('[data-test-id="fa575ace-682c-4f85-be2c-8b13abf9f558"]').click();
        cy.get("[data-test-id='edit_4918096e-fba8-45dd-90be-bb0058213159']").clear().type("3400");
        //cy.get("[data-test-id='edit_f7b8b4b1-c9aa-4589-a01e-ad6a901fcafa']").click();
        cy.get("[data-test-id='edit_f7b8b4b1-c9aa-4589-a01e-ad6a901fcafa']").type("3");
        //cy.get("[data-test-id='edit_932ef885-c3ab-49a2-aeb1-f0eb2cd6e5ee']").click();
        //cy.get("[data-test-id='edit_932ef885-c3ab-49a2-aeb1-f0eb2cd6e5ee']").clear().type("500");
        //cy.get("[data-test-id='edit_06a3f9bd-6f11-408a-b610-472ca26980e5']").click();
        cy.get("[data-test-id='edit_06a3f9bd-6f11-408a-b610-472ca26980e5']").clear().type("Ttest (Updated)");
        cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']").click();
    });
});
