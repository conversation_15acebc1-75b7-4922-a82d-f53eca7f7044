# Temporary workaround to disable GitHub Packages for building
# This creates a temporary nuget.config that excludes GitHub sources

Write-Host "Creating temporary NuGet configuration without GitHub Packages..." -ForegroundColor Yellow

# Backup original config
if (Test-Path "nuget.config") {
    Copy-Item "nuget.config" "nuget.config.backup"
    Write-Host "✅ Backed up original nuget.config to nuget.config.backup" -ForegroundColor Green
}

# Create temporary config without GitHub source
$tempConfig = @"
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <clear />
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="local-dev" value="D:\dev\nuget-local" />
  </packageSources>
  
  <packageSourceMapping>
    <!-- All packages from nuget.org -->
    <packageSource key="nuget.org">
      <package pattern="*" />
    </packageSource>
    
    <!-- Local packages -->
    <packageSource key="local-dev">
      <package pattern="GenerativeObjects.*" />
      <package pattern="FleetXQ.*" />
    </packageSource>
  </packageSourceMapping>
  
  <config>
    <add key="automatic-package-restore" value="true" />
    <add key="skip-duplicate-packages" value="true" />
  </config>
</configuration>
"@

$tempConfig | Out-File -FilePath "nuget.config" -Encoding UTF8
Write-Host "✅ Created temporary nuget.config without GitHub Packages" -ForegroundColor Green

Write-Host "You can now try building with:" -ForegroundColor Cyan
Write-Host "  dotnet restore" -ForegroundColor White
Write-Host "  dotnet build" -ForegroundColor White

Write-Host "`nTo restore original configuration later, run:" -ForegroundColor Yellow
Write-Host "  Move-Item nuget.config.backup nuget.config -Force" -ForegroundColor White
