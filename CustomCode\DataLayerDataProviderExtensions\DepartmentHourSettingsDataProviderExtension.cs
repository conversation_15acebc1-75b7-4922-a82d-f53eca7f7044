﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using System;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom;

public class DepartmentHourSettingsDataProviderExtension : IDataProviderExtension<DepartmentHourSettingsDataObject>
{
    private readonly IDataFacade _dataFacade;
    private readonly IServiceProvider _serviceProvider;
    public DepartmentHourSettingsDataProviderExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
    {
        _dataFacade = dataFacade;
        _serviceProvider = serviceProvider;
    }

    public void Init(IDataProviderExtensionProvider dataProvider)
    {
        dataProvider.OnBeforeSaveDataSet += DataProvider_OnBeforeSaveDataSetAsync;
    }

    private async Task DataProvider_OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
    {
        var departmentHourSettings = e.Entity as DepartmentHourSettingsDataObject;

        // Monday
        departmentHourSettings.MondayAvailableHours = (int)(departmentHourSettings.MondayOpenHours ?? 0) + (int)(departmentHourSettings.MondayBreaktimeHours ?? 0);
        departmentHourSettings.MondayAvailableMinutes = (int)(departmentHourSettings.MondayOpenMinutes ?? 0) + (int)(departmentHourSettings.MondayBreaktimeMinutes ?? 0);

        // Tuesday
        departmentHourSettings.TuesdayAvailableHours = (int)(departmentHourSettings.TuesdayOpenHours ?? 0) + (int)(departmentHourSettings.TuesdayBreaktimeHours ?? 0);
        departmentHourSettings.TuesdayAvailableMinutes = (int)(departmentHourSettings.TuesdayOpenMinutes ?? 0) + (int)(departmentHourSettings.TuesdayBreaktimeMinutes ?? 0);

        // Wednesday
        departmentHourSettings.WednesdayAvailableHours = (int)(departmentHourSettings.WednesdayOpenHours ?? 0) + (int)(departmentHourSettings.WednesdayBreaktimeHours ?? 0);
        departmentHourSettings.WednesdayAvailableMinutes = (int)(departmentHourSettings.WednesdayOpenMinutes ?? 0) + (int)(departmentHourSettings.WednesdayBreaktimeMinutes ?? 0);

        // Thursday
        departmentHourSettings.ThursdayAvailableHours = (int)(departmentHourSettings.ThursdayOpenHours ?? 0) + (int)(departmentHourSettings.ThursdayBreaktimeHours ?? 0);
        departmentHourSettings.ThursdayAvailableMinutes = (int)(departmentHourSettings.ThursdayOpenMinutes ?? 0) + (int)(departmentHourSettings.ThursdayBreaktimeMinutes ?? 0);

        // Friday
        departmentHourSettings.FridayAvailableHours = (int)(departmentHourSettings.FridayOpenHours ?? 0) + (int)(departmentHourSettings.FridayBreaktimeHours ?? 0);
        departmentHourSettings.FridayAvailableMinutes = (int)(departmentHourSettings.FridayOpenMinutes ?? 0) + (int)(departmentHourSettings.FridayBreaktimeMinutes ?? 0);

        // Saturday
        departmentHourSettings.SaturdayAvailableHours = (int)(departmentHourSettings.SaturdayOpenHours ?? 0) + (int)(departmentHourSettings.SaturdayBreaktimeHours ?? 0);
        departmentHourSettings.SaturdayAvailableMinutes = (int)(departmentHourSettings.SaturdayOpenMinutes ?? 0) + (int)(departmentHourSettings.SaturdayBreaktimeMinutes ?? 0);

        // Sunday
        departmentHourSettings.SundayAvailableHours = (int)(departmentHourSettings.SundayOpenHours ?? 0) + (int)(departmentHourSettings.SundayBreaktimeHours ?? 0);
        departmentHourSettings.SundayAvailableMinutes = (int)(departmentHourSettings.SundayOpenMinutes ?? 0) + (int)(departmentHourSettings.SundayBreaktimeMinutes ?? 0);
    }
}
