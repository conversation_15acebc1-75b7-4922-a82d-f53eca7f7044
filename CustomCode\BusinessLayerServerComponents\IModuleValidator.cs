﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public interface IModuleValidator
    {
        System.Threading.Tasks.Task<ModuleDataObject> EnforceModuleForVehicleAsync(String IoTDeviceId);
    }
}
