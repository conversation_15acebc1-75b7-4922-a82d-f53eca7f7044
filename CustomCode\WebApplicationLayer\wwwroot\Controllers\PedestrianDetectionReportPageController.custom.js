(function () {
    FleetXQ.Web.Controllers.PedestrianDetectionReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds ? AllowedSiteIds.replace(/[{} ]/g, '').split(',') : [];
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;

            var parameterCount = 0;

            if (customerId != null) {
                configuration.filterPredicate = '(Driver.Person.CustomerId == @0 OR Vehicle.CustomerId == @0)';
                configuration.filterParameters = '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
                parameterCount++;
            }

            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters = configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += '(Driver.Person.SiteId == @' + parameterCount + ' OR Vehicle.SiteId == @' + parameterCount + ')';
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
                parameterCount++;
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            console.log('[Debug] getConfiguration called');
            var configuration = {};
            configuration.filterPredicate = '';
            configuration.filterParameters = [];

            var filter = self.controller.PedestrianDetectionHistoryFilterFormViewModel.PedestrianDetectionHistoryFilterObject().Data;
            console.log('[Debug] Filter data:', {
                CustomerId: filter.CustomerId(),
                SiteId: filter.SiteId(),
                DepartmentId: filter.DepartmentId(),
                MultiSearch: filter.MultiSearch(),
                StartTime: filter.StartTime(),
                EndTime: filter.EndTime()
            });

            var parameterCount = 0;
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            console.log('[Debug] Current user CustomerId:', customerId);

            // For non-admin users (with CustomerId), if no customer filter is selected, use default config
            if (filter.CustomerId() == null && customerId != null) {
                return self.getDefaultConfiguration();
            }

            // Handle Customer filter - for admin users or when specific customer is selected
            if (filter.CustomerId()) {
                configuration.filterPredicate += "(Driver.Person.CustomerId == @" + parameterCount + " OR Vehicle.CustomerId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.CustomerId()
                });
                parameterCount++;
            }

            // Handle Site filter
            if (filter.SiteId()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.SiteId == @" + parameterCount + " OR Vehicle.SiteId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.SiteId()
                });
                parameterCount++;
            }

            // Handle Department filter
            if (filter.DepartmentId()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.DepartmentId == @" + parameterCount + " OR Vehicle.DepartmentId == @" + parameterCount + ")";
                configuration.filterParameters.push({
                    "TypeName": "System.Guid",
                    "IsNullable": false,
                    "Value": filter.DepartmentId()
                });
                parameterCount++;
            }

            // Handle MultiSearch (Person's FullName)
            if (filter.MultiSearch()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "(Driver.Person.FirstName.Contains(@" + parameterCount + ") || Driver.Person.LastName.Contains(@" + (parameterCount + 1) + "))";
                configuration.filterParameters.push({
                    "TypeName": "System.String",
                    "IsNullable": true,
                    "Value": filter.MultiSearch()
                });
                configuration.filterParameters.push({
                    "TypeName": "System.String",
                    "IsNullable": true,
                    "Value": filter.MultiSearch()
                });
                parameterCount += 2;
            }

            // Handle StartTime filter
            if (filter.StartTime()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "Date >= @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.DateTime",
                    "IsNullable": false,
                    "Value": filter.StartTime()
                });
                parameterCount++;
            }

            // Handle EndTime filter
            if (filter.EndTime()) {
                if (configuration.filterPredicate) configuration.filterPredicate += " && ";
                configuration.filterPredicate += "Date <= @" + parameterCount;
                configuration.filterParameters.push({
                    "TypeName": "System.DateTime",
                    "IsNullable": false,
                    "Value": filter.EndTime()
                });
                parameterCount++;
            }

            // Convert parameters to string at the end
            configuration.filterParameters = JSON.stringify(configuration.filterParameters);

            console.log('[Debug] Final configuration:', {
                filterPredicate: configuration.filterPredicate,
                filterParameters: configuration.filterParameters
            });

            return configuration;
        };

        this.initialize = function () {
            console.log('[Debug] Initializing PedestrianDetectionReportPageController');
            
            // Override IsInEditMode to always return false (prevent edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            };
            
            // Add filterData function to the filter form view model
            self.controller.PedestrianDetectionHistoryFilterFormViewModel.filterData = function () {
                try {
                    console.log('[Debug] filterData called');
                    var configuration = self.getConfiguration();

                    // Use the same configuration for both export and grid
                    self.controller.PedestrianDetectionHistoryGridViewModel.exportFilterPredicate = configuration.filterPredicate;
                    self.controller.PedestrianDetectionHistoryGridViewModel.exportFilterParameters = configuration.filterParameters;

                    // Load the grid data
                    self.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
                } catch (e) {
                    console.error("[Error] Error filtering data:", e);
                }
            };

            // Load initial data for customer users
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            console.log('[Debug] Initial load - CustomerId:', customerId);
            if (customerId != null) {
                var configuration = self.getDefaultConfiguration();
                self.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
            } else {
                // For admin users, load with empty configuration to see all data
                var configuration = {
                    filterPredicate: '',
                    filterParameters: '[]'
                };
                self.controller.PedestrianDetectionHistoryGridViewModel.LoadPedestrianDetectionHistoryObjectCollection(configuration);
            }
        };
    }
}()); 