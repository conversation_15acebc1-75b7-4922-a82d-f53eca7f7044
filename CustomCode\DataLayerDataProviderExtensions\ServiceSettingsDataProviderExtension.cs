﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Globalization;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class ServiceSettingsDataProviderExtension : IDataProviderExtension<ServiceSettingsDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthentication _authentication;
        public ServiceSettingsDataProviderExtension(IDataFacade dataFacade, IServiceProvider serviceProvider, IAuthentication authentication)
        {

            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _authentication = authentication;
        }
        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += DataProvider_OnBeforeSaveDataSetAsync;
            dataProvider.OnAfterGet += DataProvider_OnAfterGet;
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            // Only process if the result is a collection of ServiceSettingsDataObject
            if (arg.Result is DataObjectCollection<ServiceSettingsDataObject> items && arg.PageNumber > 0 && arg.PageSize > 0)
            {
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();

                if (userClaims == null || userClaims.UserId == null)
                {
                    return;
                }

                var appUserClaims = userClaims as AppUserClaims;

                var preferredLocale = appUserClaims.UserPreferredLocale != null ? appUserClaims.UserPreferredLocale : appUserClaims.CustomerPreferredLocale;

                foreach (var item in items)
                {
                    try
                    {
                        var culture = !string.IsNullOrEmpty(preferredLocale) ? new CultureInfo(preferredLocale) : new CultureInfo("en-US");

                        if (item.LastServiceDate.HasValue)
                        {
                            item.LastServiceDateDisplay = item.LastServiceDate.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern}", culture);
                        }

                        if (item.NextServiceDate.HasValue)
                        {
                            item.NextServiceDateDisplay = item.NextServiceDate.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern}", culture);
                        }
                    }
                    catch (CultureNotFoundException)
                    {
                        // If the culture is invalid, just return without modifying the datetime
                        return;
                    }
                }
            }
        }

        private Task DataProvider_OnAfterGet(OnAfterGetEventArgs e)
        {
            var serviceSettings = e.Result as ServiceSettingsDataObject;

            if (serviceSettings == null)
            {
                return Task.CompletedTask;
            }

            double? hoursRemaining = null;
            if (serviceSettings.NextServiceDate.HasValue)
            {
                var timeDiff = serviceSettings.NextServiceDate.Value - DateTime.UtcNow;

                hoursRemaining = timeDiff.TotalHours;
            }

            if (serviceSettings.CurrentMeterReading.HasValue && serviceSettings.NextServiceType.HasValue)
            {
                if (!hoursRemaining.HasValue || serviceSettings.HoursToNextService < hoursRemaining.Value)
                {
                    hoursRemaining = serviceSettings.HoursToNextService;
                }
            }

            serviceSettings.ServiceStatus = hoursRemaining switch
            {
                < 5 => "Service is due in less than 5 hours or service is overdue",
                <= 25 => "Service is due in less than 25 hours",
                > 25 => "Service is due in more than 25 hours",
                _ => string.Empty
            };

            return Task.CompletedTask;

        }

        private async Task DataProvider_OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
        {
            var serviceSettings = e.Entity as ServiceSettingsDataObject;

            var oldServiceSettings = _serviceProvider.GetRequiredService<ServiceSettingsDataObject>();

            if (serviceSettings == null)
            {
                return;
            }

            oldServiceSettings.Id = serviceSettings.Id;
            oldServiceSettings = await _dataFacade.ServiceSettingsDataProvider.GetAsync(oldServiceSettings);

            if (serviceSettings.LastServiceDate != null)
            {
                // update CurremtMeterReading if CurrentMeterReadingHrs is updated
                if (oldServiceSettings != null && oldServiceSettings.CurrentMeterReadingHrs != serviceSettings.CurrentMeterReadingHrs)
                {
                    // turn hours to seconds for CurrentMeterReading
                    serviceSettings.CurrentMeterReading = (int)(serviceSettings.CurrentMeterReadingHrs * 3600);
                }

                if ((oldServiceSettings == null || oldServiceSettings.CurrentMeterReading != serviceSettings.CurrentMeterReading) && serviceSettings.CurrentMeterReading.HasValue)
                {
                    // turn to hours from seconds and truncate to 2 decimal places
                    serviceSettings.CurrentMeterReadingHrs = (double)Math.Round((double)serviceSettings.CurrentMeterReading / 3600, 2); 
                }

                if (oldServiceSettings == null || oldServiceSettings.LastServiceDate != serviceSettings.LastServiceDate)
                {
                    serviceSettings.LastServiceHours = serviceSettings.CurrentMeterReadingHrs;
                }


                if (serviceSettings.LastServiceDate.HasValue && serviceSettings.DateIntervalValue.HasValue)
                {
                    serviceSettings.NextServiceDate = serviceSettings.LastServiceDate.Value.Date.AddMonths((int)serviceSettings.DateIntervalValue.Value);
                }
            }

            if (serviceSettings.LastServiceHours.HasValue && serviceSettings.ServiceHoursInterval.HasValue)
            {
                serviceSettings.NextServiceType = serviceSettings.LastServiceHours.Value + (int)serviceSettings.ServiceHoursInterval.Value;
            }

            return;

        }
    }
}
