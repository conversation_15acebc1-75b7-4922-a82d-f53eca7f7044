using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class PersonAPITest : TestBase
    {
        private IDataFacade _dataFacade;
        private IPersonAPI _personAPI;
        private readonly string _testDatabaseName = $"PersonAPITest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _personAPI = _serviceProvider.GetRequiredService<IPersonAPI>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();

            var httpContextAccessor = _serviceProvider.GetRequiredService<IHttpContextAccessor>();
            var httpContext = new DefaultHttpContext();
            httpContext.RequestServices = _serviceProvider;
            httpContextAccessor.HttpContext = httpContext;
            var mockHttpContextAccessor = _serviceProvider.GetService<Mock<IHttpContextAccessor>>();
            mockHttpContextAccessor.Setup(x => x.HttpContext).Returns(httpContext);
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);
        }

        [Test]
        public async Task SoftDeleteAsync_UnknownPersonId_ThrowsException()
        {
            // Arrange
            var unknownPersonId = Guid.NewGuid();

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _personAPI.SoftDeleteAsync(unknownPersonId));

            Assert.That(exception.Message, Is.EqualTo($"unknow person id {unknownPersonId}"));
        }

        [Test]
        public async Task SoftDeleteAsync_PersonWithDriver_DeactivatesDriver()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test driver
            var driver = _serviceProvider.GetRequiredService<DriverDataObject>();
            driver.Id = Guid.NewGuid();
            driver.CustomerId = customer.Id;
            driver.SiteId = site.Id;
            driver.DepartmentId = department.Id;
            driver.Active = true;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Create test person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.DriverId = driver.Id;
            person.IsDriver = true;
            person.IsActiveDriver = true;
            person.FirstName = "Test";
            person.LastName = "Driver";
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Act
            var response = await _personAPI.SoftDeleteAsync(person.Id);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Is.True);

            // Verify driver was deactivated
            var updatedDriver = await _dataFacade.DriverDataProvider.GetAsync(driver);
            Assert.That(updatedDriver, Is.Not.Null);
            Assert.That(updatedDriver.Active, Is.False);

            // Verify person was soft deleted
            var deletedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(deletedPerson, Is.Not.Null);
            Assert.That(deletedPerson.DeletedAtUtc, Is.Not.Null);
            Assert.That(deletedPerson.IsActiveDriver, Is.False);
            Assert.That(deletedPerson.Supervisor, Is.False);
        }

        [Test]
        public async Task SoftDeleteAsync_PersonWithGOUser_DeletesGOUser()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test GOUser
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "password123"; // Required for GOUser
            goUser = await _dataFacade.GOUserDataProvider.SaveAsync(goUser);

            // Create test person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.GOUserId = goUser.Id;
            person.FirstName = "Test";
            person.LastName = "User";
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Act
            var response = await _personAPI.SoftDeleteAsync(person.Id);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Is.True);

            // Verify GOUser was deleted
            var deletedGOUser = await _dataFacade.GOUserDataProvider.GetAsync(goUser);
            Assert.That(deletedGOUser, Is.Null);

            // Verify person was soft deleted and GOUserId was cleared
            var deletedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(deletedPerson, Is.Not.Null);
            Assert.That(deletedPerson.DeletedAtUtc, Is.Not.Null);
            Assert.That(deletedPerson.GOUserId, Is.Null);
        }

        [Test]
        public async Task SoftDeleteAsync_RegularPerson_SuccessfullySoftDeletes()
        {
            // Arrange
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).FirstOrDefault();

            // Create test person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.FirstName = "Test";
            person.LastName = "User";
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Act
            var response = await _personAPI.SoftDeleteAsync(person.Id);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Is.True);

            // Verify person was soft deleted
            var deletedPerson = await _dataFacade.PersonDataProvider.GetAsync(person);
            Assert.That(deletedPerson, Is.Not.Null);
            Assert.That(deletedPerson.DeletedAtUtc, Is.Not.Null);
            Assert.That(deletedPerson.IsActiveDriver, Is.False);
            Assert.That(deletedPerson.Supervisor, Is.False);
        }
    }
}