# UserAccessQueueService Component Documentation

## Overview

The `UserAccessQueueService` is a business layer service component responsible for queuing user access update requests using Azure Service Bus. This service provides asynchronous, reliable message delivery for user access operations, enabling decoupled processing and improved system scalability.

## Key Responsibilities

- **Message Queuing**: Sends user access update messages to Azure Service Bus
- **Ordered Processing**: Ensures messages for the same person are processed in order using session-based messaging
- **Reliability**: Provides durable message delivery with configurable time-to-live
- **Monitoring**: Adds metadata for message tracking and observability
- **Resource Management**: Proper disposal of Azure Service Bus resources

## Architecture

```
Application Layer (VehicleAccessUtilities)
       ↓
UserAccessQueueService
       ↓
Azure Service Bus Queue
       ↓
VehicleAccessProcessor Function
       ↓
VehicleAccessCreation Component (ManageUserAccessAsync)
```

## Interface Definition

### `IUserAccessQueueService`

```csharp
public interface IUserAccessQueueService
{
    Task SendUserAccessUpdateMessageAsync(UserAccessUpdateMessage message);
}
```

## Main Method

### `SendUserAccessUpdateMessageAsync`

Sends a user access update message to the Azure Service Bus queue for asynchronous processing.

#### Signature
```csharp
public async Task SendUserAccessUpdateMessageAsync(UserAccessUpdateMessage message)
```

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `message` | `UserAccessUpdateMessage` | The user access update message containing person and access information |

#### UserAccessUpdateMessage Properties
- `PersonId` (Guid): Unique identifier for the person
- `CustomerId` (Guid): Customer identifier for multi-tenant isolation
- `PersonToSiteAccessesJson` (string): Site access updates serialized as JSON
- `PersonToDepartmentAccessesJson` (string): Department access updates serialized as JSON
- `PersonToModelAccessesJson` (string): Model access updates serialized as JSON
- `PersonToVehicleAccessesJson` (string): Per-vehicle access updates serialized as JSON
- `CreatedAt` (DateTime): Timestamp when the message was created
- `InitiatedByUserId` (Guid?): User ID who initiated the access update
- `CorrelationId` (string): Optional correlation ID for tracking related operations
- `Priority` (string): Priority level for processing (Normal, High, Critical)

#### Process Flow

1. **Message Serialization**
   - Converts `UserAccessUpdateMessage` to JSON format
   - Ensures proper data formatting for queue consumption

2. **Service Bus Message Creation**
   - Creates `ServiceBusMessage` with serialized payload
   - Generates unique message ID for tracking
   - Sets session ID for ordered processing per person

3. **Message Configuration**
   - Sets subject for message categorization
   - Configures 24-hour time-to-live (12 hours for critical messages)
   - Adds application properties for filtering and monitoring

4. **Queue Delivery**
   - Sends message to configured Azure Service Bus queue
   - Logs successful delivery
   - Handles and logs any delivery failures

#### Message Properties

| Property | Value | Purpose |
|----------|--------|---------|
| `MessageId` | Generated GUID | Unique message identification |
| `SessionId` | Person ID | Ensures ordered processing per person |
| `Subject` | "UserAccessUpdate" | Message type identification |
| `TimeToLive` | 24 hours (12 for critical) | Prevents indefinite queue buildup |
| `CorrelationId` | Message correlation ID | Request tracking |

#### Application Properties

| Property | Source | Purpose |
|----------|--------|---------|
| `PersonId` | message.PersonId | Message filtering and routing |
| `CustomerId` | message.CustomerId | Multi-tenant message isolation |
| `CreatedAt` | message.CreatedAt | Timestamp tracking in ISO 8601 format |
| `Priority` | message.Priority | Processing priority indication |
| `InitiatedByUserId` | message.InitiatedByUserId | User tracking for audit |

## Configuration Requirements

### Required Configuration Settings

#### Connection String
```json
{
  "ConnectionStrings": {
    "ServiceBus": "Endpoint=sb://your-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-key"
  }
}
```

#### Queue Configuration
```json
{
  "ServiceBus": {
    "UserAccessQueue": "user-access-update"
  }
}
```

### Azure Service Bus Queue Requirements

#### Queue Properties
- **Session Support**: Enabled (for ordered processing)
- **Duplicate Detection**: Recommended
- **Dead Letter Queue**: Enabled
- **Message TTL**: 24 hours (configurable)
- **Max Delivery Count**: 10 (recommended)

#### Recommended Queue Settings
```json
{
  "requiresSession": true,
  "duplicateDetectionHistoryTimeWindow": "PT10M",
  "deadLetteringOnMessageExpiration": true,
  "maxDeliveryCount": 10,
  "enableBatchedOperations": true
}
```

## Dependencies

### Required Packages
- `Azure.Messaging.ServiceBus` (>= 7.0.0)
- `Microsoft.Extensions.Configuration`
- `Microsoft.Extensions.Logging`
- `System.Text.Json`

### Dependency Injection Registration
```csharp
// In Program.cs or Startup.cs
services.AddSingleton<IUserAccessQueueService, UserAccessQueueService>();
```

## Usage Examples

### Basic Usage
```csharp
// Inject the service
private readonly IUserAccessQueueService _queueService;

public async Task UpdateUserAccessAsync(Guid personId, Guid customerId, /* access collections */)
{
    var message = new UserAccessUpdateMessage
    {
        PersonId = personId,
        CustomerId = customerId,
        PersonToSiteAccessesJson = SerializeAccessCollection(siteAccesses),
        PersonToDepartmentAccessesJson = SerializeAccessCollection(departmentAccesses),
        PersonToModelAccessesJson = SerializeAccessCollection(modelAccesses),
        PersonToVehicleAccessesJson = SerializeAccessCollection(vehicleAccesses),
        CreatedAt = DateTime.UtcNow,
        InitiatedByUserId = currentUserId,
        CorrelationId = Guid.NewGuid().ToString(),
        Priority = "Normal"
    };

    await _queueService.SendUserAccessUpdateMessageAsync(message);
}
```

### Priority Processing
```csharp
public async Task ProcessCriticalUserAccessAsync(Guid personId, /* access data */)
{
    var message = new UserAccessUpdateMessage
    {
        PersonId = personId,
        CustomerId = customerId,
        // ... other properties
        Priority = "Critical" // This will reduce TTL to 12 hours
    };

    await _queueService.SendUserAccessUpdateMessageAsync(message);
}
```

## Error Handling

### Exception Types
- **InvalidOperationException**: Thrown when Service Bus connection string is not configured
- **ServiceBusException**: Azure Service Bus specific errors
- **JsonException**: Message serialization errors
- **ArgumentException**: Invalid message content

### Error Scenarios

#### Configuration Errors
```csharp
// Missing connection string
throw new InvalidOperationException("ServiceBus connection string is not configured");
```

#### Runtime Errors
- **Network Connectivity**: Service Bus endpoint unreachable
- **Authentication**: Invalid credentials or expired tokens
- **Queue Full**: Service Bus quota exceeded
- **Message Size**: Message exceeds Service Bus limits (256KB standard, 1MB premium)

### Retry Strategies

The Azure Service Bus client includes built-in retry logic:
- **Exponential Backoff**: Automatic retry with increasing delays
- **Circuit Breaker**: Prevents cascade failures
- **Timeout Handling**: Configurable operation timeouts

## Performance Characteristics

### Throughput
- **Standard Tier**: Up to 1,000 operations/second
- **Premium Tier**: Up to 1,000,000 operations/second
- **Batch Size**: Up to 100 messages per batch operation

### Latency
- **Send Operation**: Typically <10ms for successful sends
- **Network Overhead**: 1-5ms depending on region proximity
- **Serialization**: <1ms for typical message sizes

### Resource Usage
- **Memory**: Minimal overhead per service instance
- **Connections**: Single persistent connection per service instance
- **CPU**: Low overhead for message operations

## Monitoring and Observability

### Logging Events
- **Success**: User access update message sent with person ID and correlation ID
- **Failure**: Failed to send message with exception details
- **Performance**: Message sending duration

### Azure Service Bus Metrics
- **Active Messages**: Messages waiting for processing
- **Dead Letter Messages**: Failed message count
- **Incoming Messages**: Rate of message arrival
- **Outgoing Messages**: Rate of message processing

### Application Insights Integration
```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-key"
  }
}
```

## Security Considerations

### Authentication Methods
- **Connection String**: Shared Access Key authentication
- **Managed Identity**: Azure AD authentication (recommended for production)
- **Service Principal**: Application-based authentication

### Message Security
- **Encryption**: Messages encrypted in transit and at rest
- **Access Control**: Role-based access control (RBAC)
- **Network Security**: VNet integration and private endpoints

### Best Practices
- Use Managed Identity in Azure environments
- Implement proper secret management for connection strings
- Enable diagnostic logging for audit trails
- Monitor for suspicious message patterns

## Scaling Considerations

### Horizontal Scaling
- Multiple service instances can send to the same queue
- Session-based processing ensures ordered delivery per person
- Load balancing distributes message sending across instances

### Vertical Scaling
- Premium Service Bus tier for higher throughput
- Partitioned queues for increased capacity
- Multiple queues for workload distribution

## Integration Patterns

### Event-Driven Architecture
```mermaid
graph LR
    A[User Access Updated] --> B[UserAccessQueueService]
    B --> C[Service Bus Queue]
    C --> D[Azure Function]
    D --> E[VehicleAccessCreation.ManageUserAccessAsync]
    E --> F[VehicleAccessUtilities.UpdateAccessesForPersonInternalAsync]
    F --> G[Database Updates]
    F --> H[IoT Device Sync]
```

### Reliability Patterns
- **Dead Letter Handling**: Failed message processing
- **Duplicate Detection**: Prevents message duplication
- **Circuit Breaker**: Prevents cascade failures
- **Retry Logic**: Automatic failure recovery

## Troubleshooting Guide

### Common Issues

1. **Connection Failures**
   - Verify Service Bus connection string
   - Check network connectivity
   - Validate authentication credentials

2. **Message Send Failures**
   - Check Service Bus quota limits
   - Verify message size limits
   - Review queue configuration

3. **Performance Issues**
   - Monitor Service Bus metrics
   - Check for network latency
   - Review concurrent connection limits

4. **Ordering Issues**
   - Verify session ID configuration
   - Check queue session support
   - Review message processing logic

### Diagnostic Commands
```bash
# Check Service Bus namespace connectivity
nslookup your-namespace.servicebus.windows.net

# Monitor queue metrics
az servicebus queue show --resource-group rg --namespace-name ns --name user-access-update
```

## Version History

- **Current**: Azure Service Bus integration with session-based ordering and priority support
- **Features**: Configurable TTL, comprehensive logging, proper resource disposal
- **Security**: Support for multiple authentication methods

## Related Components

- **VehicleAccessUtilities**: Triggers user access queue messages
- **VehicleAccessCreation**: Processes queued messages via ManageUserAccessAsync
- **VehicleAccessProcessor**: Azure Function consumer
- **DeviceTwinHandler**: IoT device synchronization
- **User Management**: Triggers access updates 