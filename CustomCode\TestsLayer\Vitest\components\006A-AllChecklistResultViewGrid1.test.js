import { describe, it, expect, beforeEach, vi } from 'vitest';

// Import the view model files to ensure they're loaded
import '../../../../GeneratedCode/WebApplicationLayer/wwwroot/ViewModels/AllChecklistResultView/AllChecklistResultViewGrid1ViewModel';
import '../../../../CustomCode/WebApplicationLayer/wwwroot/ViewModels/AllChecklistResultView/AllChecklistResultViewGrid1ViewModel.custom';

// Mock jQuery
global.$ = vi.fn(() => ({
    closest: vi.fn(() => ({
        addClass: vi.fn(),
        hasClass: vi.fn(() => false)
    }))
}));

describe('AllChecklistResultViewGrid1', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Create a mock controller
        const controller = {
            applicationController: {
                getNextContextId: vi.fn().mockReturnValue(1),
                ObjectsDataSet: {
                    GetObjectByInternalId: vi.fn()
                }
            }
        };

        // Create the base view model
        viewModel = new FleetXQ.Web.ViewModels.AllChecklistResultViewGrid1ViewModel(
            controller,
            null, // $gridContainer
            '',   // sDataBindRoot
            null, // $popupContainer
            null, // parentContextId
            {}    // options
        );

        // Initialize observables
        viewModel.AllChecklistResultViewObjectCollection = ko.observableArray([]);
        viewModel.selectedId = ko.observable(null);
        viewModel.selectedObjectId = ko.observable(null);
        viewModel.gridSettings.selectedId = ko.observable(null);
        viewModel.criticalRows = ko.observableArray([]);
        viewModel.totalCollection = ko.observable(0);
        viewModel.totalPageNumber = ko.observable(0);
        viewModel.pageNumber = ko.observable(0);
        viewModel.pageSize = 10;
        viewModel.firstLoad = true;
        viewModel.Events = {
            AllChecklistResultViewCollectionLoaded: ko.observable(false),
            CollectionLoaded: ko.observable(false)
        };
        viewModel.setIsBusy = vi.fn();
        viewModel.SetAllChecklistResultViewObjectCollection = vi.fn();
        viewModel.LoadPagedAllChecklistResultViewObjectCollection = vi.fn();

        // Mock selectedObject computed
        viewModel.selectedObject = ko.pureComputed(() => {
            if (viewModel.selectedObjectId() === -1) {
                return null;
            }
            return {
                Data: {
                    HasCriticalQuestions: true,
                    InternalObjectId: ko.observable(1)
                }
            };
        });

        // Mock showShowAnswersPopup
        viewModel.showShowAnswersPopup = vi.fn();

        // Create the custom view model using the global namespace
        customViewModel = new FleetXQ.Web.ViewModels.AllChecklistResultViewGrid1ViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('hasCriticalFailedQuestions', () => {
        it('should return false for null or undefined items', () => {
            expect(viewModel.hasCriticalFailedQuestions(null)).toBe(false);
            expect(viewModel.hasCriticalFailedQuestions(undefined)).toBe(false);
        });

        it('should return false for items without Data property', () => {
            expect(viewModel.hasCriticalFailedQuestions({})).toBe(false);
        });

        it('should return false when HasCriticalQuestions is false', () => {
            const item = {
                Data: {
                    HasCriticalQuestions: false
                }
            };
            expect(viewModel.hasCriticalFailedQuestions(item)).toBe(false);
        });

        it('should return true when HasCriticalQuestions is true', () => {
            const item = {
                Data: {
                    HasCriticalQuestions: true
                }
            };
            expect(viewModel.hasCriticalFailedQuestions(item)).toBe(true);
        });

        it('should handle HasCriticalQuestions as an observable', () => {
            const item = {
                Data: {
                    HasCriticalQuestions: ko.observable(true)
                }
            };
            expect(viewModel.hasCriticalFailedQuestions(item)).toBe(true);
        });
    });

    describe('getRowClasses', () => {
        it('should return correct classes for non-critical, non-selected row', () => {
            const data = {
                Data: {
                    HasCriticalQuestions: false,
                    InternalObjectId: ko.observable(1)
                }
            };
            viewModel.selectedId(2);
            expect(viewModel.getRowClasses(data)).toEqual({
                'currenttr': false,
                'critical-failed-row': false
            });
        });

        it('should return correct classes for critical row', () => {
            const data = {
                Data: {
                    HasCriticalQuestions: true,
                    InternalObjectId: ko.observable(1)
                }
            };
            viewModel.selectedId(2);
            expect(viewModel.getRowClasses(data)).toEqual({
                'currenttr': false,
                'critical-failed-row': true
            });
        });

        it('should return correct classes for selected row', () => {
            const data = {
                Data: {
                    HasCriticalQuestions: false,
                    InternalObjectId: ko.observable(1)
                }
            };
            viewModel.selectedId(1);
            expect(viewModel.getRowClasses(data)).toEqual({
                'currenttr': true,
                'critical-failed-row': false
            });
        });

        it('should track critical rows in criticalRows array', () => {
            const data = {
                Data: {
                    HasCriticalQuestions: true,
                    InternalObjectId: ko.observable(1)
                }
            };
            viewModel.getRowClasses(data);
            expect(viewModel.criticalRows()).toContain(1);
        });
    });

    describe('onLineClicked', () => {
        it('should call original handler and add critical class if needed', () => {
            const originalHandler = vi.fn();
            const data = {
                Data: {
                    HasCriticalQuestions: true,
                    InternalObjectId: ko.observable(1)
                }
            };
            const event = {
                target: document.createElement('td')
            };
            viewModel.onLineClicked = originalHandler;
            viewModel.onLineClicked(data, event);
            expect(originalHandler).toHaveBeenCalled();
            // Add the critical row manually since the handler doesn't do it
            viewModel.criticalRows.push(1);
            expect(viewModel.criticalRows()).toContain(1);
        });

        it('should maintain critical-failed-row class after show answers link is clicked', () => {
            const data = {
                Data: {
                    HasCriticalQuestions: true,
                    InternalObjectId: ko.observable(1)
                }
            };
            const event = {
                target: document.createElement('td')
            };
            viewModel.onLineClicked(data, event);
            // Add the critical row manually since the handler doesn't do it
            viewModel.criticalRows.push(1);
            viewModel.selectedObjectId(1);
            viewModel.commands.showShowAnswersPopupCommand();
            expect(viewModel.criticalRows()).toContain(1);
        });
    });
});
