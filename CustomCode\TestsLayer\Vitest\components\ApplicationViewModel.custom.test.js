import { describe, it, expect, beforeEach, vi } from 'vitest';

describe('ApplicationViewModel Custom - Access Rules', () => {
    let viewModelCustom;
    let mockAppViewModel;

    beforeEach(() => {
        // Mock the app view model with all necessary properties
        mockAppViewModel = {
            security: {
                currentUserClaims: vi.fn()
            },
            navigation: {
                isUserManagementVisible: vi.fn(),
                isVehiclesVisible: vi.fn(),
                isDashboardVisible: vi.fn(),
                isCustomersVisible: vi.fn(),
                isReportsVisible: vi.fn(),
                isGeneralProductivityReportVisible: vi.fn(),
                isImpactReportVisible: vi.fn(),
                isPreOpCheckReportVisible: vi.fn(),
                isMachineUnlockReportVisible: vi.fn(),
                isCurrentStatusReportVisible: vi.fn(),
                isProficiencyReportVisible: vi.fn(),
                isExportVisible: vi.fn(),
                isOnDemandAuthorisationReportVisible: vi.fn()
            }
        };

        // Mock global GO object
        global.GO = {
            LocalePreference: {
                set: vi.fn()
            }
        };

        // Create the view model custom instance with the actual logic
        viewModelCustom = {
            appViewModel: mockAppViewModel,

            // Access rule constants (using short codes for JWT efficiency)
            AccessRules: {
                // Main access permissions
                HAS_USERS_ACCESS: 'UA',
                HAS_VEHICLES_ACCESS: 'VA',
                HAS_DASHBOARD_ACCESS: 'D',
                HAS_CUSTOMERS_ACCESS: 'CA',
                HAS_REPORTS_ACCESS: 'RA',

                // User-related permissions
                CAN_VIEW_USERS: 'VU',
                CAN_EDIT_USER: 'EU',
                CAN_CREATE_USER: 'CU',
                CAN_EXPORT_USERS: 'XU',
                CAN_VIEW_USER_CARD: 'VUC',
                CAN_EDIT_USER_CARD: 'EUC',
                CAN_VIEW_USER_LICENSE: 'VUL',
                CAN_EDIT_USER_LICENSE: 'EUL',
                CAN_CREATE_USER_LICENSE: 'CUL',
                CAN_VIEW_VEHICLE_ACCESS: 'VVA',
                CAN_EDIT_VEHICLE_ACCESS: 'EVA',
                CAN_VIEW_SUPERVISOR_ACCESS: 'VUSA',
                CAN_EDIT_SUPERVISOR_ACCESS: 'EUSA',
                CAN_VIEW_WEBSITE_ACCESS: 'VUWA',
                CAN_EDIT_WEBSITE_ACCESS: 'EUWA',
                CAN_CREATE_WEBSITE_ACCESS: 'CUWA',
                CAN_VIEW_REPORT_SUBSCRIPTION: 'VURS',
                CAN_EDIT_REPORT_SUBSCRIPTION: 'EURS',
                CAN_CREATE_REPORT_SUBSCRIPTION: 'CURS',
                CAN_DELETE_REPORT_SUBSCRIPTION: 'DURS',
                CAN_VIEW_ALERTS: 'VUA',
                CAN_CREATE_ALERTS: 'CUA',
                CAN_DELETE_ALERTS: 'DUA',

                // Vehicle-related permissions
                CAN_EDIT_VEHICLE: 'EV',
                CAN_CREATE_VEHICLE: 'CV',
                CAN_EXPORT_VEHICLE: 'XV',
                CAN_VIEW_CHECKLIST: 'VVC',
                CAN_VIEW_CHECKLIST_SETTING: 'VVCS',
                CAN_CREATE_CHECKLIST_SETTING: 'CVCS',
                CAN_EDIT_CHECKLIST_SETTING: 'EVCS',
                CAN_DELETE_CHECKLIST_SETTING: 'DVC',
                CAN_VIEW_IMPACT_SETTING: 'VVIS',
                CAN_EDIT_IMPACT_SETTING: 'EVIS',
                CAN_VIEW_SERVICE: 'VVSV',
                CAN_CREATE_SERVICE: 'CVS',
                CAN_EDIT_SERVICE: 'EVS',
                CAN_VIEW_SYNCHRONIZATION: 'VVS',
                CAN_VIEW_VOR_STATUS: 'VVOSVS',
                CAN_VIEW_FULL_IMPACT_LOCKOUT: 'VVOSFL',
                CAN_EDIT_FULL_IMPACT_LOCKOUT: 'EVOSFL',

                // Customer-related permissions
                CAN_VIEW_SITE: 'VS',
                CAN_EDIT_SITE: 'ES',
                CAN_CREATE_SITE: 'CS',
                CAN_VIEW_DEPARTMENT: 'VD',
                CAN_VIEW_EMAIL_GROUP: 'VEG',
                CAN_EDIT_EMAIL_GROUP: 'EEG',
                CAN_CREATE_EMAIL_GROUP: 'CEG',
                CAN_DELETE_EMAIL_GROUP: 'DEG',
                CAN_VIEW_MODELS: 'VM',
                CAN_VIEW_ACCESS_GROUPS: 'VAG',
                CAN_EDIT_ACCESS_GROUP: 'EAG',
                CAN_CREATE_ACCESS_GROUPS: 'CAG',
                CAN_EDIT_DEPARTMENT: 'ED',
                CAN_CREATE_DEPARTMENT: 'CD',
                CAN_VIEW_FIRMWARE: 'VF',
                CAN_EDIT_FIRMWARE: 'EF',
                CAN_CREATE_EMAIL_LIST: 'CEL',
                CAN_DELETE_EMAIL_LIST: 'DEL',

                // Report-related permissions
                CAN_VIEW_GEN_PROD: 'VGPR',
                CAN_VIEW_IMPACT_REPORT: 'VIR',
                CAN_VIEW_PREOP_REPORT: 'VPCR',
                CAN_VIEW_MACHINE_UNLOCK_REPORT: 'VMUR',
                CAN_VIEW_CURRENT_STATUS_REPORT: 'VCSR',
                CAN_VIEW_PROFICIENCY_REPORT: 'VPR',
                CAN_EXPORT_SERVICE_CHECK_REPORT: 'XSCR',
                CAN_EXPORT_GEN_PROD: 'XGPR',
                CAN_EXPORT_IMPACT_REPORT: 'XIR',
                CAN_EXPORT_PREOP_REPORT: 'XPCR',
                CAN_EXPORT_MACHINE_UNLOCK_REPORT: 'XMUR',
                CAN_EXPORT_CURRENT_STATUS_REPORT: 'XCSR',
                CAN_EXPORT_PROFICIENCY_REPORT: 'XPR'
            },

            // Reverse mapping for backward compatibility (short code to full name)
            AccessRuleReverseMapping: {
                'D': 'CanViewDashboard',
                'CA': 'HasCustomersAccess',
                'UA': 'HasUsersAccess',
                'VA': 'HasVehiclesAccess',
                'RA': 'HasReportsAccess',
                'VC': 'CanViewCustomer',
                'VS': 'CanViewCustomerSite',
                'CS': 'CanCreateCustomerSite',
                'ES': 'CanEditCustomerSite',
                'VD': 'CanViewCustomerDepartment',
                'CD': 'CanCreateCustomerDepartment',
                'ED': 'CanEditCustomerDepartment',
                'VEG': 'CanViewCustomerEmailGroup',
                'CEG': 'CanCreateCustomerEmailGroup',
                'EEG': 'CanEditCustomerEmailGroup',
                'DEG': 'CanDeleteCustomerEmailGroup',
                'CEL': 'CanCreateCustomerEmailList',
                'DEL': 'CanDeleteCustomerEmailList',
                'VF': 'CanViewCustomerFirmware',
                'EF': 'CanEditCustomerFirmware',
                'VM': 'CanViewCustomerModel',
                'VAG': 'CanViewAccessGroups',
                'CAG': 'CanCreateCustomerAccessGroups',
                'EAG': 'CanEditCustomerAccessGroups',
                'CU': 'CanCreateUser',
                'EU': 'CanEditUser',
                'DU': 'CanDeleteUser',
                'VU': 'CanViewUsers',
                'XU': 'CanExportUsers',
                'VUC': 'CanViewUserCard',
                'CUC': 'CanCreateUserCard',
                'EUC': 'CanEditUserCard',
                'VVA': 'CanViewVehicleAccess',
                'EVA': 'CanEditVehicleAccess',
                'VUL': 'CanViewUserLicense',
                'CUL': 'CanCreateUserLicense',
                'EUL': 'CanEditUserLicense',
                'VUWA': 'CanViewUserWebsiteAccess',
                'CUWA': 'CanCreateUserWebsiteAccess',
                'EUWA': 'CanEditUserWebsiteAccess',
                'VUSA': 'CanViewUserSupervisorAccess',
                'EUSA': 'CanEditUserSupervisorAccess',
                'VURS': 'CanViewUserReportSubscription',
                'CURS': 'CanCreateUserReportSubscription',
                'EURS': 'CanEditUserReportSubscription',
                'DURS': 'CanDeleteUserReportSubscription',
                'CUA': 'CanCreateUserAlert',
                'EUA': 'CanEditUserAlert',
                'DUA': 'CanDeleteUserAlert',
                'VUA': 'CanViewUserAlert',
                'CV': 'CanCreateVehicle',
                'EV': 'CanEditVehicle',
                'VV': 'CanViewVehicle',
                'XV': 'CanExportVehicle',
                'VVS': 'CanViewVehicleSynchronization',
                'VVC': 'CanViewVehicleChecklist',
                'CVC': 'CanCreateVehicleChecklist',
                'EVC': 'CanEditVehicleChecklist',
                'DVC': 'CanDeleteVehicleChecklist',
                'VVCS': 'CanViewVehicleChecklistSetting',
                'CVCS': 'CanCreateVehicleChecklistSetting',
                'EVCS': 'CanEditVehicleChecklistSetting',
                'VVIS': 'CanViewVehicleImpactSetting',
                'EVIS': 'CanEditVehicleImpactSetting',
                'VVSV': 'CanViewVehicleService',
                'CVS': 'CanCreateVehicleService',
                'EVS': 'CanEditVehicleService',
                'VVOSFL': 'CanViewVehicleOtherSettingFullLockout',
                'EVOSFL': 'CanEditVehicleOtherSettingFullLockout',
                'VVOSVS': 'CanViewVehicleOtherSettingVorStatus',
                'EVOSVS': 'CanEditVehicleOtherSettingVorStatus',
                'VGPR': 'CanViewGeneralProductivityReport',
                'XGPR': 'CanExportGeneralProductivityReport',
                'VIR': 'CanViewImpactReport',
                'XIR': 'CanExportImpactReport',
                'VPCR': 'CanViewPreopChecklistReport',
                'XPCR': 'CanExportPreopChecklistReport',
                'VMUR': 'CanViewMachineUnlockReport',
                'XMUR': 'CanExportMachineUnlockReport',
                'VCSR': 'CanViewCurrentStatusReport',
                'XCSR': 'CanExportCurrentStatusReport',
                'VPR': 'CanViewProficiencyReport',
                'XPR': 'CanExportProficiencyReport',
                'VSCR': 'CanViewServiceCheckReport',
                'XSCR': 'CanExportServiceCheckReport'
            },

            // Helper function to check if user has access based on AccessRules
            hasAccess: function (rule) {
                const userClaims = this.appViewModel.security.currentUserClaims();
                if (!userClaims) return false;

                // Always grant access to administrators and dealer admins
                if (userClaims.role?.includes('Administrator') || userClaims.role?.includes('DealerAdmin')) return true;

                // Check if AccessRules property exists (new pattern with short codes)
                if (userClaims.AccessRules) {
                    const accessRules = userClaims.AccessRules.split(',').map(r => r.trim());

                    // First try the rule as-is (for short codes)
                    if (accessRules.includes(rule)) {
                        return true;
                    }

                    // If not found, try to find the short code for this rule
                    const shortCode = this.AccessRules[rule];
                    if (shortCode && accessRules.includes(shortCode)) {
                        return true;
                    }
                }

                // Fallback to old pattern for backward compatibility
                // Check if the rule exists as a direct property
                if (userClaims.hasOwnProperty(rule)) {
                    return userClaims[rule] == null || userClaims[rule] == 'True';
                }

                return false;
            },

            initialize: function () {
                this.updateLocalePreference();
            },

            updateLocalePreference: function () {
                const userClaims = this.appViewModel.security.currentUserClaims();

                // Default fallback locale
                let preferredLocale = 'en-US';

                // Only try to access properties if userClaims exists
                if (userClaims) {
                    if (userClaims.UserPreferredLocale) {
                        preferredLocale = userClaims.UserPreferredLocale;
                    } else if (userClaims.CustomerPreferredLocale) {
                        preferredLocale = userClaims.CustomerPreferredLocale;
                    }
                }

                // Set the locale preference in the GO object
                global.GO.LocalePreference.set(preferredLocale);
            },

            updateIsUserManagementVisible: function () {
                this.appViewModel.navigation.isUserManagementVisible(this.hasAccess(this.AccessRules.HAS_USERS_ACCESS));
            },

            updateIsVehiclesVisible: function () {
                this.appViewModel.navigation.isVehiclesVisible(this.hasAccess(this.AccessRules.HAS_VEHICLES_ACCESS));
            },

            updateIsDashboardVisible: function () {
                this.appViewModel.navigation.isDashboardVisible(this.hasAccess(this.AccessRules.HAS_DASHBOARD_ACCESS));
            },

            updateIsCustomersVisible: function () {
                this.appViewModel.navigation.isCustomersVisible(this.hasAccess(this.AccessRules.HAS_CUSTOMERS_ACCESS));
            },

            updateIsReportsVisible: function () {
                this.appViewModel.navigation.isReportsVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS));
            },

            updateIsGeneralProductivityReportVisible: function () {
                this.appViewModel.navigation.isGeneralProductivityReportVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && this.hasAccess(this.AccessRules.CAN_VIEW_GEN_PROD));
            },

            updateIsImpactReportVisible: function () {
                this.appViewModel.navigation.isImpactReportVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && this.hasAccess(this.AccessRules.CAN_VIEW_IMPACT_REPORT));
            },

            updateIsPreOpCheckReportVisible: function () {
                this.appViewModel.navigation.isPreOpCheckReportVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && this.hasAccess(this.AccessRules.CAN_VIEW_PREOP_REPORT));
            },

            updateIsMachineUnlockReportVisible: function () {
                this.appViewModel.navigation.isMachineUnlockReportVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && this.hasAccess(this.AccessRules.CAN_VIEW_MACHINE_UNLOCK_REPORT));
            },

            updateIsCurrentStatusReportVisible: function () {
                this.appViewModel.navigation.isCurrentStatusReportVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && this.hasAccess(this.AccessRules.CAN_VIEW_CURRENT_STATUS_REPORT));
            },

            updateIsProficiencyReportVisible: function () {
                this.appViewModel.navigation.isProficiencyReportVisible(this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && this.hasAccess(this.AccessRules.CAN_VIEW_PROFICIENCY_REPORT));
            },

            updateIsExportVisible: function () {
                this.appViewModel.navigation.isExportVisible(
                    this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) && (
                        this.hasAccess(this.AccessRules.CAN_EXPORT_SERVICE_CHECK_REPORT) ||
                        this.hasAccess(this.AccessRules.CAN_EXPORT_GEN_PROD) ||
                        this.hasAccess(this.AccessRules.CAN_EXPORT_IMPACT_REPORT) ||
                        this.hasAccess(this.AccessRules.CAN_EXPORT_PREOP_REPORT) ||
                        this.hasAccess(this.AccessRules.CAN_EXPORT_MACHINE_UNLOCK_REPORT) ||
                        this.hasAccess(this.AccessRules.CAN_EXPORT_CURRENT_STATUS_REPORT) ||
                        this.hasAccess(this.AccessRules.CAN_EXPORT_PROFICIENCY_REPORT)
                    )
                );
            },

            updateIsOnDemandAuthorisationReportVisible: function () {
                this.appViewModel.navigation.isOnDemandAuthorisationReportVisible(
                    // Hide for Customer role, show for others with proper access
                    this.hasAccess(this.AccessRules.HAS_REPORTS_ACCESS) &&
                    this.appViewModel.security.currentUserClaims().role?.includes('Administrator') == true
                );
            }
        };
    });

    describe('Access Rules Constants', () => {
        it('should have all main access permissions defined with short codes', () => {
            expect(viewModelCustom.AccessRules.HAS_USERS_ACCESS).toBe('UA');
            expect(viewModelCustom.AccessRules.HAS_VEHICLES_ACCESS).toBe('VA');
            expect(viewModelCustom.AccessRules.HAS_DASHBOARD_ACCESS).toBe('D');
            expect(viewModelCustom.AccessRules.HAS_CUSTOMERS_ACCESS).toBe('CA');
            expect(viewModelCustom.AccessRules.HAS_REPORTS_ACCESS).toBe('RA');
        });

        it('should have all user-related permissions defined with short codes', () => {
            expect(viewModelCustom.AccessRules.CAN_VIEW_USERS).toBe('VU');
            expect(viewModelCustom.AccessRules.CAN_EDIT_USER).toBe('EU');
            expect(viewModelCustom.AccessRules.CAN_CREATE_USER).toBe('CU');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_USERS).toBe('XU');
            expect(viewModelCustom.AccessRules.CAN_VIEW_USER_CARD).toBe('VUC');
            expect(viewModelCustom.AccessRules.CAN_EDIT_USER_CARD).toBe('EUC');
            expect(viewModelCustom.AccessRules.CAN_VIEW_USER_LICENSE).toBe('VUL');
            expect(viewModelCustom.AccessRules.CAN_EDIT_USER_LICENSE).toBe('EUL');
            expect(viewModelCustom.AccessRules.CAN_CREATE_USER_LICENSE).toBe('CUL');
            expect(viewModelCustom.AccessRules.CAN_VIEW_VEHICLE_ACCESS).toBe('VVA');
            expect(viewModelCustom.AccessRules.CAN_EDIT_VEHICLE_ACCESS).toBe('EVA');
            expect(viewModelCustom.AccessRules.CAN_VIEW_SUPERVISOR_ACCESS).toBe('VUSA');
            expect(viewModelCustom.AccessRules.CAN_EDIT_SUPERVISOR_ACCESS).toBe('EUSA');
            expect(viewModelCustom.AccessRules.CAN_VIEW_WEBSITE_ACCESS).toBe('VUWA');
            expect(viewModelCustom.AccessRules.CAN_EDIT_WEBSITE_ACCESS).toBe('EUWA');
            expect(viewModelCustom.AccessRules.CAN_CREATE_WEBSITE_ACCESS).toBe('CUWA');
            expect(viewModelCustom.AccessRules.CAN_VIEW_REPORT_SUBSCRIPTION).toBe('VURS');
            expect(viewModelCustom.AccessRules.CAN_EDIT_REPORT_SUBSCRIPTION).toBe('EURS');
            expect(viewModelCustom.AccessRules.CAN_CREATE_REPORT_SUBSCRIPTION).toBe('CURS');
            expect(viewModelCustom.AccessRules.CAN_DELETE_REPORT_SUBSCRIPTION).toBe('DURS');
            expect(viewModelCustom.AccessRules.CAN_VIEW_ALERTS).toBe('VUA');
            expect(viewModelCustom.AccessRules.CAN_CREATE_ALERTS).toBe('CUA');
            expect(viewModelCustom.AccessRules.CAN_DELETE_ALERTS).toBe('DUA');
        });

        it('should have all vehicle-related permissions defined with short codes', () => {
            expect(viewModelCustom.AccessRules.CAN_EDIT_VEHICLE).toBe('EV');
            expect(viewModelCustom.AccessRules.CAN_CREATE_VEHICLE).toBe('CV');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_VEHICLE).toBe('XV');
            expect(viewModelCustom.AccessRules.CAN_VIEW_CHECKLIST).toBe('VVC');
            expect(viewModelCustom.AccessRules.CAN_VIEW_CHECKLIST_SETTING).toBe('VVCS');
            expect(viewModelCustom.AccessRules.CAN_CREATE_CHECKLIST_SETTING).toBe('CVCS');
            expect(viewModelCustom.AccessRules.CAN_EDIT_CHECKLIST_SETTING).toBe('EVCS');
            expect(viewModelCustom.AccessRules.CAN_DELETE_CHECKLIST_SETTING).toBe('DVC');
            expect(viewModelCustom.AccessRules.CAN_VIEW_IMPACT_SETTING).toBe('VVIS');
            expect(viewModelCustom.AccessRules.CAN_EDIT_IMPACT_SETTING).toBe('EVIS');
            expect(viewModelCustom.AccessRules.CAN_VIEW_SERVICE).toBe('VVSV');
            expect(viewModelCustom.AccessRules.CAN_CREATE_SERVICE).toBe('CVS');
            expect(viewModelCustom.AccessRules.CAN_EDIT_SERVICE).toBe('EVS');
            expect(viewModelCustom.AccessRules.CAN_VIEW_SYNCHRONIZATION).toBe('VVS');
            expect(viewModelCustom.AccessRules.CAN_VIEW_VOR_STATUS).toBe('VVOSVS');
            expect(viewModelCustom.AccessRules.CAN_VIEW_FULL_IMPACT_LOCKOUT).toBe('VVOSFL');
            expect(viewModelCustom.AccessRules.CAN_EDIT_FULL_IMPACT_LOCKOUT).toBe('EVOSFL');
        });

        it('should have all customer-related permissions defined with short codes', () => {
            expect(viewModelCustom.AccessRules.CAN_VIEW_SITE).toBe('VS');
            expect(viewModelCustom.AccessRules.CAN_EDIT_SITE).toBe('ES');
            expect(viewModelCustom.AccessRules.CAN_CREATE_SITE).toBe('CS');
            expect(viewModelCustom.AccessRules.CAN_VIEW_DEPARTMENT).toBe('VD');
            expect(viewModelCustom.AccessRules.CAN_VIEW_EMAIL_GROUP).toBe('VEG');
            expect(viewModelCustom.AccessRules.CAN_EDIT_EMAIL_GROUP).toBe('EEG');
            expect(viewModelCustom.AccessRules.CAN_CREATE_EMAIL_GROUP).toBe('CEG');
            expect(viewModelCustom.AccessRules.CAN_DELETE_EMAIL_GROUP).toBe('DEG');
            expect(viewModelCustom.AccessRules.CAN_VIEW_MODELS).toBe('VM');
            expect(viewModelCustom.AccessRules.CAN_VIEW_ACCESS_GROUPS).toBe('VAG');
            expect(viewModelCustom.AccessRules.CAN_EDIT_ACCESS_GROUP).toBe('EAG');
            expect(viewModelCustom.AccessRules.CAN_CREATE_ACCESS_GROUPS).toBe('CAG');
            expect(viewModelCustom.AccessRules.CAN_EDIT_DEPARTMENT).toBe('ED');
            expect(viewModelCustom.AccessRules.CAN_CREATE_DEPARTMENT).toBe('CD');
            expect(viewModelCustom.AccessRules.CAN_VIEW_FIRMWARE).toBe('VF');
            expect(viewModelCustom.AccessRules.CAN_EDIT_FIRMWARE).toBe('EF');
            expect(viewModelCustom.AccessRules.CAN_CREATE_EMAIL_LIST).toBe('CEL');
            expect(viewModelCustom.AccessRules.CAN_DELETE_EMAIL_LIST).toBe('DEL');
        });

        it('should have all report-related permissions defined with short codes', () => {
            expect(viewModelCustom.AccessRules.CAN_VIEW_GEN_PROD).toBe('VGPR');
            expect(viewModelCustom.AccessRules.CAN_VIEW_IMPACT_REPORT).toBe('VIR');
            expect(viewModelCustom.AccessRules.CAN_VIEW_PREOP_REPORT).toBe('VPCR');
            expect(viewModelCustom.AccessRules.CAN_VIEW_MACHINE_UNLOCK_REPORT).toBe('VMUR');
            expect(viewModelCustom.AccessRules.CAN_VIEW_CURRENT_STATUS_REPORT).toBe('VCSR');
            expect(viewModelCustom.AccessRules.CAN_VIEW_PROFICIENCY_REPORT).toBe('VPR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_SERVICE_CHECK_REPORT).toBe('XSCR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_GEN_PROD).toBe('XGPR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_IMPACT_REPORT).toBe('XIR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_PREOP_REPORT).toBe('XPCR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_MACHINE_UNLOCK_REPORT).toBe('XMUR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_CURRENT_STATUS_REPORT).toBe('XCSR');
            expect(viewModelCustom.AccessRules.CAN_EXPORT_PROFICIENCY_REPORT).toBe('XPR');
        });
    });

    describe('hasAccess method', () => {
        it('should return false when userClaims is null', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue(null);
            expect(viewModelCustom.hasAccess('UA')).toBe(false);
        });

        it('should return false when userClaims is undefined', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue(undefined);
            expect(viewModelCustom.hasAccess('UA')).toBe(false);
        });

        it('should return true for Administrator role regardless of permissions', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Administrator'
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
            expect(viewModelCustom.hasAccess('NonExistentPermission')).toBe(true);
        });

        it('should return true for Administrator role with partial match', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'AdministratorUser'
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
        });

        it('should return true for DealerAdmin role regardless of permissions', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'DealerAdmin'
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
            expect(viewModelCustom.hasAccess('NonExistentPermission')).toBe(true);
        });

        it('should return true for DealerAdmin role with partial match', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'DealerAdminUser'
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
        });

        it('should return true when short code exists in AccessRules string (new pattern)', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                AccessRules: 'UA,VU,EU'
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
            expect(viewModelCustom.hasAccess('VU')).toBe(true);
            expect(viewModelCustom.hasAccess('EU')).toBe(true);
        });

        it('should return true when rule name exists in AccessRules string (backward compatibility)', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                AccessRules: 'HasUsersAccess,CanViewUsers,CanEditUser'
            });
            expect(viewModelCustom.hasAccess('HasUsersAccess')).toBe(true);
            expect(viewModelCustom.hasAccess('CanViewUsers')).toBe(true);
            expect(viewModelCustom.hasAccess('CanEditUser')).toBe(true);
        });

        it('should return false when permission does not exist in AccessRules string', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                AccessRules: 'UA,VU'
            });
            expect(viewModelCustom.hasAccess('EU')).toBe(false);
        });

        it('should handle AccessRules string with spaces', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                AccessRules: ' UA , VU , EU '
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
            expect(viewModelCustom.hasAccess('VU')).toBe(true);
            expect(viewModelCustom.hasAccess('EU')).toBe(true);
        });

        it('should return true when permission exists as direct property with null value (old pattern)', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                HasUsersAccess: null
            });
            expect(viewModelCustom.hasAccess('HasUsersAccess')).toBe(true);
        });

        it('should return true when permission exists as direct property with "True" value (old pattern)', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                HasUsersAccess: 'True'
            });
            expect(viewModelCustom.hasAccess('HasUsersAccess')).toBe(true);
        });

        it('should return false when permission exists as direct property with "False" value (old pattern)', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                HasUsersAccess: 'False'
            });
            expect(viewModelCustom.hasAccess('HasUsersAccess')).toBe(false);
        });

        it('should return false when permission does not exist as direct property (old pattern)', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                CanViewUsers: 'True'
            });
            expect(viewModelCustom.hasAccess('HasUsersAccess')).toBe(false);
        });

        it('should prioritize new pattern over old pattern', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                AccessRules: 'UA',
                HasUsersAccess: 'False' // This should be ignored in favor of AccessRules
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
        });

        it('should handle mixed short codes and full names in AccessRules', () => {
            mockAppViewModel.security.currentUserClaims.mockReturnValue({
                role: 'Customer',
                AccessRules: 'UA,CanViewUsers,EU'
            });
            expect(viewModelCustom.hasAccess('UA')).toBe(true);
            expect(viewModelCustom.hasAccess('CanViewUsers')).toBe(true);
            expect(viewModelCustom.hasAccess('EU')).toBe(true);
        });
    });

    describe('Navigation visibility methods', () => {
        beforeEach(() => {
            // Reset all mocks before each test
            vi.clearAllMocks();
        });

        describe('updateIsUserManagementVisible', () => {
            it('should set visibility to true when user has UA (HasUsersAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'UA'
                });
                viewModelCustom.updateIsUserManagementVisible();
                expect(mockAppViewModel.navigation.isUserManagementVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user does not have UA (HasUsersAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'VA'
                });
                viewModelCustom.updateIsUserManagementVisible();
                expect(mockAppViewModel.navigation.isUserManagementVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsVehiclesVisible', () => {
            it('should set visibility to true when user has VA (HasVehiclesAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'VA'
                });
                viewModelCustom.updateIsVehiclesVisible();
                expect(mockAppViewModel.navigation.isVehiclesVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user does not have VA (HasVehiclesAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'UA'
                });
                viewModelCustom.updateIsVehiclesVisible();
                expect(mockAppViewModel.navigation.isVehiclesVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsDashboardVisible', () => {
            it('should set visibility to true when user has D (HasDashboardAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'D'
                });
                viewModelCustom.updateIsDashboardVisible();
                expect(mockAppViewModel.navigation.isDashboardVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user does not have D (HasDashboardAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'UA'
                });
                viewModelCustom.updateIsDashboardVisible();
                expect(mockAppViewModel.navigation.isDashboardVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsCustomersVisible', () => {
            it('should set visibility to true when user has CA (HasCustomersAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'CA'
                });
                viewModelCustom.updateIsCustomersVisible();
                expect(mockAppViewModel.navigation.isCustomersVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user does not have CA (HasCustomersAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'UA'
                });
                viewModelCustom.updateIsCustomersVisible();
                expect(mockAppViewModel.navigation.isCustomersVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsReportsVisible', () => {
            it('should set visibility to true when user has RA (HasReportsAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsReportsVisible();
                expect(mockAppViewModel.navigation.isReportsVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user does not have RA (HasReportsAccess)', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'UA'
                });
                viewModelCustom.updateIsReportsVisible();
                expect(mockAppViewModel.navigation.isReportsVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsGeneralProductivityReportVisible', () => {
            it('should set visibility to true when user has both RA and VGPR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,VGPR'
                });
                viewModelCustom.updateIsGeneralProductivityReportVisible();
                expect(mockAppViewModel.navigation.isGeneralProductivityReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but not VGPR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsGeneralProductivityReportVisible();
                expect(mockAppViewModel.navigation.isGeneralProductivityReportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsImpactReportVisible', () => {
            it('should set visibility to true when user has both RA and VIR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,VIR'
                });
                viewModelCustom.updateIsImpactReportVisible();
                expect(mockAppViewModel.navigation.isImpactReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but not VIR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsImpactReportVisible();
                expect(mockAppViewModel.navigation.isImpactReportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsPreOpCheckReportVisible', () => {
            it('should set visibility to true when user has both RA and VPCR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,VPCR'
                });
                viewModelCustom.updateIsPreOpCheckReportVisible();
                expect(mockAppViewModel.navigation.isPreOpCheckReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but not VPCR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsPreOpCheckReportVisible();
                expect(mockAppViewModel.navigation.isPreOpCheckReportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsMachineUnlockReportVisible', () => {
            it('should set visibility to true when user has both RA and VMUR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,VMUR'
                });
                viewModelCustom.updateIsMachineUnlockReportVisible();
                expect(mockAppViewModel.navigation.isMachineUnlockReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but not VMUR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsMachineUnlockReportVisible();
                expect(mockAppViewModel.navigation.isMachineUnlockReportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsCurrentStatusReportVisible', () => {
            it('should set visibility to true when user has both RA and VCSR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,VCSR'
                });
                viewModelCustom.updateIsCurrentStatusReportVisible();
                expect(mockAppViewModel.navigation.isCurrentStatusReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but not VCSR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsCurrentStatusReportVisible();
                expect(mockAppViewModel.navigation.isCurrentStatusReportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsProficiencyReportVisible', () => {
            it('should set visibility to true when user has both RA and VPR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,VPR'
                });
                viewModelCustom.updateIsProficiencyReportVisible();
                expect(mockAppViewModel.navigation.isProficiencyReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but not VPR', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsProficiencyReportVisible();
                expect(mockAppViewModel.navigation.isProficiencyReportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsExportVisible', () => {
            it('should set visibility to true when user has RA and any export permission', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,XSCR'
                });
                viewModelCustom.updateIsExportVisible();
                expect(mockAppViewModel.navigation.isExportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to true when user has multiple export permissions', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA,XGPR,XIR'
                });
                viewModelCustom.updateIsExportVisible();
                expect(mockAppViewModel.navigation.isExportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but no export permissions', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsExportVisible();
                expect(mockAppViewModel.navigation.isExportVisible).toHaveBeenCalledWith(false);
            });

            it('should set visibility to false when user has export permissions but not RA', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    AccessRules: 'XGPR'
                });
                viewModelCustom.updateIsExportVisible();
                expect(mockAppViewModel.navigation.isExportVisible).toHaveBeenCalledWith(false);
            });
        });

        describe('updateIsOnDemandAuthorisationReportVisible', () => {
            it('should set visibility to true when user has RA and is Administrator', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    role: 'Administrator',
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsOnDemandAuthorisationReportVisible();
                expect(mockAppViewModel.navigation.isOnDemandAuthorisationReportVisible).toHaveBeenCalledWith(true);
            });

            it('should set visibility to false when user has RA but is not Administrator', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    role: 'Customer',
                    AccessRules: 'RA'
                });
                viewModelCustom.updateIsOnDemandAuthorisationReportVisible();
                expect(mockAppViewModel.navigation.isOnDemandAuthorisationReportVisible).toHaveBeenCalledWith(false);
            });

            it('should set visibility to true when user is Administrator but does not have RA', () => {
                mockAppViewModel.security.currentUserClaims.mockReturnValue({
                    role: 'Administrator'
                });
                viewModelCustom.updateIsOnDemandAuthorisationReportVisible();
                expect(mockAppViewModel.navigation.isOnDemandAuthorisationReportVisible).toHaveBeenCalledWith(true);
            });
        });
    });
}); 