describe("006.b - Thai Language Settings", () => {
    beforeEach(() => {
        // Prevent uncaught exceptions from failing tests
        Cypress.on('uncaught:exception', (err, runnable) => {
            return false;
        });
        cy.login();
    });

    it("verifies Thai question visibility based on IsThaiEnabled setting", () => {
        // Access Vehicles and wait for page load
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']", { timeout: 30000 })
            .should('be.visible')
            .click({ force: true });

        // Simple wait for page to load
        cy.wait(10000);

        // Search for VH14 vehicle
        cy.get(".filterTextInputCustom", { timeout: 10000 })
            .first()
            .should('be.visible')
            .clear({ force: true })
            .type("VH14{enter}", { force: true });

        // Wait for search results
        cy.wait(5000);

        // Intercept the vehicle by ID API call
        cy.intercept('GET', '/dataset/api/vehicle/byid/*')
            .as('getVehicleById');

        // Click first vehicle
        cy.get('#VehilceGrid')
            .find('tbody tr')
            .first()
            .find('td')
            .first()
            .find('a')
            .should('be.visible')
            .click({ force: true });

        // Wait for vehicle data to load
        cy.wait('@getVehicleById', { timeout: 30000 });

        // Navigate to checklist tab
        cy.get("[data-test-id='tab_link_541c0c57-20a2-4ab6-8927-7e4965786aaa']")
            .find('a')
            .first()
            .should('be.visible')
            .click({ force: true });

        // Click settings button
        cy.get("[data-test-id='fa575ace-682c-4f85-be2c-8b13abf9f558']")
            .should('be.visible')
            .click({ force: true });

        // Get initial state and toggle
        cy.get("[data-test-id='edit_d036b937-612f-4636-ba4c-3cda3011bf71']")
            .should('be.visible')
            .then($toggle => {
                // Check initial state of the toggle and Thai column
                const initialState = $toggle.prop('checked');
                cy.log(`Initial toggle state before clicking: ${initialState}`);

                cy.get('th').then($headers => {
                    const headerTexts = Array.from($headers).map(th => th.textContent);
                    cy.log('Initial header texts:', headerTexts);
                    const hasThaiColumn = headerTexts.some(text => text.includes('Thai Question'));
                    cy.log(`Initial Thai column present: ${hasThaiColumn}`);
                });

                // Click the toggle
                cy.wrap($toggle).click({ force: true });

                // Check toggle state after clicking
                cy.get("[data-test-id='edit_d036b937-612f-4636-ba4c-3cda3011bf71']")
                    .should('be.visible')
                    .then($toggleAfterClick => {
                        const toggleStateAfterClick = $toggleAfterClick.prop('checked');
                        cy.log(`Toggle state after clicking: ${toggleStateAfterClick}`);
                        expect(toggleStateAfterClick).to.equal(!initialState);
                    });

                // Save changes and wait for dialog to close
                cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']")
                    .should('be.visible')
                    .click({ force: true });

                // Wait for dialog to close
                cy.get('.overlay').should('not.exist');

                // Wait for UI update
                cy.wait(15000);

                // Check Thai column visibility
                cy.get('th').then($headers => {
                    const headerTexts = Array.from($headers).map(th => th.textContent);
                    cy.log('After toggle header texts:', headerTexts);
                    const hasThaiColumn = headerTexts.some(text => text.includes('Thai Question'));
                    cy.log(`Initial toggle state: ${initialState}`);
                    cy.log(`Thai column present after toggle: ${hasThaiColumn}`);
                    cy.log(`Expected Thai column to be: ${!initialState}`);
                    expect(hasThaiColumn).to.equal(!initialState);
                });

                // Toggle back
                cy.get("[data-test-id='fa575ace-682c-4f85-be2c-8b13abf9f558']")
                    .should('be.visible')
                    .click({ force: true });
                cy.get("[data-test-id='edit_d036b937-612f-4636-ba4c-3cda3011bf71']")
                    .should('be.visible')
                    .click({ force: true });
                cy.get("[data-test-id='b90c5aa1-4b78-4ff4-9ead-e62568afdea3']")
                    .should('be.visible')
                    .click({ force: true });
            });
    });
}); 