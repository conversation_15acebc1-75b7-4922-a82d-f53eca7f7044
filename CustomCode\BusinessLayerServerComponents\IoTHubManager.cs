﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Azure.Devices;
using Microsoft.Azure.Devices.Common.Security;
using Microsoft.Azure.Devices.Shared;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using System.Threading;
using System.Data;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// IoTHubManager Component
	///  
	/// </summary>
    public partial class IoTHubManager : BaseServerComponent, IIoTHubManager
    {

        public string _hostName { get; set; }
        public string _sharedAccessKey { get; set; }
        private readonly ILoggingService _logger;
        private class DeviceConnection
        {
            public string DeviceId { get; set; }
            public string ConnectionState { get; set; }

            public string LastActivityTime { get; set; }
        }

        private RegistryManager _registryManager;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        public IoTHubManager(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, ILoggingService logger) : base(serviceProvider, configuration, dataFacade)
        {
            _registryManager = RegistryManager.CreateFromConnectionString(_configuration["IoThubConnectionString"]);
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;

        }

        public System.Threading.Tasks.Task ParseConnectionStringAsync()
        {
            var parts = _configuration["IoThubConnectionString"].Split(';');

            foreach (var part in parts)
            {
                if (part.StartsWith("HostName=", StringComparison.OrdinalIgnoreCase))
                {
                    _hostName = part.Substring("HostName=".Length);
                }
                else if (part.StartsWith("SharedAccessKey=", StringComparison.OrdinalIgnoreCase))
                {
                    _sharedAccessKey = part.Substring("SharedAccessKey=".Length);
                }
            }

            return System.Threading.Tasks.Task.CompletedTask;
        }

        /// <summary>
        /// GenerateSASToken Method
        /// </summary>
        /// <param name="deviceId"></param>
        /// <returns></returns>
        public System.Threading.Tasks.Task<ComponentResponse<System.String>> GenerateSASTokenAsync(System.String deviceId, Dictionary<string, object> parameters = null)
        {
            IotHubConnectionStringBuilder builder = IotHubConnectionStringBuilder.Create(_configuration["IoThubConnectionString"]);

            // Get the device
            var device = _registryManager.GetDeviceAsync(deviceId);
            if (device == null)
            {
                return null; // Device not found
            }

            string deviceConnectionString = device.Result.Authentication.SymmetricKey.PrimaryKey;

            var sasBuilder = new SharedAccessSignatureBuilder
            {
                KeyName = builder.SharedAccessKeyName,
                Key = deviceConnectionString,
                TimeToLive = TimeSpan.FromDays(365),
                Target = $"{builder.HostName}/devices/{deviceId}",
            };

            string sasToken = sasBuilder.ToSignature();

            return System.Threading.Tasks.Task.FromResult(new ComponentResponse<System.String>(sasToken));
        }

        public System.Threading.Tasks.Task<bool> GetDeviceTwinConnectionStateAsync(string deviceId)
        {
            var twin = _registryManager.GetTwinAsync(deviceId).Result;
            return System.Threading.Tasks.Task.FromResult(twin.ConnectionState == DeviceConnectionState.Connected);
        }
        /// <summary>
        /// Updates the connection status of IoT devices for vehicles based on filtering criteria
        /// </summary>
        /// <param name="customerId">Customer ID filter (optional)</param>
        /// <param name="siteId">Site ID filter (optional)</param>
        /// <param name="departmentId">Department ID filter (optional, highest priority)</param>
        /// <param name="parameters">Additional parameters (optional)</param>
        /// <returns>ComponentResponse with success status</returns>
        public async Task<ComponentResponse<bool>> GetAllDevicesTwinConnectionAsync(System.Guid customerId, System.Guid siteId, System.Guid departmentId, Dictionary<string, object> parameters = null)
        {
            // Base filter predicate - get vehicles with modules and IoT devices
            string filterPredicate = "ModuleId1 != null && Module.IoTDevice != null";
            object[] queryParams = null;

            // Apply filters based on priority (department > site > customer)
            if (departmentId != Guid.Empty)
            {
                // Department filter has highest priority
                filterPredicate += " && DepartmentId == @0";
                queryParams = new object[] { departmentId };
            }
            else if (siteId != Guid.Empty)
            {
                // Site filter has medium priority
                filterPredicate += " && SiteId == @0";
                queryParams = new object[] { siteId };
            }
            else if (customerId != Guid.Empty)
            {
                // Customer filter has lowest priority
                filterPredicate += " && CustomerId == @0";
                queryParams = new object[] { customerId };
            }

            // Step 1: Get filtered vehicles with modules in a single query
            _logger.LogInformation($"Fetching vehicles with filter: {filterPredicate}");
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                null,
                filterPredicate,
                queryParams,
                skipSecurity: true
                );

            if (!vehicles.Any())
            {
                _logger.LogInformation("No vehicles found matching the criteria");
                return new ComponentResponse<bool>(true);
            }

            // Step 2: Extract IoT device IDs from vehicles
            var deviceIds = new HashSet<string>();
            foreach (var vehicle in vehicles)
            {
                var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                if (module?.IoTDevice != null)
                {
                    deviceIds.Add(module.IoTDevice);
                }
            }

            if (!deviceIds.Any())
            {
                _logger.LogInformation("No IoT devices found for the vehicles");
                return new ComponentResponse<bool>(true);
            }

            // Step 3: Query only the specific devices we need from IoT Hub
            var query = _registryManager.CreateQuery("SELECT DeviceId, ConnectionState FROM devices");
            var deviceStatuses = new Dictionary<string, bool>();
            while (query.HasMoreResults)
            {
                var result = await query.GetNextAsJsonAsync();
                foreach (var json in result)
                {
                    var device = JsonConvert.DeserializeObject<DeviceConnection>(json);
                    if (deviceIds.Contains(device.DeviceId))
                    {
                        deviceStatuses[device.DeviceId] = device.ConnectionState == "Connected";
                    }
                }
            }

            // Mock implementation with random connection statuses
            // var deviceStatuses = new Dictionary<string, bool>();
            // var random = new Random();

            // foreach (var deviceId in deviceIds)
            // {
            //     // Randomly set connection status (roughly 60% connected, 40% disconnected)
            //     bool isConnected = random.Next(10) < 6;
            //     deviceStatuses[deviceId] = isConnected;

            //     _logger.LogDebug($"Mock device {deviceId} set to {(isConnected ? "Connected" : "Disconnected")}");
            // }

            // _logger.LogInformation($"Mock data created for {deviceStatuses.Count} devices with random connection statuses");

            // Step 4: Process updates in memory
            var updatedVehicles = new List<VehicleDataObject>();
            foreach (var vehicle in vehicles)
            {
                var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                if (module?.IoTDevice != null && deviceStatuses.TryGetValue(module.IoTDevice, out bool isConnected))
                {
                    if (vehicle.ModuleIsConnected != isConnected)
                    {
                        vehicle.ModuleIsConnected = isConnected;
                        updatedVehicles.Add(vehicle);
                    }
                }
                else if (vehicle.ModuleIsConnected)
                {
                    vehicle.ModuleIsConnected = false;
                    updatedVehicles.Add(vehicle);
                }
            }

            // Step 5: Save vehicles sequentially using SaveAsync
            if (updatedVehicles.Any())
            {
                _logger.LogInformation($"Saving {updatedVehicles.Count} updated vehicles outside of transaction");

                foreach (var vehicle in updatedVehicles)
                {
                    try
                    {
                        await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(new GOServerException($"Failed to update vehicle {vehicle.Id}: {ex.Message}"));
                    }
                }

                _logger.LogInformation($"Completed saving vehicle connection states");
            }

            return new ComponentResponse<bool>(true);
        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> FetchIoTDevicesAsync(string Filter, Dictionary<string, object> parameters = null)
        {
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 != null");

            var devices = await _registryManager.GetDevicesAsync(500);

            List<object> deviceDetails = new();
            foreach (var device in devices)
            {
                var matchingVehicles = new List<VehicleDataObject>();
                foreach (var v in vehicles)
                {
                    var module = await v.LoadModuleAsync();
                    if (module.IoTDevice == device.Id)
                    {
                        matchingVehicles.Add(v);
                    }
                }

                var vehicle = matchingVehicles.SingleOrDefault();

                var twin = _registryManager.GetTwinAsync(device.Id).Result;
                deviceDetails.Add(
                    new
                    {
                        deviceId = device.Id,
                        connectionState = twin.ConnectionState,
                        lastActivityTime = twin.LastActivityTime,
                        cloudToDeviceMessageCount = twin.CloudToDeviceMessageCount,
                        properties = twin.Properties.Desired,
                        vehicleDesignation = vehicle != null ? vehicle.HireNo : "unassigned"
                    });
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(deviceDetails));

        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> RegisterNewIoTDeviceAsync(string DeviceId, Dictionary<string, object> parameters = null)
        {
            var device = new Device(DeviceId);
            // check if the device already exists
            var existingDevice = _registryManager.GetDeviceAsync(DeviceId).Result;
            if (existingDevice != null)
            {
                _logger.LogError(new GOServerException($"Device {DeviceId} already exists"));
                throw new GOServerException($"Device {DeviceId} already exists");
            }
            _registryManager.AddDeviceAsync(device).Wait();
            // check if the device was successfully registered
            var devices = _registryManager.GetDeviceAsync(DeviceId).Result;
            if (devices == null)
            {
                _logger.LogError(new GOServerException($"Error registering device {DeviceId}"));
                throw new GOServerException($"Error registering device {DeviceId}");
            }

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.IoTDevice = DeviceId;
            // set required fields for the module
            module.FSSSBase = 0;
            module.FSSXMulti = 0;
            // set status of module to Spare
            module.Status = ModuleStatusEnum.Spare;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> SyncVehicleSettingsAsync(string DeviceId, Dictionary<string, object> parameters = null)
        {
            var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
            try
            {
                // sync the vehicle settings
                await deviceTwinHandler.SyncGeneralSettings(DeviceId);

            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Error synchronizing vehicle: {DeviceId} - {ex.ToString()}"));
                throw new GOServerException($"Error synchronizing vehicle: {DeviceId} - {ex.ToString()}");
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

        public async Task<ComponentResponse<string>> SyncTimezone(string DeviceId, Dictionary<string, object> parameters = null)
        {
            var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
            try
            {
                // sync the vehicle settings
                await deviceTwinHandler.SyncTimezone(DeviceId);

            }
            catch (Exception ex)
            {
                _logger.LogError(new GOServerException($"Error synchronizing vehicle: {ex.Message}"));
                throw new GOServerException($"Error synchronizing vehicle: {ex.Message}");
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

        public async Task<ComponentResponse<bool>> SyncTimezoneAsyncAsync(string TimezoneId, Dictionary<string, object> parameters = null)
        {
            var timezone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { Guid.Parse(TimezoneId) })).SingleOrDefault();
            var sites = (await timezone.LoadSitesAsync());
            foreach (var site in sites)
            {
                var departments = (await site.LoadDepartmentItemsAsync());
                foreach (var department in departments)
                {
                    var vehicles = (await department.LoadVehiclesAsync());
                    foreach (var vehicle in vehicles)
                    {
                        var module = await vehicle.LoadModuleAsync();
                        if (module.IoTDevice != null)
                        {
                            await SyncTimezone(module.IoTDevice);
                        }
                    }
                }
            }
            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<string[]>> SyncAllVehicleSettingsAsync(Dictionary<string, object> parameters = null)
        {
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null);

            var deviceIds = new List<string>();

            if (vehicles == null || !vehicles.Any())
            {
                return new ComponentResponse<string[]>(null);
            }

            foreach (var vehicle in vehicles)
            {
                var module = await vehicle.LoadModuleAsync();

                if (module != null)
                {
                    deviceIds.Add(module.IoTDevice);
                    await SyncVehicleSettingsAsync(module.IoTDevice, parameters);
                }
            }

            return new ComponentResponse<string[]>(deviceIds.ToArray());
        }

        public async Task<ComponentResponse<string>> SyncDriversToVehicleAsync(string DeviceId, Dictionary<string, object> parameters = null)
        {
            var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
            await deviceTwinHandler.SyncDriverToVehicle(DeviceId);
            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

        /// <summary>
        /// GetAllDeviceTwinConnection Method
        /// </summary>
        /// <param></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> GetAllDeviceTwinsLastActiveTimeAsync(System.Guid customerId, System.Guid siteId, System.Guid departmentId, Dictionary<string, object> parameters = null)
        {
            // Get messages in SENT status
            var messages = await _dataFacade.MessageHistoryDataProvider.GetCollectionAsync(null,
                $"MessageStatus == @0", new object[] { (int)MessageStatusEnum.SENT },
                skipSecurity: true);

            if (!messages.Any())
            {
                return new ComponentResponse<bool>(true);
            }

            // Build vehicle filter predicate based on provided parameters
            string vehicleFilterPredicate = "@0.Contains(outerIt.Id)";
            var vehicleIds = messages.Select(m => m.VehicleId).Distinct().ToList();
            object[] vehicleQueryParams = new object[] { vehicleIds };

            // Apply additional filters based on priority (department > site > customer)
            if (departmentId != Guid.Empty)
            {
                // Department filter has highest priority
                vehicleFilterPredicate += " && DepartmentId == @1";
                vehicleQueryParams = new object[] { vehicleIds, departmentId };
            }
            else if (siteId != Guid.Empty)
            {
                // Site filter has medium priority
                vehicleFilterPredicate += " && SiteId == @1";
                vehicleQueryParams = new object[] { vehicleIds, siteId };
            }
            else if (customerId != Guid.Empty)
            {
                // Customer filter has lowest priority
                vehicleFilterPredicate += " && CustomerId == @1";
                vehicleQueryParams = new object[] { vehicleIds, customerId };
            }

            // Get vehicles efficiently with filters
            _logger.LogInformation($"Fetching vehicles with filter: {vehicleFilterPredicate}");
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                null,
                vehicleFilterPredicate,
                vehicleQueryParams,
                skipSecurity: true);

            if (!vehicles.Any())
            {
                _logger.LogInformation("No vehicles found matching the criteria");
                return new ComponentResponse<bool>(true);
            }

            // Extract IoT device IDs from vehicles
            var deviceIds = new HashSet<string>();
            var vehicleModuleMap = new Dictionary<Guid, string>(); // Map vehicle ID to device ID

            foreach (var vehicle in vehicles)
            {
                var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                if (module?.IoTDevice != null)
                {
                    deviceIds.Add(module.IoTDevice);
                    vehicleModuleMap[vehicle.Id] = module.IoTDevice;
                }
            }

            if (!deviceIds.Any())
            {
                _logger.LogInformation("No IoT devices found for the vehicles");
                return new ComponentResponse<bool>(true);
            }

            // OPTION 1: Actual IoT Hub query (uncomment to use real data)
            // Get device statuses in one query
            var query = _registryManager.CreateQuery("SELECT DeviceId, ConnectionState, LastActivityTime FROM devices");
            var devices = new List<DeviceConnection>();
            while (query.HasMoreResults)
            {
                var result = await query.GetNextAsJsonAsync();
                devices.AddRange(result
                    .Select(json => JsonConvert.DeserializeObject<DeviceConnection>(json))
                    .Where(d => deviceIds.Contains(d.DeviceId)));
            }

            // OPTION 2: Mock implementation (comment out when using real data)
            // var devices = new List<DeviceConnection>();
            // var random = new Random();

            // foreach (var deviceId in deviceIds)
            // {
            //     // Create mock device with random connection state and activity time
            //     var device = new DeviceConnection
            //     {
            //         DeviceId = deviceId,
            //         ConnectionState = random.Next(10) < 6 ? "Connected" : "Disconnected",
            //         LastActivityTime = DateTime.UtcNow.AddMinutes(-random.Next(0, 60)).ToString("o")
            //     };

            //     devices.Add(device);
            //     _logger.LogDebug($"Mock device {deviceId} set to {device.ConnectionState} with last activity {device.LastActivityTime}");
            // }            
            // _logger.LogInformation($"Mock data created for {devices.Count} devices");

            // Process updates in memory
            var messagesToUpdate = new List<MessageHistoryDataObject>();
            foreach (var vehicle in vehicles)
            {
                if (vehicleModuleMap.TryGetValue(vehicle.Id, out var deviceId))
                {
                    var device = devices.FirstOrDefault(d => d.DeviceId == deviceId);

                    if (device != null)
                    {
                        var lastActivityTime = DateTime.Parse(device.LastActivityTime);
                        var vehicleMessages = messages.Where(m => m.VehicleId == vehicle.Id);

                        foreach (var message in vehicleMessages)
                        {
                            bool isDeviceConnected = device.ConnectionState == "Connected";
                            if (lastActivityTime > message.SentTimestamp || isDeviceConnected)
                            {
                                message.MessageStatus = MessageStatusEnum.RECEIVED;
                                message.DeliveredTimestamp = isDeviceConnected ? DateTime.UtcNow : lastActivityTime;
                                messagesToUpdate.Add(message);
                            }
                        }
                    }
                }
            }

            // Save messages sequentially to avoid concurrency issues
            if (messagesToUpdate.Any())
            {
                _logger.LogInformation($"Updating {messagesToUpdate.Count} messages");

                foreach (var message in messagesToUpdate)
                {
                    try
                    {
                        await _dataFacade.MessageHistoryDataProvider.SaveAsync(message, skipSecurity: true);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(new GOServerException($"Failed to update message {message.Id}: {ex.Message}"));
                    }
                }
            }

            return new ComponentResponse<System.Boolean>(true);
        }
    }
}

