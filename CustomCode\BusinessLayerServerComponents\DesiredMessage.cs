﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public partial class DesiredMessage
    {
        //Default values
        public List<object> DriverList { get; set; } = new List<object>();
        public List<object> CheckList { get; set; } = new List<object>();
        public List<object> MasterList { get; set; } = new List<object>();
        public List<object> SMastList { get; set; } = new List<object>();
        public List<object> TechList { get; set; } = new List<object>();
        public List<object> ChecklistTime { get; set; } = new List<object>
                    {
                        new { timeslot = -1 },
                        new { timeslot = -1 },
                        new { timeslot = -1 },
                        new { timeslot = -1 }
                    };
        public object IdleSetting { get; set; } = new { idle_time = 900, idle_mode = 0, idle_input = 16 };
        public object ImpactLockout { get; set; } = new { red_impact_threshold = 0 };
        public object FullLockoutSetting { get; set; } = new { timeout = 0, enable = false };
        public int SurveyTimeoutSetting { get; set; } = 60;
        public int ReportIntervalSec { get; set; } = 300;
        public bool Convor { get; set; } = false;
        public bool UnlockReasonScreen { get; set; } = false;

    }
}
