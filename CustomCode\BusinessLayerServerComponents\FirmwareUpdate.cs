﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Azure.Devices;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// FirmwareUpdate Component
	///  
	/// </summary>
    public partial class FirmwareUpdate : BaseServerComponent, IFirmwareUpdate
    {
        private RegistryManager _registryManager;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        public FirmwareUpdate(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler) : base(serviceProvider, configuration, dataFacade)
		{
            _registryManager = RegistryManager.CreateFromConnectionString(_configuration["IoThubConnectionString"]);
            _deviceMessageHandler = deviceMessageHandler;
        }

		/// <summary>
        /// SendFirmwareUpdateToDevices Method
		/// </summary>
		/// <param name="IoTDevices"></param>
		/// <param name="FirmwareVersion"></param>
        /// <returns></returns>
		public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> SendFirmwareUpdateToDevicesAsync(System.String[] IoTDevices, System.String FirmwareVersion, Dictionary<string, object> parameters = null) 
		{
			foreach (var deviceId in IoTDevices)
			{
                var device = await _registryManager.GetDeviceAsync(deviceId);
                if (device != null)
				{
                    var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
                    await deviceTwinHandler.UpdateFirmware(deviceId, FirmwareVersion);
                }
            }
			return new ComponentResponse<bool>(true);
		}
    }
}
