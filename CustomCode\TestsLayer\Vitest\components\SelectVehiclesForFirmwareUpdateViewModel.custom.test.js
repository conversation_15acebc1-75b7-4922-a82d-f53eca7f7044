import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

global.ko = ko;

describe('SelectVehiclesForFirmwareUpdateViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock Math.uuid function
        global.Math.uuid = vi.fn(() => 'mock-uuid-' + Math.random().toString(36).substr(2, 9));

        // Mock ko.isObservable function
        ko.isObservable = vi.fn((obj) => {
            return obj && typeof obj === 'function' && obj.hasOwnProperty('subscribe');
        });

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/SelectVehiclesForFirmwareUpdateViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create mock vehicle objects
        const mockVehicle1 = {
            Data: {
                Id: ko.observable('vehicle1'),
                Name: ko.observable('Vehicle 1')
            }
        };

        const mockVehicle2 = {
            Data: {
                Id: ko.observable('vehicle2'),
                Name: ko.observable('Vehicle 2')
            }
        };

        const mockVehicle3 = {
            Data: {
                Id: ko.observable('vehicle3'),
                Name: ko.observable('Vehicle 3')
            }
        };

        // Create base view model with required properties
        viewModel = {
            subscriptions: [],
            VehicleObjectCollection: ko.observableArray([mockVehicle1, mockVehicle2, mockVehicle3]),
            selectedVehicles: ko.observableArray([]),
            checkedStates: ko.observableArray([
                ko.observable(false),
                ko.observable(false),
                ko.observable(false)
            ]),
            updateFirmwareRequest: {
                Data: {
                    Id: ko.observable('firmware-request-1')
                }
            },
            setGridPageNumber: vi.fn(),
            Rebind: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.SelectVehiclesForFirmwareUpdateViewModelCustom(viewModel);
        customViewModel.initialize();

        // Initialize checkedStates properly
        viewModel.updateCheckStates();

        // Spy on addFilterPredicateAndParameters after custom code is loaded
        viewModel.addFilterPredicateAndParameters = vi.fn();
    });

    afterEach(() => {
        // Cleanup
        if (customViewModel && customViewModel.release) {
            customViewModel.release();
        }
    });

    describe('Initialization', () => {
        it('should initialize with empty selectedVehicles array', () => {
            expect(viewModel.selectedVehicles()).toEqual([]);
        });

        it('should initialize filter observables', () => {
            expect(viewModel.customerId).toBeDefined();
            expect(viewModel.siteId).toBeDefined();
            expect(viewModel.departmentId).toBeDefined();
        });

        it('should initialize checkedStates array', () => {
            expect(viewModel.checkedStates).toBeDefined();
        });
    });

    describe('updateCheckStates', () => {
        it('should update checked states based on selected vehicles', () => {
            // Add a vehicle to selectedVehicles
            const selectedVehicle = {
                Data: {
                    Id: ko.observable('uuid-1'),
                    VehicleId: ko.observable('vehicle1'),
                    UpdateFirmwareRequestId: ko.observable('firmware-request-1'),
                    IsNew: ko.observable(true),
                    IsMarkedForDeletion: ko.observable(false),
                    IsDirty: ko.observable(true)
                }
            };
            viewModel.selectedVehicles.push(selectedVehicle);

            viewModel.updateCheckStates();

            // Check that the first vehicle is marked as checked
            expect(viewModel.checkedStates().length).toBe(3);
            expect(viewModel.checkedStates()[0]()).toBe(true);
            expect(viewModel.checkedStates()[1]()).toBe(false);
            expect(viewModel.checkedStates()[2]()).toBe(false);
        });

        it('should clear checked states when called', () => {
            // Pre-populate with some states
            viewModel.checkedStates.push(ko.observable(true));
            viewModel.checkedStates.push(ko.observable(false));

            viewModel.updateCheckStates();

            // Should be cleared and re-populated
            expect(viewModel.checkedStates().length).toBe(3);
        });
    });

    describe('toggleChecked', () => {
        it('should add vehicle to selectedVehicles when toggling from unchecked to checked', () => {
            const mockEvent = { stopPropagation: vi.fn() };

            viewModel.toggleChecked(0, mockEvent);

            // Verify vehicle was added to selectedVehicles
            expect(viewModel.selectedVehicles().length).toBe(1);
            expect(viewModel.selectedVehicles()[0].Data.VehicleId()).toBe('vehicle1');
        });

        it('should remove vehicle from selectedVehicles when toggling from checked to unchecked', () => {
            // First add a vehicle
            const selectedVehicle = {
                Data: {
                    Id: ko.observable('uuid-2'),
                    VehicleId: ko.observable('vehicle1'),
                    UpdateFirmwareRequestId: ko.observable('firmware-request-1'),
                    IsNew: ko.observable(true),
                    IsMarkedForDeletion: ko.observable(false),
                    IsDirty: ko.observable(true)
                }
            };
            viewModel.selectedVehicles.push(selectedVehicle);

            // Re-initialize checkedStates and mark as checked
            viewModel.updateCheckStates();
            const currentCheckedStates = viewModel.checkedStates();
            currentCheckedStates[0](true);
            currentCheckedStates[1](false);
            currentCheckedStates[2](false);

            const mockEvent = { stopPropagation: vi.fn() };
            viewModel.toggleChecked(0, mockEvent);

            // Verify vehicle was removed from selectedVehicles
            expect(viewModel.selectedVehicles().length).toBe(0);
        });

        it('should create proper vehicle object structure when adding', () => {
            const mockEvent = { stopPropagation: vi.fn() };

            viewModel.toggleChecked(1, mockEvent);

            const addedVehicle = viewModel.selectedVehicles()[0];
            expect(addedVehicle.Data.Id).toBeDefined();
            expect(addedVehicle.Data.VehicleId()).toBe('vehicle2');
            expect(addedVehicle.Data.UpdateFirmwareRequestId()).toBe('firmware-request-1');
            expect(addedVehicle.Data.IsNew()).toBe(true);
            expect(addedVehicle.Data.IsMarkedForDeletion()).toBe(false);
            expect(addedVehicle.Data.IsDirty()).toBe(true);
        });
    });

    describe('selectAll and deselectAll', () => {
        it('should select all vehicles when selectAll is called', () => {
            viewModel.selectAll();

            expect(viewModel.selectedVehicles().length).toBe(3);
            expect(viewModel.selectedVehicles()[0].Data.VehicleId()).toBe('vehicle1');
            expect(viewModel.selectedVehicles()[1].Data.VehicleId()).toBe('vehicle2');
            expect(viewModel.selectedVehicles()[2].Data.VehicleId()).toBe('vehicle3');
        });

        it('should deselect all vehicles when deselectAll is called', () => {
            // First select all
            viewModel.selectAll();
            expect(viewModel.selectedVehicles().length).toBe(3);

            // Then deselect all
            viewModel.deselectAll();
            expect(viewModel.selectedVehicles().length).toBe(0);
        });
    });

    describe('addFilterPredicateAndParameters', () => {
        it('should override the filter method with custom implementation', () => {
            const predicate = 'test predicate';
            const parameters = ['param1', 'param2'];

            viewModel.addFilterPredicateAndParameters(predicate, parameters);

            expect(viewModel.addFilterPredicateAndParameters).toHaveBeenCalledWith(predicate, parameters);
        });
    });

    describe('setSiteFilter', () => {
        it('should apply site filter when siteId is provided', () => {
            const siteId = 'site-123';

            viewModel.setSiteFilter(siteId);

            expect(viewModel.baseFilterPredicate).toBe('SiteId == @0');
            expect(viewModel.baseFilterParameters).toBe('[{ "TypeName" : "System.Guid", "Value" : "' + siteId + '" }]');
            expect(viewModel.baseFilterParametersCount).toBe(1);
            expect(viewModel.filterPredicate).toBe('SiteId == @0');
            expect(viewModel.filterParameters).toBe('[{ "TypeName" : "System.Guid", "Value" : "' + siteId + '" }]');
            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });

        it('should clear site filter when siteId is null', () => {
            // First apply a filter
            viewModel.setSiteFilter('site-123');

            // Then clear it
            viewModel.setSiteFilter(null);

            expect(viewModel.baseFilterPredicate).toBeNull();
            expect(viewModel.baseFilterParameters).toBeNull();
            expect(viewModel.baseFilterParametersCount).toBe(0);
            expect(viewModel.filterPredicate).toBeNull();
            expect(viewModel.filterParameters).toBeNull();
            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });

        it('should clear site filter when siteId is undefined', () => {
            // First apply a filter
            viewModel.setSiteFilter('site-123');

            // Then clear it
            viewModel.setSiteFilter(undefined);

            expect(viewModel.baseFilterPredicate).toBeNull();
            expect(viewModel.baseFilterParameters).toBeNull();
            expect(viewModel.baseFilterParametersCount).toBe(0);
            expect(viewModel.filterPredicate).toBeNull();
            expect(viewModel.filterParameters).toBeNull();
            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });

        it('should clear site filter when siteId is empty string', () => {
            // First apply a filter
            viewModel.setSiteFilter('site-123');

            // Then clear it
            viewModel.setSiteFilter('');

            expect(viewModel.baseFilterPredicate).toBeNull();
            expect(viewModel.baseFilterParameters).toBeNull();
            expect(viewModel.baseFilterParametersCount).toBe(0);
            expect(viewModel.filterPredicate).toBeNull();
            expect(viewModel.filterParameters).toBeNull();
            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });

        it('should handle multiple site filter applications', () => {
            const siteId1 = 'site-1';
            const siteId2 = 'site-2';

            viewModel.setSiteFilter(siteId1);
            expect(viewModel.baseFilterPredicate).toBe('SiteId == @0');
            expect(viewModel.baseFilterParameters).toBe('[{ "TypeName" : "System.Guid", "Value" : "' + siteId1 + '" }]');

            viewModel.setSiteFilter(siteId2);
            expect(viewModel.baseFilterPredicate).toBe('SiteId == @0');
            expect(viewModel.baseFilterParameters).toBe('[{ "TypeName" : "System.Guid", "Value" : "' + siteId2 + '" }]');
        });

        it('should trigger grid rebind when filter is applied', () => {
            const siteId = 'site-456';

            viewModel.setSiteFilter(siteId);

            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });

        it('should trigger grid rebind when filter is cleared', () => {
            viewModel.setSiteFilter(null);

            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });
    });
}); 