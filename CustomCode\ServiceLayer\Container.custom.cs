﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using GenerativeObjects.Practices;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using FleetXQ.BusinessLayer.Components.Server;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.OAuth;
using CustomCode.ServiceLayer.Security;
using Microsoft.Extensions.DependencyInjection.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Microsoft.Extensions.Configuration;
using System.IO;
using System;
using FleetXQ.Data.DataProviders.Database;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses.NHibernate;
using FleetXQ.ServiceLayer.Middleware;
using Microsoft.AspNetCore.Hosting;

namespace FleetXQ.ServiceLayer
{
    public static class CustomContainer
    {
        public static void RegisterCustomTypes(IServiceCollection services)
        {
            // Register Azure Blob Storage Client Factory
            services.AddScoped<IStorageClientFactory, AzureBlobStorageClientFactory>();

            // Register File Service
            services.AddScoped<IFileService, FileService>();

            //TODO : register here your custom types
            // example:
            // services.AddScoped<IDataProviderExtension<MyDataObject>, MyDataProviderExtension>();
            // other example: 
            // service.AddTransient<IImportExportComponentExtension<MyImportComponent, MyDataObject>, MyImportExtension>();

            services.AddScoped<IModuleValidator, ModuleValidator>();
            services.AddScoped<IVehicleAccessQueueService, VehicleAccessQueueService>();
            services.AddScoped<IUserAccessQueueService, UserAccessQueueService>();
            services.AddScoped<IVehicleSyncQueueService, VehicleSyncQueueService>();
            services.AddScoped<IDataProviderExtension<AccessGroupDataObject>, AccessGroupDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<CustomerDataObject>, CustomerDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<SessionDataObject>, SessionDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<VehicleDataObject>, VehicleDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllVORStatusStoreProcedureDataObject>, AllVORStatusStoreProcedureDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<DetailedSessionViewDataObject>, DetailedSessionViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllImpactsViewDataObject>, AllImpactsViewDataObjectExtension>();
            services.AddScoped<IDataProviderExtension<AllChecklistResultViewDataObject>, AllChecklistResultViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<PersonDataObject>, PersonDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<LicenceDetailDataObject>, GeneralLicenseDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<DriverDataObject>, DriverDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<CardDataObject>, CardDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<FirmwareDataObject>, FirmwareDataProviderExtension>();
            // services.AddScoped<IDataProviderExtension<CardDataObject>, CardSyncVehicleAccessDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<VehicleToPreOpChecklistViewDataObject>, VehicleToPreOpChecklistViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<ChecklistDetailDataObject>, ChecklistDetailDataProviderExtension>();
            services.AddScoped<IDeviceMessageHandler, DeviceMessageHandler>();
            services.AddScoped<IDataProviderExtension<ChecklistSettingsDataObject>, ChecklistSettingsDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<ModuleDataObject>, ModuleDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<ServiceSettingsDataObject>, ServiceSettingsDataProviderExtension>();
            services.AddScoped<IDeviceTwinHandler, DeviceTwinHandler>();
            services.AddScoped<IVehicleUtils, VehicleUtils>();
            services.AddScoped<IDataProviderExtension<GORoleDataObject>, GORoleDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<SiteDataObject>, SiteDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<DepartmentDataObject>, DepartmentDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<VehicleLockoutDataObject>, VehicleLockoutDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<GOUserDataObject>, GOUserDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<GOUserDepartmentDataObject>, GOUserDepartmentDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<DealerDataObject>, DealerDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<PreOperationalChecklistDataObject>, PreOperationalChecklistDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<NetworkSettingsDataObject>, NetworkSettingsDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<CurrentStatusDriverViewDataObject>, CurrentStatusDriverViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<CurrentStatusVehicleViewDataObject>, CurrentStatusVehicleViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllMessageHistoryStoreProcedureDataObject>, AllMessageHistoryStoreProcedureDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllLicenseExpiryViewDataObject>, AllLicenseExpiryViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<DetailedVORSessionStoreProcedureDataObject>, DetailedVORSessionStoreProcedureDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllVehicleCalibrationStoreProcedureDataObject>, AllVehicleCalibrationStoreProcedureDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<LoggedHoursVersusSeatHoursViewDataObject>, LoggedHoursVersusSeatHoursViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<IncompletedChecklistViewDataObject>, IncompletedChecklistViewDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<OnDemandAuthorisationStoreProcedureDataObject>, OnDemandAuthorisationStoreProcedureDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllEmailSubscriptionStoreProcedureDataObject>, AllEmailSubscriptionStoreProcedureDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<AllVehicleUnlocksViewDataObject>, AllVehicleUnlocksViewDataProviderExtension>();

            // Race condition protection extensions
            services.AddScoped<IDataProviderExtension<DepartmentVehicleNormalCardAccessDataObject>, DepartmentVehicleNormalCardAccessDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<ModelVehicleNormalCardAccessDataObject>, ModelVehicleNormalCardAccessDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<SiteVehicleNormalCardAccessDataObject>, SiteVehicleNormalCardAccessDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<PerVehicleNormalCardAccessDataObject>, PerVehicleNormalCardAccessDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<PersonToModelVehicleNormalAccessViewDataObject>, PersonToModelVehicleNormalAccessViewDataProviderExtension>();

            services.AddScoped<IOAuthProvider, MultipleCustomersOAuthProvider>();
            // Add the MembershipProviderSelector replacement
            services.Replace(ServiceDescriptor.Scoped<IMembershipProviderSelector, CustomMembershipProviderSelector>());

            services.AddScoped<IImportExportComponentExtension<PersonImportSection0Component,
        PersonDataObject>, PersonImportComponentExtension>();

            services.AddScoped<IImportExportComponentExtension<CardImportSection0Component,
            CardDataObject>, CardImportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<SpareModuleImportSection0Component, ModuleDataObject>, SpareModuleImportComponentExtension>();

            services.AddScoped<IImportExportComponentExtension<LicenseByModelImportSection0Component,
            LicenseByModelDataObject>, LicenseByModelImportComponentExtension>();

            services.AddScoped<IImportExportComponentExtension<VehicleAccessImportSection0Component, PersonDataObject>,
                VehicleAccessImportComponentExtension>();

            services.AddScoped<IImportExportComponentExtension<VehicleImportSection0Component,
        VehicleDataObject>, VehicleImportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<VehicleOtherSettingsImportSection0Component,
        VehicleOtherSettingsDataObject>, VehicleOtherSettingsImportComponentExtension>();
            services.AddScoped<IDataProviderExtension<DepartmentHourSettingsDataObject>, DepartmentHourSettingsDataProviderExtension>();
            services.AddScoped<IDataProviderExtension<VehicleOtherSettingsDataObject>, VehicleOtherSettingsDataProviderExtension>();

            services.AddScoped<IImportExportComponentExtension<PreOperationalChecklistImportSection0Component,
        PreOperationalChecklistDataObject>, PreOperationalChecklistImportComponentExtension>();
            // Register the extension
            services.AddScoped<IImportExportComponentExtension<SupervisorAccessImportSection0Component, PersonDataObject>,
                SupervisorAccessImportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<GeneralLicenseImportSection0Component, LicenceDetailDataObject>,
                GeneralLicenseImportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<ImpactReportExportSection0Component,
                AllImpactsViewDataObject>, ImpactReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<PreOpChecklistReportExportSection0Component,
                AllChecklistResultViewDataObject>, PreOpChecklistReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<MachineUnlockReportExportSection0Component,
                AllVehicleUnlocksViewDataObject>, MachineUnlockReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<DriverCurrentStatusReportExportSection0Component,
                CurrentStatusDriverViewDataObject>, DriverCurrentStatusReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<VehicleCurrentStatusReportExportSection0Component,
                CurrentStatusVehicleViewDataObject>, VehicleCurrentStatusReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<ServiceCheckReportExportSection0Component,
                VehicleDataObject>, ServiceCheckReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<LicenseExpiryReportExportSection0Component,
                AllLicenseExpiryViewDataObject>, LicenseExpiryReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<SynchronizationStatusReportExportSection0Component,
                AllMessageHistoryStoreProcedureDataObject>, SynchronizationStatusReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<VehicleCalibrationReportExportSection0Component,
                AllVehicleCalibrationStoreProcedureDataObject>, VehicleCalibrationReportExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<VehicleExportSection0Component,
                VehicleDataObject>, VehicleExportComponentExtension>();
            services.AddScoped<IImportExportComponentExtension<BroadcastMessageHistoryExportSection0Component,
                BroadcastMessageHistoryDataObject>, BroadcastMessageHistoryExportComponentExtension>();

            services.AddScoped<IImportExportComponentExtension<DealerCategoryImportSection0Component,
                ModelDataObject>, DealerCategoryImportComponentExtension>();

            var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile($"appsettings.{env}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // console log AppInsightsKey
            var appInsightsKey = configuration["ApplicationInsights:InstrumentationKey"];

            Console.WriteLine($"AppInsightsKey: {appInsightsKey}");
            if (env != "Development")
            {
                services.AddApplicationInsightsTelemetry(options =>
                {

                    options.ConnectionString = appInsightsKey;

                });
            }

            // Configure simple console logging instead of Application Insights
            services.AddLogging(builder =>
            {
                builder.AddConsole(); // Add console logging
                builder.SetMinimumLevel(LogLevel.Information); // Set minimum log level

                // Optional: Add debug logging in development
                var env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");
                Console.WriteLine($"env: {env}");
                if (env == "Development")
                {
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Information);
                }
            });

            // Register your custom logging service
            services.AddScoped<ILoggingService, LoggingService>();

            // Register the component itself
            services.AddScoped<VehicleAccessImportSection0Component>();


            // Register the data objects we're creating
            services.AddTransient<SiteVehicleNormalCardAccessDataObject>();
            services.AddTransient<DepartmentVehicleNormalCardAccessDataObject>();
            services.AddTransient<ModelVehicleNormalCardAccessDataObject>();
            services.AddTransient<PerVehicleNormalCardAccessDataObject>();
            services.AddTransient<IStartupFilter, SubdomainMiddlewareStartupFilter>();
        }
    }
}
