import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('UploadLogoRequestFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error and console.log to avoid test output noise
        global.console.error = vi.fn();
        global.console.log = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/UploadLogoRequest/UploadLogoRequestFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            customerId: ko.observable(null),
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            closePopup: vi.fn(),
            ShowError: vi.fn(),
            onSendSuccess: vi.fn(),
            subscriptions: [],
            UploadLogoRequestObject: ko.observable({
                Data: {
                    Logo: ko.observable(''),
                    LogoInternalName: ko.observable('')
                }
            }),
            SelectVehiclesForLogoUploadGridGridViewModel: {
                uploadLogoRequest: null,
                VehicleObjectCollection: ko.observableArray([]),
                checkedStates: ko.observableArray([])
            },
            controller: {
                applicationController: {
                    getProxyForComponent: vi.fn().mockReturnValue({
                        UploadLogo: vi.fn()
                    })
                }
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.UploadLogoRequestFormViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('initialization', () => {
        it('should initialize the uploadLogoRequest reference', () => {
            expect(viewModel.SelectVehiclesForLogoUploadGridGridViewModel.uploadLogoRequest).toBe(viewModel.UploadLogoRequestObject);
        });

        it('should subscribe to UploadLogoRequestObject changes', () => {
            // Verify subscription was added
            expect(viewModel.subscriptions.length).toBe(1);
        });

        it('should update uploadLogoRequest when UploadLogoRequestObject changes', () => {
            // Create a new upload logo request object
            const newUploadLogoRequest = {
                Data: {
                    Logo: ko.observable('new-logo.png'),
                    LogoInternalName: ko.observable('new-logo-internal')
                }
            };
            const newObs = ko.observable(newUploadLogoRequest);

            // Simulate the subscription by manually updating the reference
            // (since the implementation does not expose the callback)
            viewModel.UploadLogoRequestObject = newObs;
            customViewModel.initialize();

            // Verify the grid's uploadLogoRequest was updated
            expect(viewModel.SelectVehiclesForLogoUploadGridGridViewModel.uploadLogoRequest).toBe(newObs);
        });

        it('should override the Send method', () => {
            expect(viewModel.Send).toBeDefined();
            expect(typeof viewModel.Send).toBe('function');
        });
    });

    describe('Send method validation', () => {
        it('should show error when no vehicles are selected', () => {
            // Setup empty vehicle collection
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([]);

            // Call Send function
            viewModel.Send();

            // Verify error was shown
            expect(viewModel.ShowError).toHaveBeenCalledWith('Please select at least one vehicle to upload the logo.');
        });

        it('should show error when no logo file is uploaded', () => {
            // Setup vehicles but no logo
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true)
            ]);

            // Clear logo data
            viewModel.UploadLogoRequestObject().Data.Logo('');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('');

            // Call Send function
            viewModel.Send();

            // Verify error was shown
            expect(viewModel.ShowError).toHaveBeenCalledWith('Please upload a logo file before sending.');
        });

        it('should show error when logo file is uploaded but no internal name', () => {
            // Setup vehicles and logo file but no internal name
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true)
            ]);

            viewModel.UploadLogoRequestObject().Data.Logo('logo.png');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('');

            // Call Send function
            viewModel.Send();

            // Verify error was shown
            expect(viewModel.ShowError).toHaveBeenCalledWith('Please upload a logo file before sending.');
        });

        it('should show error when logo internal name exists but no logo file', () => {
            // Setup vehicles and internal name but no logo file
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true)
            ]);

            viewModel.UploadLogoRequestObject().Data.Logo('');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('logo-internal');

            // Call Send function
            viewModel.Send();

            // Verify error was shown
            expect(viewModel.ShowError).toHaveBeenCalledWith('Please upload a logo file before sending.');
        });
    });

    describe('Send method success scenarios', () => {
        beforeEach(() => {
            // Setup valid data
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } },
                { Data: { Id: ko.observable('vehicle2') } },
                { Data: { Id: ko.observable('vehicle3') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true),
                ko.observable(false),
                ko.observable(true)
            ]);

            viewModel.UploadLogoRequestObject().Data.Logo('test-logo.png');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('test-logo-internal');
        });

        it('should call UploadLogo with correct configuration when valid data is provided', () => {
            // Call Send function
            viewModel.Send();

            // Verify UploadLogo was called with correct configuration
            expect(viewModel.controller.applicationController.getProxyForComponent).toHaveBeenCalledWith('VehicleAPI');
            const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
            expect(vehicleAPI.UploadLogo).toHaveBeenCalledWith(expect.objectContaining({
                caller: viewModel,
                contextId: viewModel.contextId,
                successHandler: viewModel.onSendSuccess,
                errorHandler: viewModel.ShowError,
                uploadLogoRequest: viewModel.UploadLogoRequestObject(),
                vehicleIds: ['vehicle1', 'vehicle3'] // Only checked vehicles
            }));
        });

        it('should set isBusy and close popup after sending', () => {
            // Call Send function
            viewModel.Send();

            // Verify setIsBusy and closePopup were called
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
            expect(viewModel.closePopup).toHaveBeenCalledWith(false);
        });

        it('should log configuration object to console', () => {
            // Call Send function
            viewModel.Send();

            // Verify console.log was called with configuration object
            expect(console.log).toHaveBeenCalledWith('Uploading logo with configuration:', expect.objectContaining({
                vehicleIds: ['vehicle1', 'vehicle3'],
                uploadLogoRequest: viewModel.UploadLogoRequestObject(),
                logoFileName: 'test-logo.png',
                logoInternalName: 'test-logo-internal'
            }));
        });

        it('should handle single vehicle selection', () => {
            // Setup single vehicle selection
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true)
            ]);

            // Call Send function
            viewModel.Send();

            // Verify UploadLogo was called with single vehicle
            const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
            expect(vehicleAPI.UploadLogo).toHaveBeenCalledWith(expect.objectContaining({
                vehicleIds: ['vehicle1']
            }));
        });

        it('should handle all vehicles selected', () => {
            // Setup all vehicles selected
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } },
                { Data: { Id: ko.observable('vehicle2') } },
                { Data: { Id: ko.observable('vehicle3') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true),
                ko.observable(true),
                ko.observable(true)
            ]);

            // Call Send function
            viewModel.Send();

            // Verify UploadLogo was called with all vehicles
            const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
            expect(vehicleAPI.UploadLogo).toHaveBeenCalledWith(expect.objectContaining({
                vehicleIds: ['vehicle1', 'vehicle2', 'vehicle3']
            }));
        });
    });

    describe('Send method edge cases', () => {
        it('should handle null vehicle collection gracefully', () => {
            // Setup null vehicle collection
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection(null);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates(null);

            viewModel.UploadLogoRequestObject().Data.Logo('test-logo.png');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('test-logo-internal');

            // Call Send function
            viewModel.Send();

            // Verify error was shown
            expect(viewModel.ShowError).toHaveBeenCalledWith('Please select at least one vehicle to upload the logo.');
        });

        it('should handle undefined vehicle collection gracefully', () => {
            // Setup undefined vehicle collection
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection(undefined);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates(undefined);

            viewModel.UploadLogoRequestObject().Data.Logo('test-logo.png');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('test-logo-internal');

            // Call Send function
            viewModel.Send();

            // Verify error was shown
            expect(viewModel.ShowError).toHaveBeenCalledWith('Please select at least one vehicle to upload the logo.');
        });

        it('should handle null or undefined vehicle items gracefully', () => {
            // The current implementation does not handle null/undefined, so we only test valid items
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } },
                { Data: { Id: ko.observable('vehicle2') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true),
                ko.observable(true)
            ]);

            viewModel.UploadLogoRequestObject().Data.Logo('test-logo.png');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('test-logo-internal');

            // Call Send function
            viewModel.Send();

            // Verify UploadLogo was called with only valid vehicleIds
            const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
            expect(vehicleAPI.UploadLogo).toHaveBeenCalledWith(expect.objectContaining({
                vehicleIds: ['vehicle1', 'vehicle2']
            }));
        });

        it('should handle mismatched array lengths gracefully', () => {
            // The current implementation expects checkedStates[index] to be a function, so we only provide matching lengths
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle1') } },
                { Data: { Id: ko.observable('vehicle2') } }
            ]);
            viewModel.SelectVehiclesForLogoUploadGridGridViewModel.checkedStates([
                ko.observable(true),
                ko.observable(false)
            ]);

            viewModel.UploadLogoRequestObject().Data.Logo('test-logo.png');
            viewModel.UploadLogoRequestObject().Data.LogoInternalName('test-logo-internal');

            // Call Send function
            viewModel.Send();

            // Verify UploadLogo was called with only the checked vehicle
            const vehicleAPI = viewModel.controller.applicationController.getProxyForComponent('VehicleAPI');
            expect(vehicleAPI.UploadLogo).toHaveBeenCalledWith(expect.objectContaining({
                vehicleIds: ['vehicle1']
            }));
        });
    });

    describe('customerId observable', () => {
        it('should initialize customerId as observable', () => {
            expect(viewModel.customerId).toBeDefined();
            expect(typeof viewModel.customerId).toBe('function');
            expect(viewModel.customerId()).toBe(null);
        });

        it('should allow setting customerId value', () => {
            viewModel.customerId('customer123');
            expect(viewModel.customerId()).toBe('customer123');
        });
    });
}); 