using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class DepartmentDataProviderExtension : IDataProviderExtension<DepartmentDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;

        public DepartmentDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
        }
    }
} 