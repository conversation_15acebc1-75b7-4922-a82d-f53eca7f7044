﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// LocationServiceAPI Component
	/// GPS Location management 
	/// </summary>
    public partial class LocationServiceAPI : BaseServerComponent, ILocationServiceAPI 
    {
        public LocationServiceAPI(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
        {
        }

        /// <summary>
        /// StoreGPSLocation Method
        /// </summary>
        /// <param name="Message">JSON Message</param>
        /// <returns></returns>
        public System.Threading.Tasks.Task<ComponentResponse<System.String>> StoreGPSLocationAsync(System.String Message, Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return System.Threading.Tasks.Task.FromResult(new ComponentResponse<string>(default(System.String))); 
		}
		/// <summary>
        /// StoreWIFIPosition Method
		/// </summary>
		/// <param name="Message">JSON Message</param>
        /// <returns></returns>
		public System.Threading.Tasks.Task<ComponentResponse<System.String>> StoreWIFIPositionAsync(System.String Message, Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return System.Threading.Tasks.Task.FromResult(new ComponentResponse<string>(default(System.String))); 
		}
	}
}
