﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Threading.Tasks;
using Microsoft.Azure.Devices;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Linq;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Globalization;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class ModuleDataProviderExtension : IDataProviderExtension<ModuleDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly IServiceProvider _serviceProvider;
        private readonly IAuthentication _authentication;
        private RegistryManager _registryManager;
        public ModuleDataProviderExtension(IDataFacade dataFacade, IConfiguration configuration, IDeviceMessageHandler deviceMessageHandler, IServiceProvider serviceProvider, IAuthentication authentication)
        {
            _dataFacade = dataFacade;
            _deviceMessageHandler = deviceMessageHandler;
            _serviceProvider = serviceProvider;
            _authentication = authentication;
            _registryManager = RegistryManager.CreateFromConnectionString(configuration["IoThubConnectionString"]);
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSet;
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            // Only process if the result is a collection of ModuleDataObject
            if (arg.Result is DataObjectCollection<ModuleDataObject> items && arg.PageNumber > 0 && arg.PageSize > 0)
            {
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();

                if (userClaims == null || userClaims.UserId == null)
                {
                    return;
                }

                var appUserClaims = userClaims as AppUserClaims;

                var preferredLocale = appUserClaims.UserPreferredLocale != null ? appUserClaims.UserPreferredLocale : appUserClaims.CustomerPreferredLocale;

                foreach (var item in items)
                {
                    try
                    {
                        var culture = !string.IsNullOrEmpty(preferredLocale) ? new CultureInfo(preferredLocale) : new CultureInfo("en-US");

                        if (item.SimCardDate.HasValue)
                        {
                            item.SimCardDateDisplay = item.SimCardDate.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern}", culture);
                        }
                    }
                    catch (CultureNotFoundException)
                    {
                        // If the culture is invalid, just return without modifying the datetime
                        return;
                    }
                }
            }
        }

        private async Task OnBeforeSaveDataSet(OnBeforeSaveDataSetEventArgs e)
        {
            var module = e.Entity as ModuleDataObject;

            if (module == null)
            {
                return;
            }

            if (module.Calibration == 100)
            {
                var G_FORCE_COEFFICIENT = 0.00388;

                module.BlueImpact = Math.Round(G_FORCE_COEFFICIENT * Math.Sqrt(module.FSSSBase * ((module.FSSXMulti / 100) + 1)), 1);
                module.AmberImpact = Math.Round(G_FORCE_COEFFICIENT * Math.Sqrt((module.FSSSBase * ((module.FSSXMulti / 100) + 1)) * 5), 1);
                module.RedImpact = Math.Round(G_FORCE_COEFFICIENT * Math.Sqrt((module.FSSSBase * ((module.FSSXMulti / 100) + 1)) * 10), 1);
            }
            else
            {
                module.BlueImpact = 0;
                module.AmberImpact = 0;
                module.RedImpact = 0;
            }

            if (module.IsNew) {
                var DeviceId = module.IoTDevice;

                // add the new device to IoT Hub Server 
                var device = new Device(DeviceId);
                // check if the device already exists
                var existingDevice = _registryManager.GetDeviceAsync(DeviceId).Result;
                if (existingDevice == null)
                {
                    await _registryManager.AddDeviceAsync(device);
                    // check if the device was successfully registered
                    var devices = _registryManager.GetDeviceAsync(DeviceId).Result;
                    if (devices == null)
                    {
                        throw new GOServerException($"Error registering device {DeviceId}");
                    }
                }
                return;
            }

            // get old module data
            var moduleBeforeSave = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { module.Id })).SingleOrDefault();
            if (moduleBeforeSave == null)
            {
                return;
            }

            // check if any of the IoT Device, CCID, RA Number, Tech Number, Sim Card Number, Sim Card Date has changed
            // if one of them has changed AND it is not a module swap, create a new module history
            if (
(                moduleBeforeSave.IoTDevice != module.IoTDevice || 
                moduleBeforeSave.CCID != module.CCID || 
                moduleBeforeSave.RANumber != module.RANumber || 
                moduleBeforeSave.TechNumber != module.TechNumber || 
                moduleBeforeSave.SimCardNumber != module.SimCardNumber || 
                moduleBeforeSave.SimCardDate != module.SimCardDate ||
                moduleBeforeSave.Status != module.Status ||
                moduleBeforeSave.ModuleType != module.ModuleType) && (moduleBeforeSave.SwapDate == module.SwapDate)  
                )
            {
                await createModuleHistory(module, moduleBeforeSave);
            }

            return;
        }

        private async Task createModuleHistory(ModuleDataObject module, ModuleDataObject moduleBeforeSave)  {
            
            // create module history
            var moduleHistory = _serviceProvider.GetRequiredService<ModuleHistoryDataObject>();
            moduleHistory.ModuleId = module.Id;
            moduleHistory.EditDateTime = DateTime.UtcNow;
            moduleHistory.Status = module.Status;
            moduleHistory.ModuleType = module.ModuleType;
            moduleHistory.NewIoTDeviceId = module.IoTDevice;
            moduleHistory.OldIoTDeviceId = moduleBeforeSave.IoTDevice;
            moduleHistory.CCID = module.CCID;
            moduleHistory.RANumber = module.RANumber;
            moduleHistory.TechNumber = module.TechNumber;
            moduleHistory.SimCardNumber = module.SimCardNumber;
            moduleHistory.SimCardDate = module.SimCardDate;

            await _dataFacade.ModuleHistoryDataProvider.SaveAsync(moduleHistory);

            return;
        }
    }
}
