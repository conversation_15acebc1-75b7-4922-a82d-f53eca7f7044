-- ================================================================================================================
-- Device ID Loading Performance Optimization - Database Indexes
-- ================================================================================================================
-- This script adds performance indexes to optimize the GetAvailableModulesAsync query performance
-- These indexes target the specific query patterns used in the optimized ModuleUtilities implementation
-- ================================================================================================================

-- Index 1: Optimize Module queries by Status and DealerId
-- This index supports the main filtering condition in GetAvailableModulesAsync
CREATE NONCLUSTERED INDEX [IX_Module_Status_DealerId] ON [dbo].[Module]
(
    [Status] ASC,
    [DealerId] ASC
)
INCLUDE ([Id], [IoTDevice], [CCID], [ModuleType])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

-- Index 2: Optimize Vehicle ModuleId1 lookups
-- This index supports the NOT IN subquery that finds modules not assigned to vehicles
CREATE NONCLUSTERED INDEX [IX_Vehicle_ModuleId1] ON [dbo].[Vehicle]
(
    [ModuleId1] ASC
)
WHERE [ModuleId1] IS NOT NULL AND [ModuleId1] != '00000000-0000-0000-0000-000000000000'
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

-- Index 3: Optimize Vehicle queries by Customer.DealerId for dealer-specific filtering
-- This index supports the dealer-specific vehicle filtering in bulk operations
CREATE NONCLUSTERED INDEX [IX_Vehicle_Customer_DealerId] ON [dbo].[Vehicle]
(
    [CustomerId] ASC
)
INCLUDE ([Id], [ModuleId1])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

-- Index 4: Optimize Module search queries by IoTDevice and CCID
-- This index supports the search functionality in GetAvailableModulesWithPaginationAsync
CREATE NONCLUSTERED INDEX [IX_Module_IoTDevice_CCID] ON [dbo].[Module]
(
    [IoTDevice] ASC,
    [CCID] ASC
)
INCLUDE ([Id], [Status], [DealerId], [ModuleType])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

-- Index 5: Composite index for Module queries with Status, DealerId, and IoTDevice
-- This index optimizes the most common query pattern in the application
CREATE NONCLUSTERED INDEX [IX_Module_Status_DealerId_IoTDevice] ON [dbo].[Module]
(
    [Status] ASC,
    [DealerId] ASC,
    [IoTDevice] ASC
)
INCLUDE ([Id], [CCID], [ModuleType])
WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON)
GO

-- ================================================================================================================
-- Performance Statistics Update
-- ================================================================================================================
-- Update statistics to ensure the query optimizer has current information
UPDATE STATISTICS [dbo].[Module] WITH FULLSCAN
GO

UPDATE STATISTICS [dbo].[Vehicle] WITH FULLSCAN
GO

-- ================================================================================================================
-- Verification Queries
-- ================================================================================================================
-- These queries can be used to verify the performance improvements

-- Query 1: Verify Module index usage
-- This should use the new IX_Module_Status_DealerId index
/*
SELECT Id, IoTDevice, CCID, Status, DealerId
FROM [dbo].[Module]
WHERE (Status = 0 OR Status IS NULL) 
  AND DealerId = '00000000-0000-0000-0000-000000000000'
  AND Id NOT IN (
      SELECT DISTINCT ModuleId1 
      FROM Vehicle 
      WHERE ModuleId1 IS NOT NULL 
        AND ModuleId1 != '00000000-0000-0000-0000-000000000000'
  )
*/

-- Query 2: Verify Vehicle index usage
-- This should use the new IX_Vehicle_ModuleId1 index
/*
SELECT DISTINCT ModuleId1
FROM Vehicle
WHERE ModuleId1 IS NOT NULL 
  AND ModuleId1 != '00000000-0000-0000-0000-000000000000'
*/

-- ================================================================================================================
-- Performance Monitoring Queries
-- ================================================================================================================
-- These queries can be used to monitor the performance of the new indexes

-- Check index usage statistics
/*
SELECT 
    OBJECT_NAME(i.object_id) AS TableName,
    i.name AS IndexName,
    ius.user_seeks,
    ius.user_scans,
    ius.user_lookups,
    ius.user_updates,
    ius.last_user_seek,
    ius.last_user_scan
FROM sys.dm_db_index_usage_stats ius
INNER JOIN sys.indexes i ON ius.object_id = i.object_id AND ius.index_id = i.index_id
WHERE i.name LIKE 'IX_Module%' OR i.name LIKE 'IX_Vehicle%'
ORDER BY ius.user_seeks + ius.user_scans DESC
*/

-- Check index fragmentation
/*
SELECT 
    OBJECT_NAME(ind.object_id) AS TableName,
    ind.name AS IndexName,
    indexstats.avg_fragmentation_in_percent
FROM sys.dm_db_index_physical_stats(DB_ID(), NULL, NULL, NULL, NULL) indexstats
INNER JOIN sys.indexes ind ON indexstats.object_id = ind.object_id AND indexstats.index_id = ind.index_id
WHERE ind.name LIKE 'IX_Module%' OR ind.name LIKE 'IX_Vehicle%'
ORDER BY indexstats.avg_fragmentation_in_percent DESC
*/

-- ================================================================================================================
-- Rollback Script (if needed)
-- ================================================================================================================
/*
-- To rollback these changes, uncomment and run the following:

DROP INDEX [IX_Module_Status_DealerId] ON [dbo].[Module]
GO

DROP INDEX [IX_Vehicle_ModuleId1] ON [dbo].[Vehicle]
GO

DROP INDEX [IX_Vehicle_Customer_DealerId] ON [dbo].[Vehicle]
GO

DROP INDEX [IX_Module_IoTDevice_CCID] ON [dbo].[Module]
GO

DROP INDEX [IX_Module_Status_DealerId_IoTDevice] ON [dbo].[Module]
GO
*/
