(function () {
    //
    FleetXQ.Web.ViewModels.GOUserDepartmentFormViewModelCustom = function (viewmodel) {
        var self = this;
        this.viewmodel = viewmodel;

        // Helper function to get SiteId from sessionStorage
        this.getPersonSiteId = function () {
            var siteId = sessionStorage.getItem('personSiteId');

            // If no SiteId in sessionStorage, try to get it from the global function
            if (!siteId || siteId === 'null' || siteId === 'undefined') {
                if (window.getCurrentPersonSiteId) {
                    siteId = window.getCurrentPersonSiteId();
                    if (siteId && siteId !== 'null' && siteId !== 'undefined') {
                        // Store it in sessionStorage for future use
                        sessionStorage.setItem('personSiteId', siteId);
                        console.log('GOUserDepartmentFormViewModelCustom: Retrieved SiteId from global function and stored in sessionStorage', { siteId: siteId });
                    }
                }
            }

            return siteId;
        };

        // Helper function to get already assigned department IDs from the grid
        this.getAssignedDepartmentIds = function () {
            var assignedDepartmentIds = [];

            // Try to get the grid collection from the parent context
            if (self.viewmodel.popupCaller && self.viewmodel.popupCaller.GOUserDepartmentObjectCollection) {
                var gridCollection = self.viewmodel.popupCaller.GOUserDepartmentObjectCollection();
                if (gridCollection && gridCollection.length > 0) {
                    for (var i = 0; i < gridCollection.length; i++) {
                        var item = gridCollection[i];
                        if (item && item.Data && item.Data.DepartmentId) {
                            var departmentId = item.Data.DepartmentId();
                            if (departmentId && departmentId !== 'null' && departmentId !== 'undefined') {
                                assignedDepartmentIds.push(departmentId);
                            }
                        }
                    }
                }
            }

            console.log('GOUserDepartmentFormViewModelCustom: Found assigned department IDs', { assignedDepartmentIds: assignedDepartmentIds });
            return assignedDepartmentIds;
        };

        // Helper function to set SiteId in sessionStorage
        this.setPersonSiteId = function (siteId) {
            if (siteId) {
                sessionStorage.setItem('personSiteId', siteId);
            } else {
                sessionStorage.removeItem('personSiteId');
            }
        };

        // Override the getDepartmentCollectionData method to filter by SiteId and exclude assigned departments
        this.getDepartmentCollectionData = function (callback) {
            self.viewmodel.isGetDepartmentCollectionBusy(true);

            var configuration = {};
            configuration.contextId = self.viewmodel.DepartmentContextId;

            // Get SiteId from sessionStorage
            var siteId = self.getPersonSiteId();
            console.log('GOUserDepartmentFormViewModelCustom: Loading departments with SiteId filter', { siteId: siteId });

            if (siteId && siteId !== 'null' && siteId !== 'undefined') {
                // Filter departments by SiteId
                var filterPredicate = 'SiteId == @0';
                configuration.filterParameters = '[  { "TypeName" : "System.Guid", "Value" : "' + siteId + '" } ]';
                configuration.filterPredicate = filterPredicate;
                console.log('GOUserDepartmentFormViewModelCustom: Applied SiteId filter', { filterPredicate: filterPredicate });
            } else {
                console.log('GOUserDepartmentFormViewModelCustom: No SiteId found, loading all departments');
            }

            // Override the success handler to filter out already assigned departments
            var originalSuccessHandler = callback || self.viewmodel.onGetDepartmentCollectionDataSuccess;
            configuration.successHandler = function (data) {
                // Get assigned department IDs
                var assignedDepartmentIds = self.getAssignedDepartmentIds();

                if (assignedDepartmentIds.length > 0) {
                    // Filter out already assigned departments
                    var filteredData = data.filter(function (department) {
                        return assignedDepartmentIds.indexOf(department.Data.Id()) === -1;
                    });

                    console.log('GOUserDepartmentFormViewModelCustom: Filtered out assigned departments', {
                        originalCount: data.length,
                        filteredCount: filteredData.length,
                        assignedDepartmentIds: assignedDepartmentIds
                    });

                    // Call the original success handler with filtered data
                    originalSuccessHandler(filteredData);
                } else {
                    // No assigned departments to filter out, call original handler
                    originalSuccessHandler(data);
                }
            };

            configuration.errorHandler = self.viewmodel.onGetDepartmentCollectionDataError;

            self.viewmodel.DataStoreDepartment.LoadObjectCollection(configuration);
        };

        // Override the getFilteredDepartmentCollectionData method to include SiteId filter and exclude assigned departments
        this.getFilteredDepartmentCollectionData = function (searchValue, callback) {
            var configuration = {};
            configuration.contextId = self.viewmodel.DepartmentContextId;

            // Get SiteId from sessionStorage
            var siteId = self.getPersonSiteId();

            if (siteId && siteId !== 'null' && siteId !== 'undefined') {
                // Filter departments by SiteId and search term
                var filterPredicate = 'SiteId == @0';
                configuration.filterParameters = '[  { "TypeName" : "System.Guid", "Value" : "' + siteId + '" } ]';
                configuration.filterPredicate = filterPredicate;
                configuration.filterPredicate += ' && Name.Contains("' + searchValue + '")';
            } else {
                // Only filter by search term if no SiteId
                configuration.filterPredicate = 'Name.Contains("' + searchValue + '")';
            }

            configuration.pageSize = 50;
            configuration.pageNumber = 1;

            // Override the success handler to filter out already assigned departments
            configuration.successHandler = function (data) {
                // Get assigned department IDs
                var assignedDepartmentIds = self.getAssignedDepartmentIds();

                if (assignedDepartmentIds.length > 0) {
                    // Filter out already assigned departments
                    var filteredData = data.filter(function (department) {
                        return assignedDepartmentIds.indexOf(department.Data.Id()) === -1;
                    });

                    console.log('GOUserDepartmentFormViewModelCustom: Filtered out assigned departments from search results', {
                        originalCount: data.length,
                        filteredCount: filteredData.length,
                        searchValue: searchValue,
                        assignedDepartmentIds: assignedDepartmentIds
                    });

                    // Call the callback with filtered data
                    callback(filteredData);
                } else {
                    // No assigned departments to filter out, call callback with original data
                    callback(data);
                }
            };

            configuration.errorHandler = self.viewmodel.onGetDepartmentCollectionDataError;

            self.viewmodel.DataStoreDepartment.LoadObjectCollection(configuration);
        };

        // Override the countDepartmentElements method to include SiteId filter
        this.countDepartmentElements = function (callback) {
            var configuration = {};
            configuration.contextId = self.viewmodel.DepartmentContextId;

            // Get SiteId from sessionStorage
            var siteId = self.getPersonSiteId();

            if (siteId && siteId !== 'null' && siteId !== 'undefined') {
                // Filter departments by SiteId
                var filterPredicate = 'SiteId == @0';
                configuration.filterParameters = '[  { "TypeName" : "System.Guid", "Value" : "' + siteId + '" } ]';
                configuration.filterPredicate = filterPredicate;
            }

            // Override the success handler to account for assigned department filtering
            configuration.successHandler = function (count) {
                // Get assigned department IDs to estimate the filtered count
                var assignedDepartmentIds = self.getAssignedDepartmentIds();

                if (assignedDepartmentIds.length > 0) {
                    // Estimate the filtered count (this is approximate since we can't count server-side)
                    // We'll use the original count as a base and let the client-side filtering handle the rest
                    console.log('GOUserDepartmentFormViewModelCustom: Count departments with assigned filtering estimate', {
                        originalCount: count,
                        assignedDepartmentIds: assignedDepartmentIds
                    });
                }

                callback(count);
            };

            configuration.errorHandler = function () { callback(0); };

            self.viewmodel.DataStoreDepartment.CountObjects(configuration);
        };

        // Override the selectiveLoadDataForDepartment method to use our custom count method
        this.selectiveLoadDataForDepartment = function (clearSelection) {
            if (clearSelection) {
                // First clear the current selection
                self.viewmodel.Department_lookupItem({ label: "", value: null });
                self.viewmodel.Department_Name(null);
            }

            self.countDepartmentElements(function (data) {
                if (data > self.viewmodel.Department_lookupThreshold) {
                    self.viewmodel.Department_lookupMethod = self.viewmodel.getDepartmentAutoComplete;
                    self.viewmodel.Department_lookupMinLength(2);
                } else {
                    self.viewmodel.getDepartmentCollectionData();
                    self.viewmodel.Department_lookupMethod = self.viewmodel.getDepartmentCollectionOneLevel;
                    self.viewmodel.Department_lookupMinLength(0);
                }
            });
        };

        // Override the loadRelatedData method to use our custom selectiveLoadDataForDepartment
        this.loadRelatedData = function () {
            self.selectiveLoadDataForDepartment(false);
        };

        this.initialize = function () {
            // Override the original methods with our custom implementations
            self.viewmodel.getDepartmentCollectionData = self.getDepartmentCollectionData.bind(self);
            self.viewmodel.getFilteredDepartmentCollectionData = self.getFilteredDepartmentCollectionData.bind(self);
            self.viewmodel.countDepartmentElements = self.countDepartmentElements.bind(self);
            self.viewmodel.selectiveLoadDataForDepartment = self.selectiveLoadDataForDepartment.bind(self);
            self.viewmodel.loadRelatedData = self.loadRelatedData.bind(self);

            // Override the release method to clean up sessionStorage
            var originalRelease = self.viewmodel.release;
            self.viewmodel.release = function () {
                // Don't clear Person SiteId from sessionStorage when GOUserDepartmentForm is released
                // This allows the SiteId to persist for subsequent opens of the form
                console.log('GOUserDepartmentFormViewModelCustom: Keeping Person SiteId in sessionStorage on form release');

                // Call original release function
                if (originalRelease) {
                    originalRelease.call(self.viewmodel);
                }
            };

            // Refresh SiteId from Person form on initialization
            var siteId = self.getPersonSiteId();
            if (siteId) {
                console.log('GOUserDepartmentFormViewModelCustom: Found SiteId on initialization', { siteId: siteId });
            } else {
                console.log('GOUserDepartmentFormViewModelCustom: No SiteId found on initialization');
            }

            console.log('GOUserDepartmentFormViewModelCustom initialized with SiteId filtering');
        };

        // Initialize when the custom view model is created
        self.initialize();
    };
}()); 