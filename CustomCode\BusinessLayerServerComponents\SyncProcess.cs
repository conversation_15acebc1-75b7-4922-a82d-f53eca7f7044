﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.Data.DataProvidersExtensions.Custom;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// SyncProcess Component
	///  
	/// </summary>
    public partial class SyncProcess : BaseServerComponent, ISyncProcess
    {
        public SyncProcess(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
        {
        }

        /// <summary>
        /// ManageSync Method
        /// </summary>
        /// <param name="Message"></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> ManageSyncAsync(System.String Message, Dictionary<string, object> parameters = null)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            var vehicleCount = 0;

            try
            {
                // Parse the sync message
                var syncMessage = System.Text.Json.JsonSerializer.Deserialize<VehicleSyncMessage>(Message);
                if (syncMessage == null)
                {
                    return new ComponentResponse<System.String>("Invalid sync message format");
                }

                vehicleCount = 1; // Now processing one vehicle at a time
                var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();

                // Load vehicle data
                var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                vehicle.Id = syncMessage.VehicleId;
                vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle, skipSecurity: true);

                if (vehicle != null)
                {
                    var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                    if (module != null && !string.IsNullOrEmpty(module.IoTDevice))
                    {
                        // Sync to IoT device directly (avoids creating new queue messages)
                        await deviceTwinHandler.SyncDriverToVehicleDirect(module.IoTDevice, syncMessage.InitiatedByUserId ?? Guid.Empty);

                        stopwatch.Stop();
                        return new ComponentResponse<System.String>($"Vehicle sync completed successfully for vehicle {syncMessage.VehicleId} ({syncMessage.VehicleSequence}/{syncMessage.TotalVehicles}) to device {module.IoTDevice} in {stopwatch.ElapsedMilliseconds}ms. Reason: {syncMessage.SyncReason}");
                    }
                    else
                    {
                        stopwatch.Stop();
                        return new ComponentResponse<System.String>($"Vehicle {syncMessage.VehicleId} has no IoT device to sync to. Completed in {stopwatch.ElapsedMilliseconds}ms.");
                    }
                }
                else
                {
                    stopwatch.Stop();
                    return new ComponentResponse<System.String>($"Vehicle {syncMessage.VehicleId} not found. Completed in {stopwatch.ElapsedMilliseconds}ms.");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return new ComponentResponse<System.String>($"Vehicle sync failed after {stopwatch.ElapsedMilliseconds}ms: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes vehicle sync messages from the queue (entry point for Azure Function)
        /// </summary>
        public async System.Threading.Tasks.Task<ComponentResponse<string>> ProcessVehicleSyncAsync(string Message, Dictionary<string, object> parameters = null)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // Parse the JSON message to get basic info for logging
                var syncMessage = System.Text.Json.JsonSerializer.Deserialize<VehicleSyncMessage>(Message);
                if (syncMessage == null)
                {
                    return new ComponentResponse<string>("Invalid sync message format");
                }

                // Call the actual sync processing
                var result = await ManageSyncAsync(Message, parameters);

                stopwatch.Stop();
                return new ComponentResponse<string>(result.Result ?? "Sync completed");
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                return new ComponentResponse<string>($"Error processing vehicle sync: {ex.Message}");
            }
        }
    }
}
