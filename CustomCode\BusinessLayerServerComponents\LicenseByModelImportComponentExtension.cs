using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;
using System.Globalization;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class LicenseByModelImportComponentExtension : IImportExportComponentExtension<LicenseByModelImportSection0Component, LicenseByModelDataObject>
    {
        private readonly IDataFacade dataFacade;

        public LicenseByModelImportComponentExtension(IDataFacade dataFacade)
        {     
            this.dataFacade = dataFacade;
        }

        public void Init(IImportExportComponent<LicenseByModelDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<LicenseByModelDataObject> args)
        {
           if (args.Entity == null || args.DataRow == null) return;

            try
            {
                // Get First Name and Last Name from the import row
                var firstName = args.DataRow[LicenseByModelImportSection0Component.COL_FIRSTNAME]?.ToString();
                var lastName = args.DataRow[LicenseByModelImportSection0Component.COL_LASTNAME]?.ToString();
                var modelName = args.DataRow[LicenseByModelImportSection0Component.COL_MODEL]?.ToString();
                var expiryDateStr = args.DataRow[LicenseByModelImportSection0Component.COL_EXPIRYDATE]?.ToString();


                if (string.IsNullOrEmpty(firstName) || string.IsNullOrEmpty(lastName))
                {
                    throw new Exception($"First Name and Last Name are required. First Name: {firstName}, Last Name: {lastName}");
                }

                if (string.IsNullOrEmpty(modelName))
                {
                    throw new Exception($"Model name is required.");
                }

                // Handle Expiry Date
                if (!string.IsNullOrEmpty(expiryDateStr))
                {
                    if (DateTime.TryParseExact(expiryDateStr, 
                        "dd/MM/yyyy", 
                        CultureInfo.InvariantCulture,
                        DateTimeStyles.None, 
                        out DateTime parsedDate))
                    {
                        // Set time to noon (12:00:00) as seen in your target data
                        args.Entity.ExpiryDate = parsedDate.Date.AddHours(12);
                    }
                    else
                    {
                        throw new Exception($"Invalid date format: {expiryDateStr}. Please use format dd/MM/yyyy");
                    }
                }
                else
                {
                    throw new Exception("Expiry Date is required.");
                }

                // Find Person by First Name and Last Name
                var person = (await dataFacade.PersonDataProvider.GetCollectionAsync(
                    null, 
                    "FirstName == @0 && LastName == @1", 
                    new object[] { firstName, lastName }
                )).SingleOrDefault();

                if (person == null)
                {
                    throw new Exception($"Person not found with First Name: {firstName} and Last Name: {lastName}");
                }

                // Find Model by Name
                var model = (await dataFacade.ModelDataProvider.GetCollectionAsync(
                    null,
                    "Name == @0",
                    new object[] { modelName }
                )).SingleOrDefault();

                if (model == null)
                {
                    throw new Exception($"Model not found with name: {modelName}");
                }

                // Set the IDs in the entity
                if (person.DriverId == null)
                {
                    throw new Exception($"Person {person.FirstName} {person.LastName} does not have an associated DriverId");
                }
                args.Entity.DriverId = person.DriverId.Value;
                args.Entity.ModelId = model.Id;

                // Update Driver License Mode to ByModel (since this is a model-specific license import)
                var driver = (await dataFacade.DriverDataProvider.GetCollectionAsync(
                    null,
                    "Id == @0",
                    new object[] { person.DriverId.Value }
                )).SingleOrDefault();

                if (driver != null && driver.LicenseMode != ModeEnum.ByModel)
                {
                    driver.LicenseMode = ModeEnum.ByModel;
                    await dataFacade.DriverDataProvider.SaveAsync(driver);                  
                }
            }
            catch (Exception ex)
            {
                args.Logger?.LogError($"Error processing row: {ex.Message}");
                throw;
            }
        }
    }
}