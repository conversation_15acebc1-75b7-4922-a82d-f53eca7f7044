﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <None Remove="testhost.runtimeconfig.json" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="testhost.runtimeconfig.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" />
    <PackageReference Include="NFluent" />
    <PackageReference Include="NSubstitute" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" />
    <PackageReference Include="NUnit" />
    <PackageReference Include="NUnit3TestAdapter" />
    <PackageReference Include="QuickGraph" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\GeneratedCode\ServiceLayer\FleetXQ.ServiceLayer.csproj" />
    <ProjectReference Include="..\..\BusinessLayerServerComponents\FleetXQ.BusinessLayer.Components.Server.Custom.csproj" />
    <ProjectReference Include="..\..\TestsLayer\FleetXQ.Tests.Common.csproj" />
  </ItemGroup>
</Project>
