﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;

namespace FleetXQ.BusinessLayer.Components.Server
{

    public partial class EHelpdeskAPI : BaseServerComponent, IEHelpdeskAPI
    {
        public EHelpdeskAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade)
            : base(serviceProvider, configuration, dataFacade)
        {
        }
		/// <summary>
        /// GetVehicleDetail Method
		/// </summary>
		/// <param name="id"></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<object>> GetVehicleDetailAsync(string id, Dictionary<string, object> parameters = null)
        {
            var result = new VehicleDetail();
            VehicleDataObject vehicle = null;
            ModuleDataObject module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { id })).SingleOrDefault();

            if (vehicle == null)
            {
                vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "SerialNo == @0", new object[] { id })).SingleOrDefault();
            }
            if (vehicle == null)
            {
                vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "HireNo == @0", new object[] { id })).SingleOrDefault();
            }
            if (module != null)
            {
                vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "ModuleId1 == @0", new object[] { module.Id })).SingleOrDefault();
            }
            if (vehicle == null)
            {
                return new ComponentResponse<object>(result);
            }
            if (module == null) {
                module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicle.ModuleId1 })).SingleOrDefault();
            }

            var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync();

            result.GMTP_ID = module.IoTDevice;
            result.HireNo = vehicle.HireNo;
            result.Serial = vehicle.SerialNo;
            result.SimCardNumber = module.SimCardNumber;
            result.Model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicle.ModelId })).SingleOrDefault().Name;
            result.OnHire = vehicle.OnHire;
            result.IsVOR = otherSettings?.VORStatus == true;
            result.FullLockoutEnabled = otherSettings?.FullLockout == true;
            
            // Populate Driver List
            var driverList = new List<DriverInfo>();
            var model = await vehicle.LoadModelAsync(skipSecurity: true);
            // get all permission id of Normal Driver
            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.NormalDriver }, skipSecurity: true)).SingleOrDefault();
            // get all card with access to the vehicle
            var perVehicleNormalAccess = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0", new object[] { vehicle.Id })).ToList();
            foreach (var card in perVehicleNormalAccess)
            {
                var item = await card.LoadCardAsync();
                var person = await (await item.LoadDriverAsync()).LoadPersonAsync();
                if (person != null)
                {
                    var licenceAllowed = true;
                    if(person.LicenseActive)
                    {
                        var generalLicence = await (await person.LoadDriverAsync()).LoadGeneralLicenceAsync();

                        // check if ExpiryDate is expired
                        if (generalLicence != null &&  generalLicence.ExpiryDate < DateTime.Now)
                        {
                            licenceAllowed = false;
                        }
                        // question here... if there is a license per model, should the driver then not be allowed for other models?
                        var modelLicence =await (await person.LoadDriverAsync(skipSecurity: true)).LoadLicensesByModelAsync(skipSecurity: true);
                        foreach (var licence in modelLicence)
                        {
                            if (licence.ModelId == model.Id && licence.ExpiryDate > DateTime.Now)
                            {
                                licenceAllowed = true;
                                break;
                            } else
                            {
                                licenceAllowed = false;
                            }
                        }

                    }
                    if (item.Active && (await person.LoadDriverAsync(skipSecurity: true)).Active && licenceAllowed)
                    {
                        var siteName = (await person.LoadSiteAsync()).Name;
                        var departmentName = (await person.LoadDepartmentAsync()).Name;
                        var fullName = person.FirstName + " " + person.LastName;
                        var driverObject = new DriverInfo
                        {
                            Fullname = fullName,
                            Site = siteName,
                            Department = departmentName,
                            FacilityCode = item.FacilityCode,
                            CardPinNo = item.CardNumber,
                            Weigand = item.Weigand
                        };
                        if (!driverList.Any(x => x.Weigand == driverObject.Weigand))
                        {
                            driverList.Add(driverObject);
                        }
                    }
                }
            }
            result.DriverList = driverList;

            // Populate Supervisor List
            var masterList = new List<SupervisorInfo>();
            var permissionMaster = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 1 }, skipSecurity: true)).SingleOrDefault();
            var perVehicleMasterAccess = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 && PermissionId == @1", new object[] { vehicle.Id, permissionMaster.Id })).ToList();

            foreach (var card in perVehicleMasterAccess)
            {
                var item = await card.LoadCardAsync();
                var person = await (await item.LoadDriverAsync()).LoadPersonAsync();
                var fullName = person.FirstName + " " + person.LastName;
                var siteName = (await person.LoadSiteAsync()).Name;
                var departmentName = (await person.LoadDepartmentAsync()).Name;
                if (person != null)
                {
                    if (item.Active && (await person.LoadDriverAsync()).Active && person.Supervisor == true)
                    {
                        var driverObject = new SupervisorInfo
                        {
                            Fullname = fullName,
                            Site = siteName,
                            Department = departmentName,
                            FacilityCode = item.FacilityCode,
                            CardPinNo = item.CardNumber,
                            Weigand = item.Weigand
                        };
                        if (!masterList.Any(x => x.Weigand == driverObject.Weigand))
                        {
                            masterList.Add(driverObject);
                        }
                    }
                }
            }
            result.SupervisorList = masterList;

            // Populate Idle Settings
            result.IdleSetting = new IdleSetting
            {
                IdleTimeoutEnabled = vehicle.TimeoutEnabled,
                IdleTimeoutTimer = vehicle.IDLETimer 
                // InputPolarity = vehicle.InputPolarity
            };

            float multi = (float)(module.FSSXMulti == 0 ? 1 : ((module.FSSXMulti / 100) + 1));
            var redImpactThreshold = (int)(module.FSSSBase * multi * 10);

            // Populate Impact Settings
            result.ImpactSetting = new ImpactSetting
            {
                BlueImpactGForce = module.BlueImpact,
                AmberImpactGForce = module.AmberImpact,
                RedImpactGForce = module.RedImpact,
                RedImpactThreshold = redImpactThreshold,
                ImpactLockout = vehicle.ImpactLockout
            };

            var checklistSetting = (await _dataFacade.ChecklistSettingsDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicle.ChecklistSettingsId })).SingleOrDefault();
            var timeSlot1 = checklistSetting.TimeslotOne?.ToString(@"hh\:mm");
            var timeSlot2 = checklistSetting.TimeslotTwo?.ToString(@"hh\:mm");
            var timeSlot3 = checklistSetting.TimeslotThree?.ToString(@"hh\:mm");
            var timeSlot4 = checklistSetting.TimeslotFour?.ToString(@"hh\:mm");
            // Populate Checklist Settings
            result.ChecklistSetting = new ChecklistSetting
            {
                DriverBaseOrTimebase = checklistSetting.Type == (Type1Enum)2 ? "Time Base" : "Driver Base",
                Timeslot1 = timeSlot1,
                Timeslot2 = timeSlot2,
                Timeslot3 = timeSlot3,
                Timeslot4 = timeSlot4,
                // ChecklistTimeoutEnabled = vehicle.ChecklistTimeout > 0,
                ChecklistTimeoutInSec = checklistSetting.QuestionTimeout
            };

            var department = await vehicle.LoadDepartmentAsync();
            //sort by order
            var preopChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "ModelId == @0 && DepartmentId == @1", new object[] { model.Id, department.Id })).SingleOrDefault();
            var checkLists = new List<PreopChecklist>();
            if (preopChecklist == null)
            {
                result.PreopChecklists = checkLists;
            } else {
                var preopChecklistItems = (await preopChecklist.LoadPreOperationalChecklistsAsync()).Where(x => x.Active).OrderBy(x => x.Order).ToList();
                foreach (var question in preopChecklistItems) {
                    checkLists.Add(new PreopChecklist
                    {
                        Order = question.Order,
                        Questions = question.Question,
                        Type = question.AnswerType == 0 ? "Yes/No" : "Text",
                        ExpectedAnswer = question.ExpectedAnswer == true ? "Yes" : "No",
                        IsCriticalQuestion = question.Critical
                    });
                }
                result.PreopChecklists = checkLists;
            }

            var lastSessionId = vehicle.LastSessionId;
            if (lastSessionId != null) {
                var lastSession = (await _dataFacade.SessionDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { Guid.Parse(lastSessionId) })).SingleOrDefault();
                var driver = (await _dataFacade.DriverDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { lastSession.DriverId })).SingleOrDefault();
                var person = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null, "DriverId == @0", new object[] { driver.Id })).SingleOrDefault();
                var fullName = person.FirstName + " " + person.LastName;
                // Populate Last Session Details
                result.LastSessionDetails = new LastSessionDetails
                {
                    SessionStartDateTime = lastSession.StartTime,
                    SessionEndDateTime = lastSession.EndTime,
                    DriverName = fullName,
                };

                // get the checklist result of the session
                var checklistResult = (await _dataFacade.ChecklistResultDataProvider.GetCollectionAsync(null, "SessionId1 == @0", new object[] { lastSession.Id })).SingleOrDefault();
                var checklistAnswers = new List<ChecklistAnswerDetails>();
                if (checklistResult != null)
                {
                    var checklistAnswerDetails = (await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null, "ChecklistResultId == @0", new object[] { checklistResult.Id })).ToList();
                    foreach (var item in checklistAnswerDetails)
                    {
                        var question = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { item.PreOperationalChecklistId })).SingleOrDefault();
                        checklistAnswers.Add(new ChecklistAnswerDetails
                        {
                            Question = question.Question,
                            Answer = item.Answer == true ? "Yes" : "No",
                            Failed = item.Failed == true ? "Yes" : "No",
                            Critical = question.Critical == true ? "Yes" : "No"
                        });
                        result.LastSessionDetails.LastSessionChecklistResult = new LastSessionChecklistResult
                        {
                            ChecklistResultStartTime = checklistResult.StartTime,
                            ChecklistResultEndTime = checklistResult.EndTime,
                            Comment = checklistResult.Comment,
                            ChecklistAnswers = checklistAnswers
                        };
                    }
                } else {
                    checklistAnswers = null;
                }

                // get the impact of the session
                var impact = (await _dataFacade.ImpactDataProvider.GetCollectionAsync(null, "SessionId == @0", new object[] { lastSession.Id })).SingleOrDefault();
                if (impact != null)
                {
                    result.LastSessionDetails.LastSessionImpact = new LastSessionImpact
                    {
                        ImpactDateTime = impact.ImpactDateTime,
                        ShockValue = impact.ShockValue,
                        Threshold = impact.Threshold
                    };
                } else {
                    result.LastSessionDetails.LastSessionImpact = null;
                }
            } else {
                result.LastSessionDetails = null;
            }

            // result.FirmwareVersion = vehicle.FirmwareVersion;
            // result.LockoutStatus = vehicle.LockoutStatus;

            return new ComponentResponse<System.Object>(result);
        }
    }

    public class VehicleDetail
    {
        public string GMTP_ID { get; set; }
        public string HireNo { get; set; }
        public string Serial { get; set; }
        public string Model { get; set; }

        public string SimCardNumber { get; set; }
        public bool OnHire { get; set; }
        public bool IsVOR { get; set; }
        public bool FullLockoutEnabled { get; set; }
        public ImpactSetting ImpactSetting { get; set; }
        public ChecklistSetting ChecklistSetting { get; set; }
        public IdleSetting IdleSetting { get; set; }
        public LastSessionDetails LastSessionDetails { get; set; }
        // public string FirmwareVersion { get; set; }
        // public string LockoutStatus { get; set; }
        public List<DriverInfo> DriverList { get; set; }
        public List<SupervisorInfo> SupervisorList { get; set; }
        public List<PreopChecklist> PreopChecklists { get; set; }
    }

    public class DriverInfo
    {
        public string Fullname { get; set; }
        // public string Username { get; set; }
        public string Site { get; set; }
        public string Department { get; set; }
        public string FacilityCode { get; set; }
        public string CardPinNo { get; set; }
        public string Weigand { get; set; }
    }

    public class SupervisorInfo
    {
        public string Fullname { get; set; }
        // public string Username { get; set; }
        public string Site { get; set; }
        public string Department { get; set; }
        public string FacilityCode { get; set; }
        public string CardPinNo { get; set; }
        public string Weigand { get; set; }
    }

    public class IdleSetting
    {
        public bool IdleTimeoutEnabled { get; set; }
        public int? IdleTimeoutTimer { get; set; }
        // public string InputPolarity { get; set; }
        public bool IsCanBus { get; set; }
    }

    public class ImpactSetting
    {
        public double? BlueImpactGForce { get; set; }
        public double? AmberImpactGForce { get; set; }
        public double? RedImpactGForce { get; set; }
        public double RedImpactThreshold { get; set; }
        public bool ImpactLockout { get; set; }
    }

    public class ChecklistSetting
    {
        public string DriverBaseOrTimebase { get; set; }
        public string Timeslot1 { get; set; }
        public string Timeslot2 { get; set; }
        public string Timeslot3 { get; set; }
        public string Timeslot4 { get; set; }
        // public bool ChecklistTimeoutEnabled { get; set; }
        public short? ChecklistTimeoutInSec { get; set; }
    }

    public class PreopChecklist
    {
        public int Order { get; set; }
        public string Questions { get; set; }
        public string Type { get; set; }
        public string ExpectedAnswer { get; set; }
        public bool IsCriticalQuestion { get; set; }
        // public bool IsRandomized { get; set; }
    }

    public class LastSessionDetails
    {
        public DateTime SessionStartDateTime { get; set; }
        public DateTime? SessionEndDateTime { get; set; }
        public string DriverName { get; set; }
        public LastSessionChecklistResult LastSessionChecklistResult { get; set; }
        public LastSessionImpact LastSessionImpact { get; set; } 
        // public DateTime LastIgnition { get; set; }
        // public DateTime IgnitionDate { get; set; }
        // public string DriverDenyList { get; set; }
    }

    public class LastSessionImpact
    {
        public DateTime ImpactDateTime { get; set; }
        public double ShockValue { get; set; }
        public double Threshold { get; set; }
    }

    public class LastSessionChecklistResult
    {
        public DateTime ChecklistResultStartTime { get; set; }
        public DateTime? ChecklistResultEndTime { get; set; }
        public string Comment { get; set; }
        public List<ChecklistAnswerDetails> ChecklistAnswers { get; set; }
    }

    public class ChecklistAnswerDetails
    {
        public string Question { get; set; }
        public string Answer { get; set; }
        public string Failed { get; set; }
        public string Critical { get; set; }
    }
}