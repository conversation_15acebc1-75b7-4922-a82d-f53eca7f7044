﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// DriverImpactAPI Component
	/// Manage driver shock messages 
	/// </summary>
    public partial class DriverImpactAPI : BaseServerComponent, IDriverImpactAPI
    {

        private readonly IServiceProvider _serviceProvider;
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private const int _impactCountNeededForCalibration = 100;
        private readonly ILoggingService _logger;

        public DriverImpactAPI(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, ILoggingService logger) : base(provider, configuration, dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = provider;
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;
        }

        /// <summary>
        /// StoreImpactMessage Method
        /// </summary>
        /// <param name="Message">JSON Message</param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> StoreImpactMessageAsync(System.String Message, Dictionary<string, object> parameters = null)
        {
            PayloadDataObject payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            if (payloadObject == null)
            {
                _logger.LogError(new GOServerException("Invalid Payload"));
                throw new GOServerException("Invalid Payload");
            }

            ShockPayloadDataObject shockPayloadObject = HandleShockPayload(payloadObject.Payload);

            if (shockPayloadObject == null)
            {
                _logger.LogError(new GOServerException("Shock Payload is null"));
                throw new GOServerException("Shock Payload is null");
            }

            if (shockPayloadObject.CardData.CardId == "0")
            {
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Unknown Driver")));
            }

            SessionDataObject session = (await _dataFacade.SessionDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { Guid.Parse(payloadObject.SessionId) })).SingleOrDefault();
            if (session == null)
            {
                _logger.LogError(new GOServerException("Session not found"));
                throw new GOServerException("Session not found");
            }

            var vehicle = await session.LoadVehicleAsync();
            if (vehicle == null)
            {
                _logger.LogError(new GOServerException("Vehicle not found"));
                throw new GOServerException("Vehicle not found");
            }

            var vehicleModule = await vehicle.LoadModuleAsync();
            if (vehicleModule == null)
            {
                _logger.LogError(new GOServerException("Vehicle Module not found"));
                throw new GOServerException("Vehicle Module not found");
            }

            ImpactDataObject impactDataObject = _serviceProvider.GetRequiredService<ImpactDataObject>();
            impactDataObject.SessionId = session.Id;
            impactDataObject.ShockValue = shockPayloadObject.ShockValue;
            float multi = (float)(vehicleModule.FSSXMulti == 0 ? 1 : ((vehicleModule.FSSXMulti / 100) + 1));
            impactDataObject.Threshold = (int)vehicleModule.FSSSBase * multi;
            impactDataObject.ImpactDateTime = DataUtils.HexToUtcTime(shockPayloadObject.CardData.ModuleHexTimeStamp);

            if (shockPayloadObject.latitude != 0)
            {
                impactDataObject.Latitude = shockPayloadObject.latitude / 10000000.0;
            }
            if (shockPayloadObject.longitude != 0)
            {
                impactDataObject.Longitude = shockPayloadObject.longitude / 10000000.0;
            }

            impactDataObject = await _dataFacade.ImpactDataProvider.SaveAsync(impactDataObject);
            // vehicleModule.CalibrationResetDate compare with now() to check if the vehicle is in calibration mode
            bool isInCalibration = vehicleModule.CalibrationResetDate == null || (vehicleModule.CalibrationResetDate < DateTime.UtcNow && vehicleModule.FSSSBase <= 0);

            if (!isInCalibration && vehicle.ImpactLockout && vehicleModule.FSSSBase > 0)
            {
                var redImpactThreshold = (int)(vehicleModule.FSSSBase * multi * 10);
                // check if the impact is greater than the threshold
                if (impactDataObject.ShockValue > redImpactThreshold)
                {
                   await ProcessImpactAlertAsync(impactDataObject);
                }
            }

            if (vehicleModule.FSSSBase == 0 && isInCalibration)
            {
                // check if vehicle is ready for calibration
                var sessionList = (await vehicle.LoadSessionsAsync())?.Where(x => vehicleModule.CalibrationResetDate == null || x.StartTime > vehicleModule.CalibrationResetDate);
                var impactCount = 0;
                foreach (var sessionItem in sessionList ?? Enumerable.Empty<SessionDataObject>())
                {
                    var impactList = (await sessionItem.LoadImpactsAsync()).ToList();
                    if (impactList != null)
                    {
                        impactCount += impactList.Count();
                    }
                    
                }

                vehicleModule.Calibration = impactCount >= _impactCountNeededForCalibration ? 100 : (int)((impactCount / (double)_impactCountNeededForCalibration) * 100);

                await _dataFacade.ModuleDataProvider.SaveAsync(vehicleModule);

                if (impactCount >= _impactCountNeededForCalibration)
                {
                    // Get all impacts from the sessionList
                    var impactList = new List<ImpactDataObject>();
                    foreach (var sessionItem in sessionList)
                    {
                        var impactListTemp = await sessionItem.LoadImpactsAsync();
                        impactList.AddRange(impactListTemp);
                    }

                    // Get the average of the impacts
                    var averageImpact = impactList.Average(x => x.ShockValue);
                    // Get the standard deviation of the impacts
                    var standardDeviation = Math.Sqrt(impactList.Average(x => Math.Pow(x.ShockValue - averageImpact, 2)));

                    var calibratedFSSSBase = averageImpact + standardDeviation;

                    // update the calibration date to date now
                    vehicleModule.CalibrationDate = DateTime.UtcNow;    

                    // Save the calibrated FSSSBase
                    vehicleModule.FSSSBase = calibratedFSSSBase;
                    await _dataFacade.ModuleDataProvider.SaveAsync(vehicleModule);

                    // check if vehicle impact lockout is enabled
                    if (vehicle.ImpactLockout == true)
                    {
                        int FSSX = (int)(calibratedFSSSBase * multi * 10);
                        
                        object desiredProperties = new
                        {
                           impact_lockout = new { red_impact_threshold = FSSX },
                        };
                        
                       await _deviceMessageHandler.UpdateDesiredProperties(vehicleModule.IoTDevice, desiredProperties, 9);
                    }
                    EmailDetail emailDetail = new EmailDetail();
                    emailDetail.TimeStamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") + " - UTC";
                    emailDetail.Alert = "Impact Calibration Alert";
                    emailDetail.VehicleId = vehicle.Id;
                    IEmailService emailService = _serviceProvider.GetRequiredService<IEmailService>();
                    await emailService.SendEmailAsync(JsonConvert.SerializeObject(emailDetail));
                }
            }

            // ToDo: Process Impact Alert

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }



        private static ShockPayloadDataObject HandleShockPayload(string payload)
        {
            ShockPayloadDataObject shockPayloadObject = new();
            string[] payloadArray = payload.Split(" ");
            foreach (string payloadItem in payloadArray)
            {
                string[] payloadItemArray = payloadItem.Split("=");
                if (payloadItemArray[0] == "AUTH")
                {
                    string[] authArray = payloadItemArray[1].Split(",");
                    shockPayloadObject.CardData = new CardPayloadDataObject();
                    shockPayloadObject.CardData.CardId = authArray[0];
                    shockPayloadObject.CardData.ModuleHexTimeStamp = authArray[1];
                }
                else if (payloadItemArray[0] == "SHOCK")
                {
                    string[] shockItemArray = payloadItemArray[1].Split(",");
                    shockPayloadObject.ShockValue = DataUtils.ConvertHexToInt(shockItemArray[0]);

                    if (shockItemArray.Length > 1)
                    {
                        shockPayloadObject.longitude = long.Parse(shockItemArray[1]);
                        shockPayloadObject.latitude = long.Parse(shockItemArray[2]);
                    }
                }
            }
            return shockPayloadObject;
        }

        private async Task ProcessImpactAlertAsync(ImpactDataObject impactDataObject)
        {
            EmailDetail emailDetail = new EmailDetail();
            emailDetail.TimeStamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") + " - UTC";
            emailDetail.Alert = "Red Impact Alert";
            var person = (await impactDataObject.LoadSessionAsync().Result.LoadDriverAsync().Result.LoadPersonAsync());
            emailDetail.DriverName = person.FirstName + " " + person.LastName;
            emailDetail.VehicleId = (await impactDataObject.LoadSessionAsync()).VehicleId;


            IEmailService emailService = _serviceProvider.GetRequiredService<IEmailService>();
            await emailService.SendEmailAsync(JsonConvert.SerializeObject(emailDetail));

        }

    }


    internal class ShockPayloadDataObject
    {
        public CardPayloadDataObject CardData { get; set; }
        public string ShockId { get; set; }
        public double ShockValue { get; set; }
        public string ShockStatus { get; set; }
        public long latitude { get; set; }
        public long longitude { get; set; }
    }
}

//Sample Shock Message: AUTH=32F466D,64E89E23 SHOCK=574D8,0,-1