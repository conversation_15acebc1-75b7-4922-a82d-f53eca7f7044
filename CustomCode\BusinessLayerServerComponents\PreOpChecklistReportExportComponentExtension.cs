﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom;

public class PreOpChecklistReportExportComponentExtension : IImportExportComponentExtension<PreOpChecklistReportExportSection0Component,
        AllChecklistResultViewDataObject>
{
    private readonly IDataFacade dataFacade;
    private readonly IAuthentication authentication;

    public PreOpChecklistReportExportComponentExtension(IDataFacade dataFacade, IAuthentication authentication)
    {
        this.dataFacade = dataFacade;
        this.authentication = authentication;
    }

    public void Init(IImportExportComponent<AllChecklistResultViewDataObject> importExportComponent)
    {
        importExportComponent.OnAfterExportDataRowAsync += ImportExportComponent_OnAfterExportDataRowAsync;
    }

    private async Task ImportExportComponent_OnAfterExportDataRowAsync(OnAfterExportDataRowEventArgs<AllChecklistResultViewDataObject> arg)
    {
        try
        {
            if (arg.Entity.ChecklistResult != null)
            {
                var userClaims = await authentication.GetCurrentUserClaimsAsync();
                var callingUser = await dataFacade.GOUserDataProvider.GetAsync(
                    new GOUserDataObject(userClaims.UserId.Value), 
                    includes: new List<string> { "Person", "Person.Customer" }
                );

                var preferredLocale = callingUser.PreferredLocale != null ? 
                    callingUser.PreferredLocale.Value : 
                    callingUser.Person?.Customer?.PreferredLocale;

                // Format date using default or preferred locale
                try
                {
                    var culture = preferredLocale.HasValue ? 
                        new CultureInfo(DataUtils.GetLocaleString(preferredLocale.Value)) : 
                        CultureInfo.InvariantCulture;

                    // Use the culture's short date pattern plus time
                    var dateFormat = $"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss";
                    
                    arg.DataRow[PreOpChecklistReportExportSection0Component.COL_DATEANDTIME] =
                        arg.Entity.ChecklistResult.StartTime.ToString(dateFormat, culture);
                }
                catch
                {
                    arg.DataRow[PreOpChecklistReportExportSection0Component.COL_DATEANDTIME] =
                        arg.Entity.ChecklistResult.StartTime.ToString("M/d/yyyy HH:mm:ss");
                }

                var checklistDetails = await dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(
                    filterPredicate: "ChecklistResultId = @0",
                    filterArguments: new object[] { arg.Entity.ChecklistResult.Id },
                    includes: new List<string> { "PreOperationalChecklist" }
                );

                // Calculate failures
                var totalFailures = 0;
                if (checklistDetails.Any())
                {
                    var preOpChecklistIds = checklistDetails.Select(cd => cd.PreOperationalChecklistId).Distinct().ToList();
                    foreach (var preOpChecklistId in preOpChecklistIds)
                    {
                        var failures = await dataFacade.ChecklistFailureViewDataProvider.GetCollectionAsync(
                            filterPredicate: "PreOperationalChecklistId = @0",
                            filterArguments: new object[] { preOpChecklistId }
                        );
                        
                        if (failures.Any())
                        {
                            totalFailures += failures.Sum(f => f.NumberOfFailedAnswersPerQuestion);
                        }
                    }
                }
                arg.DataRow[PreOpChecklistReportExportSection0Component.COL_CHECKLISTFAILURES] = totalFailures.ToString();

                // Handle questions
                if (checklistDetails.Any())
                {
                    var questions = checklistDetails
                        .Where(cd => cd.PreOperationalChecklist != null)
                        .Select(cd => $"{cd.PreOperationalChecklist.Question} (Ans: {(cd.Answer ? "Y" : "N")})")
                        .ToList();

                    if (questions.Any())
                    {
                        // Put first question in main data row
                        arg.DataRow[PreOpChecklistReportExportSection0Component.COL_QUESTIONS] = questions[0];
                        
                        // Write the main row with the first question
                        arg.Writer.WriteRow(arg.DataRow);

                        // Write remaining questions in separate rows
                        for (int i = 1; i < questions.Count; i++)
                        {
                            var questionRow = new DataRow();
                            questionRow.AddColumn(5, PreOpChecklistReportExportSection0Component.COL_QUESTIONS, questions[i]);
                            arg.Writer.WriteRow(questionRow);
                        }

                        // Skip the original row write since we've already written it
                        arg.SkipRow = true;
                    }
                }
            }
        }
        catch (Exception)
        {
            arg.DataRow[PreOpChecklistReportExportSection0Component.COL_QUESTIONS] = "";
            arg.DataRow[PreOpChecklistReportExportSection0Component.COL_CHECKLISTFAILURES] = "";
        }
    }
}
