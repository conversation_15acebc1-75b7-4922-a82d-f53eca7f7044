import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock the global FleetXQ structure if it's not already available in the test environment
if (!global.FleetXQ) {
    global.FleetXQ = {
        Web: {
            ViewModels: {}
        }
    };
}

// Import or ensure the IIFE from AccessGroupForm2ViewModel.custom.js runs.
// This might require specific setup if your bundler/runner doesn't pick up IIFEs.
// For this example, we assume it can be loaded or a similar class definition is available.
// If AccessGroupForm2ViewModel.custom.js is a true IIFE, you might need to
// dynamically load it or ensure it's part of the test bundle.
// For simplicity, let's assume we can get the constructor:
// import '../CustomCode/WebApplicationLayer/wwwroot/ViewModels/AccessGroup/AccessGroupForm2ViewModel.custom.js'; 
// const AccessGroupForm2ViewModelCustom = FleetXQ.Web.ViewModels.AccessGroupForm2ViewModelCustom;
// If the above import doesn't work due to IIFE, you'd need a different loading strategy
// or refactor the custom.js to be a module. For now, we mock the class directly for testing.

// Mock implementation for the test
const MockAccessGroupForm2ViewModelCustom = function (viewmodel) {
    this.viewmodel = viewmodel;
    var self = this; // Mimic the 'self' pattern

    // Copy relevant functions from your actual custom.js here for testing
    // Or ensure the actual file is loaded and this.viewmodel is correctly assigned.

    this.selectAllEditCommand = function () {
        self.viewmodel.CurrentObject().Data.CanEditVehicle(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklist(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklistSetting(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleImpactSetting(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleService(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingFullLockout(true);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingVorStatus(true);
        self.viewmodel.CurrentObject().Data.CanEditRAModuleSwap(true);
    };

    this.deselectAllEditCommand = function () {
        self.viewmodel.CurrentObject().Data.CanEditVehicle(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklist(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleChecklistSetting(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleImpactSetting(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleService(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingFullLockout(false);
        self.viewmodel.CurrentObject().Data.CanEditVehicleOtherSettingVorStatus(false);
        self.viewmodel.CurrentObject().Data.CanEditRAModuleSwap(false);
    };

    this.IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible = function () {
        return self.viewmodel.AccessGroupObject().Data.HasVehiclesAccess();
    };

    this.IsCanEditRAModuleSwapVisible = function () {
        return self.viewmodel.AccessGroupObject().Data.HasVehiclesAccess();
    };
};


describe('AccessGroupForm2ViewModelCustom', () => {
    let mockViewModel;
    let customViewModel;

    beforeEach(() => {
        // Reset mocks for each test
        mockViewModel = {
            CurrentObject: vi.fn().mockReturnValue({
                Data: {
                    CanEditVehicle: vi.fn(),
                    CanEditVehicleChecklist: vi.fn(),
                    CanEditVehicleChecklistSetting: vi.fn(),
                    CanEditVehicleImpactSetting: vi.fn(),
                    CanEditVehicleService: vi.fn(),
                    CanEditVehicleOtherSettingFullLockout: vi.fn(),
                    CanEditVehicleOtherSettingVorStatus: vi.fn(),
                    CanEditRAModuleSwap: vi.fn(), // Key property to test
                }
            }),
            AccessGroupObject: vi.fn().mockReturnValue({
                Data: {
                    HasVehiclesAccess: vi.fn(),
                }
            })
        };
        // Use the mock for testing purposes if direct import of IIFE is problematic
        customViewModel = new MockAccessGroupForm2ViewModelCustom(mockViewModel);
        // Replace MockAccessGroupForm2ViewModelCustom with your actual class if loaded
        // customViewModel = new FleetXQ.Web.ViewModels.AccessGroupForm2ViewModelCustom(mockViewModel);
    });

    describe('selectAllEditCommand', () => {
        it('should set CanEditRAModuleSwap to true', () => {
            customViewModel.selectAllEditCommand();
            expect(mockViewModel.CurrentObject().Data.CanEditRAModuleSwap).toHaveBeenCalledWith(true);
        });

        it('should set other vehicle edit permissions to true', () => {
            customViewModel.selectAllEditCommand();
            expect(mockViewModel.CurrentObject().Data.CanEditVehicle).toHaveBeenCalledWith(true);
            // Add more checks for other properties if necessary
        });
    });

    describe('deselectAllEditCommand', () => {
        it('should set CanEditRAModuleSwap to false', () => {
            customViewModel.deselectAllEditCommand();
            expect(mockViewModel.CurrentObject().Data.CanEditRAModuleSwap).toHaveBeenCalledWith(false);
        });

        it('should set other vehicle edit permissions to false', () => {
            customViewModel.deselectAllEditCommand();
            expect(mockViewModel.CurrentObject().Data.CanEditVehicle).toHaveBeenCalledWith(false);
            // Add more checks for other properties if necessary
        });
    });

    describe('IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible', () => {
        it('should return true if HasVehiclesAccess is true', () => {
            mockViewModel.AccessGroupObject().Data.HasVehiclesAccess.mockReturnValue(true);
            expect(customViewModel.IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible()).toBe(true);
        });

        it('should return false if HasVehiclesAccess is false', () => {
            mockViewModel.AccessGroupObject().Data.HasVehiclesAccess.mockReturnValue(false);
            expect(customViewModel.IsHTMLField_2fef2b8e3768426bb28309856472a826_Visible()).toBe(false);
        });
    });

    describe('IsCanEditRAModuleSwapVisible', () => {
        it('should return true if HasVehiclesAccess is true', () => {
            mockViewModel.AccessGroupObject().Data.HasVehiclesAccess.mockReturnValue(true);
            expect(customViewModel.IsCanEditRAModuleSwapVisible()).toBe(true);
        });

        it('should return false if HasVehiclesAccess is false', () => {
            mockViewModel.AccessGroupObject().Data.HasVehiclesAccess.mockReturnValue(false);
            expect(customViewModel.IsCanEditRAModuleSwapVisible()).toBe(false);
        });
    });
}); 