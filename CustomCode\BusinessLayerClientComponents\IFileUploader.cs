﻿
////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.Commands;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// FileUploader Component
	///  
	/// </summary>
	public interface IFileUploader : IModelBase 
    {
		/// <summary>
		/// UploadFile Method
		///  
      /// </summary>
		/// <param name="importJobStatus"></param>
        /// <returns></returns>		
		/// <param name="importComponentToUse"></param>
        /// <returns></returns>		
		System.Threading.Tasks.Task<ImportJobStatusContainer> UploadFileAsync(ImportJobStatusContainer importJobStatus, System.String importComponentToUse, Dictionary<string, object> parameters = null);
		
	}
}
