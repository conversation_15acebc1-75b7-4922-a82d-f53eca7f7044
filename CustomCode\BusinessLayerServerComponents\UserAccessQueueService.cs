using Azure.Messaging.ServiceBus;
using FleetXQ.Data.DataObjects;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Text.Json;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    /// <summary>
    /// Interface for user access queue service that handles sending user access update messages to Azure Service Bus
    /// </summary>
    public interface IUserAccessQueueService
    {
        /// <summary>
        /// Sends a user access update message to the Azure Service Bus queue for asynchronous processing
        /// </summary>
        /// <param name="message">The user access update message containing person and access information</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task SendUserAccessUpdateMessageAsync(UserAccessUpdateMessage message);


    }

    /// <summary>
    /// Service responsible for queuing user access update requests using Azure Service Bus
    /// Provides asynchronous, reliable message delivery for user access operations
    /// </summary>
    public class UserAccessQueueService : IUserAccessQueueService, IAsyncDisposable
    {
        private readonly ServiceBusClient _serviceBusClient;
        private readonly ServiceBusSender _sender;
        private readonly ILogger<UserAccessQueueService> _logger;
        private readonly string _queueName;

        public UserAccessQueueService(IConfiguration configuration, ILogger<UserAccessQueueService> logger)
        {
            _logger = logger;

            var connectionString = configuration.GetConnectionString("ServiceBus");
            _queueName = configuration.GetValue<string>("ServiceBus:UserAccessQueue", "user-access-update");

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("ServiceBus connection string is not configured");
            }

            _serviceBusClient = new ServiceBusClient(connectionString);
            _sender = _serviceBusClient.CreateSender(_queueName);
        }

        /// <summary>
        /// Sends a user access update message to the Azure Service Bus queue for asynchronous processing
        /// </summary>
        /// <param name="message">The user access update message containing person and access information</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SendUserAccessUpdateMessageAsync(UserAccessUpdateMessage message)
        {
            if (message == null)
                throw new ArgumentNullException(nameof(message));

            try
            {
                var messageJson = JsonSerializer.Serialize(message);
                var serviceBusMessage = new ServiceBusMessage(messageJson)
                {
                    MessageId = Guid.NewGuid().ToString(),
                    SessionId = message.PersonId.ToString(), // Use person ID as session ID for ordered processing
                    Subject = "UserAccessUpdate",
                    TimeToLive = TimeSpan.FromHours(24), // Message expires after 24 hours
                    CorrelationId = message.CorrelationId ?? Guid.NewGuid().ToString()
                };

                // Add custom properties for easier filtering and monitoring
                serviceBusMessage.ApplicationProperties["PersonId"] = message.PersonId.ToString();
                serviceBusMessage.ApplicationProperties["CustomerId"] = message.CustomerId.ToString();
                serviceBusMessage.ApplicationProperties["CreatedAt"] = message.CreatedAt.ToString("O");
                serviceBusMessage.ApplicationProperties["Priority"] = message.Priority;
                serviceBusMessage.ApplicationProperties["InitiatedByUserId"] = message.InitiatedByUserId?.ToString() ?? "";

                // Set message priority if high or critical
                if (message.Priority.Equals("High", StringComparison.OrdinalIgnoreCase))
                {
                    serviceBusMessage.ApplicationProperties["MessagePriority"] = "High";
                }
                else if (message.Priority.Equals("Critical", StringComparison.OrdinalIgnoreCase))
                {
                    serviceBusMessage.ApplicationProperties["MessagePriority"] = "Critical";
                    serviceBusMessage.TimeToLive = TimeSpan.FromHours(12); // Shorter TTL for critical messages
                }

                await _sender.SendMessageAsync(serviceBusMessage);

                _logger.LogInformation("[PERF] User access update message sent to queue for person {PersonId} with correlation {CorrelationId}",
                    message.PersonId, serviceBusMessage.CorrelationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[PERF] Failed to send user access update message to queue for person {PersonId}", message.PersonId);
                throw;
            }
        }

        /// <summary>
        /// Disposes the Azure Service Bus resources asynchronously
        /// </summary>
        /// <returns>Task representing the asynchronous disposal operation</returns>
        public async ValueTask DisposeAsync()
        {
            if (_sender != null)
            {
                await _sender.DisposeAsync();
            }

            if (_serviceBusClient != null)
            {
                await _serviceBusClient.DisposeAsync();
            }
        }
    }
}