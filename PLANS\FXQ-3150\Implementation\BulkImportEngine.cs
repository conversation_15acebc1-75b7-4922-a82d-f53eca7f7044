using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace FleetXQ.Tools.BulkImporter
{
    /// <summary>
    /// Bulk import engine that uses temporary tables for high-performance data import
    /// without requiring permanent staging table schema changes
    /// </summary>
    public class BulkImportEngine
    {
        private readonly string _connectionString;
        private readonly ILogger<BulkImportEngine> _logger;
        private readonly BulkImportConfiguration _config;

        public BulkImportEngine(
            IConfiguration configuration,
            ILogger<BulkImportEngine> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
            _logger = logger;
            _config = configuration.GetSection("BulkImport").Get<BulkImportConfiguration>();
        }

        /// <summary>
        /// Import drivers using temporary table approach
        /// </summary>
        public async Task<ImportResult> ImportDriversAsync(
            IEnumerable<DriverImportRow> drivers,
            bool allowUpdates = true)
        {
            var sessionId = Guid.NewGuid();
            var result = new ImportResult { SessionId = sessionId, EntityType = "Driver" };

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                _logger.LogInformation("Starting driver import session {SessionId}", sessionId);

                // Step 1: Initialize the import session (creates temp table)
                await InitializeDriverImportAsync(connection, sessionId);

                // Step 2: Bulk load data into temporary table
                var bulkLoadResult = await BulkLoadDriverDataAsync(connection, drivers);
                result.InputRowCount = bulkLoadResult.RowCount;

                // Step 3: Process and merge data
                var processResult = await ProcessDriverImportAsync(connection, sessionId, allowUpdates);
                result.ProcessedRowCount = processResult.ProcessedRows;
                result.Success = true;
                result.Message = "Driver import completed successfully";

                _logger.LogInformation(
                    "Driver import completed. Session: {SessionId}, Input: {InputRows}, Processed: {ProcessedRows}",
                    sessionId, result.InputRowCount, result.ProcessedRowCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Driver import failed for session {SessionId}", sessionId);
                result.Success = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Import vehicles using temporary table approach
        /// </summary>
        public async Task<ImportResult> ImportVehiclesAsync(
            IEnumerable<VehicleImportRow> vehicles,
            bool allowUpdates = true)
        {
            var sessionId = Guid.NewGuid();
            var result = new ImportResult { SessionId = sessionId, EntityType = "Vehicle" };

            try
            {
                using var connection = new SqlConnection(_connectionString);
                await connection.OpenAsync();

                _logger.LogInformation("Starting vehicle import session {SessionId}", sessionId);

                // Step 1: Initialize the import session
                await InitializeVehicleImportAsync(connection, sessionId);

                // Step 2: Bulk load data into temporary table
                var bulkLoadResult = await BulkLoadVehicleDataAsync(connection, vehicles);
                result.InputRowCount = bulkLoadResult.RowCount;

                // Step 3: Process and merge data
                var processResult = await ProcessVehicleImportAsync(connection, sessionId, allowUpdates);
                result.ProcessedRowCount = processResult.ProcessedRows;
                result.Success = true;
                result.Message = "Vehicle import completed successfully";

                _logger.LogInformation(
                    "Vehicle import completed. Session: {SessionId}, Input: {InputRows}, Processed: {ProcessedRows}",
                    sessionId, result.InputRowCount, result.ProcessedRowCount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Vehicle import failed for session {SessionId}", sessionId);
                result.Success = false;
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// Initialize driver import session - creates temporary table
        /// </summary>
        private async Task InitializeDriverImportAsync(SqlConnection connection, Guid sessionId)
        {
            using var command = new SqlCommand("dbo.BeginDriverImport", connection);
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@ImportSessionId", sessionId);
            command.CommandTimeout = _config.CommandTimeoutSeconds;

            await command.ExecuteNonQueryAsync();
            _logger.LogDebug("Driver import session {SessionId} initialized", sessionId);
        }

        /// <summary>
        /// Initialize vehicle import session - creates temporary table
        /// </summary>
        private async Task InitializeVehicleImportAsync(SqlConnection connection, Guid sessionId)
        {
            using var command = new SqlCommand("dbo.BeginVehicleImport", connection);
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@ImportSessionId", sessionId);
            command.CommandTimeout = _config.CommandTimeoutSeconds;

            await command.ExecuteNonQueryAsync();
            _logger.LogDebug("Vehicle import session {SessionId} initialized", sessionId);
        }

        /// <summary>
        /// Bulk load driver data into temporary table using SqlBulkCopy
        /// </summary>
        private async Task<BulkLoadResult> BulkLoadDriverDataAsync(
            SqlConnection connection,
            IEnumerable<DriverImportRow> drivers)
        {
            using var dataReader = new DriverDataReader(drivers);
            using var bulkCopy = new SqlBulkCopy(connection);

            // Configure SqlBulkCopy for optimal performance
            bulkCopy.DestinationTableName = "#ImportDrivers";
            bulkCopy.BatchSize = _config.BatchSize;
            bulkCopy.BulkCopyTimeout = _config.BulkCopyTimeoutSeconds;
            bulkCopy.EnableStreaming = true;

            // Set column mappings
            bulkCopy.ColumnMappings.Add("ExternalId", "ExternalId");
            bulkCopy.ColumnMappings.Add("FirstName", "FirstName");
            bulkCopy.ColumnMappings.Add("LastName", "LastName");
            bulkCopy.ColumnMappings.Add("Email", "Email");
            bulkCopy.ColumnMappings.Add("PhoneNumber", "PhoneNumber");
            bulkCopy.ColumnMappings.Add("EmployeeNumber", "EmployeeNumber");
            bulkCopy.ColumnMappings.Add("DepartmentName", "DepartmentName");
            bulkCopy.ColumnMappings.Add("SiteName", "SiteName");
            bulkCopy.ColumnMappings.Add("CustomerName", "CustomerName");
            bulkCopy.ColumnMappings.Add("Active", "Active");

            await bulkCopy.WriteToServerAsync(dataReader);

            var rowCount = dataReader.RecordsProcessed;
            _logger.LogDebug("Bulk loaded {RowCount} driver records into temporary table", rowCount);

            return new BulkLoadResult { RowCount = rowCount };
        }

        /// <summary>
        /// Bulk load vehicle data into temporary table using SqlBulkCopy
        /// </summary>
        private async Task<BulkLoadResult> BulkLoadVehicleDataAsync(
            SqlConnection connection,
            IEnumerable<VehicleImportRow> vehicles)
        {
            using var dataReader = new VehicleDataReader(vehicles);
            using var bulkCopy = new SqlBulkCopy(connection);

            // Configure SqlBulkCopy for optimal performance
            bulkCopy.DestinationTableName = "#ImportVehicles";
            bulkCopy.BatchSize = _config.BatchSize;
            bulkCopy.BulkCopyTimeout = _config.BulkCopyTimeoutSeconds;
            bulkCopy.EnableStreaming = true;

            // Set column mappings
            bulkCopy.ColumnMappings.Add("VIN", "VIN");
            bulkCopy.ColumnMappings.Add("HireNo", "HireNo");
            bulkCopy.ColumnMappings.Add("ModelName", "ModelName");
            bulkCopy.ColumnMappings.Add("SerialNumber", "SerialNumber");
            bulkCopy.ColumnMappings.Add("RegistrationNumber", "RegistrationNumber");
            bulkCopy.ColumnMappings.Add("DepartmentName", "DepartmentName");
            bulkCopy.ColumnMappings.Add("SiteName", "SiteName");
            bulkCopy.ColumnMappings.Add("CustomerName", "CustomerName");
            bulkCopy.ColumnMappings.Add("Active", "Active");
            bulkCopy.ColumnMappings.Add("YearManufactured", "YearManufactured");

            await bulkCopy.WriteToServerAsync(dataReader);

            var rowCount = dataReader.RecordsProcessed;
            _logger.LogDebug("Bulk loaded {RowCount} vehicle records into temporary table", rowCount);

            return new BulkLoadResult { RowCount = rowCount };
        }

        /// <summary>
        /// Process driver import - validation and merge from temp to production tables
        /// </summary>
        private async Task<ProcessResult> ProcessDriverImportAsync(
            SqlConnection connection,
            Guid sessionId,
            bool allowUpdates)
        {
            using var command = new SqlCommand("dbo.ProcessDriverImport", connection);
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@ImportSessionId", sessionId);
            command.Parameters.AddWithValue("@AllowUpdates", allowUpdates);
            command.CommandTimeout = _config.CommandTimeoutSeconds;

            using var reader = await command.ExecuteReaderAsync();

            // Check if there are validation errors (first result set)
            var errors = new List<string>();
            while (await reader.ReadAsync())
            {
                if (reader.FieldCount > 2) // Error result set has RowNumber, ExternalId, ErrorMessage
                {
                    errors.Add($"Row {reader["RowNumber"]}: {reader["ErrorMessage"]}");
                }
            }

            if (errors.Any())
            {
                throw new ValidationException($"Import validation failed:\n{string.Join("\n", errors)}");
            }

            // Move to summary result set
            await reader.NextResultAsync();
            await reader.ReadAsync();

            return new ProcessResult
            {
                ProcessedRows = reader.GetInt32("ProcessedRows"),
                InputRows = reader.GetInt32("InputRows")
            };
        }

        /// <summary>
        /// Process vehicle import - validation and merge from temp to production tables
        /// </summary>
        private async Task<ProcessResult> ProcessVehicleImportAsync(
            SqlConnection connection,
            Guid sessionId,
            bool allowUpdates)
        {
            using var command = new SqlCommand("dbo.ProcessVehicleImport", connection);
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@ImportSessionId", sessionId);
            command.Parameters.AddWithValue("@AllowUpdates", allowUpdates);
            command.CommandTimeout = _config.CommandTimeoutSeconds;

            using var reader = await command.ExecuteReaderAsync();

            // Check for validation errors
            var errors = new List<string>();
            while (await reader.ReadAsync())
            {
                if (reader.FieldCount > 2) // Error result set
                {
                    errors.Add($"Row {reader["RowNumber"]}: {reader["ErrorMessage"]}");
                }
            }

            if (errors.Any())
            {
                throw new ValidationException($"Import validation failed:\n{string.Join("\n", errors)}");
            }

            // Move to summary result set
            await reader.NextResultAsync();
            await reader.ReadAsync();

            return new ProcessResult
            {
                ProcessedRows = reader.GetInt32("ProcessedRows"),
                InputRows = reader.GetInt32("InputRows")
            };
        }

        /// <summary>
        /// Get import session status
        /// </summary>
        public async Task<ImportSessionStatus> GetImportSessionStatusAsync(Guid sessionId)
        {
            using var connection = new SqlConnection(_connectionString);
            await connection.OpenAsync();

            using var command = new SqlCommand("dbo.GetImportSessionStatus", connection);
            command.CommandType = CommandType.StoredProcedure;
            command.Parameters.AddWithValue("@ImportSessionId", sessionId);

            using var reader = await command.ExecuteReaderAsync();

            if (await reader.ReadAsync())
            {
                return new ImportSessionStatus
                {
                    Id = reader.GetGuid("Id"),
                    EntityType = reader.GetString("EntityType"),
                    Status = reader.GetString("Status"),
                    StartedAt = reader.GetDateTime("StartedAt"),
                    EndedAt = reader.IsDBNull("EndedAt") ? null : reader.GetDateTime("EndedAt"),
                    ProcessedCount = reader.GetInt32("ProcessedCount"),
                    ErrorMessage = reader.IsDBNull("ErrorMessage") ? null : reader.GetString("ErrorMessage"),
                    DurationMs = reader.GetInt32("DurationMs")
                };
            }

            return null;
        }
    }

    #region Supporting Classes

    public class BulkImportConfiguration
    {
        public int BatchSize { get; set; } = 10000;
        public int CommandTimeoutSeconds { get; set; } = 300;
        public int BulkCopyTimeoutSeconds { get; set; } = 600;
    }

    public class ImportResult
    {
        public Guid SessionId { get; set; }
        public string EntityType { get; set; }
        public bool Success { get; set; }
        public int InputRowCount { get; set; }
        public int ProcessedRowCount { get; set; }
        public string Message { get; set; }
        public string ErrorMessage { get; set; }
    }

    public class BulkLoadResult
    {
        public int RowCount { get; set; }
    }

    public class ProcessResult
    {
        public int ProcessedRows { get; set; }
        public int InputRows { get; set; }
    }

    public class ImportSessionStatus
    {
        public Guid Id { get; set; }
        public string EntityType { get; set; }
        public string Status { get; set; }
        public DateTime StartedAt { get; set; }
        public DateTime? EndedAt { get; set; }
        public int ProcessedCount { get; set; }
        public string ErrorMessage { get; set; }
        public int DurationMs { get; set; }
    }

    public class ValidationException : Exception
    {
        public ValidationException(string message) : base(message) { }
    }

    #endregion
}
