﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.BusinessLayer.Tasks;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// BroadcastMessageHistoryAPI Component
	///  
	/// </summary>
    public partial class BroadcastMessageHistoryAPI : BaseServerComponent, IBroadcastMessageHistoryAPI
    {
        private readonly ILoggingService _logger;
        private readonly IEmailService _emailService;

        public BroadcastMessageHistoryAPI(
            IServiceProvider serviceProvider,
            IConfiguration configuration,
            IDataFacade dataFacade,
            ILoggingService logger,
            IEmailService emailService) : base(serviceProvider, configuration, dataFacade)
        {
            _logger = logger;
            _emailService = emailService;
        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> StoreBroadcastMessageHistoryAsync(string Message, Dictionary<string, object> parameters = null)
        {
            var payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            if (payloadObject == null)
            {
                _logger.LogError(new GOServerException("Invalid Payload"));
                throw new GOServerException("Invalid Payload");
            }

            var payload = HandleBroadcastMessageResponsePayload(payloadObject.Payload);

            if (payload == null)
            {
                _logger.LogError(new GOServerException("Broadcast message response payload is null"));
                throw new GOServerException("Broadcast message response payload is null");
            }

            if (string.IsNullOrEmpty(payload.MessageId))
            {
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(400, "MessageId is required")));
            }

            try
            {
                // Try to find existing history by MessageId only
                var existingHistory = (await _dataFacade.BroadcastMessageHistoryDataProvider.GetCollectionAsync(
                    null,
                    "MessageId == @0",
                    new object[] { int.Parse(payload.MessageId) }
                )).FirstOrDefault();

                if (existingHistory != null)
                {
                    // Find the card with the matching weigand
                    var card = (await _dataFacade.CardDataProvider.GetCollectionAsync(
                        null,
                        "Weigand == @0",
                        new object[] { payload.DriverId }
                    )).FirstOrDefault();

                    if (card == null)
                    {
                        _logger.LogError(new GOServerException($"Card with Weigand {payload.DriverId} not found"));
                        throw new GOServerException($"Card with Weigand {payload.DriverId} not found");
                    }

                    // Load the driver associated with the card
                    var driver = await card.LoadDriverAsync();
                    if (driver != null)
                    {
                        existingHistory.DriverId = driver.Id;
                    }

                    // Map response codes to human-readable responses
                    switch (payload.Response)
                    {
                        case "0":
                            existingHistory.SetResponseValue("OK");
                            break;
                        case "1":
                            existingHistory.SetResponseValue("Yes");
                            break;
                        case "2":
                            existingHistory.SetResponseValue("No");
                            break;
                        case "3":
                            existingHistory.SetResponseValue("Timeout");
                            break;
                        case "4":
                            existingHistory.SetResponseValue("Logout");
                            break;
                        case "5":
                            existingHistory.SetResponseValue("No Driver");
                            break;
                        default:
                            _logger.LogWarning($"Unexpected response code received: {payload.Response}");
                            existingHistory.SetResponseValue(payload.Response); // Store the raw value
                            break;
                    }

                    // Update timestamps
                    existingHistory.ResponseTime = DataUtils.HexToUtcTime(payload.Timestamp);
                    existingHistory.DisplayTime = DataUtils.HexToUtcTime(payload.DisplayTimestamp);

                    await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(existingHistory);

                    // Get the vehicle
                    var vehicle = await existingHistory.LoadVehicleAsync();
                    if (vehicle == null)
                    {
                        throw new Exception($"Vehicle not found with ID: {existingHistory.VehicleId}");
                    }

                    // Get the "Broadcast Message Alert" alert type
                    var alertType = (await _dataFacade.AlertDataProvider.GetCollectionAsync(
                        null, "Name == @0", new object[] { "Broadcast Message Alert" })).SingleOrDefault();

                    if (alertType == null)
                    {
                        _logger.LogError(new GOServerException("Broadcast Message Alert type not found"));
                        // Continue processing instead of throwing - just skip alert processing
                        return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Broadcast message history updated successfully")));
                    }

                    // Get all alert subscriptions for this vehicle
                    var vehicleAlertSubscriptions = await vehicle.LoadVehicleAlertSubscriptionItemsAsync();

                    if (vehicleAlertSubscriptions != null)
                    {
                        foreach (var subscription in vehicleAlertSubscriptions)
                        {
                            var alertSubscription = await subscription.LoadAlertSubscriptionAsync();

                            // Check if this subscription is for Broadcast Message Alert and is active
                            if (alertSubscription.AlertId == alertType.Id && alertSubscription.IsActive)
                            {
                                var person = await driver.LoadPersonAsync();
                                var driverName = person != null ? $"{person.FirstName} {person.LastName}" : "Unknown Driver";

                                // Create email details
                                var emailDetail = new EmailDetail
                                {
                                    Alert = "Broadcast Message Alert",
                                    TimeStamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") + " - UTC",
                                    VehicleId = vehicle.Id,
                                    DriverName = driverName,
                                    Details = new EmailDetail.AlertDetails
                                    {
                                        Response = existingHistory.Response,
                                        Vehicle = vehicle.Id
                                    }
                                };

                                // Send email notification
                                await _emailService.SendEmailAsync(JsonConvert.SerializeObject(emailDetail));
                            }
                        }
                    }

                    return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Broadcast message history updated successfully")));
                }
                else
                {
                    // Do not create a new record
                    return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(404, "Broadcast message history not found")));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex);
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(500, "Error storing broadcast message history")));
            }
        }

        private static BroadcastMessageResponseDataObject HandleBroadcastMessageResponsePayload(string payload)
        {
            BroadcastMessageResponseDataObject broadcastMessagePayloadObject = new BroadcastMessageResponseDataObject();
            string[] payloadArray = payload.Split(" ");
            foreach (string payloadItem in payloadArray)
            {
                string[] payloadItemArray = payloadItem.Split("=");
                if (payloadItemArray[0] == "MSG_RSP")
                {
                    string[] authArray = payloadItemArray[1].Split(",");
                    broadcastMessagePayloadObject.MessageId = authArray[0];
                    broadcastMessagePayloadObject.DriverId = authArray[1];
                    broadcastMessagePayloadObject.Timestamp = authArray[2];
                    broadcastMessagePayloadObject.DisplayTimestamp = authArray[3];
                    broadcastMessagePayloadObject.Response = authArray[4];
                }
            }
            return broadcastMessagePayloadObject;
        }
    }
}
