# Unit Tests

This folder contains standalone unit tests for FleetXQ components that are independent of the main test project.

## VehicleAccessCreation Tests

### Overview
The `VehicleAccessCreationTest.cs` file contains unit tests for the `VehicleAccessCreation` component. These tests are designed to be:
- **Standalone** - No dependencies on TestBase or other shared test infrastructure
- **Independent** - Can run without the main test project compilation issues
- **Focused** - Test specific functionality of VehicleAccessCreation

### Test Methods

1. **`CreateVehicleAccessAsync_WithValidMessage_ShouldReturnSuccessResponse`**
   - Tests normal operation with valid JSON message
   - Verifies JSON parsing works correctly

2. **`CreateVehicleAccessAsync_WithInvalidJsonMessage_ShouldReturnErrorResponse`** 
   - Tests error handling with malformed JSON
   - Ensures proper error response format

3. **`CreateVehicleAccessAsync_WithNullMessage_ShouldReturnInvalidFormatResponse`**
   - Tests null message handling
   - Verifies specific error response for invalid format

4. **`CreateVehicleAccessAsync_WithEmptyMessage_ShouldHandleGracefully`**
   - Tests empty string message handling
   - Ensures graceful error handling

5. **`VehicleAccessCreation_Constructor_ShouldInitializeCorrectly`**
   - Tests constructor initialization
   - Verifies object creation works properly

6. **`CreateTestVehicleAccessMessage_ShouldCreateValidMessage`**
   - Tests the helper method for creating test data
   - Validates test message structure

### Running Tests

#### Run All Tests
```bash
dotnet test
```

#### Run Specific Test
```bash
dotnet test --filter "CreateVehicleAccessAsync_WithValidMessage_ShouldReturnSuccessResponse"
```

#### Run Tests with Verbose Output
```bash
dotnet test --logger "console;verbosity=detailed"
```

### Dependencies
- **NUnit 3.13.3** - Test framework
- **NSubstitute 5.0.0** - Mocking framework
- **Microsoft.Extensions.*** - Configuration, DI, Logging support

### Project References
- `FleetXQ.BusinessLayer.Components.Server.Custom` - Contains VehicleAccessCreation component
- `FleetXQ.Data.DataObjects.Custom` - Data objects and interfaces
- Generated assemblies for ORM support classes

### Notes
- Tests use NSubstitute for mocking instead of Moq for better .NET 7 compatibility
- Mock interfaces are defined locally to avoid compilation issues with incomplete interface definitions
- Tests focus on critical paths: JSON parsing, error handling, and basic functionality
