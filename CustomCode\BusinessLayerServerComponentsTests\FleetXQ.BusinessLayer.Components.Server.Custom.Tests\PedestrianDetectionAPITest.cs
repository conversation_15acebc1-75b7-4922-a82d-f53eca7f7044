using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.Data.DataObjects.Custom;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    internal class PedestrianDetectionAPITest : TestBase
    {
        private IPedestrianDetectionAPI _pedestrianDetectionAPI;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"PedestrianDetectionAPI-{Guid.NewGuid()}";
        
        // Infrastructure IDs (created once, reused across tests)
        private Guid _countryId;
        private Guid _regionId;
        private Guid _dealerId;
        private Guid _customerId;
        private Guid _timezoneId;
        private Guid _siteId;
        private Guid _departmentId;
        private Guid _modelId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _pedestrianDetectionAPI = _serviceProvider.GetRequiredService<IPedestrianDetectionAPI>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_ValidPedestrianDetectedMessage_CreatesNewRecord()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var driver = await session.LoadDriverAsync(skipSecurity: true);

            string validMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(validMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("created successfully"), "Pedestrian detection history should be created successfully.");

            // Verify the record was created
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0 AND VehicleId == @1",
                new object[] { driver.Id, vehicle.Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history, Is.Not.Null, "Pedestrian detection history record should exist.");
            Assert.That(history.DriverId, Is.EqualTo(driver.Id), "Driver ID should match.");
            Assert.That(history.VehicleId, Is.EqualTo(vehicle.Id), "Vehicle ID should match.");
            Assert.That(history.AlertDuration, Is.EqualTo("0"), "Alert duration should be 0 for initial detection event.");
            Assert.That(history.SEAT, Is.False, "SEAT should be false based on payload.");
            Assert.That(history.TRACK, Is.False, "TRACK should be false based on payload.");
            Assert.That(history.HYDR, Is.False, "HYDR should be false based on payload.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_ValidPedestrianLeftMessage_UpdatesExistingRecord()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var driver = await session.LoadDriverAsync(skipSecurity: true);
            
            // First, create a detection record (seen_state=1)
            string detectionMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 1 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 1 1 0 9: 1 1 0 10: 0 0 0 SEAT: 1 0 4b6 TRACK: 1 0 4b6 HYDR: 1 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";
            await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(detectionMessage);

            // Then, send the pedestrian left message (seen_state=0)
            string leftMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,0,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(leftMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("updated successfully"), "Pedestrian detection history should be updated successfully.");

            // Verify the record was updated
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0 AND VehicleId == @1",
                new object[] { driver.Id, vehicle.Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history, Is.Not.Null, "Pedestrian detection history record should exist.");
            Assert.That(history.AlertDuration, Is.EqualTo("00:05"), "Alert duration should be updated to mm:ss format for 5376ms (5.376s) => 00:05.");
            Assert.That(history.SEAT, Is.False, "SEAT should be updated to false.");
            Assert.That(history.TRACK, Is.False, "TRACK should be updated to false.");
            Assert.That(history.HYDR, Is.False, "HYDR should be updated to false.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_DuplicateDetectionMessage_IgnoresDuplicate()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();

            string message = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 1 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act - Send the same message twice
            var firstResponse = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(message);
            var secondResponse = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(message);

            // Assert
            Assert.That(firstResponse.Result, Does.Contain("created successfully"), "First message should create a record.");
            Assert.That(secondResponse.Result, Does.Contain("ignored"), "Second message should be ignored as duplicate.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_PedestrianLeftWithoutDetection_ReturnsNotFound()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();

            // Send pedestrian left message without a corresponding detection message
            string leftMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,0,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(leftMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("404"), "Should return 404 status.");
            Assert.That(response.Result, Does.Contain("No corresponding pedestrian detection event found"), "Should indicate no corresponding detection event found.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_MissingDriverId_ReturnsError()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            string invalidMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=,686549DF,1500,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(invalidMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("400"), "Should return 400 status.");
            Assert.That(response.Result, Does.Contain("DriverId is required"), "Should indicate DriverId is required.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_MissingTimestamp_ReturnsError()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            string invalidMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,,1500,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(invalidMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("400"), "Should return 400 status.");
            Assert.That(response.Result, Does.Contain("Timestamp is required"), "Should indicate Timestamp is required.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_InvalidSeenState_ReturnsError()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            string invalidMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,2,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(invalidMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("400"), "Should return 400 status.");
            Assert.That(response.Result, Does.Contain("Invalid seen_state value"), "Should indicate invalid seen_state value.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_WithSEATTrue_SetsSEATFlag()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();

            string message = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 1 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(message);

            // Assert
            Assert.That(response.Result, Does.Contain("created successfully"), "Should create record successfully.");

            // Verify SEAT flag is set correctly
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0",
                new object[] { (await session.LoadDriverAsync(skipSecurity: true)).Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history.SEAT, Is.True, "SEAT flag should be true when SEAT: 1 in payload.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_WithTRACKTrue_SetsTRACKFlag()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();

            string message = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,1,0: SEAT: 0 0 4b6 TRACK: 1 0 4b6 HYDR: 0 0 4b6\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(message);

            // Assert
            Assert.That(response.Result, Does.Contain("created successfully"), "Should create record successfully.");

            // Verify TRACK flag is set correctly
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0",
                new object[] { (await session.LoadDriverAsync(skipSecurity: true)).Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history.TRACK, Is.True, "TRACK flag should be true when TRACK: 1 in payload.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_WithHYDRTrue_SetsHYDRFlag()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();

            string message = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1500,1,0: SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 1 0 4b6\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(message);

            // Assert
            Assert.That(response.Result, Does.Contain("created successfully"), "Should create record successfully.");

            // Verify HYDR flag is set correctly
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0",
                new object[] { (await session.LoadDriverAsync(skipSecurity: true)).Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history.HYDR, Is.True, "HYDR flag should be true when HYDR: 1 in payload.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_HexDurationTimeConversion_ConvertsCorrectly()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var driver = await session.LoadDriverAsync(skipSecurity: true);
            
            // First, create a detection record (seen_state=1)
            string detectionMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1A2B,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 1 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 1 1 0 9: 1 1 0 10: 0 0 0 SEAT: 1 0 4b6 TRACK: 1 0 4b6 HYDR: 1 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";
            await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(detectionMessage);

            // Then, send the pedestrian left message (seen_state=0) with hex duration time
            string leftMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,1A2B,0,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(leftMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("updated successfully"), "Pedestrian detection history should be updated successfully.");

            // Verify the record was updated with correct hex to decimal conversion
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0 AND VehicleId == @1",
                new object[] { driver.Id, vehicle.Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history, Is.Not.Null, "Pedestrian detection history record should exist.");
            // 1A2B in hex = 6699 in decimal, converted to seconds = 6.699 => 00:06
            Assert.That(history.AlertDuration, Is.EqualTo("00:06"), "Alert duration should be converted from hex 1A2B to mm:ss format (6.699s) => 00:06.");
        }

        [Test]
        public async Task StorePedestrianDetectionHistoryAsync_InvalidHexDurationTime_HandlesGracefully()
        {
            // Arrange - Create a new session for this test
            var session = await CreateTestSessionAsync();
            var vehicle = await session.LoadVehicleAsync(skipSecurity: true);
            var driver = await session.LoadDriverAsync(skipSecurity: true);
            
            // First, create a detection record (seen_state=1)
            string detectionMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,INVALID,1,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 1 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 1 1 0 9: 1 1 0 10: 0 0 0 SEAT: 1 0 4b6 TRACK: 1 0 4b6 HYDR: 1 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";
            await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(detectionMessage);

            // Then, send the pedestrian left message (seen_state=0) with invalid hex duration time
            string leftMessage = "{\"event_type\":\"SEEN\",\"payload\":\"SEEN=6F5842044,686549DF,INVALID,0,0: 1 4b6 0 1: 0 0 4b6 2: 0 0 4b6 3: 0 53 462 4: 1 53 463 5: 0 0 0 6: 0 0 0 7: 0 0 0 8: 0 1 0 9: 0 1 0 10: 0 0 0 SEAT: 0 0 4b6 TRACK: 0 0 4b6 HYDR: 0 0 4b6 #HRSS0: 0\",\"session_id\":\"" + session.Id + "\"}";

            // Act
            var response = await _pedestrianDetectionAPI.StorePedestrianDetectionHistoryAsync(leftMessage);

            // Assert
            Assert.That(response, Is.Not.Null, "Response should not be null.");
            Assert.That(response.Result, Does.Contain("updated successfully"), "Pedestrian detection history should be updated successfully.");

            // Verify the record was updated with default value for invalid hex
            var history = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                null,
                "DriverId == @0 AND VehicleId == @1",
                new object[] { driver.Id, vehicle.Id },
                skipSecurity: true
            )).FirstOrDefault();

            Assert.That(history, Is.Not.Null, "Pedestrian detection history record should exist.");
            Assert.That(history.AlertDuration, Is.EqualTo("00:00"), "Alert duration should default to 00:00 when hex conversion fails.");
        }

        private async Task<SessionDataObject> CreateTestSessionAsync()
        {
            // Create fresh test data for this test
            var (vehicle, driver) = await CreateTestVehicleAndDriverAsync();

            // Create a new session for this test
            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = driver.Id;
            session.StartTime = DateTime.UtcNow.AddHours(-1);
            session.EndTime = DateTime.UtcNow;
            session = await _dataFacade.SessionDataProvider.SaveAsync(session, skipSecurity: true);
            
            return session;
        }

        private async Task<(VehicleDataObject vehicle, DriverDataObject driver)> CreateTestVehicleAndDriverAsync()
        {
            // Get the existing test infrastructure (country, region, dealer, customer, site, department)
            var country = (await _dataFacade.CountryDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _countryId }, skipSecurity: true)).FirstOrDefault();
            var region = (await _dataFacade.RegionDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _regionId }, skipSecurity: true)).FirstOrDefault();
            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _dealerId }, skipSecurity: true)).FirstOrDefault();
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _customerId }, skipSecurity: true)).FirstOrDefault();
            var timeZone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _timezoneId }, skipSecurity: true)).FirstOrDefault();
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _siteId }, skipSecurity: true)).FirstOrDefault();
            var department = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _departmentId }, skipSecurity: true)).FirstOrDefault();
            var model = (await _dataFacade.ModelDataProvider.GetCollectionAsync(
                null, "Id == @0", new object[] { _modelId }, skipSecurity: true)).FirstOrDefault();

            // Create a new person for this test
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.CustomerId = customer.Id;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Create a new driver for this test
            var driver = await person.LoadDriverAsync(skipSecurity: true);
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);

            // Create a new module for this vehicle
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.IoTDevice = $"test_{Guid.NewGuid()}";
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

            // Create a new vehicle for this test
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.HireNo = $"TEST{Guid.NewGuid().ToString().Substring(0, 8)}";
            vehicle.SerialNo = $"TEST_SERIAL_{Guid.NewGuid().ToString().Substring(0, 8)}";
            vehicle.SiteId = site.Id;
            vehicle.DepartmentId = department.Id;
            vehicle.CustomerId = customer.Id;
            vehicle.ModelId = model.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);

            return (vehicle, driver);
        }

        private async Task CreateTestDataAsync()
        {
            // Create test country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);
            _countryId = country.Id;

            // Create test region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);
            _regionId = region.Id;

            // Create test dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test Dealer";
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);
            _dealerId = dealer.Id;

            // Create test customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);
            _customerId = customer.Id;

            // Create test timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);
            _timezoneId = timeZone.Id;

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Name = "Test Site";
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);
            _siteId = site.Id;

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            department.CustomerId = customer.Id;
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);
            _departmentId = department.Id;

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);
            _modelId = model.Id;
        }
    }
} 