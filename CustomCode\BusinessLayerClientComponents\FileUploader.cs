﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// FileUploader Component
	///  
	/// </summary>
    public partial class FileUploader : IFileUploader 
    {
        /// <summary>
        /// UploadFile Method
        ///  
        /// </summary>
        /// <param name="importJobStatus"></param>
        public Task<ImportJobStatusContainer> UploadFileAsync(ImportJobStatusContainer importJobStatus, System.String importComponentToUse, Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }


        public void Dispose()
		{
		}

    }
}
