using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    public class DealerCategoryImportComponentExtension : IImportExportComponentExtension<DealerCategoryImportSection0Component, ModelDataObject>
    {
        private readonly IDataFacade dataFacade;

        public DealerCategoryImportComponentExtension(IDataFacade dataFacade)
        {
            this.dataFacade = dataFacade;
        }

        public void Init(IImportExportComponent<ModelDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<ModelDataObject> arg)
        {
            var dealerName = arg.DataRow[DealerCategoryImportSection0Component.COL_DEALER].ToString();
            
            var dealer = (await dataFacade.DealerDataProvider.GetCollectionAsync(
                null, 
                "Name == @0", 
                new object[] { dealerName }
            )).SingleOrDefault();

            if (dealer == null)
            {
                throw new Exception($"Dealer not found: {dealerName}");
            }

            arg.Entity.Name = arg.DataRow[DealerCategoryImportSection0Component.COL_CATEGORYNAME].ToString();
            arg.Entity.Description = arg.DataRow[DealerCategoryImportSection0Component.COL_DESCRIPTION]?.ToString();
            arg.Entity.DealerId = dealer.Id;
        }
    }
} 