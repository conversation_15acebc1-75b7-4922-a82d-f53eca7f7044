﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataObjects.Custom
{

    public class DeviceTwinReportedProperties
    {
        [JsonPropertyName("configuration")]
        public Configuration? Configuration { get; set; }

        [JsonPropertyName("system")]
        public System? System { get; set; }

        [JsonPropertyName("diagnostics")]
        public Diagnostics? Diagnostics { get; set; }

        [JsonPropertyName("status")]
        public Status? Status { get; set; }

        // Helper method to check if required sections are present
        public bool HasRequiredSections()
        {
            return Configuration != null && System != null && Diagnostics != null && Status != null;
        }
    }

    public class Configuration
    {
        [JsonPropertyName("config_error_code")]
        public int? ConfigErrorCode { get; set; }

        [JsonPropertyName("last_preop_check")]
        public long? LastPreopCheck { get; set; }

        [JsonPropertyName("shock_parameter")]
        public ShockParameter? ShockParameter { get; set; }

        [JsonPropertyName("time_server")]
        public string? TimeServer { get; set; }

        [JsonPropertyName("keepalive")]
        public int? Keepalive { get; set; }

        [JsonPropertyName("desired_config_version")]
        public int? DesiredConfigVersion { get; set; }

        [JsonPropertyName("timezone")]
        public int? Timezone { get; set; }

        [JsonPropertyName("maintenance")]
        public Maintenance? Maintenance { get; set; }

        [JsonPropertyName("idle_parameter")]
        public IdleParameter? IdleParameter { get; set; }

        [JsonPropertyName("checklist_timeout")]
        public int? ChecklistTimeout { get; set; }

        [JsonPropertyName("shutdown_timer")]
        public int? ShutdownTimer { get; set; }

        [JsonPropertyName("checklist_schedule")]
        public List<string>? ChecklistSchedule { get; set; }

        [JsonPropertyName("wifi_mac_address")]
        public string? WifiMacAddress { get; set; }

        [JsonPropertyName("mac_address")]
        public string? MacAddress { get; set; }

        [JsonPropertyName("unlock_reason")]
        public int? UnlockReason { get; set; }
    }

    public class ShockParameter
    {
        [JsonPropertyName("threshold")]
        public int? Threshold { get; set; }

        [JsonPropertyName("period")]
        public int? Period { get; set; }

        [JsonPropertyName("red_impact")]
        public int? RedImpact { get; set; }

        [JsonPropertyName("timer")]
        public int? Timer { get; set; }
    }

    public class Maintenance
    {
        [JsonPropertyName("session_id")]
        public string? SessionId { get; set; }

        [JsonPropertyName("timestamp")]
        public long? Timestamp { get; set; }

        [JsonPropertyName("driver")]
        public int? Driver { get; set; }

        [JsonPropertyName("code")]
        public int? Code { get; set; }
    }

    public class IdleParameter
    {
        [JsonPropertyName("polarity")]
        public bool? Polarity { get; set; }

        [JsonPropertyName("source")]
        public int? Source { get; set; }

        [JsonPropertyName("timeout")]
        public int? Timeout { get; set; }
    }

    public class System
    {
        [JsonPropertyName("rootfs_version")]
        public string? RootfsVersion { get; set; }

        [JsonPropertyName("app_version")]
        public string? AppVersion { get; set; }

        [JsonPropertyName("hardware_flag")]
        public int? HardwareFlag { get; set; }

        [JsonPropertyName("modem_iccid")]
        public string? ModemIccid { get; set; }

        [JsonPropertyName("gmtp_id")]
        public string? GmtpId { get; set; }

        [JsonPropertyName("kernel_version")]
        public string? KernelVersion { get; set; }

        [JsonPropertyName("expansion_module_version")]
        public string? ExpansionModuleVersion { get; set; }
    }

    public class Diagnostics
    {
        [JsonPropertyName("charge_fault")]
        public int? ChargeFault { get; set; }

        [JsonPropertyName("light_sensor_lux")]
        public int? LightSensorLux { get; set; }

        [JsonPropertyName("temperature")]
        public int? Temperature { get; set; }

        [JsonPropertyName("current")]
        public int? Current { get; set; }

        [JsonPropertyName("remaining_capacity")]
        public int? RemainingCapacity { get; set; }

        [JsonPropertyName("charge_state")]
        public int? ChargeState { get; set; }

        [JsonPropertyName("last_scanned_weigand_id")]
        public int? LastScannedWeigandId { get; set; }

        [JsonPropertyName("voltage")]
        public int? Voltage { get; set; }

        [JsonPropertyName("battery_present")]
        public bool? BatteryPresent { get; set; }
    }

    public class Status
    {
        [JsonPropertyName("server_connection")]
        public string? ServerConnection { get; set; }

        [JsonPropertyName("vor")]
        public string? Vor { get; set; }

        [JsonPropertyName("time_error")]
        public int? TimeError { get; set; }

        [JsonPropertyName("super_checksum")]
        public int? SuperChecksum { get; set; }

        [JsonPropertyName("wifi")]
        public string? Wifi { get; set; }

        [JsonPropertyName("question_checksum")]
        public int? QuestionChecksum { get; set; }

        [JsonPropertyName("last_time_server_update")]
        public long? LastTimeServerUpdate { get; set; }

        [JsonPropertyName("modem_apn")]
        public string? ModemApn { get; set; }

        [JsonPropertyName("on_demand")]
        public string? OnDemand { get; set; }

        [JsonPropertyName("session_uuid")]
        public string? SessionUuid { get; set; }

        [JsonPropertyName("driver_checksum")]
        public int? DriverChecksum { get; set; }

        //Commenting this for now since the format fetched from RegistryManager is inconsistent
        //[JsonPropertyName("relay_state")]
        //public RelayState? RelayState { get; set; }

        [JsonPropertyName("can_attributes")]
        public string? CanAttributes { get; set; }

        [JsonPropertyName("digital_io")]
        public string? DigitalIo { get; set; }

        [JsonPropertyName("modem_cgmr")]
        public string? ModemCgmr { get; set; }

        [JsonPropertyName("moni")]
        public string? Moni { get; set; }

        [JsonPropertyName("modem")]
        public string? Modem { get; set; }

        [JsonPropertyName("can_config_crc")]
        public long? CanConfigCrc { get; set; }

        [JsonPropertyName("csq")]
        public string? Csq { get; set; }

        [JsonPropertyName("master_checksum")]
        public long? MasterChecksum { get; set; }
    }

    public class RelayState
    {
        [JsonPropertyName("relay2")]
        public int? Relay2 { get; set; }

        [JsonPropertyName("relay1")]
        public int? Relay1 { get; set; }
    }

    // Extension methods for safe value access
    public static class DeviceTwinExtensions
    {
        public static T GetValueOrDefault<T>(this T? nullable, T defaultValue) where T : struct
        {
            return nullable ?? defaultValue;
        }

        public static string GetValueOrDefault(this string? nullable, string defaultValue = "")
        {
            return nullable ?? defaultValue;
        }
    }
}
