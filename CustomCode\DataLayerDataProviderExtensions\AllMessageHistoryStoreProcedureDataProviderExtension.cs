using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class AllMessageHistoryStoreProcedureDataProviderExtension : IDataProviderExtension<AllMessageHistoryStoreProcedureDataObject>
    {
        private readonly IAuthentication _authentication;
        private readonly IDataFacade _dataFacade;

        public AllMessageHistoryStoreProcedureDataProviderExtension(IAuthentication authentication, IDataFacade dataFacade)
        {
            _authentication = authentication;
            _dataFacade = dataFacade;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            // Only process if the result is a collection of AllMessageHistoryStoreProcedureDataObject
            if (arg.Result is DataObjectCollection<AllMessageHistoryStoreProcedureDataObject> items && arg.PageNumber > 0 && arg.PageSize > 0)
            {
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();

                if (userClaims == null || userClaims.UserId == null)
                {
                    return;
                }

                var appUserClaims = userClaims as AppUserClaims;

                var preferredLocale = appUserClaims.UserPreferredLocale != null ? appUserClaims.UserPreferredLocale : appUserClaims.CustomerPreferredLocale;

                foreach (var item in items)
                {
                    try
                    {
                        var culture = !string.IsNullOrEmpty(preferredLocale) ? new CultureInfo(preferredLocale) : new CultureInfo("en-US");

                        if (item.TimezoneAdjustedDeliveryTime.HasValue)
                        {
                            item.TimezoneAdjustedDeliveryTimeDisplay = item.TimezoneAdjustedDeliveryTime.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss", culture);
                        }
                        if (item.TimezoneAdjustedSentTime.HasValue)
                        {
                            item.TimezoneAdjustedSentTimeDisplay = item.TimezoneAdjustedSentTime.Value.ToString($"{culture.DateTimeFormat.ShortDatePattern} HH:mm:ss", culture);
                        }
                    }
                    catch (CultureNotFoundException)
                    {
                        // If the culture is invalid, just return without modifying the datetime
                        return;
                    }
                }
            }
        }
    }
} 