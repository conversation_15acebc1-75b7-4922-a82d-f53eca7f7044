import { describe, it, expect, beforeEach, vi } from 'vitest';
import { VehilceGridViewModelCustom } from '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehilceGridViewModel.custom.js';

describe('VehicleGrid Admin Actions Visibility', () => {
    let viewModel;
    let mockApplicationController;

    beforeEach(() => {
        // Mock the ApplicationController
        mockApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn()
                }
            }
        };

        // Set the global ApplicationController
        global.ApplicationController = mockApplicationController;

        // Create a mock view model
        viewModel = {
            VehicleFilterViewModel: {
                filterData: {
                    fields: {
                        CustomerValue: vi.fn(),
                        SiteValue: vi.fn(),
                        DepartmentValue: vi.fn()
                    }
                }
            },
            commands: {}
        };

        // Mock window.location
        global.window = {
            location: {
                hash: '',
                reload: vi.fn()
            }
        };

        // Mock sessionStorage
        global.sessionStorage = {
            getItem: vi.fn(),
            setItem: vi.fn(),
            removeItem: vi.fn()
        };
    });

    it('should show admin actions column when user is Administrator', () => {
        // Arrange
        mockApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: 'Administrator'
        });

        // Act
        const customViewModel = new VehilceGridViewModelCustom(viewModel);
        customViewModel.initialize();

        // Assert
        expect(viewModel.IsAdminActionsColumnVisible()).toBe(true);
    });

    it('should hide admin actions column when user is DealerAdmin', () => {
        // Arrange
        mockApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: 'DealerAdmin'
        });

        // Act
        const customViewModel = new VehilceGridViewModelCustom(viewModel);
        customViewModel.initialize();

        // Assert
        expect(viewModel.IsAdminActionsColumnVisible()).toBe(false);
    });

    it('should hide admin actions column when user is Customer', () => {
        // Arrange
        mockApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
            role: 'Customer'
        });

        // Act
        const customViewModel = new VehilceGridViewModelCustom(viewModel);
        customViewModel.initialize();

        // Assert
        expect(viewModel.IsAdminActionsColumnVisible()).toBe(false);
    });

});
