# FXQ-2925: Optimize Device ID Loading Performance in Vehicle Creation Form

## 📄 Task Description

**Optimize the performance of device ID loading during vehicle creation to reduce loading times and improve user experience.**

The current `GetAvailableModulesAsync` method in `ModuleUtilities` performs inefficient database queries that cause slow loading times when populating device ID dropdowns in vehicle creation forms. Based on codebase analysis, the performance bottleneck stems from:

- **Inefficient Query Pattern**: The method executes two separate database queries - first loading all vehicles with modules, then filtering modules based on used IDs
- **Missing Database Indexes**: Lack of composite indexes on Vehicle.ModuleId1 and Vehicle.CustomerId columns
- **N+1 Query Potential**: Related entity loading may trigger additional queries
- **Frontend Blocking**: Synchronous loading of large device lists without pagination or search optimization

**Key Systems Affected:**
- `CustomCode/BusinessLayerServerComponents/ModuleUtilities.cs` - Core service requiring optimization
- `CustomCode/WebApplicationLayer/wwwroot/ViewModels/Vehicle/` - Frontend vehicle creation forms
- Database indexes on Vehicle and Module tables
- NHibernate ORM query generation and execution

**Architectural Considerations:**
- Maintain backward compatibility with existing API contracts
- Implement caching strategies for frequently accessed module data
- Add progressive loading and search capabilities for large device lists
- Ensure proper cache invalidation when module status changes

## Acceptance Criteria

### Performance Requirements
- **Given** a user opens the vehicle creation form
- **When** the device ID dropdown is populated
- **Then** the loading time should be reduced by at least 70% from current baseline
- **And** the user-perceived loading time should be under 2 seconds for typical scenarios

### Database Optimization
- **Given** the current database schema with Vehicle and Module tables
- **When** the `GetAvailableModulesAsync` method is called
- **Then** the query execution time should be reduced by at least 60%
- **And** the number of database round trips should be minimized to 1-2 queries maximum
- **And** appropriate composite indexes should be added for Vehicle.ModuleId1 and Vehicle.CustomerId

### Caching Implementation
- **Given** frequently accessed module data
- **When** the same dealer's available modules are requested multiple times
- **Then** subsequent requests should be served from cache
- **And** cache invalidation should occur when module status changes
- **And** cache warming strategies should be implemented for common scenarios

### Frontend User Experience
- **Given** a large number of available device IDs
- **When** the user interacts with the device ID dropdown
- **Then** progressive loading with search-as-you-type functionality should be implemented
- **And** loading indicators should provide clear feedback during data retrieval
- **And** keyboard navigation should be supported for device selection

### API Response Optimization
- **Given** the current API response structure
- **When** device data is returned to the frontend
- **Then** response size should be optimized through selective field loading
- **And** pagination should be implemented for large result sets
- **And** response compression should be enabled for network efficiency

### Error Handling and Resilience
- **Given** potential database connectivity issues
- **When** device ID loading fails
- **Then** graceful degradation should provide fallback options
- **And** comprehensive error logging should capture performance metrics
- **And** retry mechanisms should handle transient failures

### Monitoring and Observability
- **Given** the performance optimization implementation
- **When** device ID loading operations occur
- **Then** detailed performance metrics should be collected and logged
- **And** alerting should be configured for slow loading operations (>2 seconds)
- **And** performance dashboards should display real-time metrics

### Backward Compatibility
- **Given** existing integrations with the ModuleUtilities service
- **When** the optimized version is deployed
- **Then** all existing API contracts should remain unchanged
- **And** existing frontend code should continue to function without modifications
- **And** graceful degradation should be provided for older client versions

### Testing Requirements
- **Given** the performance optimization changes
- **When** automated tests are executed
- **Then** all existing functionality tests should pass
- **And** new performance tests should validate the 70% improvement target
- **And** load testing should confirm performance under concurrent user scenarios
- **And** regression testing should ensure no data integrity issues

### Documentation
- **Given** the performance optimization implementation
- **When** the changes are deployed
- **Then** technical documentation should be updated with optimization details
- **And** troubleshooting guides should be created for performance issues
- **And** monitoring runbooks should be provided for ongoing maintenance
