// 002 - customer.cy.js

describe("015 - Customer User Access", () => {
    beforeEach(() => {
        // Use the centralized login function from the support file
        cy.loginCustomer();
        
        // Prevent uncaught exception from failing tests
        Cypress.on('uncaught:exception', (err, runnable) => {
            // returning false here prevents <PERSON><PERSON> from failing the test
            return false;
        });
    });

    // it("tests 015.a - Customer User Access checklist", () => {
    //     // Step 1: Open the vehicle menu (referencing vehicles.cy.js)
    //     cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

    //     cy.wait(3000);
    //     // select vehicle
    //     cy.get('[data-id="39"] > [data-bind="jqStopBubble: \'a\'"] > a').click();

    //     // // Step 3: Click on the checklist tab (referencing checklist.cy.js)
    //     cy.get("[data-test-id='tab_link_541c0c57-20a2-4ab6-8927-7e4965786aaa'] > a > span:nth-of-type(1)")
    //         .should('exist')
    //         .should('be.visible')
    //         .click();

    //     // Step 5: Click on the checklist
    //     cy.get('[data-id="12"] > .multiline')
    //         .should('exist')
    //         .should('be.visible')
    //         .click();

    //     // step 6: click on the edit button
    //     cy.get(':nth-child(2) > .command-button')
    //         .should('exist')
    //         .should('be.visible')
    //         .click();

    //     // Step 7: Check that the edit form is displayed
    //     cy.get('[data-test-id="label_0ce87aed-cd3b-4bf2-8c68-a41df412f007"]')
    //         .should('exist')
    //         .should('be.visible');
    // });

    it("tests 015.b - Customer User Access create new user, export user, create new vehicle", () => {
        // Step 1: Open the user menu
        cy.get("[data-test-id='2cdc2ef2-af43-4274-97ea-54e2987e29af']").click();

        // Step 2: check visibility of create new user button
        cy.get('.topGridCommands > :nth-child(1) > .command-button')
            .should('exist')
            .should('be.visible');

        // Step 3: check visibility of export user button
        cy.get(':nth-child(4) > .command-button')
            .should('exist')
            .should('be.visible');

        // Step 4: Open the vehicle menu (referencing vehicles.cy.js)
        cy.get("[data-test-id='\\33 fa2d3b4-384e-4532-aec9-4c8bcfb8ff5c']").click();

        // Step 5: check visibility of create new vehicle button
        cy.get('.topGridCommands > :nth-child(1) > .command-button')
            .should('exist')
            .should('be.visible');

        // Step 6: Check visibility of export vehicle button
        cy.get(':nth-child(8) > .command-button')
            .should('exist')
            .should('be.visible');

    });
});
