﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// PstatAPI Component
	///  
	/// </summary>
    public partial class PstatAPI : BaseServerComponent, IPstatAPI 
    {
		public PstatAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        /// <summary>
        /// storePSTATMessage Method
        /// </summary>
        /// <param name="Message"></param>
        /// <returns></returns>
        public System.Threading.Tasks.Task<ComponentResponse<System.String>> storePSTATMessageAsync(System.String Message, Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return System.Threading.Tasks.Task.FromResult(new ComponentResponse<string>(default(System.String))); 
		}
	}
}
