//import './005 - users.cy';

describe("009 - Subscription Tab", () => {
    beforeEach(() => {
        // Use the global login function defined in Cypress support file
        cy.login(); // This assumes `cy.login()` is globally set up in Cypress' support file.
    });

    it("tests 006 - Vehicle Tab", () => {
        // Clicking on the relevant navigation after login
        cy.get("[data-test-id='\\32 cdc2ef2-af43-4274-97ea-54e2987e29af']").click();

        // Intercept and wait for the initial API call to load the person list
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/list*')
            .as('getPersonList');

        // Wait for the initial person list load
        // Wait for the initial person list load
        cy.wait('@getPersonList', { timeout: 30000 });

        // Function to search for "Alex_Active_Test"
        function searchForAlexActiveTest() {
            // Initial search for "Alex_Active_Test"
            cy.get('input.filterTextInputCustom.form-control')
                .should('exist')         // Ensure the input exists
                .should('be.visible')     // Ensure the input is visible
                .clear()                  // Clear any existing text
                .type('Alex_Active_Test'); // Type the desired search text

            cy.get("[data-test-id='searchCommand']").click();

            // Intercept the API response for the person list again after the search
            cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/list*')
                .as('getPersonListAfterSearch');

            // Wait for the search result and start table search
            cy.wait('@getPersonListAfterSearch').then(() => {
                performTableSearch();
            });
        }

        // Function to search the table for a valid row
        function performTableSearch() {
            let validRecordFound = false; // Flag to check if a valid record is found


            cy.wait('@getPersonList', { timeout: 30000 });

            cy.get('table tbody tr').each(($row) => {
                const userName = $row.find('td:nth-of-type(2)'); // "Card/Pin" column (6th column)

                // Check if both the "Card/Pin" and "Weigand" columns are empty
                if (userName.text().trim()) {
                    // If both columns are empty, click the first anchor found in the row
                    cy.wrap($row)
                        .find('a[href^="#!/UserManagement/UserDetail/"]') // First find the anchor with UserDetail in href
                        .should('exist') // Ensure it exists
                        .then(($anchor) => {
                            cy.wrap($anchor).click(); // Click the anchor link
                        });

                    validRecordFound = true; // Mark that we found a valid row
                    return false; // Exit the loop
                }
            }).then(() => {
                // If no valid record was found in the table, try the next page
                if (!validRecordFound) {
                    goToNextPageIfExists(); // Check for next page
                }
            });
        }

        // Function to handle pagination and go to the next page if available
        function goToNextPageIfExists() {
            cy.get('li.page-numbers.page-controls:not(.invisible)')  // Select all li elements without the 'invisible' class
                .last()                                               // Select the last visible pagination element
                .then($pagination => {
                    if ($pagination.length > 0) {                     // Check if the pagination element exists
                        cy.wrap($pagination)
                            .click()                                  // Click the pagination button
                            .then(() => {
                                // Call the performTableSearch function again after the click
                                performTableSearch();
                            });
                    } else {
                        // No more pages, exit the flow
                        cy.log("No valid records found, and no more pages.");
                    }
                });
        }

        // Start by searching for "Alex_Active_Test" and validate
        searchForAlexActiveTest();

        //Intercept and wait for the API call for person details
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/person/byid/*')
            .as('getPersonDetails');

        cy.wait('@getPersonDetails', { timeout: 30000 });


        cy.get("[data-test-id='tab_link_5ba6db73-6add-4663-a9f3-5b2379d0ac4c'] > a > span:nth-of-type(1)").click();
        cy.get("[data-test-id='\\32 bc34357-70be-469d-8e00-91a7796af6c0']").click();

        // Intercept the API request for the alert list
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/alert/list*').as('getAlertList');

        // Wait for the intercepted request
        cy.wait('@getAlertList', { timeout: 30000 });

        cy.get("#popupContainer [data-test-id='lookup_input']").click();
        cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']").click();
        cy.get("[data-test-id='edit_a1bb8451-39be-41be-9f30-30922ed2a6c7']").click();
        cy.get("[data-test-id='\\31 defa668-bb8b-46b2-881e-c154ee8e539f']").click();

        // Intercept and wait for the vehicle list API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/vehicle/list?dateformat=ISO8601&include=Module&pageSize=15&pageNumber=1*')
            .as('getVehicleList');
        cy.wait('@getVehicleList', { timeout: 30000 });

        // Select the second checkbox in the table
        //cy.get('table tbody tr').eq(1) // Get the second row (index 1)
        //    .find('input[type="checkbox"]') // Find the checkbox input
        //    .check({ force: true }); // Check the checkbox

        //cy.get("#checkbox-667").click();
        //cy.get("#checkbox-733").click();
        // Click the second instance of the form-check-input checkbox
        // Click the second instance of the span wrapping the checkbox

        // Click the second span wrapping the checkbox
        cy.get('td > span.checkbox-field.form-switch')
            .eq(1) // Get the second span (index starts from 0)
            .click({ force: true }) // Click the span first
            .then(() => {
                // After clicking the span, click the corresponding checkbox
                cy.get('td > span.checkbox-field.form-switch input.form-check-input')
                    .eq(1) // Get the second checkbox
                    .click({ force: true }); // Click the checkbox
            });


        cy.get("[data-test-id='\\31 4e07a1d-f2ea-4189-9235-7bd84d8752c9']").click();
        cy.get("[data-test-id='\\31 ed94475-9cdd-4309-b583-50762354c9ab']").click();
        //cy.wait('@getVehicleList', { timeout: 30000 });
        cy.get("td.multiline").eq(1).click();
        cy.get("[data-test-id='\\35 8c2ad40-1464-4411-80fb-aecbdc44b8d3']").click();
        cy.get("[data-test-id='view_6d1712d7-45c8-4ad5-99af-715a12be2f6b']").click();
        cy.get("[data-test-id='view_6d1712d7-45c8-4ad5-99af-715a12be2f6b']").click();
        cy.get("[data-test-id='\\37 959d69c-447d-495b-afce-34a7fd920680']").click();

        // Wait for the intercepted request
        cy.wait('@getAlertList', { timeout: 30000 });

        cy.get("#popupContainer [data-test-id='lookup_input']").click();
        cy.get("li:nth-of-type(3) > [data-test-id='lookup_item']").click();
        cy.get("[data-test-id='\\31 ed94475-9cdd-4309-b583-50762354c9ab']").click();
        cy.get("[data-test-id='\\39 3c69fd3-db0a-4477-a1b6-9f5e3958ac69']").click();
        cy.get("[data-test-id='fb03ea79-aadf-4f8f-99bc-1413649624f1']").click();
        cy.get("html > body > #form > [data-test-id='main-page-content'] > div.main-wrapper > div.content-wrapper > div > div > div > div > [data-test-id='contentZone_MainZone'] > [data-test-id='\\32 cdbeb9c-1683-41c4-8f60-7469a720fe08'] > div > div:nth-of-type(1) > [data-test-id='\\32 cdbeb9c-1683-41c4-8f60-7469a720fe08'] > #PersonFormControl-PersonFormData > #PersonFormControl-PersonForm-tabs > div.tabs-container > [data-test-id='\\35 ba6db73-6add-4663-a9f3-5b2379d0ac4c'] > div > div > [data-test-id='\\34 e4aa56b-3c2d-4923-8f09-ddda6885de08'] [data-test-id='tab_link_9ec44e4c-c869-4d10-92d1-0a5caab2f9e1'] > a > span:nth-of-type(1)").eq(0).click();
        cy.wait(1000);
        cy.get("[data-test-id='\\30 1a352c6-76dd-44a7-b1f1-0360ff39d669']").click();

        // Intercept and wait for the customer list API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/customer/list*')
            .as('getCustomerList');

        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/reporttype/list*')
            .as('getReportTypeList');

        cy.wait('@getCustomerList', { timeout: 30000 });

        cy.get("[data-test-id='\\30 e00826c-f275-45a0-9d50-3167bc2e852c'] [data-test-id='lookup_input']").click();
        cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']").click();

        // Intercept and wait for the customer list API call
        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/site/list*')
            .as('getSiteList');
        cy.wait('@getSiteList', { timeout: 30000 });

        cy.get("[data-test-id='fb648521-f208-4b72-8800-93666f6898db'] [data-test-id='lookup_input']").click();
        cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']").eq(1).click();

        cy.intercept('GET', '/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f/dataset/api/department/list*')
            .as('getDepartmentList');
        cy.wait('@getDepartmentList', { timeout: 30000 });

        cy.get("[data-test-id='\\31 2bffe40-7193-492e-9a8f-c1b6c1e2b388'] [data-test-id='lookup_input']").click();
        cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']").eq(2).click();
        cy.wait('@getReportTypeList', { timeout: 30000 });

        cy.get("[data-test-id='\\34 ef21d7c-f167-485a-a50d-d6acf62a2112'] [data-test-id='lookup_input']").click();
        cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']").eq(3).click();

        cy.get("[data-test-id='edit_f42702b2-9031-47cf-bb06-0bcdaffb4321']").click();
        cy.get("div.active").click();
        cy.get("[data-test-id='edit_5bcc4c03-f560-482e-ade0-5e62d29cf766']").click();
        cy.get("body > div.show div.active").click();
        cy.get("[data-test-id='edit_777f4ee5-b071-428c-aff5-0e6882ef44bc']").click();
        cy.get("body > div.show div:nth-of-type(34)").click();
        cy.get("[data-test-id='edit_80dd00c8-53d5-47bf-be5c-589e59c35c6c']").click();
        cy.get("[data-test-id='edit_80dd00c8-53d5-47bf-be5c-589e59c35c6c']").type("Test SUbject");
        cy.get("[data-test-id='c5565faa-b880-4dba-bdfa-9ed397147e0c']").click();
    });
});
