// 017 - vehicle-calibration-report.cy.js

describe("007.a - Vehicle Calibration Report", () => {
    beforeEach(() => {
        // Clear any existing session
        cy.clearCookies();
        cy.clearLocalStorage();

        // Prevent uncaught exception from failing tests
        Cypress.on('uncaught:exception', (err, runnable) => {
            // returning false here prevents Cypress from failing the test
            return false;
        });
    });

    it("tests Vehicle Calibration Report Grid Visibility as a customer and admin", () => {
        // Step 1: login as customer
        cy.loginCustomer();

        // Step 2: navigate to Vehicle Calibration Report page
        cy.get('[data-bs-target="#AdminSettings-collapse"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get('#ee81058e-2ea4-4e71-a2d4-df5aa915a2a5 > .btn')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get(`[data-bind="'enable' : navigation.isVehicleCalibrationReport3Enabled(), 'visible' : navigation.isVehicleCalibrationReport3Visible()"] > .link-dark`)
            .should('exist')
            .should('be.visible')
            .click();

        // Step 3: check that the Vehicle Calibration Value is not visible
        cy.get('[class="filter command-button"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get('[data-bind="safeHtml: \'Current Calibration Value\', click: function(data, event){ onHeaderClicked(\'Vehicle.Module.FSSSBase\') }, css: { \'ui-grid-sort-s\':((sortOrder() == \'desc\') && (sortColumnName() == \'Vehicle.Module.FSSSBase\')), \'ui-grid-sort-n\':((sortOrder() == \'asc\') && (sortColumnName() == \'Vehicle.Module.FSSSBase\')), \'ui-grid-sort-n-s\':(sortColumnName() != \'Vehicle.Module.FSSSBase\') }"]')
            .should('not.be.visible');

        // Step 4: logout
        cy.logout();

    });

    it("tests Vehicle Calibration Report Grid Visibility as an admin", () => {
        // Step 1: login as admin
        cy.login();

        // Step 2: navigate to Vehicle Calibration Report page
        cy.get('[data-bs-target="#Reports-collapse"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get('#e6fe1ea1-0332-4428-8f7b-c39232d25a11 > .btn')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get('[data-test-id="02303038-e64a-46eb-b769-defa08aa8f41"]')
            .should('exist')
            .should('be.visible')
            .click();

        // Step 3: check that the Vehicle Calibration Value is not visible
        cy.get('[class="filter command-button"]')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get('[data-bind="safeHtml: \'Current Calibration Value\', click: function(data, event){ onHeaderClicked(\'Vehicle.Module.FSSSBase\') }, css: { \'ui-grid-sort-s\':((sortOrder() == \'desc\') && (sortColumnName() == \'Vehicle.Module.FSSSBase\')), \'ui-grid-sort-n\':((sortOrder() == \'asc\') && (sortColumnName() == \'Vehicle.Module.FSSSBase\')), \'ui-grid-sort-n-s\':(sortColumnName() != \'Vehicle.Module.FSSSBase\') }"]')
            .should('exist')
            .should('be.visible')

        // Step 4: logout
        cy.logout();
    });
});
