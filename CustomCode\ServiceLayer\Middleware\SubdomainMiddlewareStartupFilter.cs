using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using System;

namespace FleetXQ.ServiceLayer.Middleware
{
    /// <summary>
    /// Startup filter that automatically registers the SubdomainMiddleware in the application pipeline
    /// </summary>
    public class SubdomainMiddlewareStartupFilter : IStartupFilter
    {
        /// <summary>
        /// Configures the application builder to include subdomain middleware
        /// </summary>
        /// <param name="next">The next configuration action in the pipeline</param>
        /// <returns>An action that configures the application builder</returns>
        public Action<IApplicationBuilder> Configure(Action<IApplicationBuilder> next)
        {
            return builder =>
            {
                builder.UseMiddleware<SubdomainMiddleware>();
                next(builder);
            };
        }
    }
}
