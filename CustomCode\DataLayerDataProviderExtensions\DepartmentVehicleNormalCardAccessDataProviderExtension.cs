using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.ORMSupportClasses;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    /// <summary>
    /// DataProviderExtension for DepartmentVehicleNormalCardAccess with race condition protection
    /// </summary>
    public class DepartmentVehicleNormalCardAccessDataProviderExtension : IDataProviderExtension<DepartmentVehicleNormalCardAccessDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<DepartmentVehicleNormalCardAccessDataProviderExtension> _logger;

        public DepartmentVehicleNormalCardAccessDataProviderExtension(
            IServiceProvider serviceProvider,
            IDataFacade dataFacade)
        {
            _serviceProvider = serviceProvider;
            _dataFacade = dataFacade;
            _logger = serviceProvider.GetService<ILogger<DepartmentVehicleNormalCardAccessDataProviderExtension>>();
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeDelete += OnBeforeDeleteAsync;
        }

        private async Task OnBeforeDeleteAsync(OnBeforeDeleteEventArgs e)
        {
            var instance = e.Entity as DepartmentVehicleNormalCardAccessDataObject;
            if (instance == null) return;

            try
            {
                // Add logging for troubleshooting
                _logger?.LogDebug("Processing OnBeforeDelete for DepartmentVehicleNormalCardAccess Id: {Id}, DepartmentId: {DepartmentId}, CardId: {CardId}",
                    instance.Id, instance.DepartmentId, instance.CardId);

                // Check if the record still exists before attempting deletion
                // This helps prevent race conditions where another process already deleted it
                var existingRecord = await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.GetAsync(instance, skipSecurity: true);

                if (existingRecord == null)
                {
                    _logger?.LogWarning("DepartmentVehicleNormalCardAccess record Id: {Id} already deleted by another process. Skipping deletion.", instance.Id);

                    // Mark as handled to prevent the framework from attempting the delete
                    e.IsHandled = true;
                    return;
                }

                _logger?.LogDebug("DepartmentVehicleNormalCardAccess record Id: {Id} exists and will be deleted normally.", instance.Id);
            }
            catch (Exception ex)
            {
                // Handle specific race condition errors gracefully
                if (IsRaceConditionError(ex))
                {
                    _logger?.LogWarning("Race condition detected during OnBeforeDelete for DepartmentVehicleNormalCardAccess Id: {Id}. Record likely already deleted by concurrent process: {Message}",
                        instance.Id, ex.Message);

                    // Mark as handled - treat as successful since the record is gone
                    e.IsHandled = true;
                    return;
                }

                _logger?.LogError(ex, "Error in OnBeforeDelete for DepartmentVehicleNormalCardAccess Id: {Id}: {Message}",
                    instance.Id, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Determines if an exception is likely due to a race condition
        /// </summary>
        private bool IsRaceConditionError(Exception ex)
        {
            var message = ex.Message?.ToLowerInvariant() ?? "";

            return message.Contains("batch update returned unexpected row count") ||
                   message.Contains("actual row count: 0; expected: 1") ||
                   message.Contains("could not execute batch command") ||
                   message.Contains("sql not available") ||
                   message.Contains("row was updated or deleted by another transaction") ||
                   message.Contains("optimistic locking") ||
                   message.Contains("concurrency") ||
                   (ex.InnerException?.Message?.ToLowerInvariant().Contains("row count") == true);
        }
    }
}