# VehicleAccessCreation Component Documentation

## Overview

The `VehicleAccessCreation` component is a business layer server component responsible for creating and managing vehicle access records in the FleetXQ system. This component processes vehicle access requests, validates permissions, and establishes the necessary access relationships between drivers, vehicles, and access cards.

## Key Responsibilities

- **Vehicle Access Provisioning**: Creates access records that allow drivers to operate specific vehicles
- **Permission Management**: Validates and applies driver permissions (Level 1 and Level 3)
- **Access Card Integration**: Links access cards to vehicles and models
- **IoT Device Synchronization**: Syncs driver access data to vehicle IoT devices
- **Performance Monitoring**: Provides detailed performance logging and metrics

## Architecture

```
VehicleAccessCreation
├── Message Processing (JSON deserialization)
├── Vehicle Data Loading (Vehicle, Site, Model)
├── Permission Resolution (Driver permissions)
├── Access Record Creation (Model & Vehicle access)
└── IoT Device Synchronization
```

## Main Method

### `CreateVehicleAccessAsync`

Creates vehicle access records based on the provided message.

#### Signature
```csharp
public async Task<ComponentResponse<string>> CreateVehicleAccessAsync(
    string Message, 
    Dictionary<string, object> parameters = null)
```

#### Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `Message` | `string` | JSON-serialized `VehicleAccessCreationMessage` containing vehicle information |
| `parameters` | `Dictionary<string, object>` | Optional additional parameters (currently unused) |

#### VehicleAccessCreationMessage Structure
```json
{
    "VehicleId": "guid",
    "IoTDevice": "string (optional)"
}
```

#### Return Value
- **Type**: `ComponentResponse<string>`
- **Success**: Returns success message with count of created access records
- **Failure**: Returns error message describing the failure

#### Process Flow

1. **Message Parsing**
   - Deserializes JSON message to extract vehicle information
   - Validates message format and required fields

2. **Entity Loading**
   - Loads vehicle data from database
   - Loads associated site and model information
   - Validates all required entities exist

3. **Permission Resolution**
   - Retrieves driver permissions (Level 1 and Level 3)
   - Validates permissions exist in the system

4. **Site Access Retrieval**
   - Gets site-specific access configurations
   - Loads associated access cards for each configuration

5. **Existing Access Check**
   - Queries existing model access records
   - Queries existing vehicle access records
   - Creates lookup keys to prevent duplicates

6. **Access Record Creation**
   - Creates new `ModelVehicleNormalCardAccess` records
   - Creates new `PerVehicleNormalCardAccess` records
   - Skips records that already exist

7. **IoT Device Synchronization** (Optional)
   - Syncs driver access data to vehicle's IoT device
   - Logs errors but doesn't fail the operation

## Supporting Methods

### `GetDriverPermissionsAsync()`

Retrieves driver permissions for Level 1 and Level 3 access.

- **Returns**: `List<PermissionDataObject>`
- **Performance**: Executes two separate queries for different permission levels
- **Error Handling**: Logs and re-throws exceptions

### `GetSiteAccessesAsync(siteId, permissions)`

Gets site-specific vehicle access configurations.

- **Parameters**:
  - `siteId`: Site identifier
  - `permissions`: List of driver permissions
- **Returns**: `List<SiteVehicleNormalCardAccessDataObject>`
- **Behavior**: Queries access configurations for each permission level

### `CreateAccessRecordsAsync(...)`

Creates the actual access records in the database.

- **Creates**: Both model-level and vehicle-level access records
- **Duplicate Prevention**: Uses existing access keys to avoid duplicates
- **Performance Logging**: Logs slow database operations (>1000ms)

### `GetExistingModelAccessesSimplifiedAsync(...)`

Retrieves existing model access records to prevent duplicates.

- **Returns**: `HashSet<string>` of access keys
- **Key Format**: `{ModelId}_{PermissionId}_{CardId}_{DepartmentId}`

### `GetExistingVehicleAccessesSimplifiedAsync(...)`

Retrieves existing vehicle access records to prevent duplicates.

- **Returns**: `HashSet<string>` of access keys
- **Key Format**: `{VehicleId}_{PermissionId}_{CardId}`

## Dependencies

### Required Services
- `IServiceProvider`: Dependency injection container
- `IConfiguration`: Application configuration
- `IDataFacade`: Data access layer
- `ILogger<VehicleAccessCreation>`: Logging service
- `IDeviceTwinHandler`: IoT device synchronization (optional)

### Data Objects
- `VehicleDataObject`
- `SiteDataObject`
- `ModelDataObject`
- `PermissionDataObject`
- `SiteVehicleNormalCardAccessDataObject`
- `ModelVehicleNormalCardAccessDataObject`
- `PerVehicleNormalCardAccessDataObject`

## Performance Characteristics

### Optimization Features
- **Sequential Entity Loading**: Prevents session conflicts
- **Simplified Queries**: Uses targeted queries instead of complex joins
- **Batch Processing**: Processes multiple permissions efficiently
- **Duplicate Prevention**: Avoids unnecessary database operations

### Performance Logging
- **Operation Timing**: Logs elapsed time for all major operations
- **Slow Query Detection**: Warns about operations taking >1000ms
- **Detailed Metrics**: Provides breakdown of time spent in each phase

### Typical Performance Metrics
- Message parsing: <10ms
- Vehicle/Site/Model loading: 50-200ms
- Permission queries: 100-500ms
- Access record creation: 200-1000ms per batch
- IoT synchronization: 100-300ms

## Error Handling

### Input Validation
- Validates JSON message format
- Checks for required vehicle, site, and model entities
- Verifies driver permissions exist

### Database Errors
- Logs detailed error information with performance metrics
- Returns user-friendly error messages
- Continues processing where possible (e.g., IoT sync failures)

### Common Error Scenarios
- **"Invalid message format"**: JSON deserialization failed
- **"Vehicle not found"**: Vehicle ID doesn't exist in database
- **"Site or model not found"**: Referenced entities missing
- **"No driver permissions found"**: No Level 1 or 3 permissions configured
- **"No site accesses found"**: No access configurations for the site

## Usage Example

```csharp
// Inject the component
var vehicleAccessCreation = serviceProvider.GetRequiredService<IVehicleAccessCreation>();

// Prepare the message
var message = JsonSerializer.Serialize(new {
    VehicleId = Guid.Parse("12345678-1234-1234-1234-123456789012"),
    IoTDevice = "device-12345"
});

// Create vehicle access
var result = await vehicleAccessCreation.CreateVehicleAccessAsync(message);

if (result.IsSuccess)
{
    Console.WriteLine($"Success: {result.Data}");
}
else
{
    Console.WriteLine($"Error: {result.Data}");
}
```

## Configuration Requirements

### Database Tables
- `Vehicle`: Vehicle master data
- `Site`: Site master data
- `Model`: Vehicle model data
- `Permission`: Driver permission levels
- `SiteVehicleNormalCardAccess`: Site-level access configurations
- `ModelVehicleNormalCardAccess`: Model-level access records
- `PerVehicleNormalCardAccess`: Vehicle-specific access records

### Required Permissions
- Level 1: Basic driver permissions
- Level 3: Advanced driver permissions

## Monitoring and Troubleshooting

### Key Metrics to Monitor
- **Processing Time**: Total time per vehicle access creation
- **Success Rate**: Percentage of successful operations
- **Database Performance**: Query execution times
- **IoT Sync Rate**: Success rate of device synchronization

### Troubleshooting Guide
1. **Slow Performance**: Check database query performance and indexing
2. **High Failure Rate**: Verify data integrity and permission configurations
3. **IoT Sync Issues**: Check device connectivity and handler configuration
4. **Memory Issues**: Monitor entity loading and disposal patterns

## Security Considerations

- All database operations use `skipSecurity: true` for system-level access
- Component should only be called by authorized system processes
- Input validation prevents malformed message attacks
- No sensitive data is logged in performance metrics

## Version History

- **Current**: Performance-optimized version with detailed logging
- **Features**: Sequential loading, simplified queries, comprehensive error handling 