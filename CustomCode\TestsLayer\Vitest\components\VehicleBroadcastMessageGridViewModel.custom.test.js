import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehicleBroadcastMessageGridViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/VehicleBroadcastMessage/VehicleBroadcastMessageGridViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with minimal required properties
        viewModel = {};

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.VehicleBroadcastMessageGridViewModelCustom(viewModel);
    });

    it('should initialize observable properties', () => {
        // Verify properties exist
        expect(viewModel.customerId).toBeDefined();
        expect(viewModel.siteId).toBeDefined();
        expect(viewModel.departmentId).toBeDefined();

        // Verify they are observables
        expect(typeof viewModel.customerId.subscribe).toBe('function');
        expect(typeof viewModel.siteId.subscribe).toBe('function');
        expect(typeof viewModel.departmentId.subscribe).toBe('function');

        // Verify initial values
        expect(viewModel.customerId()).toBeNull();
        expect(viewModel.siteId()).toBeNull();
        expect(viewModel.departmentId()).toBeNull();
    });

    it('should allow setting and getting customerId', () => {
        const testId = 'test-customer-id';
        viewModel.customerId(testId);
        expect(viewModel.customerId()).toBe(testId);
    });

    it('should allow setting and getting siteId', () => {
        const testId = 'test-site-id';
        viewModel.siteId(testId);
        expect(viewModel.siteId()).toBe(testId);
    });

    it('should allow setting and getting departmentId', () => {
        const testId = 'test-department-id';
        viewModel.departmentId(testId);
        expect(viewModel.departmentId()).toBe(testId);
    });

    it('should handle null values for all properties', () => {
        // Set some initial values
        viewModel.customerId('customer1');
        viewModel.siteId('site1');
        viewModel.departmentId('department1');

        // Set to null
        viewModel.customerId(null);
        viewModel.siteId(null);
        viewModel.departmentId(null);

        // Verify values are null
        expect(viewModel.customerId()).toBeNull();
        expect(viewModel.siteId()).toBeNull();
        expect(viewModel.departmentId()).toBeNull();
    });

    it('should handle undefined values for all properties', () => {
        // Set some initial values
        viewModel.customerId('customer1');
        viewModel.siteId('site1');
        viewModel.departmentId('department1');

        // Set to undefined
        viewModel.customerId(undefined);
        viewModel.siteId(undefined);
        viewModel.departmentId(undefined);

        // Verify values are undefined
        expect(viewModel.customerId()).toBeUndefined();
        expect(viewModel.siteId()).toBeUndefined();
        expect(viewModel.departmentId()).toBeUndefined();
    });

    it('should maintain independent values for each property', () => {
        // Set different values for each property
        viewModel.customerId('customer1');
        viewModel.siteId('site1');
        viewModel.departmentId('department1');

        // Change one property
        viewModel.customerId('customer2');

        // Verify other properties remain unchanged
        expect(viewModel.customerId()).toBe('customer2');
        expect(viewModel.siteId()).toBe('site1');
        expect(viewModel.departmentId()).toBe('department1');
    });

    it('should handle empty string values', () => {
        // Set empty strings
        viewModel.customerId('');
        viewModel.siteId('');
        viewModel.departmentId('');

        // Verify empty strings are preserved (not converted to null)
        expect(viewModel.customerId()).toBe('');
        expect(viewModel.siteId()).toBe('');
        expect(viewModel.departmentId()).toBe('');
    });
}); 