﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <OutputType>Library</OutputType>
    <GeneratePackageOnBuild>True</GeneratePackageOnBuild>
  </PropertyGroup>
  <PropertyGroup>
    <Authors>Generative Objects</Authors>
    <Description>FleetXQ Custom SDK</Description>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="GenerativeObjects.Practices.LayerSupportClasses" />
    <PackageReference Include="GenerativeObjects.Services" />
    <PackageReference Include="Microsoft.CSharp" />
    <PackageReference Include="Newtonsoft.Json" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\GeneratedCode\DataLayer\FleetXQ.Data.DataObjects.csproj" />
  </ItemGroup>
</Project>