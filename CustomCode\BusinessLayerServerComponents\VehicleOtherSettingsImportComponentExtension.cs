using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using Microsoft.Extensions.DependencyInjection;
using System.IO;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    /// <summary>
    /// Extension for the VehicleOtherSettingsImportComponent that handles custom import logic
    /// for vehicle other settings data.
    /// </summary>
    public class VehicleOtherSettingsImportComponentExtension : IImportExportComponentExtension<VehicleOtherSettingsImportSection0Component,
        VehicleOtherSettingsDataObject>
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IDataFacade _dataFacade;

        public VehicleOtherSettingsImportComponentExtension(IDataFacade dataFacade, IServiceProvider serviceProvider)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// Initializes the extension by hooking into the import component's events.
        /// </summary>
        public void Init(IImportExportComponent<VehicleOtherSettingsDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        /// <summary>
        /// Called after importing each row. Links the vehicle other settings to the corresponding vehicle.
        /// </summary>
        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<VehicleOtherSettingsDataObject> arg)
        {
            try
            {
                // Extract all required identifiers from the import row
                var customerName = arg.DataRow[VehicleOtherSettingsImportSection0Component.COL_CUSTOMER].ToString();
                var siteName = arg.DataRow[VehicleOtherSettingsImportSection0Component.COL_SITE].ToString();
                var departmentName = arg.DataRow[VehicleOtherSettingsImportSection0Component.COL_DEPARTMENT].ToString();
                var hireNo = arg.DataRow[VehicleOtherSettingsImportSection0Component.COL_HIRENO]?.ToString();
                var serialNo = arg.DataRow[VehicleOtherSettingsImportSection0Component.COL_SERIALNO].ToString();

                await FileLogger.LogAsync($"Processing row - Customer: {customerName}, Site: {siteName}, Department: {departmentName}, Hire No: {hireNo}, Serial No: {serialNo}");

                // Step 1: Find and validate customer
                var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { customerName })).SingleOrDefault();
                if (customer == null)
                {
                    throw new ImportValidationException($"Customer not found: {customerName}");
                }

                // Step 2: Find and validate site within customer
                await customer.LoadSitesAsync();
                var site = customer.Sites.FirstOrDefault(x => x.Name == siteName);
                if (site == null)
                {
                    throw new ImportValidationException($"Site '{siteName}' not found for customer '{customerName}'");
                }

                // Step 3: Find and validate department within site
                await site.LoadDepartmentItemsAsync();
                var department = site.DepartmentItems.FirstOrDefault(x => x.Name == departmentName);
                if (department == null)
                {
                    throw new ImportValidationException($"Department '{departmentName}' not found for site '{siteName}'");
                }

                // Step 4: Find vehicle by serial number and validate its relationships
                var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "SerialNo == @0", new object[] { serialNo })).SingleOrDefault();
                if (vehicle == null)
                {
                    throw new ImportValidationException($"Vehicle not found with Serial No: {serialNo}");
                }

                // Step 5: Validate vehicle belongs to the correct customer/site/department
                if (vehicle.CustomerId != customer.Id)
                {
                    throw new ImportValidationException($"Vehicle {serialNo} does not belong to customer {customerName}");
                }
                if (vehicle.SiteId != site.Id)
                {
                    throw new ImportValidationException($"Vehicle {serialNo} does not belong to site {siteName}");
                }
                if (vehicle.DepartmentId != department.Id)
                {
                    throw new ImportValidationException($"Vehicle {serialNo} does not belong to department {departmentName}");
                }

                // Step 6: Validate hire number if provided
                if (!string.IsNullOrEmpty(hireNo) && vehicle.HireNo != hireNo)
                {
                    throw new ImportValidationException($"Vehicle {serialNo} hire number does not match. Expected: {hireNo}, Found: {vehicle.HireNo}");
                }

                // Step 7: Save the settings first
                await _dataFacade.VehicleOtherSettingsDataProvider.SaveAsync(arg.Entity);

                // Step 8: Link the vehicle to the settings
                vehicle.VehicleOtherSettingsId = arg.Entity.Id;
                await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
                
                await FileLogger.LogAsync($"Successfully linked and saved settings for vehicle: {serialNo}");
            }
            catch (Exception ex)
            {
                await FileLogger.LogAsync($"Error processing vehicle other settings: {ex.Message}");
                if (ex.InnerException != null)
                {
                    await FileLogger.LogAsync($"Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }
        }

        private class ImportValidationException : Exception
        {
            public ImportValidationException(string message) : base(message) { }
        }

        private static class FileLogger
        {
            private static readonly string LogPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                "VehicleOtherSettingsImport.log"
            );

            public static async Task LogAsync(string message)
            {
                var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}";
                await File.AppendAllTextAsync(LogPath, logMessage);
            }
        }
    }
} 