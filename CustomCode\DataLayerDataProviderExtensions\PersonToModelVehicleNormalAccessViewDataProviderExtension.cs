using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.ORMSupportClasses;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    /// <summary>
    /// Adds graceful handling for delete operations on PersonToModelVehicleNormalAccessView to avoid
    /// surfacing low-level SQL errors when a record has already been deleted by a concurrent process.
    /// </summary>
    public class PersonToModelVehicleNormalAccessViewDataProviderExtension : IDataProviderExtension<PersonToModelVehicleNormalAccessViewDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<PersonToModelVehicleNormalAccessViewDataProviderExtension> _logger;

        public PersonToModelVehicleNormalAccessViewDataProviderExtension(
            IServiceProvider serviceProvider,
            IDataFacade dataFacade)
        {
            _serviceProvider = serviceProvider;
            _dataFacade = dataFacade;
            _logger = serviceProvider.GetService<ILogger<PersonToModelVehicleNormalAccessViewDataProviderExtension>>();
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeDelete += OnBeforeDeleteAsync;
        }

        private async Task OnBeforeDeleteAsync(OnBeforeDeleteEventArgs e)
        {
            var instance = e.Entity as PersonToModelVehicleNormalAccessViewDataObject;
            if (instance == null)
            {
                return;
            }

            try
            {
                _logger?.LogDebug(
                    "Processing OnBeforeDelete for PersonToModelVehicleNormalAccessView DepartmentId: {DepartmentId}, ModelId: {ModelId}, PermissionId: {PermissionId}",
                    instance.DepartmentId, instance.ModelId, instance.PermissionId);

                // Verify the record still exists before attempting delete to avoid race-condition SQL errors
                var existingRecord = await _dataFacade.PersonToModelVehicleNormalAccessViewDataProvider
                    .GetAsync(instance, skipSecurity: true);

                if (existingRecord == null)
                {
                    _logger?.LogWarning(
                        "PersonToModelVehicleNormalAccessView record DeptId: {DepartmentId}, ModelId: {ModelId}, PermissionId: {PermissionId} already deleted. Skipping.",
                        instance.DepartmentId, instance.ModelId, instance.PermissionId);

                    // Prevent framework from issuing the actual delete
                    e.IsHandled = true;
                    return;
                }

                _logger?.LogDebug("Record exists; allowing delete to proceed");
            }
            catch (Exception ex)
            {
                if (IsRaceConditionError(ex))
                {
                    _logger?.LogWarning(
                        ex,
                        "Race/concurrency condition during OnBeforeDelete for PersonToModelVehicleNormalAccessView DeptId: {DepartmentId}, ModelId: {ModelId}, PermissionId: {PermissionId}. Treating as already deleted.",
                        instance?.DepartmentId, instance?.ModelId, instance?.PermissionId);
                    e.IsHandled = true; // swallow and treat as handled
                    return;
                }

                _logger?.LogError(ex, "Unexpected error in OnBeforeDelete for PersonToModelVehicleNormalAccessView: {Message}", ex.Message);
                throw;
            }
        }

        private static bool IsRaceConditionError(Exception ex)
        {
            var message = (ex.Message ?? string.Empty).ToLowerInvariant();
            var inner = (ex.InnerException?.Message ?? string.Empty).ToLowerInvariant();
            return
                message.Contains("batch update returned unexpected row count") ||
                message.Contains("actual row count: 0; expected: 1") ||
                message.Contains("could not execute batch command") ||
                message.Contains("sql not available") ||
                message.Contains("row was updated or deleted by another transaction") ||
                message.Contains("optimistic locking") ||
                message.Contains("concurrency") ||
                inner.Contains("row count") ||
                inner.Contains("could not execute batch command") ||
                inner.Contains("sql not available");
        }
    }
}


