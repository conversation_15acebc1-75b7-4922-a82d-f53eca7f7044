import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('CurrentStatusReportPageControllerCustom', () => {
    let controller;
    let customController;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                Controllers: {},
                Model: {
                    Components: {
                        IoTHubManagerProxy: vi.fn()
                    }
                }
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/Controllers/CurrentStatusReportPageController.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base controller with required properties
        controller = {
            DashboardFilterFormViewModel: {
                CurrentObject: vi.fn().mockReturnValue({
                    Data: {
                        CustomerId: ko.observable(),
                        SiteId: ko.observable(),
                        DepartmentId: ko.observable()
                    }
                })
            },
            CurrentStatusCombinedViewFormViewModel: {
                CurrentStatusDriverViewItemsGridViewModel: {
                    LoadCurrentStatusDriverViewObjectCollection: vi.fn(),
                    exportFilterPredicate: '',
                    exportFilterParameters: ''
                },
                CurrentStatusVehicleViewItemsGridViewModel: {
                    LoadCurrentStatusVehicleViewObjectCollection: vi.fn(),
                    exportFilterPredicate: '',
                    exportFilterParameters: ''
                },
                StatusData: {
                    IsBusy: ko.observable(false)
                }
            },
            applicationController: {
                viewModel: {
                    security: {
                        currentUserClaims: vi.fn().mockReturnValue({
                            CustomerId: '123',
                            AllowedSiteIds: '{456}'
                        })
                    }
                }
            },
            ObjectsDataSet: {}
        };

        // Create the custom controller and initialize it
        customController = new FleetXQ.Web.Controllers.CurrentStatusReportPageControllerCustom(controller);
    });

    describe('LoadCurrentStatusDriverViewGridViewData', () => {
        it('should use default configuration when customerId is null and user has customerId', () => {
            // Setup the scenario where currentData.CustomerId is null but user has customerId
            controller.DashboardFilterFormViewModel.CurrentObject.mockReturnValue({
                Data: {
                    CustomerId: ko.observable(null),
                    SiteId: ko.observable(),
                    DepartmentId: ko.observable()
                }
            });

            // Call the method
            customController.LoadCurrentStatusDriverViewGridViewData();

            // Verify the grid view model was called with correct configuration
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection)
                .toHaveBeenCalledWith(expect.objectContaining({
                    filterPredicate: 'Driver.Person.CustomerId == @0 && Driver.Person.SiteId == @1',
                    filterParameters: expect.stringContaining('"123"')
                }));

            // Also verify that vehicle grid view was called with the correct configuration
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection)
                .toHaveBeenCalledWith(expect.objectContaining({
                    filterPredicate: 'Vehicle.CustomerId == @0 && Vehicle.SiteId == @1',
                    filterParameters: expect.stringContaining('"123"')
                }));
        });

        it('should use filter values when they are set', () => {
            // Setup filter values
            controller.DashboardFilterFormViewModel.CurrentObject.mockReturnValue({
                Data: {
                    CustomerId: ko.observable('789'),
                    SiteId: ko.observable('101'),
                    DepartmentId: ko.observable('202')
                }
            });

            // Call the method
            customController.LoadCurrentStatusDriverViewGridViewData();

            // Verify the grid view model was called with correct configuration
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection)
                .toHaveBeenCalledWith(expect.objectContaining({
                    filterPredicate: 'Driver.Person.CustomerId == @0 && Driver.Person.SiteId == @1 && Driver.Person.DepartmentId == @2',
                    filterParameters: expect.stringContaining('"789"')
                }));

            // Verify export parameters were set
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.exportFilterPredicate)
                .toBe('Driver.Person.CustomerId == @0 && Driver.Person.SiteId == @1 && Driver.Person.DepartmentId == @2');
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.exportFilterParameters)
                .toContain('"789"');
        });
    });

    describe('LoadCurrentStatusVehicleViewGridViewData', () => {
        it('should set up configuration and call LoadCurrentStatusVehicleViewObjectCollection', () => {
            // Setup filter values
            controller.DashboardFilterFormViewModel.CurrentObject.mockReturnValue({
                Data: {
                    CustomerId: ko.observable('789'),
                    SiteId: ko.observable('101'),
                    DepartmentId: ko.observable('202')
                }
            });

            // Call the method
            customController.LoadCurrentStatusVehicleViewGridViewData();

            // Verify the grid view model was called
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection)
                .toHaveBeenCalled();

            // Verify export parameters were set
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.exportFilterPredicate)
                .toBe('Vehicle.Department.Site.CustomerId == @0 && Vehicle.Department.SiteId == @1 && Vehicle.DepartmentId == @2');
            expect(controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.exportFilterParameters)
                .toContain('"789"');
        });
    });
}); 