﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class DriverLicenseExpiryViewDataProvider : DataProvider<DriverLicenseExpiryViewDataObject>
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DriverLicenseExpiryViewDataProvider> _logger;
        private readonly IServiceProvider _serviceProvider;

        public DriverLicenseExpiryViewDataProvider(
            IServiceProvider serviceProvider,
            IDataProviderTransaction transaction,
            IEntityDataProvider entityDataProvider,
            IDataProviderDispatcher<DriverLicenseExpiryViewDataObject> dispatcher,
            IDataProviderDeleteStrategy dataProviderDeleteStrategy,
            IAutoInclude autoInclude,
            IThreadContext threadContext,
            IDataProviderTransaction dataProviderTransaction,
            IConfiguration configuration,
            ILogger<DriverLicenseExpiryViewDataProvider> logger)
            : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        protected override async Task<DataObjectCollection<DriverLicenseExpiryViewDataObject>> DoGetCollectionAsync(
            LambdaExpression securityFilterExpression,
            string filterPredicate,
            object[] filterArguments,
            string orderByPredicate,
            int pageNumber,
            int pageSize,
            List<string> includes,
            IObjectsDataSet context,
            Dictionary<string, object> parameters)
        {
            try
            {
                _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Starting DoGetCollectionAsync");
                var result = new DataObjectCollection<DriverLicenseExpiryViewDataObject>();
                result.ObjectsDataSet = context;

                // Get parameters from filter arguments
                DateTime? referenceDate = null;
                Guid? customerId = null;
                Guid? siteId = null;
                Guid? departmentId = null;

                if (filterArguments != null)
                {
                    _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Filter arguments length: {Length}", filterArguments.Length);

                    // Log all filter arguments for debugging
                    for (int i = 0; i < filterArguments.Length; i++)
                    {
                        _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Filter argument {Index}: Type = {Type}, Value = {Value}",
                            i,
                            filterArguments[i]?.GetType().FullName ?? "null",
                            filterArguments[i]?.ToString() ?? "null");
                    }

                    // When only one parameter is passed and it's a DateTime, it's the reference date
                    if (filterArguments.Length == 1 && filterArguments[0] is DateTime)
                    {
                        referenceDate = (DateTime)filterArguments[0];
                        _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Using single date parameter: {Date}", referenceDate);
                    }
                    else
                    {
                        // Process each parameter based on its type
                        for (int i = 0; i < filterArguments.Length; i++)
                        {
                            if (filterArguments[i] == null) continue;

                            // If it's a DateTime, it's our reference date
                            if (filterArguments[i] is DateTime dateValue)
                            {
                                referenceDate = dateValue;
                                _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Found date at index {Index}: {Date}", i, referenceDate);
                            }
                            // If it's a Guid, assign it to the appropriate ID based on order
                            else if (filterArguments[i] is Guid guidValue)
                            {
                                if (customerId == null)
                                {
                                    customerId = guidValue;
                                    _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Found CustomerId at index {Index}: {Id}", i, customerId);
                                }
                                else if (siteId == null)
                                {
                                    siteId = guidValue;
                                    _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Found SiteId at index {Index}: {Id}", i, siteId);
                                }
                                else if (departmentId == null)
                                {
                                    departmentId = guidValue;
                                    _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Found DepartmentId at index {Index}: {Id}", i, departmentId);
                                }
                            }
                            else
                            {
                                _logger.LogWarning("[DriverLicenseExpiryViewDataProvider] Unexpected parameter type at index {Index}: {Type}", 
                                    i, 
                                    filterArguments[i].GetType().FullName);
                            }
                        }
                    }

                    _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Final parameter values: CustomerId = {CustomerId}, SiteId = {SiteId}, DepartmentId = {DepartmentId}, ReferenceDate = {ReferenceDate}",
                        customerId,
                        siteId,
                        departmentId,
                        referenceDate);
                }

                using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
                {
                    _logger.LogDebug("[DriverLicenseExpiryViewDataProvider] Opening SQL connection");
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("GetDriverLicenseExpiry", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters in the order expected by the stored procedure
                        command.Parameters.AddWithValue("@CustomerId", customerId.HasValue ? (object)customerId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@SiteId", siteId.HasValue ? (object)siteId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@DepartmentId", departmentId.HasValue ? (object)departmentId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@ReferenceDate", referenceDate.HasValue ? (object)referenceDate.Value : DBNull.Value);

                        _logger.LogDebug("[DriverLicenseExpiryViewDataProvider] Executing stored procedure");
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            // Get column ordinals for safer mapping
                            var columnOrdinals = new Dictionary<string, int>();
                            for (int i = 0; i < reader.FieldCount; i++)
                            {
                                columnOrdinals[reader.GetName(i)] = i;
                            }

                            while (await reader.ReadAsync())
                            {
                                try
                                {
                                    var entity = _serviceProvider.GetRequiredService<DriverLicenseExpiryViewDataObject>();
                                    entity.ObjectsDataSet = context;

                                    // Map fields from reader to entity
                                    if (columnOrdinals.ContainsKey("Id"))
                                    {
                                        entity.Id = reader.GetGuid(columnOrdinals["Id"]);
                                        entity.IsNew = false;
                                    }

                                    if (columnOrdinals.ContainsKey("CustomerId") && !reader.IsDBNull(columnOrdinals["CustomerId"]))
                                        entity.CustomerId = reader.GetGuid(columnOrdinals["CustomerId"]);

                                    if (columnOrdinals.ContainsKey("SiteId") && !reader.IsDBNull(columnOrdinals["SiteId"]))
                                        entity.SiteId = reader.GetGuid(columnOrdinals["SiteId"]);

                                    if (columnOrdinals.ContainsKey("DepartmentId") && !reader.IsDBNull(columnOrdinals["DepartmentId"]))
                                        entity.DepartmentId = reader.GetGuid(columnOrdinals["DepartmentId"]);

                                    if (columnOrdinals.ContainsKey("TimePeriod") && !reader.IsDBNull(columnOrdinals["TimePeriod"]))
                                        entity.TimePeriod = reader.GetString(columnOrdinals["TimePeriod"]);

                                    if (columnOrdinals.ContainsKey("ExpiredLicenseCount") && !reader.IsDBNull(columnOrdinals["ExpiredLicenseCount"]))
                                    {
                                        var value = reader.GetInt32(columnOrdinals["ExpiredLicenseCount"]);
                                        entity.ExpiredLicenseCount = value <= Int16.MaxValue ? (Int16)value : Int16.MaxValue;
                                    }

                                    result.Add(entity);
                                }
                                catch (Exception ex)
                                {
                                    _logger.LogError(ex, "[DriverLicenseExpiryViewDataProvider] Error mapping data reader to entity");
                                    throw new GOServerException("Error mapping data reader to entity", ex.Message, ex);
                                }
                            }
                        }

                        _logger.LogInformation("[DriverLicenseExpiryViewDataProvider] Retrieved {Count} records", result.Count);
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DriverLicenseExpiryViewDataProvider] Error in DoGetCollectionAsync");
                throw;
            }
        }

        // Other methods remain as NotImplemented
        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task DoDeleteAsync(DriverLicenseExpiryViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DriverLicenseExpiryViewDataObject> DoGetAsync(DriverLicenseExpiryViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DriverLicenseExpiryViewDataObject> DoSaveAsync(DriverLicenseExpiryViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
