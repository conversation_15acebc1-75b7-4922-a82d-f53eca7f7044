﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// DriverSessionAPI Component
	/// Session details API 
	/// </summary>
    public partial class DriverSessionAPI : BaseServerComponent, IDriverSessionAPI 
    {
        public DriverSessionAPI(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
        {
        }

        /// <summary>
        /// StoreSessionDetails Method
        /// </summary>
        /// <param name="Message">JSON Message</param>
        /// <returns></returns>
        public System.Threading.Tasks.Task<ComponentResponse<System.String>> StoreSessionDetailsAsync(System.String Message, Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return System.Threading.Tasks.Task.FromResult(new ComponentResponse<string>(default(System.String))); 
		}
	}
}
