﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.ServiceLayer;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using VDS.RDF;
using FleetXQ.Data.DataObjects.Custom;
using DocumentFormat.OpenXml.Bibliography;
using NHibernate.Util;
using FleetXQ.Data.DataProviders.Database;
using Microsoft.SqlServer.Management.XEvent;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    internal class DriverAuthenticationAPITestTestBase : TestBase
    {
        private IDriverAuthenticationAPI _iDriverAuthenticationAPI;
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"DriverAuthenticationAPITest-{Guid.NewGuid()}";

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _iDriverAuthenticationAPI = _serviceProvider.GetRequiredService<IDriverAuthenticationAPI>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [Test]
        public async Task VerifyDriverAccessAsync_ValidCard_ReturnsAuth()
        {
            // Arrange
            var vehicleNormalAccess = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicleNormalAccess.VehicleId }, skipSecurity: true)).FirstOrDefault();
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);

            //get card
            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 3 }, skipSecurity: true)).SingleOrDefault();
            var siteAccess = (await _dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "SiteId == @0  and PermissionId == @1  and CardId == @2", new object[] { vehicle.SiteId, permissionDriver.Id, vehicleNormalAccess.CardId }, skipSecurity: true)).SingleOrDefault();
            Assert.That(siteAccess, Is.Not.Null, "Test data setup failed: No siteAccess found.");

            var card = await siteAccess.LoadCardAsync(skipSecurity: true);

            string validMessage = "{\"event_type\":\"CARD\",\"msg_id\":\"2\",\"session_id\":\"" + Guid.NewGuid() + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\",\"Payload\":\"CARD="+ card.Weigand + ",64CC6BC4\"}";

            // Act
            var response = await _iDriverAuthenticationAPI.VerifyDriverAccessAsync(validMessage);

            // Assert
            Assert.That(response, Is.Not.Null);
            Assert.That(response.Result, Does.Contain("Success"));
        }

        [Test]
        public async Task VerifyDriverAccessAsync_InvalidCard_ReturnsDeny()
        {
            // Arrange
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();

            string invalidMessage = "{\"event_type\":\"CARD\",\"msg_id\":\"2\",\"session_id\":\"" + Guid.NewGuid() + "\",\"IoTDeviceId\":\""+ module.IoTDevice + "\",\"Payload\":\"CARD=1234,64CC6BC4\"}";

            // Act & Assert
            try
            {
                await _iDriverAuthenticationAPI.VerifyDriverAccessAsync(invalidMessage);
                Assert.That(true, Is.False, "Expected exception for invalid Card was not thrown.");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Does.Contain("unknow card id"), "Expected exception for invalid card.");
            }
        }

        [Test]
        public async Task VerifyDriverAccessAsync_CardNotAssignedToDriver_ReturnsDeny()
        {
            // Arrange
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            // Facility Code is random between 1 to 254 in string
            Random random = new Random();
            card.FacilityCode = random.Next(1, 255).ToString();
            // Card Number is random between 100001 to 675899 in string
            card.CardNumber = random.Next(100001, 675900).ToString();
            card.Active = true;
            card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
            card.Type = CardTypeEnum.CardID;

            card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();


            string messageWithUnassignedCard = "{\"event_type\":\"CARD\",\"msg_id\":\"2\",\"session_id\":\"" + Guid.NewGuid() + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\",\"Payload\":\"CARD="+ card.Weigand +",64CC6BC4\"}";

            // Act & Assert
            try
            {
                await _iDriverAuthenticationAPI.VerifyDriverAccessAsync(messageWithUnassignedCard);
                Assert.That(true, Is.False, "Expected exception for unassigned Card was not thrown.");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Does.Contain("not assign to any driver"), "Expected exception for unassigned card.");
            }
        }

        [Test]
        public async Task VerifyDriverAccessAsync_CardNotAssignedToVehicle_ReturnsDeny()
        {
            // Arrange
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();

            var card = (await _dataFacade.CardDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();


            string messageWithUnassignedCard = "{\"event_type\":\"CARD\",\"msg_id\":\"2\",\"session_id\":\"" + Guid.NewGuid() + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\",\"Payload\":\"CARD="+ card.Weigand + ",64CC6BC4\"}";

            // Act & Assert
            try
            {
                await _iDriverAuthenticationAPI.VerifyDriverAccessAsync(messageWithUnassignedCard);
                Assert.That(true, Is.False, "Expected exception for unassigned Card was not thrown.");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Does.Contain("not assign to vehicle"), "Expected exception for unassigned card.");
            }
        }

        [Test]
        public async Task VerifyDriverAccessAsync_ModuleNotAssignedToVehicle_ReturnsDeny()
        {
            // Arrange
            // create a module for the vehicle
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID123456789";
            module.FSSSBase = 12345;
            module.FSSXMulti = 1;         
            module.IoTDevice = "unassigned";
            module.IsAllocatedToVehicle = false;
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

            var card = (await _dataFacade.CardDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();

            string messageWithUnassignedModule = "{\"event_type\":\"CARD\",\"msg_id\":\"2\",\"session_id\":\"" + Guid.NewGuid() + "\",\"IoTDeviceId\":\"" + module.IoTDevice + "\",\"Payload\":\"CARD=" + card.Weigand + ",64CC6BC4\"}";  

            // Act & Assert
            try
            {
                await _iDriverAuthenticationAPI.VerifyDriverAccessAsync(messageWithUnassignedModule);
                Assert.That(true, Is.False, "Expected exception for unassigned module was not thrown.");
            }
            catch (Exception ex)
            {
                Assert.That(ex.Message, Does.Contain("Module is not assign to any vehicle"), "Expected exception for unassigned module.");
            }
        }


        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country, skipSecurity: true);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region, skipSecurity: true);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer, skipSecurity: true);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer, skipSecurity: true);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();

            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone, skipSecurity: true);

            //Create IOs
            var iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "0";
            iofields.Description = "ignition";
            iofields.IOType = " ";
            iofields.CANBUS = false;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "SEAT";
            iofields.Description = "Canbus Seat Switch Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "HYDL";
            iofields.Description = "Canbus Hydrolic Raising Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "TRACK";
            iofields.Description = "Canbus Traction/Movement Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields, skipSecurity: true);

            var sites = new List<string> { "Site 1", "Site 2", "Site 3" };
            var IoTHubIds1 = new string[] { "test_00000001", "test_00000002", "test_00000003", "test_00000004", "test_00000005", "test_00000006", "test_00000007", "test_00000008", "test_00000009", "test_00000010" };
            var IoTHubIds2 = new string[] { "test_00000011", "test_00000012", "test_00000013", "test_00000014", "test_00000015", "test_00000016", "test_00000017", "test_00000018", "test_00000019", "test_00000020" };
            var IoTHubIds3 = new string[] { "test_00000021", "test_00000022", "test_00000023", "test_00000024", "test_00000025", "test_00000026", "test_00000027", "test_00000028", "test_00000029", "test_00000030" };
            var VehicleHireNos1 = new string[] { "VH1", "VH2", "VH3", "VH4", "VH5", "VH6", "VH7", "VH8", "VH9", "VH10" };
            var VehicleHireNos2 = new string[] { "VH11", "VH12", "VH13", "VH14", "VH15", "VH16", "VH17", "VH18", "VH19", "VH20" };
            var VehicleHireNos3 = new string[] { "VH21", "VH22", "VH23", "VH24", "VH25", "VH26", "VH27", "VH28", "VH29", "VH30" };
            var VehicleSerialNos1 = new string[] { "VS1", "VS2", "VS3", "VS4", "VS5", "VS6", "VS7", "VS8", "VS9", "VS10" };
            var VehicleSerialNos2 = new string[] { "VS11", "VS12", "VS13", "VS14", "VS15", "VS16", "VS17", "VS18", "VS19", "VS20" };
            var VehicleSerialNos3 = new string[] { "VS21", "VS22", "VS23", "VS24", "VS25", "VS26", "VS27", "VS28", "VS29", "VS30" };
            var PersonFirstName1 = new string[] { "John", "Peter", "Paul", "Mark", "Luke", "Matthew", "James", "Jude", "Simon", "Andrew" };
            var PersonFirstName2 = new string[] { "Mary", "Elizabeth", "Anna", "Ruth", "Esther", "Sarah", "Rebecca", "Leah", "Rachel", "Deborah" };
            var PersonFirstName3 = new string[] { "David", "Solomon", "Elijah", "Elisha", "Isaiah", "Jeremiah", "Ezekiel", "Daniel", "Hosea", "Joel" };
            var PersonLastName1 = new string[] { "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor" };
            var PersonLastName2 = new string[] { "Anderson", "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson", "Garcia", "Martinez", "Robinson" };
            var PersonLastName3 = new string[] { "Clark", "Rodriguez", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "Hernandez", "King" };

            var siteVehicleNormalCardAccessCollection = new DataObjectCollection<SiteVehicleNormalCardAccessDataObject>();
            siteVehicleNormalCardAccessCollection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            var departmentVehicleleNormalCardAccessCollection = new DataObjectCollection<DepartmentVehicleNormalCardAccessDataObject>();
            departmentVehicleleNormalCardAccessCollection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            var modelVehicleNormalCardAccessCollection = new DataObjectCollection<ModelVehicleNormalCardAccessDataObject>();
            modelVehicleNormalCardAccessCollection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            var perVehicleNormalCardAccessCollection = new DataObjectCollection<PerVehicleNormalCardAccessDataObject>();
            perVehicleNormalCardAccessCollection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            var permissionDriver = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { 3 }, skipSecurity: true)).SingleOrDefault();

            foreach (var siteName in sites)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.CustomerId = customer.Id;
                site.Name = siteName;
                site.TimezoneId = timeZone.Id;
                site.Id = Guid.NewGuid();

                site = await _dataFacade.SiteDataProvider.SaveAsync(site, skipSecurity: true);

                var departmentNames = new List<string> { "Warehouse", "Logistics", "Production" };

                // create 3 departments for each site
                for (int j = 0; j < 3; j++)
                {
                    var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                    department.Id = Guid.NewGuid();
                    department.Name = siteName + " " + departmentNames[j];
                    department.SiteId = site.Id;
                    department = await _dataFacade.DepartmentDataProvider.SaveAsync(department, skipSecurity: true);

                    // get only 3 models
                    var Models = new List<ModelDataObject>();
                    for (int i = 0; i < 3; i++)
                    {
                        var model = _serviceProvider.GetRequiredService<ModelDataObject>();
                        model.Id = Guid.NewGuid();
                        model.Name = "Model " + (i + 1).ToString();
                        model.Description = "Description for Model " + (i + 1).ToString();
                        model.DealerId = dealer.Id;
                        // Assigning Model.Type based on the ModelTypesEnum
                        switch (i)
                        {
                            case 0:
                                model.Type = ModelTypesEnum.Electric;
                                break;
                            case 1:
                                model.Type = ModelTypesEnum.ICForklifts;
                                break;
                            case 2:
                                model.Type = ModelTypesEnum.OrderPickers;
                                break;
                            // Additional cases can be added here for other types if necessary
                            default:
                                model.Type = ModelTypesEnum.PalletJack; // Default case if more than 3 models are created
                                break;
                        }
                        // Removed setting the non-existent Active property
                        model = await _dataFacade.ModelDataProvider.SaveAsync(model, skipSecurity: true);
                        Models.Add(model);
                    }
                    // create 10 vehicles for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = Guid.NewGuid();
                        vehicle.CustomerId = customer.Id;
                        vehicle.SiteId = site.Id;
                        vehicle.DepartmentId = department.Id;
                        vehicle.IDLETimer = 300;
                        vehicle.OnHire = true;
                        vehicle.ImpactLockout = true;
                        // set random modelId with index rand 0 to 2
                        if (Models.Any())
                        {
                            vehicle.ModelId = Models[k % 3].Id;
                        }
                        else
                        {
                            throw new InvalidOperationException("No models found to assign to vehicle.");
                        }
                        if (j == 0)
                        {
                            vehicle.HireNo = VehicleHireNos1[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos1[k] + department.Id;
                        }
                        else if (j == 1)
                        {
                            vehicle.HireNo = VehicleHireNos2[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos2[k] + department.Id;
                        }
                        else
                        {
                            vehicle.HireNo = VehicleHireNos3[k] + department.Id;
                            vehicle.SerialNo = VehicleSerialNos3[k] + department.Id;
                        }

                        // create a module for the vehicle
                        var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                        module.Id = Guid.NewGuid();
                        module.Calibration = 100;
                        module.CCID = "CCID" + j + k;
                        // set FSSSBASE random from 100000 to 200000 in increment of 10000
                        Random random = new Random();
                        int randomNumber = random.Next(10, 21);
                        module.FSSSBase = randomNumber * 10000;
                        module.FSSXMulti = 1;
                        if (j == 0)
                            module.IoTDevice = IoTHubIds1[k] + department.Id;
                        else if (j == 1)
                            module.IoTDevice = IoTHubIds2[k] + department.Id;
                        else
                            module.IoTDevice = IoTHubIds3[k] + department.Id;
                        module.IsAllocatedToVehicle = true;
                        await _dataFacade.ModuleDataProvider.SaveAsync(module, skipSecurity: true);

                        vehicle.ModuleId1 = module.Id;
                        vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle, skipSecurity: true);
                    }
                    // create 10 persons for each department
                    for (int k = 0; k < 10; k++)
                    {
                        var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                        person.Id = Guid.NewGuid();
                        person.CustomerId = customer.Id;
                        person.SiteId = site.Id;
                        person.DepartmentId = department.Id;
                        if (j == 0)
                        {
                            person.FirstName = PersonFirstName1[k];
                            person.LastName = PersonLastName1[k];
                        }
                        else if (j == 1)
                        {
                            person.FirstName = PersonFirstName2[k];
                            person.LastName = PersonLastName2[k];
                        }
                        else
                        {
                            person.FirstName = PersonFirstName3[k];
                            person.LastName = PersonLastName3[k];
                        }
                        person.IsDriver = true;
                        person.IsActiveDriver = true;

                        person = await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true); //Crear person and driver

                        var card = _serviceProvider.GetRequiredService<CardDataObject>();
                        card.Id = Guid.NewGuid();
                        // Facility Code is random between 1 to 254 in string
                        Random random = new Random();
                        card.FacilityCode = random.Next(1, 255).ToString();
                        // Card Number is random between 100001 to 675899 in string
                        card.CardNumber = random.Next(100001, 675900).ToString();
                        card.Active = true;
                        card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
                        card.Type = CardTypeEnum.CardID;

                        card = await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);

                        // get the driver object from person and assign the card ID to the driver
                        var driver = person.Driver;
                        driver.CardDetailsId = card.Id;
                        driver = await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);

                        //add to the siteVehicleNormalCardAccess, depatVehicleNormalCardAccess, modelVehicleNormalCardAccess, PerVehicleNormalCardAccess
                      
                        var siteVehicleNormalCardAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                        siteVehicleNormalCardAccess.SetIdValue(Guid.NewGuid());
                        siteVehicleNormalCardAccess.SetCardIdValue(card.Id);
                        siteVehicleNormalCardAccess.SetSiteIdValue(site.Id);                    
                        siteVehicleNormalCardAccess.SetPermissionIdValue(permissionDriver.Id);
                        siteVehicleNormalCardAccessCollection.Add(siteVehicleNormalCardAccess);

                        var departmentVehicleleNormalCardAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                        departmentVehicleleNormalCardAccess.SetIdValue(Guid.NewGuid());
                        departmentVehicleleNormalCardAccess.SetCardIdValue(card.Id);
                        departmentVehicleleNormalCardAccess.SetDepartmentIdValue(department.Id);
                        departmentVehicleleNormalCardAccess.SetPermissionIdValue(permissionDriver.Id);
                        departmentVehicleleNormalCardAccessCollection.Add(departmentVehicleleNormalCardAccess);

                        var modelVehicleNormalCardAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                        modelVehicleNormalCardAccess.SetIdValue(Guid.NewGuid());
                        modelVehicleNormalCardAccess.SetCardIdValue(card.Id);
                        modelVehicleNormalCardAccess.SetDepartmentIdValue(department.Id);
                        var modelVechile = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null, skipSecurity: true)).FirstOrDefault();
                        modelVehicleNormalCardAccess.SetModelIdValue(modelVechile.Id);
                        modelVehicleNormalCardAccess.SetPermissionIdValue(permissionDriver.Id);                      
                        modelVehicleNormalCardAccessCollection.Add(modelVehicleNormalCardAccess);

                        var perVehicleNormalCardAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                        perVehicleNormalCardAccess.SetIdValue(Guid.NewGuid());
                        perVehicleNormalCardAccess.SetCardIdValue(card.Id);
                        var perVechile = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "DepartmentId == @0", new object[] { department.Id }, skipSecurity: true)).FirstOrDefault();
                        perVehicleNormalCardAccess.SetVehicleIdValue(perVechile.Id);
                        perVehicleNormalCardAccess.SetPermissionIdValue(permissionDriver.Id);
                        perVehicleNormalCardAccessCollection.Add(perVehicleNormalCardAccess);
                        //end
                    }
                }
            }
            //save DepartmentVehicleNormalCardAccess
            await _dataFacade.SiteVehicleNormalCardAccessDataProvider.SaveAsync(siteVehicleNormalCardAccessCollection.First(), skipSecurity: true);
            await _dataFacade.DepartmentVehicleNormalCardAccessDataProvider.SaveAsync(departmentVehicleleNormalCardAccessCollection.First(), skipSecurity: true);
            await _dataFacade.ModelVehicleNormalCardAccessDataProvider.SaveAsync(modelVehicleNormalCardAccessCollection.First(), skipSecurity: true);
            await _dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(perVehicleNormalCardAccessCollection.First(), skipSecurity: true);
        } // End create demo data
    }
}

 