using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Generic;
using System.IO;
using System.Globalization;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Logging;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class GeneralLicenseImportComponentExtension : IImportExportComponentExtension<GeneralLicenseImportSection0Component, LicenceDetailDataObject>
    {
        private readonly IDataFacade dataFacade;
        private readonly IServiceProvider serviceProvider;
        private readonly ILogEngine logEngine;

        public GeneralLicenseImportComponentExtension(
            IDataFacade dataFacade,
            IServiceProvider serviceProvider,
            ILogEngine logEngine
        )
        {
            this.dataFacade = dataFacade;
            this.serviceProvider = serviceProvider;
            this.logEngine = logEngine;
        }

        public void Init(IImportExportComponent<LicenceDetailDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<LicenceDetailDataObject> arg)
        {
            try
            {
                // Validate input data
                var firstName = arg.DataRow["First Name"]?.ToString();
                var lastName = arg.DataRow["Last Name"]?.ToString();
                var licenseNumber = arg.DataRow[GeneralLicenseImportSection0Component.COL_LICENSENUMBER]?.ToString();

                if (string.IsNullOrWhiteSpace(firstName) || string.IsNullOrWhiteSpace(lastName))
                {
                    throw new Exception("First Name and Last Name are required fields");
                }

                if (string.IsNullOrWhiteSpace(licenseNumber))
                {
                    throw new Exception("License Number is required");
                }

                var person = (await dataFacade.PersonDataProvider.GetCollectionAsync(
                    null,
                    "FirstName == @0 && LastName == @1",
                    new object[] { firstName, lastName }
                )).SingleOrDefault();

                if (person == null)
                {
                    throw new Exception($"Person not found with First Name: {firstName} and Last Name: {lastName}");
                }

                // Get the Driver record
                var driver = (await dataFacade.DriverDataProvider.GetCollectionAsync(
                    null,
                    "Id == @0",
                    new object[] { person.DriverId.Value }
                )).SingleOrDefault();

                if (driver == null)
                {
                    throw new Exception($"Driver record not found for Person {person.FirstName} {person.LastName}");
                }

                // Set up the license details
                var licenseDetail = arg.Entity;
                licenseDetail.LicenseNumber = licenseNumber;

                // Handle Expiry Date
                var expiryDateStr = arg.DataRow[GeneralLicenseImportSection0Component.COL_EXPIRYDATE]?.ToString();
                if (!string.IsNullOrEmpty(expiryDateStr))
                {
                    if (!DateTime.TryParseExact(expiryDateStr,
                        new[] { "dd/MM/yyyy", "d/M/yyyy", "dd-MM-yyyy", "d-M-yyyy" },
                        CultureInfo.InvariantCulture,
                        DateTimeStyles.None,
                        out DateTime parsedDate))
                    {
                        throw new Exception($"Invalid date format: {expiryDateStr}. Please use format dd/MM/yyyy");
                    }
                    licenseDetail.ExpiryDate = parsedDate.Date.AddHours(12);
                }

                // Update Driver License Mode to General (since this is a general license import)
                if (driver.LicenseMode != ModeEnum.General)
                {
                    driver.LicenseMode = ModeEnum.General;
                 
                }

                // Set up the dataset and relationship
                var objectsDataSet = serviceProvider.GetRequiredService<IObjectsDataSet>();
                licenseDetail.ObjectsDataSet = objectsDataSet;

                driver.SetGeneralLicenceValue(licenseDetail);
                licenseDetail.SetDriverValue(driver);

                await dataFacade.LicenceDetailDataProvider.SaveAsync(licenseDetail);
                await dataFacade.DriverDataProvider.SaveAsync(driver);
            }
            catch (Exception ex)
            {
                throw;
            }
        }

    }
}
