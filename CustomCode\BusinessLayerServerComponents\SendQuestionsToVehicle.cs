﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using Newtonsoft.Json;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using FleetXQ.Data.DataProvidersExtensions.Custom;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// SendQuestionsToVehicle Component
	///  
	/// </summary>
    public partial class SendQuestionsToVehicle : BaseServerComponent, ISendQuestionsToVehicle 
    {
        private readonly IDataFacade _dataFacade;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly ILoggingService _logger;
        public SendQuestionsToVehicle(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, ILoggingService logger) : base(serviceProvider, configuration, dataFacade)
        {
            _dataFacade = dataFacade;
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;
        }

        public async System.Threading.Tasks.Task<ComponentResponse<string>> UploadChecklistQuestionsAsync(Guid VehicleId, Dictionary<string, object> parameters = null)
        {
            var vehicle = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { VehicleId })).SingleOrDefault();
            if (vehicle == null)
            {
                _logger.LogError(new GOServerException($"Vehicle not found with id {VehicleId}"));
                throw new GOServerException($"unknow vehicle id {VehicleId}");
            }
            
            var module = await vehicle.LoadModuleAsync();
            if (module != null)
            {
                var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
                _ = deviceTwinHandler.SyncChecklistToVehicle(module.IoTDevice);
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }
    }
}
