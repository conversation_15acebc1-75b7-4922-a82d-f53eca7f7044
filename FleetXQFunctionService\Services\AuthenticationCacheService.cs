using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace FleetXQFunctionService.Services
{
    public interface IAuthenticationCacheService
    {
        Task<(string csrfToken, string applicationToken, string userToken)> GetCachedAuthenticationAsync(string baseUrl);
        void InvalidateCache(string baseUrl);
    }

    public class AuthenticationCacheService : IAuthenticationCacheService
    {
        private readonly IMemoryCache _cache;
        private readonly ILogger<AuthenticationCacheService> _logger;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(45); // Cache for 45 minutes

        public AuthenticationCacheService(IMemoryCache cache, ILogger<AuthenticationCacheService> logger)
        {
            _cache = cache;
            _logger = logger;
        }

        public async Task<(string csrfToken, string applicationToken, string userToken)> GetCachedAuthenticationAsync(string baseUrl)
        {
            var cacheKey = $"auth_{baseUrl}";

            if (_cache.TryGetValue(cacheKey, out var cachedAuth))
            {
                var authResult = (ValueTuple<string, string, string>)cachedAuth;
                _logger.LogDebug("Using cached authentication for {BaseUrl}", baseUrl);
                return authResult;
            }

            _logger.LogDebug("No cached authentication found for {BaseUrl}", baseUrl);
            return (null, null, null);
        }

        public void CacheAuthentication(string baseUrl, string csrfToken, string applicationToken, string userToken)
        {
            var cacheKey = $"auth_{baseUrl}";
            var authResult = (csrfToken, applicationToken, userToken);

            _cache.Set(cacheKey, authResult, _cacheExpiry);
            _logger.LogDebug("Cached authentication for {BaseUrl} for {ExpiryMinutes} minutes", baseUrl, _cacheExpiry.TotalMinutes);
        }

        public void InvalidateCache(string baseUrl)
        {
            var cacheKey = $"auth_{baseUrl}";
            _cache.Remove(cacheKey);
            _logger.LogDebug("Invalidated authentication cache for {BaseUrl}", baseUrl);
        }
    }
}