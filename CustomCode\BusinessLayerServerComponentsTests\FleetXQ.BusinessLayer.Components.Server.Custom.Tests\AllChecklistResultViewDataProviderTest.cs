﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VDS.RDF;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    public class AllChecklistResultViewDataProviderTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"AllChecklistResultViewDataProviderTest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _siteId;
        private Guid _departmentId;
        private Guid _modelId;
        private Guid _driverId;
        private Guid _departmentChecklistId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();

            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);


            //create permission
            var permission = _serviceProvider.GetRequiredService<PermissionDataObject>();
            permission.Id = Guid.NewGuid();
            permission.Description = "Normal Driver";
            permission.LevelName = PermissionLevelEnum.NormalDriver;
            await _dataFacade.PermissionDataProvider.SaveAsync(permission);

            permission = _serviceProvider.GetRequiredService<PermissionDataObject>();
            permission.Id = Guid.NewGuid();
            permission.Description = "Master";
            permission.LevelName = PermissionLevelEnum.Master;
            await _dataFacade.PermissionDataProvider.SaveAsync(permission);
            //end

            //Create IOs
            var iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "0";
            iofields.Description = "ignition";
            iofields.IOType = " ";
            iofields.CANBUS = false;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "SEAT";
            iofields.Description = "Canbus Seat Switch Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);


            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "HYDL";
            iofields.Description = "Canbus Hydrolic Raising Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);

            iofields = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
            iofields.Id = Guid.NewGuid();
            iofields.Name = "TRACK";
            iofields.Description = "Canbus Traction/Movement Detection";
            iofields.IOType = " ";
            iofields.CANBUS = true;
            await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields);

            // Create a single site for test data
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = customer.Id;
            site.Name = "Test Site";
            site.TimezoneId = timeZone.Id;
            site.Id = Guid.NewGuid();
            _siteId = site.Id;

            await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create a single department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            _departmentId = department.Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create a single model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.DealerId = dealer.Id;
            model.Type = ModelTypesEnum.Electric;
            _modelId = model.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create a single person and driver
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.CustomerId = customer.Id;
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.IsDriver = true;
            person.IsActiveDriver = true;

            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Id = Guid.NewGuid();
            card.FacilityCode = "123";
            card.CardNumber = "123456";
            card.Active = true;
            card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
            card.Type = CardTypeEnum.CardID;

            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var driver = person.Driver;
            driver.CardDetailsId = card.Id;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);
            _driverId = driver.Id;

            // Create DepartmentChecklist
            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = department.Id;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);
            _departmentChecklistId = departmentChecklist.Id;

            // Create PreOperationalChecklist
            var questions = new string[] { "Do the brakes work properly ?", "Is the steering operating correctly?" };
            for (int i = 0; i < 2; i++)
            {
                var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                preOperationalChecklist.Id = Guid.NewGuid();
                preOperationalChecklist.SiteChecklistId = departmentChecklist.Id;
                preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
                preOperationalChecklist.Question = questions[i];
                preOperationalChecklist.ExpectedAnswer = true;
                preOperationalChecklist.Critical = true;
                preOperationalChecklist.Order = (short)(i + 1);
                await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist);
            }
        }

        /// <summary>
        /// Helper method to create a vehicle, module, and vehicleHireHistory for testing
        /// </summary>
        /// <param name="hireNo">The hire number for the vehicle</param>
        /// <param name="serialNo">The serial number for the vehicle</param>
        /// <param name="ioTDevice">The IoT device ID for the module</param>
        /// <returns>The created vehicle</returns>
        private async Task<VehicleDataObject> CreateTestVehicleAsync(string hireNo, string serialNo, string ioTDevice)
        {
            // Create a module for the vehicle
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID" + Guid.NewGuid().ToString().Substring(0, 4);
            module.FSSSBase = 150000;
            module.FSSXMulti = 1;
            module.IoTDevice = ioTDevice;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create the vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = _customerId;
            vehicle.SiteId = _siteId;
            vehicle.DepartmentId = _departmentId;
            vehicle.ModelId = _modelId;
            vehicle.IDLETimer = 300;
            vehicle.OnHire = true;
            vehicle.ImpactLockout = true;
            vehicle.HireNo = hireNo;
            vehicle.SerialNo = serialNo;
            vehicle.ModuleId1 = module.Id;
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Create VehicleHireDehireHistory
            var vehicleHireHistory = _serviceProvider.GetRequiredService<VehicleHireDehireHistoryDataObject>();
            vehicleHireHistory.Id = Guid.NewGuid();
            vehicleHireHistory.VehicleId = vehicle.Id;
            vehicleHireHistory.DepartmentId = _departmentId;

            // Set dates to ensure they fall within the stored procedure's default date range
            // Default range is: StartDate = 3 months ago, EndDate = today
            var now = DateTime.Now;
            vehicleHireHistory.HireTime = now.AddMonths(-2); // 2 months ago
            vehicleHireHistory.DehireTime = now.AddMonths(1); // 1 month in future
            vehicleHireHistory.DeviceIdWhenSaved = module.IoTDevice;
            vehicleHireHistory.SerialNoWhenSaved = vehicle.SerialNo;
            vehicleHireHistory.VehicleIdWhenSaved = vehicle.HireNo;
            await _dataFacade.VehicleHireDehireHistoryDataProvider.SaveAsync(vehicleHireHistory);

            return vehicle;
        }

        [Test]
        public async Task CheckComplete_ShouldBeFalse_WhenEndTimeIsNull()
        {
            // Arrange
            var now = DateTime.Now;
            var vehicle = await CreateTestVehicleAsync("VH_TEST1", "VS_TEST1", "test_00000001");

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = now.AddMonths(-2); // 2 months ago
            session.EndTime = null;
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = null;
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Create ChecklistDetail records
            var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preOperationalChecklist.Id = Guid.NewGuid();
            preOperationalChecklist.SiteChecklistId = _departmentChecklistId;
            preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preOperationalChecklist.Question = "Test Question";
            preOperationalChecklist.ExpectedAnswer = true;
            preOperationalChecklist.Critical = true;
            preOperationalChecklist.Order = 1;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist);

            var checklistDetail = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
            checklistDetail.Id = Guid.NewGuid();
            checklistDetail.ChecklistResultId = checklistResult.Id;
            checklistDetail.PreOperationalChecklistId = preOperationalChecklist.Id;
            checklistDetail.Answer = true;
            checklistDetail.Failed = false;
            await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetail);

            // Act
            var allChecklistResult = _serviceProvider.GetRequiredService<AllChecklistResultViewDataObject>();
            allChecklistResult.ChecklistResultId = checklistResult.Id;
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetAsync(allChecklistResult);

            // Assert
            Assert.That(result.CheckComplete, Is.EqualTo(false));
        }

        [Test]
        public async Task CheckComplete_ShouldBeFalse_WhenStartTimeEqualsEndTime()
        {
            // Arrange
            var now = DateTime.Now;
            var vehicle = await CreateTestVehicleAsync("VH_TEST2", "VS_TEST2", "test_00000002");

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = now.AddMonths(-2); // 2 months ago
            session.EndTime = session.StartTime; // Same as start time
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = session.StartTime; // Same as start time
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Create ChecklistDetail records
            var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preOperationalChecklist.Id = Guid.NewGuid();
            preOperationalChecklist.SiteChecklistId = _departmentChecklistId;
            preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preOperationalChecklist.Question = "Test Question";
            preOperationalChecklist.ExpectedAnswer = true;
            preOperationalChecklist.Critical = true;
            preOperationalChecklist.Order = 1;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist);

            var checklistDetail = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
            checklistDetail.Id = Guid.NewGuid();
            checklistDetail.ChecklistResultId = checklistResult.Id;
            checklistDetail.PreOperationalChecklistId = preOperationalChecklist.Id;
            checklistDetail.Answer = true;
            checklistDetail.Failed = false;
            await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetail);

            // Act
            var allChecklistResult = _serviceProvider.GetRequiredService<AllChecklistResultViewDataObject>();
            allChecklistResult.ChecklistResultId = checklistResult.Id;
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetAsync(allChecklistResult);

            // Assert
            Assert.That(result.CheckComplete, Is.EqualTo(false));
        }

        [Test]
        public async Task CheckComplete_ShouldBeTrue_WhenStartTimeIsBeforeEndTime()
        {
            // Arrange
            var now = DateTime.Now;
            var vehicle = await CreateTestVehicleAsync("VH_TEST3", "VS_TEST3", "test_00000003");

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = now.AddMonths(-2); // 2 months ago
            session.EndTime = now.AddMonths(-1); // 1 month ago
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = session.EndTime;
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Create ChecklistDetail records
            var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preOperationalChecklist.Id = Guid.NewGuid();
            preOperationalChecklist.SiteChecklistId = _departmentChecklistId;
            preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preOperationalChecklist.Question = "Test Question";
            preOperationalChecklist.ExpectedAnswer = true;
            preOperationalChecklist.Critical = true;
            preOperationalChecklist.Order = 1;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist);

            var checklistDetail = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
            checklistDetail.Id = Guid.NewGuid();
            checklistDetail.ChecklistResultId = checklistResult.Id;
            checklistDetail.PreOperationalChecklistId = preOperationalChecklist.Id;
            checklistDetail.Answer = true;
            checklistDetail.Failed = false;
            await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetail);

            // Act
            var allChecklistResult = _serviceProvider.GetRequiredService<AllChecklistResultViewDataObject>();
            allChecklistResult.ChecklistResultId = checklistResult.Id;
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetAsync(allChecklistResult);

            // Assert
            Assert.That(result.CheckComplete, Is.EqualTo(true));
        }

        [Test]
        public async Task GetCollectionAsync_ShouldFilterByDateRange()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync("VH_TEST5", "VS_TEST5", "test_00000005");

            var startDate = DateTime.Now.AddDays(-1);
            var endDate = DateTime.Now.AddDays(1);

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = DateTime.Now;
            session.EndTime = DateTime.Now.AddMinutes(5);
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = session.EndTime;
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Act
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetCollectionAsync(
                securityFilterExpression: null,
                filterPredicate: "StartDate = @0 AND EndDate = @1 AND ReturnTotalCount = @2",
                filterArguments: new object[] { startDate, endDate, true },
                orderByPredicate: null,
                pageNumber: 1,
                pageSize: 10,
                includes: null,
                context: null,
                parameters: null
            );
            var firstResult = result.First();

            // Assert
            Assert.That(firstResult.ChecklistResultId, Is.EqualTo(checklistResult.Id));
            Assert.That(firstResult.TimezoneAdjustedChecklistStartTime, Is.GreaterThanOrEqualTo(startDate));
            Assert.That(firstResult.TimezoneAdjustedChecklistStartTime, Is.LessThanOrEqualTo(endDate));
        }

        [Test]
        public async Task GetCollectionAsync_ShouldFilterByCustomer()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync("VH_TEST6", "VS_TEST6", "test_00000006");

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = DateTime.Now;
            session.EndTime = DateTime.Now.AddMinutes(5);
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = session.EndTime;
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Act
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetCollectionAsync(
                securityFilterExpression: null,
                filterPredicate: "CustomerId = @0 AND ReturnTotalCount = @1",
                filterArguments: new object[] { _customerId, true },
                orderByPredicate: null,
                pageNumber: 1,
                pageSize: 10,
                includes: null,
                context: null,
                parameters: null
            );
            var firstResult = result.First();

            // Assert
            Assert.That(firstResult.ChecklistResultId, Is.EqualTo(checklistResult.Id));
        }

        [Test]
        public async Task GetCollectionAsync_ShouldFilterByMultiSearch()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync("VH_TEST8", "VS_TEST8", "test_00000008");

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = DateTime.Now;
            session.EndTime = DateTime.Now.AddMinutes(5);
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = session.EndTime;
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Act
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetCollectionAsync(
                securityFilterExpression: null,
                filterPredicate: "MultiSearch = @0 AND ReturnTotalCount = @1",
                filterArguments: new object[] { "VH_TEST8", true },
                orderByPredicate: null,
                pageNumber: 1,
                pageSize: 10,
                includes: null,
                context: null,
                parameters: null
            );
            var firstResult = result.First();

            // Assert
            Assert.That(firstResult.ChecklistResultId, Is.EqualTo(checklistResult.Id));
        }

        [Test]
        public async Task GetCollectionAsync_ShouldHandleTimezoneOffset()
        {
            // Arrange
            var vehicle = await CreateTestVehicleAsync("VH_TEST9", "VS_TEST9", "test_00000009");

            var session = _serviceProvider.GetRequiredService<SessionDataObject>();
            session.Id = Guid.NewGuid();
            session.VehicleId = vehicle.Id;
            session.DriverId = _driverId;
            session.StartTime = DateTime.Now;
            session.EndTime = DateTime.Now.AddMinutes(5);
            session.isVOR = false;
            await _dataFacade.SessionDataProvider.SaveAsync(session);

            var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
            checklistResult.Id = Guid.NewGuid();
            checklistResult.SessionId1 = session.Id;
            checklistResult.StartTime = session.StartTime;
            checklistResult.EndTime = session.EndTime;
            await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

            // Act
            var result = await _dataFacade.AllChecklistResultViewDataProvider.GetCollectionAsync(
                securityFilterExpression: null,
                filterPredicate: "ReturnTotalCount = @0",
                filterArguments: new object[] { true },
                orderByPredicate: null,
                pageNumber: 1,
                pageSize: 10,
                includes: null,
                context: null,
                parameters: null
            );
            var firstResult = result.First();

            // Assert
            Assert.That(firstResult.ChecklistResultId, Is.EqualTo(checklistResult.Id));
            Assert.That(firstResult.TimezoneAdjustedChecklistStartTime, Is.Not.Null);
        }
    }
}
