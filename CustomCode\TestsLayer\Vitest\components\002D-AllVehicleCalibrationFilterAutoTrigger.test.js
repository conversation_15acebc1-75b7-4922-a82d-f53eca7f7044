import { describe, it, expect, vi, beforeEach } from 'vitest';
import '../../../WebApplicationLayer/wwwroot/ViewModels/AllVehicleCalibrationFilter/AllVehicleCalibrationFilterFormViewModel.custom';

describe('AllVehicleCalibrationFilterFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let customerIdSpy;

    beforeEach(() => {
        // Create spy for CustomerId
        customerIdSpy = vi.fn();

        // Mock the view model
        viewModel = {
            CustomerContextId: 'test-context',
            Customer_CompanyName: vi.fn(),
            Customer_lookupItem: vi.fn(),
            AllVehicleCalibrationFilterObject: vi.fn(() => ({
                Data: {
                    CustomerId: customerIdSpy
                }
            })),
            Commands: {
                FilterCommand: vi.fn()
            },
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            selectiveLoadDataForSite: vi.fn(),
            ShowError: vi.fn(),
            Modify: vi.fn(),
            subscribeToMessages: vi.fn()
        };

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn()
                }
            }
        };

        // Mock window.location
        global.window = {
            location: {
                href: ''
            }
        };

        // Mock setTimeout to execute immediately
        const originalSetTimeout = global.setTimeout;
        global.setTimeout = (fn) => fn();

        customViewModel = new FleetXQ.Web.ViewModels.AllVehicleCalibrationFilterFormViewModelCustom(viewModel);

        // Restore original setTimeout
        global.setTimeout = originalSetTimeout;
    });

    describe('initialize', () => {
        it('should not trigger filter for non-customer users', () => {
            // Mock non-customer user
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: ['Admin'],
                CustomerId: null
            });

            customViewModel.initialize();
            expect(viewModel.DataStoreCustomer.LoadObject).not.toHaveBeenCalled();
        });

        it('should trigger filter for customer users', () => {
            // Mock customer user
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                role: ['Customer'],
                CustomerId: '123'
            });

            // Mock customer data response
            const mockCustomerData = {
                Data: {
                    CompanyName: vi.fn(() => 'Test Company'),
                    Id: vi.fn(() => '123')
                }
            };

            viewModel.DataStoreCustomer.LoadObject.mockImplementation((config) => {
                config.successHandler(mockCustomerData);
            });

            // Mock setTimeout to execute immediately
            const originalSetTimeout = global.setTimeout;
            global.setTimeout = (fn) => fn();

            customViewModel.initialize();

            // Verify customer data was loaded
            expect(viewModel.DataStoreCustomer.LoadObject).toHaveBeenCalledWith(expect.objectContaining({
                contextId: 'test-context',
                pks: { Id: '123' }
            }));

            // Verify customer data was set
            expect(viewModel.Customer_CompanyName).toHaveBeenCalledWith('Test Company');
            expect(viewModel.Customer_lookupItem).toHaveBeenCalledWith(expect.objectContaining({
                label: 'Test Company',
                value: mockCustomerData,
                selectable: true
            }));
            expect(customerIdSpy).toHaveBeenCalledWith('123');

            // Verify filter command was triggered
            expect(viewModel.Commands.FilterCommand).toHaveBeenCalled();

            // Restore original setTimeout
            global.setTimeout = originalSetTimeout;
        });
    });
});
