Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Local Pipeline Test for VehicleAccessProcessor" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Set variables (similar to pipeline variables)
$buildConfiguration = "Release"
$outputPath = "bin\publish"

try {
    Write-Host "`n[Step 1] Checking .NET SDK version..." -ForegroundColor Yellow
    $dotnetVersion = dotnet --version
    Write-Host "✅ .NET SDK Version: $dotnetVersion" -ForegroundColor Green

    Write-Host "`n[Step 2] Restoring NuGet packages..." -ForegroundColor Yellow
    $restoreResult = dotnet restore VehicleAccessProcessor.csproj
    if ($LASTEXITCODE -ne 0) {
        throw "NuGet restore failed"
    }
    Write-Host "✅ NuGet packages restored successfully" -ForegroundColor Green

    Write-Host "`n[Step 3] Building Azure Function..." -ForegroundColor Yellow
    $buildResult = dotnet build VehicleAccessProcessor.csproj --configuration $buildConfiguration --no-restore
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✅ Build completed successfully" -ForegroundColor Green

    Write-Host "`n[Step 4] Publishing Azure Function..." -ForegroundColor Yellow
    $publishResult = dotnet publish VehicleAccessProcessor.csproj --configuration $buildConfiguration --output $outputPath
    if ($LASTEXITCODE -ne 0) {
        throw "Publish failed"
    }
    Write-Host "✅ Publish completed successfully" -ForegroundColor Green

    Write-Host "`n[Step 5] Checking published files..." -ForegroundColor Yellow
    if (Test-Path $outputPath) {
        Write-Host "✅ Published files:" -ForegroundColor Green
        Get-ChildItem $outputPath | Select-Object Name, Length | Format-Table -AutoSize
        
        # Check for essential files
        $essentialFiles = @("VehicleAccessProcessor.dll", "host.json", "local.settings.json")
        foreach ($file in $essentialFiles) {
            if (Test-Path (Join-Path $outputPath $file)) {
                Write-Host "  ✅ $file found" -ForegroundColor Green
            } else {
                Write-Host "  ⚠️ $file not found" -ForegroundColor Yellow
            }
        }
    } else {
        throw "Output directory not found"
    }

    Write-Host "`n[Step 6] Local testing options..." -ForegroundColor Yellow
    Write-Host "You can now test the function locally using:" -ForegroundColor White
    Write-Host "  1. Azure Functions Core Tools:" -ForegroundColor Cyan
    Write-Host "     func start --port 7071" -ForegroundColor Gray
    Write-Host "  2. Test health check:" -ForegroundColor Cyan
    Write-Host "     curl http://localhost:7071/api/HealthCheck" -ForegroundColor Gray
    Write-Host "  3. Or use Postman/browser to test endpoints" -ForegroundColor Cyan

    Write-Host "`n========================================" -ForegroundColor Cyan
    Write-Host "✅ Local pipeline test completed successfully!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan

} catch {
    Write-Host "`n❌ Pipeline test failed: $_" -ForegroundColor Red
    Write-Host "========================================" -ForegroundColor Red
    exit 1
} 