import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('DepartmentGridViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Mock window.location
        global.window = {
            location: {
                reload: vi.fn(),
                hash: ''
            }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Messages: {
                    confirmDeleteMessage: 'Are you sure you want to delete %ENTITY%?',
                    confirmDeletePopupTitle: 'Confirm Delete'
                }
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Department/DepartmentGridViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        HasCustomersAccess: 'True',
                        CanCreateDepartment: 'True'
                    })
                }
            },
            getProxyForComponent: vi.fn().mockReturnValue({
                SoftDelete: vi.fn()
            })
        };

        // Create base view model with required properties
        viewModel = {
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            onDeleteSuccess: vi.fn(),
            selectedObject: ko.observable({
                Data: {
                    Id: ko.observable('123')
                }
            }),
            controller: {
                applicationController: {
                    showConfirmPopup: vi.fn()
                }
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.DepartmentGridViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    it('should set base filter to exclude deleted records', () => {
        // Verify that the base filter is set to exclude deleted records
        expect(viewModel.baseFilterPredicate).toBe('DeletedAtUtc == null');
        expect(viewModel.baseFilterParameters).toBeNull();
        expect(viewModel.baseFilterParametersCount).toBe(0);
    });

    it('should override delete function with confirmation popup', () => {
        it('should show confirmation popup when Delete is called', () => {
            viewModel.Delete();
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                FleetXQ.Web.Messages.confirmDeleteMessage.replace(/%ENTITY%/g, "Department"),
                FleetXQ.Web.Messages.confirmDeletePopupTitle,
                expect.any(Function),
                viewModel.contextId
            );
        });

        it('should call SoftDelete when confirmation is accepted', () => {
            const departmentAPI = global.ApplicationController.getProxyForComponent("DepartmentAPI");
            let capturedSuccessHandler;
            departmentAPI.SoftDelete = vi.fn((config) => {
                capturedSuccessHandler = config.successHandler;
            });
            // Simulate confirmation
            viewModel.onConfirmDelete(true);
            expect(departmentAPI.SoftDelete).toHaveBeenCalledWith({
                contextId: viewModel.contextId,
                successHandler: viewModel.onDeleteSuccess,
                errorHandler: viewModel.ShowError,
                departmentId: '123',
                viewmodel: viewModel
            });
        });

        it('should not call SoftDelete when confirmation is rejected', () => {
            const departmentAPI = global.ApplicationController.getProxyForComponent("DepartmentAPI");
            departmentAPI.SoftDelete = vi.fn();
            // Simulate rejection
            viewModel.onConfirmDelete(false);
            expect(departmentAPI.SoftDelete).not.toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });
    });
}); 