﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom;

public class VehicleExportComponentExtension : IImportExportComponentExtension<VehicleExportSection0Component,
        VehicleDataObject>
{
    private readonly IDataFacade dataFacade;
    private readonly IAuthentication authentication;

    public VehicleExportComponentExtension(IDataFacade dataFacade, IAuthentication authentication)
    {
        this.dataFacade = dataFacade;
        this.authentication = authentication;
    }

    public void Init(IImportExportComponent<VehicleDataObject> importExportComponent)
    {
        importExportComponent.OnAfterExportDataRowAsync += ImportExportComponent_OnAfterExportDataRowAsync;
    }

    private async Task ImportExportComponent_OnAfterExportDataRowAsync(OnAfterExportDataRowEventArgs<VehicleDataObject> arg)
    {
        if (arg.Entity.Module == null)
        {
            return;
        }

        var userClaims = await authentication.GetCurrentUserClaimsAsync();

        var callingUser = await dataFacade.GOUserDataProvider.GetAsync(new GOUserDataObject(userClaims.UserId.Value), includes: new List<string> { "Person", "Person.Customer" });

        var preferredLocale = callingUser.PreferredLocale != null ? callingUser.PreferredLocale.Value : callingUser.Person?.Customer?.PreferredLocale;

        if (!preferredLocale.HasValue)
        {
            return;
        }

        // Validate if the locale is valid
        try
        {
            var culture = new CultureInfo(DataUtils.GetLocaleString(preferredLocale.Value));

            // Format the datetime using the preferred locale
            if (arg.Entity.Module.CCIDUpdateDateTime.HasValue)
            {
                arg.DataRow[VehicleExportSection0Component.COL_CCIDUPDATEDATE] =
                    arg.Entity.Module.CCIDUpdateDateTime.Value.ToString(culture);
            }
        }
        catch (CultureNotFoundException)
        {
            // If the culture is invalid, just return without modifying the datetime
            return;
        }
    }
}
