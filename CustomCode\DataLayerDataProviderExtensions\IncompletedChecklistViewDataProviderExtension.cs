using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Feature.Security.Common;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Threading.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.IdentityModel.Tokens;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class IncompletedChecklistViewDataProviderExtension : IDataProviderExtension<IncompletedChecklistViewDataObject>
    {
        private readonly IAuthentication _authentication;
        private readonly IDataFacade _dataFacade;

        public IncompletedChecklistViewDataProviderExtension(IAuthentication authentication, IDataFacade dataFacade)
        {
            _authentication = authentication;
            _dataFacade = dataFacade;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnAfterGetCollection += DataProvider_OnAfterGetCollection;
        }

        private async Task DataProvider_OnAfterGetCollection(OnAfterGetCollectionEventArgs arg)
        {
            // Only process if the result is a collection of IncompletedChecklistViewDataObject
            if (arg.Result is DataObjectCollection<IncompletedChecklistViewDataObject> items)
            {
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();

                if (userClaims == null || userClaims.UserId == null)
                {
                    return;
                }

                var appUserClaims = userClaims as AppUserClaims;

                var preferredLocale = appUserClaims.UserPreferredLocale != null ? appUserClaims.UserPreferredLocale : appUserClaims.CustomerPreferredLocale;

                foreach (var item in items)
                {
                    var culture = !string.IsNullOrEmpty(preferredLocale) ? new CultureInfo(preferredLocale) : new CultureInfo("en-US");

                    try
                    {
                        var dateString = $"{item.Month}/{item.Day}/{item.Year}";
                        if (DateTime.TryParse(dateString, new CultureInfo("en-US"), DateTimeStyles.None, out var parsedDate))
                        {
                            item.DateDisplay = parsedDate.ToString(culture.DateTimeFormat.ShortDatePattern, culture);
                        }
                        else
                        {
                            // Handle invalid date gracefully
                            item.DateDisplay = item.Date; // Or some default value
                        }
                    }
                    catch (Exception ex)
                    {
                        // Log the exception or handle it as needed
                        item.DateDisplay = item.Date; // Or some default value
                    }
                }
            }
        }
    }
} 