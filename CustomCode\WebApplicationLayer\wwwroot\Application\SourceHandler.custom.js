﻿(function (global) {
    FleetXQ.Web.Application.SourceHandlerCustom = function () {
        var self = this;

        this.globalRequiredSources = [];

        this.requiredSourcesByElement = {
            "CustomerItemsPage-Page": [
                "/Model/Components/CustomerAPIProxy.js"
            ],
            "PersonDetailPagePage-Page": [
                "/Model/Components/VehicleAccessUtilitiesProxy.js",
                "/ViewModels/PersonToDepartmentVehicleNormalAccessView/Filters/PersonToDepartmentVehicleNormalAccessViewFilterViewModel.js",
                "/ViewModels/PersonToModelVehicleNormalAccessView/Filters/PersonToModelVehicleNormalAccessViewFilterViewModel.js",
                "/ViewModels/PersonToSiteVehicleNormalAccessView/Filters/PersonToSiteVehicleNormalAccessViewFilterViewModel.js",
                "/ViewModels/PersonToPerVehicleNormalAccessView/Filters/PersonToPerVehicleNormalAccessViewFilterViewModel.js",
            ],
            "PersonItemsPage-Page": [
                "/Model/DataObjects/SiteObject.js",
                "/Model/DataObjectValidators/SiteObjectValidator.js",
                "/Model/DataSets/SiteDataSet.js",
                "/Model/DataObjects/DepartmentObject.js",
                "/Model/DataObjectValidators/DepartmentObjectValidator.js",
                "/Model/DataSets/DepartmentDataSet.js",
            ],
            "CurrentStatusReportPage-Page": [
                "/Model/Components/IoTHubManagerProxy.js",
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "SynchronizationStatusReportPage-Page": [
                "/Model/Components/IoTHubManagerProxy.js",
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "UploadFileForm-Form": [
                "/Model/Components/VehicleImportComponentProxy.js",
                "/Model/Components/PersonImportComponentProxy.js",
                "/Model/Components/UpdateVehicleLastServiceDateImportComponentProxy.js",
            ],
            "SubscribeForm-Form": [
                "/Model/Components/ReportActionsAPIProxy.js",
            ],
            "SendReportForm-Form": [
                "/Model/Components/ReportActionsAPIProxy.js",
            ],
            "UpdateLastServiceDateForm-Form": [
                "/Model/Components/UpdateVehicleLastServiceDateImportComponentProxy.js",
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js"
            ],
            "VehicleHireDehireForm-Form": [
                "/Model/Components/CustomerUtilitiesProxy.js",
            ],
            "ImpactReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "GeneralProductivityReportPagePage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "PreOpCheckReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "MachineUnlockReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "ProficiencyReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "ServiceCheckReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "DriverAccessAbuseReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "LicenseExpiryReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "VORReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "VehicleCalibrationReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
            ],
            "BroadcastMessageReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
                "/Model/Components/BroadcastMessageHistoryExportComponentProxy.js"
            ],
            "PedestrianDetectionReportPage-Page": [
                "/Model/Components/FileDownloader.js",
                "/Model/Components/FileDownloaderProxy.js",
                "/Model/Components/PedestrianDetectionReportExportComponentProxy.js"
            ],
            "DealerPage-Page": [
                "/Model/Components/CustomerAPIProxy.js"
            ]
        };

    }
}());