using DocumentFormat.OpenXml.Office2010.Drawing;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.CsvImportExport;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using GenerativeObjects.Practices.Logging;
using System.Collections.Generic;
using FleetXQ.Data.DataProviders.Database;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using NHibernate;
using System.Threading;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public static class DataRowExtensions
    {
        public static string GetValue(this DataRow row, string columnName)
        {
            return row[columnName]?.ToString();
        }

        public static void AddError(this DataRow row, string message)
        {
            row.AddError(message);
        }
    }

    public class VehicleAccessImportComponentExtension : IImportExportComponentExtension<VehicleAccessImportSection0Component, PersonDataObject>
    {
        private readonly IDataFacade dataFacade;
        private readonly IServiceProvider serviceProvider;
        private readonly ISession session;
        private readonly object _permissionLock = new object();
        private Guid? _cachedPermissionId;
        private readonly SemaphoreSlim _permissionSemaphore = new SemaphoreSlim(1, 1);
        private bool _isInitialized = false;
        private static readonly SemaphoreSlim _importSemaphore = new SemaphoreSlim(1, 1);

        public VehicleAccessImportComponentExtension(
            IDataFacade dataFacade,
            IServiceProvider serviceProvider
        )
        {
            this.dataFacade = dataFacade;
            this.serviceProvider = serviceProvider;
            this.session = serviceProvider.GetService<ISession>();
        }

        public void Init(IImportExportComponent<PersonDataObject> importExportComponent)
        {
            if (_isInitialized) return;

            importExportComponent.OnAfterImportDataRowAsync -= OnAfterImportDataRowAsync;
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;

            _isInitialized = true;
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<PersonDataObject> arg)
        {
            await _importSemaphore.WaitAsync();
            try
            {
                await FileLogger.LogAsync("=== Starting import process ===");

                try
                {
                    // 1. Get person from firstName and lastName
                    var firstName = arg.DataRow["First Name"]?.ToString()?.Trim();
                    var lastName = arg.DataRow["Last Name"]?.ToString()?.Trim();
                    var siteName = arg.DataRow["Site"]?.ToString()?.Trim();

                    await FileLogger.LogAsync($"Looking for person: {firstName} {lastName}, Site: {siteName}");

                    // Get site first if we need to filter by it
                    PersonDataObject person = null;
                    SiteDataObject site = null;

                    // Get the site (required)
                    site = (await dataFacade.SiteDataProvider.GetCollectionAsync(
                        null,
                        "Name == @0",
                        new object[] { siteName },
                        null, 0, 0, null, null, null, true
                    )).FirstOrDefault();

                    if (site == null)
                        throw new Exception($"Site not found: {siteName}");

                    await FileLogger.LogAsync($"Found site with ID: {site.Id}, Name: {siteName}");

                    // Try to find person by name and site
                    var potentialMatches = await dataFacade.PersonDataProvider.GetCollectionAsync(
                        null,
                        "FirstName == @0 AND LastName == @1",
                        new object[] { firstName, lastName },
                        null, 0, 0, null, null, null, true
                    );

                    // Log all potential matches to help with debugging
                    if (potentialMatches.Count > 1)
                    {
                        await FileLogger.LogAsync($"Found {potentialMatches.Count} people with name {firstName} {lastName}");
                        foreach (var match in potentialMatches)
                        {
                            await FileLogger.LogAsync($"  Person ID: {match.Id}, SiteId: {match.SiteId}");
                        }
                    }

                    // First try to find person by name AND site (most specific match)
                    person = potentialMatches.FirstOrDefault(p => p.SiteId == site.Id);

                    if (person != null)
                    {
                        await FileLogger.LogAsync($"Found person with ID: {person.Id} matched by name and site");
                    }
                    else if (potentialMatches.Count == 1)
                    {
                        // If exactly one match found but at a different site, use that person
                        person = potentialMatches.First();
                        await FileLogger.LogAsync($"Found single person with ID: {person.Id} matched by name only (no site match)");
                    }
                    else if (potentialMatches.Count > 1)
                    {
                        // If multiple matches and none at the current site, this is ambiguous
                        throw new Exception($"Multiple people found with name {firstName} {lastName} but none at site '{siteName}'. Please specify which person by ensuring they are assigned to the correct site.");
                    }

                    if (person == null)
                    {
                        throw new Exception($"Person not found: {firstName} {lastName}");
                    }

                    await FileLogger.LogAsync($"Found person with ID: {person.Id}");

                    // 2. Get Driver from person
                    var driver = (await dataFacade.DriverDataProvider.GetCollectionAsync(
                        null,
                        "Id == @0",
                        new object[] { person.DriverId },
                        null, 0, 0, null, null, null, true
                    )).FirstOrDefault();

                    if (driver == null)
                    {
                        throw new Exception($"No driver record found for person: {firstName} {lastName}");
                    }

                    await FileLogger.LogAsync($"Found driver with ID: {driver.Id}");

                    // 3. Get CardId from driver
                    if (!driver.CardDetailsId.HasValue)
                    {
                        throw new Exception($"No card details found for driver: {firstName} {lastName}");
                    }
                    var cardId = driver.CardDetailsId.Value;
                    await FileLogger.LogAsync($"Found card ID: {cardId}");

                    // 4. Get NormalDriver permission ID
                    var normalPermissionId = await GetDefaultPermissionId();

                    // 5. Update access
                    var siteAccess = (await dataFacade.SiteVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                        null,
                        "CardId = @0 AND SiteId = @1",
                        new object[] { cardId, site.Id },
                        null, 0, 0, null, null, null, true
                    ))?.FirstOrDefault();

                    if (siteAccess == null)
                    {
                        siteAccess = new SiteVehicleNormalCardAccessDataObject(serviceProvider)
                        {
                            CardId = cardId,
                            SiteId = site.Id,
                            PermissionId = normalPermissionId
                        };
                    }
                    else
                    {
                        siteAccess.PermissionId = normalPermissionId;
                    }
                    await dataFacade.SiteVehicleNormalCardAccessDataProvider.SaveAsync(siteAccess, skipSecurity: true);
                    await FileLogger.LogAsync($"Updated site access for {siteName}");

                    // Variables to hold department and model for later use
                    DepartmentDataObject department = null;
                    ModelDataObject model = null;

                    // 7. Get Department
                    var departmentName = arg.DataRow.GetValue("Department")?.Trim();

                    if (string.IsNullOrWhiteSpace(departmentName))
                    {
                        await FileLogger.LogAsync("Department is empty, skipping department access");
                    }
                    else
                    {
                        await FileLogger.LogAsync($"Searching for department: '{departmentName}'");

                        department = (await dataFacade.DepartmentDataProvider.GetCollectionAsync(
                            null,
                            "Name = @0 AND SiteId = @1",
                            new object[] { departmentName, site.Id },
                            null, 0, 0, null, null, null, true
                        )).FirstOrDefault();

                        if (department == null)
                        {
                            await FileLogger.LogAsync($"Warning: Department not found: '{departmentName}', skipping department access");
                        }
                        else
                        {
                            await FileLogger.LogAsync($"Found department with ID: {department.Id}, Name: {department.Name}");

                            // 8. Update department access
                            var departmentAccess = (await dataFacade.DepartmentVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                                null,
                                "CardId = @0 AND DepartmentId = @1",
                                new object[] { cardId, department.Id },
                                null, 0, 0, null, null, null, true
                            ))?.FirstOrDefault();

                            if (departmentAccess == null)
                            {
                                departmentAccess = new DepartmentVehicleNormalCardAccessDataObject(serviceProvider)
                                {
                                    CardId = cardId,
                                    DepartmentId = department.Id,
                                    PermissionId = normalPermissionId
                                };
                            }
                            else
                            {
                                departmentAccess.PermissionId = normalPermissionId;
                            }
                            await dataFacade.DepartmentVehicleNormalCardAccessDataProvider.SaveAsync(departmentAccess, skipSecurity: true);
                            await FileLogger.LogAsync($"Updated department access for {department.Name}");
                        }
                    }

                    // Category (Model) access
                    var categoryName = arg.DataRow.GetValue("Category")?.Trim();

                    if (string.IsNullOrWhiteSpace(categoryName))
                    {
                        await FileLogger.LogAsync("Category is empty, skipping category access");
                    }
                    else if (department == null)
                    {
                        await FileLogger.LogAsync($"Found category name '{categoryName}' but no valid department found. Skipping category access.");
                    }
                    else
                    {
                        model = (await dataFacade.ModelDataProvider.GetCollectionAsync(
                            null,
                            "Name = @0",
                            new object[] { categoryName },
                            null, 0, 0, null, null, null, true
                        )).FirstOrDefault();

                        if (model == null)
                        {
                            await FileLogger.LogAsync($"Warning: Category not found: '{categoryName}', skipping category access");
                        }
                        else
                        {
                            await FileLogger.LogAsync($"Found category with ID: {model.Id}, Name: {model.Name}");

                            var modelAccess = (await dataFacade.ModelVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                                null,
                                "CardId = @0 AND ModelId = @1 AND DepartmentId = @2",
                                new object[] { cardId, model.Id, department.Id },
                                null, 0, 0, null, null, null, true
                            ))?.FirstOrDefault();

                            if (modelAccess == null)
                            {
                                modelAccess = new ModelVehicleNormalCardAccessDataObject(serviceProvider)
                                {
                                    CardId = cardId,
                                    ModelId = model.Id,
                                    DepartmentId = department.Id,
                                    PermissionId = normalPermissionId
                                };
                            }
                            else
                            {
                                modelAccess.PermissionId = normalPermissionId;
                            }
                            await dataFacade.ModelVehicleNormalCardAccessDataProvider.SaveAsync(modelAccess, skipSecurity: true);
                            await FileLogger.LogAsync($"Updated model access for {categoryName}");
                        }
                    }

                    // Vehicle access
                    var vehicleId = arg.DataRow.GetValue("Vehicle ID")?.Trim();

                    if (string.IsNullOrWhiteSpace(vehicleId))
                    {
                        await FileLogger.LogAsync("Vehicle ID is empty, skipping vehicle access");
                    }
                    else
                    {
                        // Try to find vehicle by HireNo or other identifiers
                        var vehicle = (await dataFacade.VehicleDataProvider.GetCollectionAsync(
                            null,
                            "HireNo = @0",
                            new object[] { vehicleId },
                            null, 0, 0, null, null, null, true
                        )).FirstOrDefault();

                        if (vehicle == null)
                        {
                            await FileLogger.LogAsync($"Warning: Vehicle not found with ID: {vehicleId}, skipping vehicle access");
                        }
                        else
                        {
                            await FileLogger.LogAsync($"Found vehicle with ID: {vehicle.Id}, HireNo: {vehicleId}");

                            try
                            {
                                var vehicleAccess = (await dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(
                                    null,
                                    "CardId = @0 AND VehicleId = @1",
                                    new object[] { cardId, vehicle.Id },
                                    null, 0, 0, null, null, null, true
                                ))?.FirstOrDefault();

                                if (vehicleAccess == null)
                                {
                                    vehicleAccess = new PerVehicleNormalCardAccessDataObject(serviceProvider)
                                    {
                                        CardId = cardId,
                                        VehicleId = vehicle.Id,
                                        PermissionId = normalPermissionId
                                    };
                                }
                                else
                                {
                                    vehicleAccess.PermissionId = normalPermissionId;
                                }
                                await dataFacade.PerVehicleNormalCardAccessDataProvider.SaveAsync(vehicleAccess, skipSecurity: true);
                                await FileLogger.LogAsync($"Updated vehicle access for {vehicleId}");
                            }
                            catch (Exception ex)
                            {
                                await FileLogger.LogAsync($"ERROR in vehicle access: {ex.Message}");
                                if (ex.InnerException != null)
                                    await FileLogger.LogAsync($"Inner exception: {ex.InnerException.Message}");
                                // Log but don't throw to allow processing to continue
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    await FileLogger.LogAsync($"ERROR: {ex.Message}");
                    if (ex.InnerException != null)
                        await FileLogger.LogAsync($"Inner exception: {ex.InnerException.Message}");
                    arg.DataRow.AddError(ex.Message);
                    throw;
                }
            }
            finally
            {
                _importSemaphore.Release();
            }
        }

        private async Task<Guid> GetDefaultPermissionId()
        {
            var permission = (await dataFacade.PermissionDataProvider.GetCollectionAsync(
                null,
                "LevelName = @0",
                new object[] { 3 },
                null, 0, 0, null, null, null, true
            )).FirstOrDefault();

            if (permission == null)
                throw new Exception("Permission level 3 not found");

            return permission.Id;
        }

        private static class FileLogger
        {
            private static readonly string LogPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                "VehicleAccessImport.log"
            );
            private static readonly object _lock = new object();

            public static async Task LogAsync(string message)
            {
                var logMessage = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}{Environment.NewLine}";
                var retryCount = 3;
                while (retryCount > 0)
                {
                    try
                    {
                        await File.AppendAllTextAsync(LogPath, logMessage);
                        break;
                    }
                    catch (IOException)
                    {
                        retryCount--;
                        if (retryCount == 0) throw;
                        await Task.Delay(100); // Wait before retry
                    }
                }
            }
        }
    }
}
