const { defineConfig } = require("cypress");

module.exports = defineConfig({
    e2e: {
        setupNodeEvents(on, config) {
            // implement node event listeners here
            on('before:spec', (spec) => {
                console.log('Running:', spec.name);
            });
        },
        //baseUrl: 'https://godev.collectiveintelligence.com.au/FleetXQ-8735218d-3aeb-4563-bccb-8cdfcdf1188f',
        baseUrl: 'https://localhost:53052', // Add your base URL here
        // Configure test file order - these will run in the specified order
        specPattern: [
            'cypress/e2e/**/*.cy.js',
        ],
        // Exclude ignored folders
        excludeSpecPattern: [
            '**/ignored/**/*.cy.js',
            '**/skip/**/*.cy.js',
            '**/disabled/**/*.cy.js',
            '**/temp/**/*.cy.js',
            '**/example-usage.cy.js'
        ],
        // Increase timeout for waiting on elements to appear
        defaultCommandTimeout: 8000,
        // Add additional configuration
        viewportWidth: 1280,
        viewportHeight: 800,
        video: true,
        screenshotOnRunFailure: true,
        watchForFileChanges: false,
    },
});