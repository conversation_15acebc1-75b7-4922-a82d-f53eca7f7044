import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('PersonGridViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Mock window.location
        global.window = {
            location: {
                reload: vi.fn(),
                hash: ''
            }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {},
                Messages: {
                    confirmDeleteMessage: 'Are you sure you want to delete %ENTITY%?',
                    confirmDeletePopupTitle: 'Confirm Delete'
                }
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();
        global.console.log = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Person/PersonGridViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        const getProxyForComponentMock = vi.fn().mockReturnValue({
            SoftDelete: vi.fn()
        });
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        HasUsersAccess: 'True',
                        CanCreateUser: 'True',
                        CanExportUsers: 'True'
                    })
                }
            },
            getProxyForComponent: getProxyForComponentMock
        };

        // Create base view model with required properties
        viewModel = {
            contextId: 'test-context',
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            onDeleteSuccess: vi.fn(),
            selectedObject: ko.observable({
                Data: {
                    Id: ko.observable('123')
                },
                getDriver: vi.fn()
            }),
            PersonFilterViewModel: {
                filterData: {
                    fields: {
                        CustomerValue: ko.observable({ value: { Data: { Id: ko.observable('cust123') } } }),
                        SiteValue: ko.observable({ value: { Data: { Id: ko.observable('site123') } } }),
                        DepartmentValue: ko.observable({ value: { Data: { Id: ko.observable('dept123') } } })
                    }
                }
            },
            commands: {},
            controller: {
                applicationController: {
                    showConfirmPopup: vi.fn()
                }
            }
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.PersonGridViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    it('should set base filter to exclude deleted records', () => {
        // Verify that the base filter is set to exclude deleted records
        expect(viewModel.baseFilterPredicate).toBe('DeletedAtUtc == null');
        expect(viewModel.baseFilterParameters).toBeNull();
        expect(viewModel.baseFilterParametersCount).toBe(0);
    });

    it('should override AddNewUserNewWindow function', () => {
        it('should store filter values and navigate to user detail', () => {
            viewModel.AddNewUserNewWindow();
            expect(sessionStorageData['personFilterValues']).toBeDefined();
            expect(window.location.hash).toBe('!/UserManagement/UserDetail');
            expect(window.location.reload).toHaveBeenCalled();
        });
    });

    describe('Delete functionality', () => {
        it('should show confirmation popup when Delete is called', () => {
            viewModel.Delete();
            expect(viewModel.controller.applicationController.showConfirmPopup).toHaveBeenCalledWith(
                viewModel,
                FleetXQ.Web.Messages.confirmDeleteMessage.replace(/%ENTITY%/g, "User"),
                FleetXQ.Web.Messages.confirmDeletePopupTitle,
                expect.any(Function),
                viewModel.contextId
            );
        });

        it('should call SoftDelete when confirmation is accepted', () => {
            const personAPI = global.ApplicationController.getProxyForComponent("PersonAPI");
            let capturedSuccessHandler;
            personAPI.SoftDelete = vi.fn((config) => {
                capturedSuccessHandler = config.successHandler;
            });
            // Simulate confirmation
            viewModel.onConfirmDelete(true);
            expect(personAPI.SoftDelete).toHaveBeenCalledWith({
                contextId: viewModel.contextId,
                successHandler: viewModel.onDeleteSuccess,
                errorHandler: viewModel.ShowError,
                personId: '123',
                viewmodel: viewModel
            });
        });

        it('should not call SoftDelete when confirmation is rejected', () => {
            const personAPI = global.ApplicationController.getProxyForComponent("PersonAPI");
            personAPI.SoftDelete = vi.fn();
            // Simulate rejection
            viewModel.onConfirmDelete(false);
            expect(personAPI.SoftDelete).not.toHaveBeenCalled();
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(false);
        });
    });

    describe('IsShowCopyDriverAccessSettingsPopupCommandVisible', () => {
        it('should return false when no person is selected', () => {
            viewModel.selectedObject(null);
            expect(customViewModel.IsShowCopyDriverAccessSettingsPopupCommandVisible()).toBe(false);
        });

        it('should return false when person has no driver', () => {
            viewModel.selectedObject().getDriver.mockReturnValue(null);
            expect(customViewModel.IsShowCopyDriverAccessSettingsPopupCommandVisible()).toBe(false);
        });

        it('should return false when driver has no card', () => {
            viewModel.selectedObject().getDriver.mockReturnValue({
                Data: {
                    Card: () => null
                }
            });
            expect(customViewModel.IsShowCopyDriverAccessSettingsPopupCommandVisible()).toBe(false);
        });

        it('should return true when driver has active card', () => {
            viewModel.selectedObject().getDriver.mockReturnValue({
                Data: {
                    Card: () => ({
                        Data: {
                            Active: () => true
                        }
                    })
                }
            });
            expect(customViewModel.IsShowCopyDriverAccessSettingsPopupCommandVisible()).toBe(true);
        });
    });
}); 