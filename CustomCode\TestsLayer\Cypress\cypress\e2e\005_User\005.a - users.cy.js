describe("005 - Users Flow", () => {
    let testFirstName;
    let testLastName;
    let uniqueSiteName;
    let cypressCompanyName;
    let uniqueDepartmentName;
    before(() => {
        cy.fixture('testData').then((testData) => {
            testFirstName = testData.cypressFirstName + Math.floor(Math.random() * 1000000);
            testLastName = testData.cypressLastName;
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            cypressCompanyName = testData.cypressCompanyName;
            uniqueDepartmentName = testData.uniqueDepartmentName;
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login()
        
         // Navigate to the users section
         cy.get(`[data-bind="'enable' : navigation.isUserManagementEnabled(), 'visible' : navigation.isUserManagementVisible()"] > .nav-link`).click();
         cy.wait(1000);

          // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/customer/list*').as('getCustomerList');

    });

    it("should create a new user", () => {
       // Click on the new user button
       cy.get('.topGridCommands > :nth-child(1) > .command-button')
            .should('be.visible')
            .click();

        cy.wait(3000);

        cy.wait('@getCustomerList').then((interception) => {
            cy.log('Customer list API completed:', interception);
        });
        // Fill in the form fields using reliable selectors
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsCustomerVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(cypressCompanyName, { force: true });

        cy.wait(1000);
        // select the first option 
        cy.get('[data-test-id="lookup_wrapper"] [data-test-id="lookup_item"]')
            .first()
            .should('be.visible')
            .click();


        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsSiteVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueSiteName, { force: true });

        // Wait for dropdown to appear and be ready
        cy.wait(2000);
        
        // Force Cypress to find the site dropdown item, even if it's detached from DOM
        cy.get('body').then($body => {
            // Use jQuery to find the dropdown item containing the site name text
            const $siteOption = $body.find(`[data-test-id="lookup_item"]:contains("${uniqueSiteName}")`);
            
            if ($siteOption.length) {
                // Use cy.wrap to get a Cypress-wrapped element and click it with force option
                cy.wrap($siteOption).first().click({ force: true });
            } else {
                // If we can't find the exact match, fall back to clicking the first item
                cy.get('[data-test-id="lookup_item"]')
                    .first()
                    .click({ force: true });
            }
        });

        // Wait for UI to update after selection
        cy.wait(2000);

        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsDepartmentVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
            .should('be.visible')
            .clear({ force: true })
            .type(uniqueDepartmentName, { force: true });

        // Wait for dropdown to appear and be ready
        cy.wait(2000);
        
        // Force Cypress to find the department dropdown item, even if it's detached from DOM
        cy.get('body').then($body => {
            // Use jQuery to find the dropdown item containing the department name text
            const $deptOption = $body.find(`[data-test-id="lookup_item"]:contains("${uniqueDepartmentName}")`);
            
            if ($deptOption.length) {
                // Use cy.wrap to get a Cypress-wrapped element and click it with force option
                cy.wrap($deptOption).first().click({ force: true });
            } else {
                // If we can't find the exact match, fall back to clicking the first item
                cy.get('[data-test-id="lookup_item"]')
                    .first()
                    .click({ force: true });
            }
        });

        // Wait for UI to update after selection
        cy.wait(2000);
        
        // Now add first name and last name
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsFirstNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(testFirstName, { force: true });

        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsLastNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(testLastName, { force: true });
            
        // Save the user
        cy.get('.save')
            .should('be.visible')
            .click({ force: true });

        // Wait for save to complete
        cy.wait(3000);
    });

    it("Should update the user", () => {
        
         // Search for the user we just created
        cy.get('input.filterTextInputCustom.form-control')
        .should('exist')
        .should('be.visible')
        .click({ force: true })
        .clear({ force: true })
        .type(testFirstName, { force: true });

        
        // Click on the search button
        cy.get('.btn-primary')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // select the first user
        cy.get(`[data-bind="jqStopBubble: 'a'"] > a`)
            .should('be.visible')
            .click();

        cy.wait(3000);

        // Now look for any action button we can click
        cy.get('button.edit.command-button')
            .filter(':visible')
            .first()
            .then($button => {
                if ($button.length > 0) {
                    cy.wrap($button).click({ force: true });
                } else {
                    // If we can't find an edit command button, try looking for buttons with specific text
                    cy.contains('button', 'New').then($newBtn => {
                        if ($newBtn.length > 0) {
                            cy.wrap($newBtn).click({ force: true });
                        } else {
                            cy.contains('button', 'Add').then($addBtn => {
                                if ($addBtn.length > 0) {
                                    cy.wrap($addBtn).click({ force: true });
                                } else {
                                    cy.contains('button', 'Modify').click({ force: true });
                                }
                            });
                        }
                    });
                }
            });

        // Wait for form to be in edit mode
        cy.wait(1000);

        // Fill in the form fields using reliable selectors
        // Customer field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsCustomerVisible()"] > .form-field-label-zone > .form-field-label`).should('be.visible');
        
        // Site field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsSiteVisible()"] > .form-field-label-zone > .form-field-label`).should('be.visible');
        
        // Department field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsDepartmentVisible()"] > .form-field-label-zone > .form-field-label`).should('be.visible');
        
        // First Name field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsFirstNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(testFirstName + ' - updated', { force: true });

        // Last Name field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsLastNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(testLastName + ' - updated', { force: true });

    
        // Save the changes using button text
        cy.get('.save')
            .should('be.visible')
            .click({ force: true });

        // Wait for save to complete
        cy.wait(3000);  

        // Verify the changes were saved by checking the user info
        cy.contains(testFirstName + ' - updated').should('be.visible');
        cy.contains(testLastName + ' - updated').should('be.visible');

        // rename the user back to the original name
        cy.get('button.edit.command-button')
            .filter(':visible')
            .first()
            .then($button => {
                if ($button.length > 0) {
                    cy.wrap($button).click({ force: true });
                } else {
                    // If we can't find an edit command button, try looking for buttons with specific text
                    cy.contains('button', 'New').then($newBtn => {
                        if ($newBtn.length > 0) {
                            cy.wrap($newBtn).click({ force: true });
                        } else {
                            cy.contains('button', 'Add').then($addBtn => {
                                if ($addBtn.length > 0) {
                                    cy.wrap($addBtn).click({ force: true });
                                } else {
                                    cy.contains('button', 'Modify').click({ force: true });
                                }
                            });
                        }
                    });
                }
            });

        // First Name field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsFirstNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(testFirstName, { force: true });

        // Last Name field
        cy.get(`[data-bind="'visible':PersonInformationFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonInformationFormFormViewModel.StatusData.IsLastNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear({ force: true })
            .type(testLastName, { force: true });
            
        // Save the changes using button text
        cy.get('.save')
            .should('be.visible')
            .click({ force: true });

        // Go back to the user list using link text
        cy.get('.back-button-placing-custom')
            .should('be.visible')
            .click({ force: true });

        // Wait for the list to load
        cy.wait(3000);

        // Search for the user we just updated
        cy.get('input.filterTextInputCustom.form-control')
            .should('exist')
            .should('be.visible')
            .click({ force: true })
            .clear({ force: true })
            .type(testFirstName, { force: true });

        cy.contains('button', 'Search')
            .should('be.visible')
            .click({ force: true });

        // Wait for search results
        cy.wait(1000);

        // Verify our user shows up in the results
        cy.contains(testFirstName).should('be.visible');
    });
});
