using System;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public interface IStorageClientFactory
    {
        BlobServiceClient CreateBlobServiceClient();
        BlobContainerClient CreateBlobContainerClient(string containerName);
        BlobClient CreateBlobClient(string containerName, string blobName);
    }

    public class AzureBlobStorageClientFactory : IStorageClientFactory
    {
        private readonly IConfiguration _configuration;
        private BlobServiceClient _blobServiceClient;

        public AzureBlobStorageClientFactory(IConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        public BlobServiceClient CreateBlobServiceClient()
        {
            if (_blobServiceClient == null)
            {
                var storageConnectionString = _configuration["AzureStorage:ConnectionString"];
                if (string.IsNullOrEmpty(storageConnectionString))
                {
                    throw new InvalidOperationException("Azure Storage connection string is not configured");
                }
                _blobServiceClient = new BlobServiceClient(storageConnectionString);
            }
            return _blobServiceClient;
        }

        public BlobContainerClient CreateBlobContainerClient(string containerName)
        {
            if (string.IsNullOrEmpty(containerName))
            {
                throw new ArgumentNullException(nameof(containerName));
            }

            var blobServiceClient = CreateBlobServiceClient();
            return blobServiceClient.GetBlobContainerClient(containerName);
        }

        public BlobClient CreateBlobClient(string containerName, string blobName)
        {
            if (string.IsNullOrEmpty(containerName))
            {
                throw new ArgumentNullException(nameof(containerName));
            }
            if (string.IsNullOrEmpty(blobName))
            {
                throw new ArgumentNullException(nameof(blobName));
            }

            var containerClient = CreateBlobContainerClient(containerName);
            return containerClient.GetBlobClient(blobName);
        }
    }
}