﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// PedestrianDetectionAPI Component
	///  
	/// </summary>
    public partial class PedestrianDetectionAPI : BaseServerComponent, IPedestrianDetectionAPI 
    {
		public PedestrianDetectionAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async System.Threading.Tasks.Task<ComponentResponse<string>> StorePedestrianDetectionHistoryAsync(string Message, Dictionary<string, object> parameters = null)
        {
            var payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            if (payloadObject == null)
            {
                throw new GOServerException("Invalid Payload");
            }

            var payload = HandlePedestrianDetectionPayload(payloadObject.Payload);

            if (payload == null)
            {
                throw new GOServerException("Pedestrian detection payload is null");
            }

            if (string.IsNullOrEmpty(payload.DriverId))
            {
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(400, "DriverId is required")));
            }

            if (string.IsNullOrEmpty(payload.Timestamp))
            {
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(400, "Timestamp is required")));
            }

            try
            {
                // Get the session using the session ID from the payload
                var session = (await _dataFacade.SessionDataProvider.GetCollectionAsync(
                    null,
                    "Id == @0",
                    new object[] { Guid.Parse(payloadObject.SessionId) }
                )).SingleOrDefault();

                if (session == null)
                {
                    throw new GOServerException($"Session not found for session ID {payloadObject.SessionId}");
                }

                // Load the vehicle associated with the session
                var vehicle = await session.LoadVehicleAsync();
                if (vehicle == null)
                {
                    throw new GOServerException($"Vehicle not found for session {session.Id}");
                }

                // Load the driver associated with the session
                var driver = await session.LoadDriverAsync();
                if (driver == null)
                {
                    throw new GOServerException($"Driver not found for session {session.Id}");
                }

                if (payload.SeenState == "1")
                {
                    // Pedestrian detected - create new record
                    // Check if a record already exists for this driver and timestamp
                    var existingHistory = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                        null,
                        "DriverId == @0 AND Date == @1",
                        new object[] { driver.Id, DataUtils.HexToUtcTime(payload.Timestamp) }
                    )).FirstOrDefault();

                    if (existingHistory != null)
                    {
                        // Duplicate seen_state=1 event with same timestamp - ignore
                        return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Duplicate pedestrian detection event ignored")));
                    }

                    // Create new pedestrian detection history record
                    var newHistory = _serviceProvider.GetRequiredService<PedestrianDetectionHistoryDataObject>();
                    newHistory.DriverId = driver.Id;
                    newHistory.VehicleId = vehicle.Id;
                    newHistory.Date = DataUtils.HexToUtcTime(payload.Timestamp);
                    newHistory.AlertDuration = "0"; // Initial duration is 0

                    // Set boolean flags from explicit SEAT, TRACK, HYDR values
                    newHistory.SEAT = payload.SEAT;
                    newHistory.TRACK = payload.TRACK;
                    newHistory.HYDR = payload.HYDR;

                    await _dataFacade.PedestrianDetectionHistoryDataProvider.SaveAsync(newHistory);
                    return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Pedestrian detection history created successfully")));
                }
                else if (payload.SeenState == "0")
                {
                    // Pedestrian left detection zone - find and update existing record
                    var existingHistory = (await _dataFacade.PedestrianDetectionHistoryDataProvider.GetCollectionAsync(
                        null,
                        "DriverId == @0 AND Date == @1",
                        new object[] { driver.Id, DataUtils.HexToUtcTime(payload.Timestamp) }
                    )).FirstOrDefault();

                    if (existingHistory != null)
                    {
                        // Update the existing record with the duration time from payload
                        // Convert DurationTime from hex to decimal, then convert from milliseconds to seconds
                        string durationTimeFormatted = "00:00";
                        if (!string.IsNullOrEmpty(payload.DurationTime))
                        {
                            try
                            {
                                int durationTimeInt = DataUtils.ConvertHexToInt(payload.DurationTime);
                                double durationTimeSeconds = durationTimeInt / 1000.0;
                                int totalSeconds = (int)Math.Floor(durationTimeSeconds);
                                int minutes = totalSeconds / 60;
                                int seconds = totalSeconds % 60;
                                durationTimeFormatted = $"{minutes:D2}:{seconds:D2}";
                            }
                            catch
                            {
                                // If conversion fails, use "00:00" as default
                                durationTimeFormatted = "00:00";
                            }
                        }
                        existingHistory.AlertDuration = durationTimeFormatted;
                        
                        // Set boolean flags from explicit SEAT, TRACK, HYDR values
                        existingHistory.SEAT = payload.SEAT;
                        existingHistory.TRACK = payload.TRACK;
                        existingHistory.HYDR = payload.HYDR;

                        await _dataFacade.PedestrianDetectionHistoryDataProvider.SaveAsync(existingHistory);
                        return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Pedestrian detection history updated successfully")));
                    }
                    else
                    {
                        // seen_state=0 but no corresponding seen_state=1 record found
                        return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(404, "No corresponding pedestrian detection event found")));
                    }
                }
                else
                {
                    // Invalid seen_state value
                    return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(400, "Invalid seen_state value")));
                }
            }
            catch (Exception ex)
            {
                return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(500, $"Error storing pedestrian detection history: {ex.Message}")));
            }
        }

        private static PedestrianDetectionPayloadDataObject HandlePedestrianDetectionPayload(string payload)
        {
            PedestrianDetectionPayloadDataObject pedestrianDetectionPayloadObject = new PedestrianDetectionPayloadDataObject();
            
            // Find the SEEN part of the payload
            if (payload.Contains("SEEN="))
            {
                int seenStart = payload.IndexOf("SEEN=");
                int seenEnd = payload.IndexOf(":", seenStart);
                
                if (seenEnd > seenStart)
                {
                    // Extract the SEEN data: "SEEN=<driver_id>,<timestamp>,<detection_time>,<seen_state>,<digin_state_0>..."
                    string seenData = payload.Substring(seenStart + 5, seenEnd - seenStart - 5);
                    string[] seenArray = seenData.Split(",");
                    
                    if (seenArray.Length >= 4)
                    {
                        pedestrianDetectionPayloadObject.DriverId = seenArray[0];
                        pedestrianDetectionPayloadObject.Timestamp = seenArray[1];
                        pedestrianDetectionPayloadObject.DurationTime = seenArray[2];
                        pedestrianDetectionPayloadObject.SeenState = seenArray[3];
                        
                        // Parse additional data after the colon for SEAT, TRACK, HYDR sections
                        string additionalData = payload.Substring(seenEnd + 1).Trim();
                        if (!string.IsNullOrEmpty(additionalData))
                        {
                            // Parse SEAT section
                            if (additionalData.Contains("SEAT:"))
                            {
                                int seatStart = additionalData.IndexOf("SEAT:");
                                string seatSection = additionalData.Substring(seatStart + 5).Trim();
                                string[] seatValues = seatSection.Split(" ");
                                if (seatValues.Length > 0)
                                {
                                    pedestrianDetectionPayloadObject.SEAT = seatValues[0] == "1";
                                }
                            }
                            
                            // Parse TRACK section
                            if (additionalData.Contains("TRACK:"))
                            {
                                int trackStart = additionalData.IndexOf("TRACK:");
                                string trackSection = additionalData.Substring(trackStart + 6).Trim();
                                string[] trackValues = trackSection.Split(" ");
                                if (trackValues.Length > 0)
                                {
                                    pedestrianDetectionPayloadObject.TRACK = trackValues[0] == "1";
                                }
                            }
                            
                            // Parse HYDR section
                            if (additionalData.Contains("HYDR:"))
                            {
                                int hydrStart = additionalData.IndexOf("HYDR:");
                                string hydrSection = additionalData.Substring(hydrStart + 5).Trim();
                                string[] hydrValues = hydrSection.Split(" ");
                                if (hydrValues.Length > 0)
                                {
                                    pedestrianDetectionPayloadObject.HYDR = hydrValues[0] == "1";
                                }
                            }
                        }
                    }
                }
            }
            
            return pedestrianDetectionPayloadObject;
        }
    }
}
