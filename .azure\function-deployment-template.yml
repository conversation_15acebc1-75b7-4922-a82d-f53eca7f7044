# Template for deploying VehicleAccessProcessor Azure Function to different environments

parameters:
  - name: environment
    type: string
  - name: functionAppName
    type: string
  - name: azureSubscription
    type: string
  - name: resourceGroupName
    type: string

steps:
  - task: AzureFunctionApp@1
    displayName: "Deploy Azure Function to ${{ parameters.environment }}"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      appType: "functionApp"
      appName: "${{ parameters.functionAppName }}"
      resourceGroupName: "${{ parameters.resourceGroupName }}"
      package: "$(Pipeline.Workspace)/function-drop/VehicleAccessProcessor.zip"
      deploymentMethod: "auto"
      appSettings: >-
        -AzureWebJobsStorage "$(AzureWebJobsStorage)"
        -FUNCTIONS_WORKER_RUNTIME "dotnet-isolated"
        -FUNCTIONS_EXTENSION_VERSION "~4"
        -ServiceBusConnection "$(ServiceBusConnection)"
        -VehicleAccessQueue "$(VehicleAccessQueue)"
        -FleetXQApiBaseUrl "$(FleetXQApiBaseUrl)"
        -FleetXQUsername "$(FleetXQUsername)"
        -FleetXQPassword "$(FleetXQPassword)"
        -APPINSIGHTS_INSTRUMENTATIONKEY "$(APPINSIGHTS_INSTRUMENTATIONKEY)"
        -APPLICATIONINSIGHTS_CONNECTION_STRING "$(APPLICATIONINSIGHTS_CONNECTION_STRING)"
        -ASPNETCORE_ENVIRONMENT "${{ parameters.environment }}"

  - task: AzureCLI@2
    displayName: "Verify Function Deployment in ${{ parameters.environment }}"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      scriptType: "ps"
      scriptLocation: "inlineScript"
      inlineScript: |
        Write-Host "Verifying Function App deployment for ${{ parameters.environment }}..."
        $functionApp = az functionapp show --name ${{ parameters.functionAppName }} --resource-group ${{ parameters.resourceGroupName }} | ConvertFrom-Json
        Write-Host "Function App State: $($functionApp.state)"
        Write-Host "Function App URL: https://$($functionApp.defaultHostName)"
        
        # Test the health check endpoint
        Write-Host "Testing health check endpoint..."
        try {
          $healthResponse = Invoke-RestMethod -Uri "https://$($functionApp.defaultHostName)/api/HealthCheck" -Method GET -TimeoutSec 30
          Write-Host "Health check response: $($healthResponse | ConvertTo-Json)"
          Write-Host "##[command]✅ Function App health check passed for ${{ parameters.environment }}"
        } catch {
          Write-Warning "Health check failed: $_"
          Write-Host "##[warning]⚠️ Function App health check failed for ${{ parameters.environment }}"
        }

        # Check function app logs
        Write-Host "Checking recent function app logs..."
        try {
          $logs = az monitor log-analytics query --workspace $(LogAnalyticsWorkspaceId) --analytics-query "FunctionAppLogs | where TimeGenerated > ago(5m) | order by TimeGenerated desc | limit 10" --output table
          Write-Host "Recent logs:"
          Write-Host $logs
        } catch {
          Write-Warning "Could not retrieve logs: $_"
        }

  - task: PowerShell@2
    displayName: "Deployment Summary for ${{ parameters.environment }}"
    inputs:
      targetType: "inline"
      script: |
        Write-Host "##[section]Deployment Summary for ${{ parameters.environment }}"
        Write-Host "##[command]Function App: ${{ parameters.functionAppName }}"
        Write-Host "##[command]Resource Group: ${{ parameters.resourceGroupName }}"
        Write-Host "##[command]Environment: ${{ parameters.environment }}"
        Write-Host "##[command]✅ VehicleAccessProcessor function deployed successfully to ${{ parameters.environment }}!" 