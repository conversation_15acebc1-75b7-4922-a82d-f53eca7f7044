﻿(function () {

    FleetXQ.Web.Controllers.CurrentStatusReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;
        this.IoTHubManagerProxy = new FleetXQ.Web.Model.Components.IoTHubManagerProxy(this.ObjectsDataSet);

        this.getDefaultConfiguration = function (forGrid = false) {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += !forGrid ? 'CustomerId == @' + parameterCount++ :  'Driver.Person.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += !forGrid ? 'SiteId == @' + parameterCount++ : 'Driver.Person.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            configuration.filterPredicate = "CustomerId == @0 && SiteId == @1 && DepartmentId == @2";
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.CustomerId() ? '"' + currentData.CustomerId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.SiteId() ? '"' + currentData.SiteId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.DepartmentId() ? '"' + currentData.DepartmentId() + '"' : 'null') + ' }]';
            return configuration;
        };

        this.LoadCurrentStatusDriverViewGridViewData = function () {
            var configuration = {
            };

            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            var parameterCount = 0;

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration(true);
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection(defaultConfig);
                var vehicleConfiguration = JSON.parse(JSON.stringify(defaultConfig));
                vehicleConfiguration.filterPredicate = vehicleConfiguration.filterPredicate.replace(/Driver\.Person/g, 'Vehicle');
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection(vehicleConfiguration);
                return;
            }

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'Driver.Person.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }

            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                // configuration.filterPredicate += 'Driver.Person.PersonAllocationItems.Where(SiteId == @' + parameterCount++ +').Any()';
                configuration.filterPredicate += 'Driver.Person.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }

            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

                configuration.filterPredicate += 'Driver.Person.DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection(configuration);

            //if (currentData.CustomerId() != null) {
            //    var vehicleConfiguration = JSON.parse(JSON.stringify(configuration));
            //    vehicleConfiguration.filterPredicate = vehicleConfiguration.filterPredicate.replace(/Driver\.Person/g, 'Vehicle');
            //    self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection(vehicleConfiguration);
            //    return;
            //}
        };

        this.LoadCurrentStatusVehicleViewGridViewData = function () {
            var configuration = {
            };
        
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
        
            var parameterCount = 0;
        
            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Vehicle.DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }
        
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
        
            // self.controller.CurrentStatusVehicleViewGridViewModel.filterPredicate = configuration.filterPredicate;
            // self.controller.CurrentStatusVehicleViewGridViewModel.filterParameters =configuration.filterParameters;

            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection(configuration);
        };
        
        
        this.loadReportData = function () {
            var configuration = this.getConfiguration();
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.CurrentVehicleStatusChartViewReportViewModel.LoadCurrentVehicleStatusChartViewObjectCollection(defaultConfig);
                return;
            }

            self.controller.CurrentVehicleStatusChartViewReportViewModel.LoadCurrentVehicleStatusChartViewObjectCollection(configuration);
        };

        /**
         * Helper method to update IoT device connection status
         * This method queries the IoT Hub to get the connection status of all devices
         * based on the current filter criteria (customer, site, department)
         * @returns {Promise} - Promise that resolves when the update is complete
         */
        this.updateIoTDeviceConnectionStatus = async function() {
            // Set busy indicators to show loading state
            self.controller.DashboardFilterFormViewModel.StatusData.IsBusy(true);
            self.controller.CurrentStatusCombinedViewFormViewModel.StatusData.IsBusy(true);
            
            // Create configuration object for the API call
            var configuration = {};
            
            // Get current filter values from the dashboard filter
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            
            // Use filter values if available, otherwise fall back to user claims or empty GUID
            // Priority: Filter value > User claim > Empty GUID
            var customerId = currentData.CustomerId() || 
                self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId || 
                "00000000-0000-0000-0000-000000000000";
                
            var siteId = currentData.SiteId() || "00000000-0000-0000-0000-000000000000";
            var departmentId = currentData.DepartmentId() || "00000000-0000-0000-0000-000000000000";
            
            // Add IDs to configuration object
            configuration.customerId = customerId;
            configuration.siteId = siteId;
            configuration.departmentId = departmentId;
            
            // Success handler - clears busy indicators when complete
            configuration.successHandler = function (result) {
                self.controller.DashboardFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.CurrentStatusCombinedViewFormViewModel.StatusData.IsBusy(false);

                // Then load grid and report data with updated connection status
                self.LoadCurrentStatusVehicleViewGridViewData();
                self.LoadCurrentStatusDriverViewGridViewData();
                self.loadReportData();
            };
            
            // Error handler - shows error popup and clears busy indicators
            configuration.errorHandler = function () {
                self.controller.applicationController.showAlertPopup(
                    self.controller, 
                    "Failed to update vehicle status", 
                    "Error", 
                    null, 
                    self.controller.contextId
                );
                self.controller.DashboardFilterFormViewModel.StatusData.IsBusy(false);
                self.controller.CurrentStatusCombinedViewFormViewModel.StatusData.IsBusy(false);
            }; 
            
            // Call the API to update device connection status
            return self.IoTHubManagerProxy.GetAllDevicesTwinConnection(configuration);
        };

        /**
         * Load initial data when the page loads
         * For users with customer ID, updates IoT status before loading data
         */
        this.loadInitialData = async function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            
            if (customerId != null) {
                self.LoadCurrentStatusDriverViewGridViewData();
                self.loadReportData();
                return;
            }
            if (!GO.Filter.hasUrlFilter(self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.FILTER_NAME, self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel)) {
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusDriverViewItemsGridViewModel.LoadCurrentStatusDriverViewObjectCollection();
            }

            if (!GO.Filter.hasUrlFilter(self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.FILTER_NAME, self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel)) {
                self.controller.CurrentStatusCombinedViewFormViewModel.CurrentStatusVehicleViewItemsGridViewModel.LoadCurrentStatusVehicleViewObjectCollection();
            }
        }

        /**
         * Initialize the controller
         * Sets up event handlers and loads initial data
         */
        this.initialize = async function () {
            // Prevent confirmation dialog when changing page
            // This avoids asking to confirm changing page and lose changes
            // (caused by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            };

            // // Handle page reload to ensure hash is set correctly
            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            /**
             * Modified filterData function to update IoT connection status before loading grid data
             * This ensures we have the latest connection status before displaying data
             */
            self.controller.DashboardFilterFormViewModel.filterData = async function () {
                // Check if the user is a DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                
                if (userRole === 'DealerAdmin') {
                    // Get the customer ID from the form
                    var currentObject = self.controller.DashboardFilterFormViewModel.CurrentObject();
                    var customerId = currentObject.Data.CustomerId();
                    
                    // If no customer is selected, show an error and return
                    if (!customerId || customerId === '') {
                        self.controller.DashboardFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                try {
                    // First update IoT device connection status
                    await self.updateIoTDeviceConnectionStatus();
                } catch (error) {
                    console.error("Error in filterData:", error);
                    // Continue with data loading even if IoT status update fails
                    self.LoadCurrentStatusVehicleViewGridViewData();
                    self.LoadCurrentStatusDriverViewGridViewData();
                    self.loadReportData();
                }
            };
        };
    };

})();