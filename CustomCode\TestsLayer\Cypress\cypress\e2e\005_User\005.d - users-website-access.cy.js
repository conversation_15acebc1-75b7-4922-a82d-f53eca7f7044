describe("005d - Users Website Access", () => {
    let testFirstName;
    let testLastName;
    let uniqueSiteName;
    let cypressCompanyName;
    let uniqueDepartmentName;
    before(() => {
        cy.fixture('testData').then((testData) => {
            testFirstName = testData.cypressFirstName;
            testLastName = testData.cypressLastName;
            uniqueSiteName = testData.uniqueSiteNamePrefix;
            cypressCompanyName = testData.cypressCompanyName;
            uniqueDepartmentName = testData.uniqueDepartmentName;
        });
    });

    beforeEach(() => {
        // Perform the login using the login command
        cy.login()

        // Navigate to the users section
        cy.get(`[data-bind="'enable' : navigation.isUserManagementEnabled(), 'visible' : navigation.isUserManagementVisible()"] > .nav-link`).click();
        cy.wait(1000);

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/customer/list*').as('getCustomerList');

        // Search for the user
        cy.get('input.filterTextInputCustom.form-control')
            .should('exist')
            .should('be.visible')
            .click({ force: true })
            .clear({ force: true })
            .type(testFirstName, { force: true });


        // Click on the search button
        cy.get('.btn-primary')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // select the first user
        cy.get(`[data-bind="jqStopBubble: 'a'"] > a`)
            .first()
            .should('be.visible')
            .click();

        cy.wait(3000);


    });

    it('should Add/Update website access', () => {
        // ENABLE EDIT MODE
        cy.get('#PersonFormControlCommands > .btn-group > .btn').click();

        // make sure that website is enabled
        // enable the website access and make sure yes button is selected
        // Select "Yes" for Website role
        cy.get('input.btn-check[name="WebSiteAccess-edit-yesno"][value="true"]')
            .should('exist')
            .click({ force: true });

        // move to website access tab
        cy.get('[data-id="PersonFormControl-PersonForm-tabs-3"]')
            .should('be.visible')
            .click();

        cy.wait(1000);

        // Check if "No user information" message is present
        cy.get('body').then($body => {
            const $noUserInfo = $body.find('.form-spacing-custom.no-data-message');
            if ($noUserInfo.length > 0 && $noUserInfo.is(':visible')) {
                cy.log('No user information available - setting up website access');

                // Add new website access details
                cy.get(`[data-bind="click : PersonWebsiteAccessFormFormViewModel.createNewGOUser, visible : PersonWebsiteAccessFormFormViewModel.isCreateNewGOUserVisible()"]`)
                    .should('be.visible')
                    .click({ force: true });

                cy.wait(1000);

                // Enter username
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsUserNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .type(`${testFirstName.toLowerCase()}_${Math.floor(Math.random() * 10000)}`, { force: true });

                // Select preferred locale dropdown
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsPreferredLocaleVisible()"] > .form-field-control-container > .enum-field > .form-select`)
                    .should('be.visible')
                    .then($select => {
                        // Select the 2nd option (index 1)
                        cy.wrap($select)
                            .find('option')
                            .eq(1)  // This selects the 2nd option (index 1)
                            .then($option => {
                                cy.wrap($select).select($option.val());
                            });
                    });

                // Enter email address
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsEmailAddressVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .type(`${testFirstName.toLowerCase()}.${testLastName.toLowerCase()}@example.com`, { force: true });

                // Enter password
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsPasswordVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .type('Password123!', { force: true });

                // Select Access Group
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.StatusData.IsAccessGroupVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
                    .should('be.visible')
                    .click({ force: true })
                    .type('Level 1', { force: true });

                // Force wait for autocomplete to appear
                cy.wait(1000);

                // Target the specific jQuery UI autocomplete structure
                cy.get('ul.ui-autocomplete.ui-treeautocomplete-menu')
                    .should('be.visible');

                // Click on the Level 1 item from the autocomplete dropdown
                cy.get('ul.ui-autocomplete.ui-treeautocomplete-menu')
                    .find('li.ui-menu-item')
                    .first()  // Get the first item which should be Level 1
                    .find('a[data-test-id="lookup_item"]')
                    .click({ force: true });

                // Add a longer wait to ensure selection is processed
                cy.wait(1000);

                cy.get('.save')
                    .should('be.visible')
                    .click({ force: true });

                cy.wait(3000);

            } else {
                cy.log('User information already exists - updating website access');

                // Enter username
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsUserNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .clear({ force: true })
                    .type(`${testFirstName.toLowerCase()}_${Math.floor(Math.random() * 10000)}`, { force: true });

                // Select preferred locale dropdown
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsPreferredLocaleVisible()"] > .form-field-control-container > .enum-field > .form-select`)
                    .should('be.visible')
                    .then($select => {
                        // Select the 2nd option (index 1)
                        cy.wrap($select)
                            .find('option')
                            .eq(1)  // This selects the 2nd option (index 1)
                            .then($option => {
                                cy.wrap($select).select($option.val());
                            });
                    });

                // Enter email address
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsEmailAddressVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .clear({ force: true })
                    .type(`${testFirstName.toLowerCase()}.${testLastName.toLowerCase()}@example.com`, { force: true });

                // Enter password
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.GOUserFormViewModel.StatusData.IsPasswordVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
                    .should('be.visible')
                    .clear({ force: true })
                    .type('Password123!', { force: true });

                // Select Access Group
                cy.get(`[data-bind="'visible':PersonWebsiteAccessFormFormViewModel.StatusData.DisplayMode() == 'edit' && PersonWebsiteAccessFormFormViewModel.StatusData.IsAccessGroupVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper > .ui-treeautocomplete-input`)
                    .should('be.visible')
                    .clear({ force: true })
                    .click({ force: true })
                    .type('Level 1', { force: true });

                // Force wait for autocomplete to appear
                cy.wait(1000);

                // Target the specific jQuery UI autocomplete structure
                cy.get('ul.ui-autocomplete.ui-treeautocomplete-menu')
                    .should('be.visible');

                // Click on the Level 1 item from the autocomplete dropdown
                cy.get('ul.ui-autocomplete.ui-treeautocomplete-menu')
                    .find('li.ui-menu-item')
                    .first()  // Get the first item which should be Level 1
                    .find('a[data-test-id="lookup_item"]')
                    .click({ force: true });

                // Add a longer wait to ensure selection is processed
                cy.wait(1000);

                cy.get('.save')
                    .should('be.visible')
                    .click({ force: true });

                cy.wait(3000);
            }
        });
    });

});