import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehicleInformationFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let sessionStorageData = {};

    beforeEach(() => {
        // Mock sessionStorage
        global.sessionStorage = {
            getItem: (key) => sessionStorageData[key],
            setItem: (key, value) => { sessionStorageData[key] = value },
            removeItem: (key) => { delete sessionStorageData[key] }
        };

        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehicleInformationFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ko.postbox
        ko.postbox = {
            subscribe: vi.fn()
        };

        // Mock ApplicationController
        global.ApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: () => ({
                        CustomerId: '123',
                        SiteId: '456',
                        DepartmentId: '789',
                        role: ['Administrator']
                    })
                }
            }
        };

        // Create base view model with required properties
        viewModel = {
            VehicleObject: ko.observable({
                Data: {
                    IsNew: ko.observable(true),
                    CustomerId: ko.observable(),
                    SiteId: ko.observable(),
                    DepartmentId: ko.observable()
                },
                StatusData: {
                    isCustomerValid: ko.observable(true),
                    isSiteValid: ko.observable(true),
                    isDepartmentValid: ko.observable(true)
                }
            }),
            StatusData: {
                IsUIDirty: ko.observable(false),
                isValid: ko.observable(true),
                errorSummary: ko.observableArray([])
            },
            controller: {
                ObjectsDataSet: {
                    isContextIdDirty: vi.fn()
                }
            },
            contextId: 'test-context',
            rebindLookups: vi.fn(),
            rebindSubFormFields: vi.fn(),
            subscriptions: [],
            CustomerContextId: 'customer-context',
            SiteContextId: 'site-context',
            DepartmentContextId: 'department-context',
            DataStoreCustomer: {
                LoadObject: vi.fn()
            },
            DataStoreSite: {
                LoadObject: vi.fn()
            },
            DataStoreDepartment: {
                LoadObject: vi.fn()
            },
            Customer_CompanyName: ko.observable(),
            Customer_lookupItem: ko.observable(),
            Site_Name: ko.observable(),
            Site_lookupItem: ko.observable(),
            Department_Name: ko.observable(),
            Department_lookupItem: ko.observable(),
            selectiveLoadDataForSite: vi.fn(),
            selectiveLoadDataForDepartment: vi.fn(),
            selectiveLoadDataForModel: vi.fn(),
            onVehicleObjectChanged: vi.fn()
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.VehicleInformationFormViewModelCustom(viewModel);
        customViewModel.initialize();

        // Setup fake timers
        vi.useFakeTimers();

        // Clear sessionStorage data
        sessionStorageData = {};
    });

    afterEach(() => {
        vi.useRealTimers();
        sessionStorageData = {};
    });

    it('should call loadDefaultCustomer when onVehicleObjectChanged is triggered for new vehicle', () => {
        // Setup spy on loadDefaultCustomer
        const loadDefaultCustomerSpy = vi.spyOn(customViewModel, 'loadDefaultCustomer');

        // Setup filter values in sessionStorage
        const filterData = {
            customerId: '456',
            siteId: '789',
            departmentId: '012'
        };
        sessionStorage.setItem('vehicleFilterValues', JSON.stringify(filterData));

        // Trigger onVehicleObjectChanged
        viewModel.onVehicleObjectChanged();

        // Use vi.advanceTimersByTime to handle the setTimeout
        vi.advanceTimersByTime(500);

        // Assert loadDefaultCustomer was called with correct parameters
        expect(loadDefaultCustomerSpy).toHaveBeenCalledWith('456', expect.any(Function));
    });

    it('should not call loadDefaultCustomer when vehicle is not new', () => {
        // Setup vehicle as not new
        viewModel.VehicleObject().Data.IsNew(false);

        // Setup spy on loadDefaultCustomer
        const loadDefaultCustomerSpy = vi.spyOn(customViewModel, 'loadDefaultCustomer');

        // Trigger onVehicleObjectChanged
        viewModel.onVehicleObjectChanged();

        // Use vi.advanceTimersByTime to handle the setTimeout
        vi.advanceTimersByTime(500);

        // Assert loadDefaultCustomer was not called
        expect(loadDefaultCustomerSpy).not.toHaveBeenCalled();
    });

    it('should call rebindLookups when onVehicleObjectChanged is triggered', () => {
        // Trigger onVehicleObjectChanged
        viewModel.onVehicleObjectChanged();

        // Assert rebindLookups was called
        expect(viewModel.rebindLookups).toHaveBeenCalled();
    });

    it('should load customer data successfully when loadDefaultCustomer is called', () => {
        const mockCustomer = {
            Data: {
                CompanyName: ko.observable('Test Company')
            }
        };

        // Setup success handler to simulate API response
        viewModel.DataStoreCustomer.LoadObject.mockImplementation(({ successHandler }) => {
            successHandler(mockCustomer);
        });

        // Call loadDefaultCustomer
        customViewModel.loadDefaultCustomer('123', vi.fn());

        // Assert customer data was set correctly
        expect(viewModel.VehicleObject().Data.CustomerId()).toBe('123');
        expect(viewModel.Customer_CompanyName()).toBe('Test Company');
        expect(viewModel.Customer_lookupItem()).toEqual({
            label: 'Test Company',
            value: mockCustomer,
            selectable: true
        });
    });

    it('should allow site and department to be editable when in edit mode', () => {
        // Setup vehicle as not new (edit mode)
        viewModel.VehicleObject().Data.IsNew(false);

        // Check if there are explicit read-only flags for site and department
        // If these functions don't exist, it means the fields are always editable
        expect(viewModel.StatusData.IsSiteReadOnly).toBeUndefined();
        expect(viewModel.StatusData.IsDepartmentReadOnly).toBeUndefined();

        // In contrast, customer should be read-only when not in new mode
        expect(viewModel.StatusData.IsCustomerReadOnly()).toBe(true);

        // Test that vehicle info can be modified
        const newSiteId = 'site-999';
        const newDepartmentId = 'dept-999';

        viewModel.VehicleObject().Data.SiteId(newSiteId);
        viewModel.VehicleObject().Data.DepartmentId(newDepartmentId);

        expect(viewModel.VehicleObject().Data.SiteId()).toBe(newSiteId);
        expect(viewModel.VehicleObject().Data.DepartmentId()).toBe(newDepartmentId);
    });

    describe('Module1 readonly behavior', () => {
        it('should allow Module1 to be editable when vehicle is new', () => {
            // Setup vehicle as new
            viewModel.VehicleObject().Data.IsNew(true);

            // Module1 should be editable (not readonly)
            expect(viewModel.StatusData.IsModule1ReadOnly()).toBe(false);
        });

        it('should make Module1 readonly when vehicle is not new', () => {
            // Setup vehicle as not new (edit mode)
            viewModel.VehicleObject().Data.IsNew(false);

            // Module1 should be readonly
            expect(viewModel.StatusData.IsModule1ReadOnly()).toBe(true);
        });

        it('should update readonly state when vehicle IsNew status changes', () => {
            // Start with new vehicle
            viewModel.VehicleObject().Data.IsNew(true);
            expect(viewModel.StatusData.IsModule1ReadOnly()).toBe(false);

            // Change to existing vehicle
            viewModel.VehicleObject().Data.IsNew(false);
            expect(viewModel.StatusData.IsModule1ReadOnly()).toBe(true);

            // Change back to new vehicle
            viewModel.VehicleObject().Data.IsNew(true);
            expect(viewModel.StatusData.IsModule1ReadOnly()).toBe(false);
        });
    });

    describe('Serial Number and Hire Number access control', () => {
        beforeEach(() => {
            // Reset ApplicationController mock for each test
            global.ApplicationController = {
                viewModel: {
                    security: {
                        currentUserClaims: () => ({
                            CustomerId: '123',
                            SiteId: '456',
                            DepartmentId: '789',
                            role: [],
                            CanEditVehicle: 'False'
                        })
                    }
                }
            };
        });

        describe('Serial Number (IsSerialNoReadOnly)', () => {
            it('should allow editing for new vehicles regardless of user role', () => {
                // Setup vehicle as new
                viewModel.VehicleObject().Data.IsNew(true);

                // Should be editable for new vehicles
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
            });

            it('should allow editing for Administrators on existing vehicles', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user as Administrator
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: ['Administrator'],
                    CanEditVehicle: 'False'
                });

                // Should be editable for Administrators
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
            });

            it('should allow editing for users with CanEditVehicle permission on existing vehicles', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user with CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'True'
                });

                // Should be editable for users with CanEditVehicle permission
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
            });

            it('should prevent editing for regular users on existing vehicles', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup regular user without special permissions
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'False'
                });

                // Should be read-only for regular users
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
            });

            it('should allow editing when user is both Administrator and has CanEditVehicle permission', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user with both Administrator role and CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: ['Administrator'],
                    CanEditVehicle: 'True'
                });

                // Should be editable
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
            });

            it('should handle undefined CanEditVehicle permission correctly', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user without CanEditVehicle permission defined
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: undefined
                });

                // Should be read-only when CanEditVehicle is undefined
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
            });

            it('should handle null CanEditVehicle permission correctly', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user with null CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: null
                });

                // Should be read-only when CanEditVehicle is null
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
            });
        });

        describe('Hire Number (IsHireNoReadOnly)', () => {
            it('should allow editing for new vehicles regardless of user role', () => {
                // Setup vehicle as new
                viewModel.VehicleObject().Data.IsNew(true);

                // Should be editable for new vehicles
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);
            });

            it('should allow editing for Administrators on existing vehicles', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user as Administrator
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: ['Administrator'],
                    CanEditVehicle: 'False'
                });

                // Should be editable for Administrators
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);
            });

            it('should allow editing for users with CanEditVehicle permission on existing vehicles', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user with CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'True'
                });

                // Should be editable for users with CanEditVehicle permission
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);
            });

            it('should prevent editing for regular users on existing vehicles', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup regular user without special permissions
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'False'
                });

                // Should be read-only for regular users
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);
            });

            it('should allow editing when user is both Administrator and has CanEditVehicle permission', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user with both Administrator role and CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: ['Administrator'],
                    CanEditVehicle: 'True'
                });

                // Should be editable
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);
            });

            it('should handle undefined CanEditVehicle permission correctly', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user without CanEditVehicle permission defined
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: undefined
                });

                // Should be read-only when CanEditVehicle is undefined
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);
            });

            it('should handle null CanEditVehicle permission correctly', () => {
                // Setup vehicle as existing
                viewModel.VehicleObject().Data.IsNew(false);

                // Setup user with null CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: null
                });

                // Should be read-only when CanEditVehicle is null
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);
            });
        });

        describe('Access control integration tests', () => {
            it('should maintain consistent behavior when vehicle status changes', () => {
                // Setup user with CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'True'
                });

                // Test new vehicle
                viewModel.VehicleObject().Data.IsNew(true);
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);

                // Test existing vehicle
                viewModel.VehicleObject().Data.IsNew(false);
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);

                // Test back to new vehicle
                viewModel.VehicleObject().Data.IsNew(true);
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);
            });

            it('should handle role changes dynamically', () => {
                // Setup existing vehicle
                viewModel.VehicleObject().Data.IsNew(false);

                // Test as regular user
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'False'
                });
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);

                // Test as Administrator
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: ['Administrator'],
                    CanEditVehicle: 'False'
                });
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);

                // Test as user with CanEditVehicle permission
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'True'
                });
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(false);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(false);
            });

            it('should handle edge cases with role arrays', () => {
                // Setup existing vehicle
                viewModel.VehicleObject().Data.IsNew(false);

                // Test with empty role array
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: [],
                    CanEditVehicle: 'False'
                });
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);

                // Test with undefined role
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: undefined,
                    CanEditVehicle: 'False'
                });
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);

                // Test with null role
                global.ApplicationController.viewModel.security.currentUserClaims = () => ({
                    role: null,
                    CanEditVehicle: 'False'
                });
                expect(viewModel.StatusData.IsSerialNoReadOnly()).toBe(true);
                expect(viewModel.StatusData.IsHireNoReadOnly()).toBe(true);
            });
        });
    });

    describe('getModule1CollectionData', () => {
        let mockApplicationController;
        let mockModuleUtilities;

        beforeEach(() => {
            // Mock the application controller and module utilities
            mockModuleUtilities = {
                GetAvailableModules: vi.fn()
            };

            mockApplicationController = {
                getProxyForComponent: vi.fn().mockReturnValue(mockModuleUtilities)
            };

            viewModel.controller.applicationController = mockApplicationController;
            viewModel.Module1ContextId = 'module1-context';
            viewModel.isGetModule1CollectionBusy = ko.observable(false);
            viewModel.onGetModule1CollectionDataSuccess = vi.fn();
            viewModel.onGetModule1CollectionDataError = vi.fn();

            // Mock customer with dealerId
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: ko.observable('dealer-123')
                }
            });
        });

        it('should call GetAvailableModules with correct configuration when customer has dealerId', () => {
            // Call the function
            viewModel.getModule1CollectionData();

            // Verify GetAvailableModules was called with correct configuration
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: '',
                dealerId: 'dealer-123',
                successHandler: viewModel.onGetModule1CollectionDataSuccess,
                errorHandler: viewModel.onGetModule1CollectionDataError
            });
        });

        it('should call GetAvailableModules without dealerId when customer has no dealerId', () => {
            // Mock customer without dealerId
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: ko.observable(null)
                }
            });

            // Call the function
            viewModel.getModule1CollectionData();

            // Verify GetAvailableModules was called without dealerId
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: '',
                dealerId: null,
                successHandler: viewModel.onGetModule1CollectionDataSuccess,
                errorHandler: viewModel.onGetModule1CollectionDataError
            });
        });

        it('should call GetAvailableModules without dealerId when customer is null', () => {
            // Mock customer as null
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue(null);

            // Call the function
            viewModel.getModule1CollectionData();

            // Verify GetAvailableModules was called without dealerId
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: '',
                successHandler: viewModel.onGetModule1CollectionDataSuccess,
                errorHandler: viewModel.onGetModule1CollectionDataError
            });
        });

        it('should set isGetModule1CollectionBusy to true when called', () => {
            // Call the function
            viewModel.getModule1CollectionData();

            // Verify busy state was set to true
            expect(viewModel.isGetModule1CollectionBusy()).toBe(true);
        });

        it('should use custom callback when provided', () => {
            const customCallback = vi.fn();

            // Call the function with custom callback
            viewModel.getModule1CollectionData(customCallback);

            // Verify GetAvailableModules was called with custom callback
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: '',
                dealerId: 'dealer-123',
                successHandler: customCallback,
                errorHandler: viewModel.onGetModule1CollectionDataError
            });
        });
    });

    describe('getFilteredModule1CollectionData', () => {
        let mockApplicationController;
        let mockModuleUtilities;

        beforeEach(() => {
            // Mock the application controller and module utilities
            mockModuleUtilities = {
                GetAvailableModules: vi.fn()
            };

            mockApplicationController = {
                getProxyForComponent: vi.fn().mockReturnValue(mockModuleUtilities)
            };

            viewModel.controller.applicationController = mockApplicationController;
            viewModel.Module1ContextId = 'module1-context';
            viewModel.isGetModule1CollectionBusy = ko.observable(false);
            viewModel.onGetModule1CollectionDataError = vi.fn();

            // Mock customer with dealerId
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: ko.observable('dealer-123')
                }
            });
        });

        it('should call GetAvailableModules with correct configuration for filtered search', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with correct configuration
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: 'IoTDevice.Contains("test-device")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: 'dealer-123',
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should set isGetModule1CollectionBusy to true when called', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Verify busy state was set to true
            expect(viewModel.isGetModule1CollectionBusy()).toBe(true);
        });

        it('should call GetAvailableModules without dealerId when customer has no dealerId', () => {
            // Mock customer without dealerId
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: ko.observable(null)
                }
            });

            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Verify GetAvailableModules was called without dealerId
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: 'IoTDevice.Contains("test-device")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: null,
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should handle success callback correctly', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();
            const mockData = [{ id: 1, name: 'Device 1' }, { id: 2, name: 'Device 2' }];

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Get the success handler that was passed to GetAvailableModules
            const successHandler = mockModuleUtilities.GetAvailableModules.mock.calls[0][0].successHandler;

            // Call the success handler
            successHandler(mockData);

            // Verify busy state was set to false
            expect(viewModel.isGetModule1CollectionBusy()).toBe(false);

            // Verify callback was called with the data
            expect(callback).toHaveBeenCalledWith(mockData);
        });

        it('should handle error callback correctly', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();
            const mockError = new Error('API Error');

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Get the error handler that was passed to GetAvailableModules
            const errorHandler = mockModuleUtilities.GetAvailableModules.mock.calls[0][0].errorHandler;

            // Call the error handler
            errorHandler(mockError);

            // Verify busy state was set to false
            expect(viewModel.isGetModule1CollectionBusy()).toBe(false);

            // Verify onGetModule1CollectionDataError was called with the error
            expect(viewModel.onGetModule1CollectionDataError).toHaveBeenCalledWith(mockError);
        });

        it('should handle success callback when no custom callback is provided', () => {
            const searchValue = 'test-device';
            const mockData = [{ id: 1, name: 'Device 1' }];

            // Call the function without custom callback
            viewModel.getFilteredModule1CollectionData(searchValue);

            // Get the success handler that was passed to GetAvailableModules
            const successHandler = mockModuleUtilities.GetAvailableModules.mock.calls[0][0].successHandler;

            // Call the success handler
            successHandler(mockData);

            // Verify busy state was set to false
            expect(viewModel.isGetModule1CollectionBusy()).toBe(false);
        });

        it('should handle empty search value correctly', () => {
            const searchValue = '';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with empty search filter
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: 'IoTDevice.Contains("")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: 'dealer-123',
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should handle special characters in search value correctly', () => {
            const searchValue = 'test-device-123';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModule1CollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with correct filter
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module1-context',
                filterPredicate: 'IoTDevice.Contains("test-device-123")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: 'dealer-123',
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });
    });
}); 