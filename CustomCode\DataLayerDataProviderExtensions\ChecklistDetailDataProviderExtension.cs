﻿using DocumentFormat.OpenXml.Drawing.Charts;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using NHibernate.Criterion;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class ChecklistDetailDataProviderExtension : IDataProviderExtension<ChecklistDetailDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDeviceMessageHandler _deviceMessageHandler;

        public ChecklistDetailDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
            _deviceMessageHandler = deviceMessageHandler;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += OnBeforeSaveDataSetAsync;
        }

        private async Task OnBeforeSaveDataSetAsync(OnBeforeSaveDataSetEventArgs e)
        {
            var checklistDetail = e.Entity as ChecklistDetailDataObject;
            var preOp = await checklistDetail.LoadPreOperationalChecklistAsync();
            // check if checklistDetail.Answer is equal to preOp.ExpectedAnswer, if so set checklistDetail.Failed to false, else set it to true
            if (checklistDetail.Answer == preOp.ExpectedAnswer)
            {
                checklistDetail.Failed = false;
            }
            else
            {
                checklistDetail.Failed = true;
            }
        }
    }
}
