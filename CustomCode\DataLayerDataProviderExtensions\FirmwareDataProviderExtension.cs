using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class FirmwareDataProviderExtension : IDataProviderExtension<FirmwareDataObject>
    {
        private readonly IDataFacade _dataFacade;

        public FirmwareDataProviderExtension(
            IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSaveDataSet += DataProvider_OnBeforeSaveDataSet;
        }

        private async Task DataProvider_OnBeforeSaveDataSet(OnBeforeSaveDataSetEventArgs arg)
        {
            if (arg.Entity is FirmwareDataObject firmware)
            {
                var existingFirmware = (await _dataFacade.FirmwareDataProvider.GetCollectionAsync(null, "Version == @0", new object[] { firmware.Version })).FirstOrDefault();

                if (existingFirmware != null)
                {
                    throw new GOServerException("Version already exists");
                }
            }
        }
    }
} 