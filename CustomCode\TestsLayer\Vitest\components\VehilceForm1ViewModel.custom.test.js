import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehilceForm1ViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let mockApplicationController;
    let mockModuleUtilities;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error to avoid test output noise
        global.console.error = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/VehilceForm1ViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Mock ko.postbox
        ko.postbox = {
            subscribe: vi.fn()
        };

        // Mock the application controller and module utilities
        mockModuleUtilities = {
            GetAvailableModules: vi.fn()
        };

        mockApplicationController = {
            getProxyForComponent: vi.fn().mockReturnValue(mockModuleUtilities)
        };

        // Create base view model with required properties
        viewModel = {
            VehicleObject: ko.observable({
                Data: {
                    IsNew: ko.observable(true),
                    CustomerId: ko.observable(),
                    SiteId: ko.observable(),
                    DepartmentId: ko.observable()
                },
                getCustomer: vi.fn().mockReturnValue({
                    Data: {
                        DealerId: ko.observable('dealer-123')
                    }
                })
            }),
            StatusData: {
                IsUIDirty: ko.observable(false),
                isValid: ko.observable(true),
                errorSummary: ko.observableArray([])
            },
            controller: {
                applicationController: mockApplicationController,
                ObjectsDataSet: {
                    isContextIdDirty: vi.fn()
                }
            },
            contextId: 'test-context',
            ModuleContextId: 'module-context',
            isGetModuleCollectionBusy: ko.observable(false),
            onGetModuleCollectionDataSuccess: vi.fn(),
            onGetModuleCollectionDataError: vi.fn(),
            rebindLookups: vi.fn(),
            rebindSubFormFields: vi.fn(),
            subscriptions: []
        };

        // Create the custom view model and initialize it
        customViewModel = new FleetXQ.Web.ViewModels.VehilceForm1ViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('getModuleCollectionData', () => {
        it('should call GetAvailableModules with correct configuration when customer has dealerId', () => {
            // Call the function
            viewModel.getModuleCollectionData();

            // Verify GetAvailableModules was called with correct configuration
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: '',
                dealerId: 'dealer-123',
                successHandler: viewModel.onGetModuleCollectionDataSuccess,
                errorHandler: viewModel.onGetModuleCollectionDataError
            });
        });

        it('should call GetAvailableModules without dealerId when customer has no dealerId', () => {
            // Mock customer without dealerId
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: ko.observable(null)
                }
            });

            // Call the function
            viewModel.getModuleCollectionData();

            // Verify GetAvailableModules was called with dealerId: null
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: '',
                dealerId: null,
                successHandler: viewModel.onGetModuleCollectionDataSuccess,
                errorHandler: viewModel.onGetModuleCollectionDataError
            });
        });

        it('should call GetAvailableModules without dealerId when customer is null', () => {
            // Mock customer as null
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue(null);

            // Call the function
            viewModel.getModuleCollectionData();

            // Verify GetAvailableModules was called without dealerId
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: '',
                successHandler: viewModel.onGetModuleCollectionDataSuccess,
                errorHandler: viewModel.onGetModuleCollectionDataError
            });
        });

        it('should set isGetModuleCollectionBusy to true when called', () => {
            // Call the function
            viewModel.getModuleCollectionData();

            // Verify busy state was set to true
            expect(viewModel.isGetModuleCollectionBusy()).toBe(true);
        });

        it('should use custom callback when provided', () => {
            const customCallback = vi.fn();

            // Call the function with custom callback
            viewModel.getModuleCollectionData(customCallback);

            // Verify GetAvailableModules was called with custom callback
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: '',
                dealerId: 'dealer-123',
                successHandler: customCallback,
                errorHandler: viewModel.onGetModuleCollectionDataError
            });
        });
    });

    describe('getFilteredModuleCollectionData', () => {
        it('should call GetAvailableModules with correct configuration for filtered search', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with correct configuration
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: 'IoTDevice.Contains("test-device")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: 'dealer-123',
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should set isGetModuleCollectionBusy to true when called', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Verify busy state was set to true
            expect(viewModel.isGetModuleCollectionBusy()).toBe(true);
        });

        it('should call GetAvailableModules without dealerId when customer has no dealerId', () => {
            // Mock customer without dealerId
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: ko.observable(null)
                }
            });

            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with dealerId: null
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: 'IoTDevice.Contains("test-device")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: null,
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should handle success callback correctly', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();
            const mockData = [{ id: 1, name: 'Device 1' }, { id: 2, name: 'Device 2' }];

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Get the success handler that was passed to GetAvailableModules
            const successHandler = mockModuleUtilities.GetAvailableModules.mock.calls[0][0].successHandler;

            // Call the success handler
            successHandler(mockData);

            // Verify busy state was set to false
            expect(viewModel.isGetModuleCollectionBusy()).toBe(false);

            // Verify callback was called with the data
            expect(callback).toHaveBeenCalledWith(mockData);
        });

        it('should handle error callback correctly', () => {
            const searchValue = 'test-device';
            const callback = vi.fn();
            const mockError = new Error('API Error');

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Get the error handler that was passed to GetAvailableModules
            const errorHandler = mockModuleUtilities.GetAvailableModules.mock.calls[0][0].errorHandler;

            // Call the error handler
            errorHandler(mockError);

            // Verify busy state was set to false
            expect(viewModel.isGetModuleCollectionBusy()).toBe(false);

            // Verify onGetModuleCollectionDataError was called with the error
            expect(viewModel.onGetModuleCollectionDataError).toHaveBeenCalledWith(mockError);
        });

        it('should handle success callback when no custom callback is provided', () => {
            const searchValue = 'test-device';
            const mockData = [{ id: 1, name: 'Device 1' }];

            // Call the function without custom callback
            viewModel.getFilteredModuleCollectionData(searchValue);

            // Get the success handler that was passed to GetAvailableModules
            const successHandler = mockModuleUtilities.GetAvailableModules.mock.calls[0][0].successHandler;

            // Call the success handler
            successHandler(mockData);

            // Verify busy state was set to false
            expect(viewModel.isGetModuleCollectionBusy()).toBe(false);
        });

        it('should handle empty search value correctly', () => {
            const searchValue = '';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with empty search filter
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: 'IoTDevice.Contains("")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: 'dealer-123',
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should handle special characters in search value correctly', () => {
            const searchValue = 'test-device-123';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Verify GetAvailableModules was called with correct filter
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: 'IoTDevice.Contains("test-device-123")',
                pageSize: 50,
                pageNumber: 1,
                dealerId: 'dealer-123',
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });

        it('should handle customer with undefined DealerId property', () => {
            // Mock customer with undefined DealerId property
            viewModel.VehicleObject().getCustomer = vi.fn().mockReturnValue({
                Data: {
                    DealerId: undefined
                }
            });

            const searchValue = 'test-device';
            const callback = vi.fn();

            // Call the function
            viewModel.getFilteredModuleCollectionData(searchValue, callback);

            // Verify GetAvailableModules was called without dealerId
            expect(mockModuleUtilities.GetAvailableModules).toHaveBeenCalledWith({
                contextId: 'module-context',
                filterPredicate: 'IoTDevice.Contains("test-device")',
                pageSize: 50,
                pageNumber: 1,
                successHandler: expect.any(Function),
                errorHandler: expect.any(Function)
            });
        });
    });
}); 