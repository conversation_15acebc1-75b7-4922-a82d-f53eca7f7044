﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using FleetXQ.Data.DataProvidersExtensions.Custom;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// VORStatusAPI Component
	///  
	/// </summary>
    public partial class VORStatusAPI : BaseServerComponent, IVORStatusAPI 
    {
		public VORStatusAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

		/// <summary>
        /// ProcessConvorMessage Method
		/// </summary>
		/// <param name="Message"></param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<string>> ProcessConvorMessageAsync(string Message, Dictionary<string, object> parameters)
        {
            //{"event_type":"CONVOR","payload":"CONVOR=0,66D0068C,1","session_id":"00000000-0000-0000-0000-**********"}
            PayloadDataObject payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            // create a new VORStatus object
            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { payloadObject.IoTDeviceId })).SingleOrDefault();
            if (module == null)
            {
                throw new GOServerException("Invalid IoTDeviceId");
            }

            var vehicle = await module.LoadVehicleAsync();
            if (vehicle == null)
            {
                throw new GOServerException("Invalid Vehicle");
            }

            var otherSettings = await vehicle.LoadVehicleOtherSettingsAsync();

            if (payloadObject == null || payloadObject.EventType != "CONVOR")
            {
                throw new GOServerException("Invalid Payload");
            }
            // remove CONVOR= from the payload
            var payloadArray = payloadObject.Payload.Split('=').Last().Split(',');
            if (payloadArray.Length != 3)
            {
                throw new GOServerException("Invalid Payload");
            }
            // 0- driverId, 1 - TimeStamp, 2 - Status
            var driverId = payloadArray[0];
            var timeStamp = payloadArray[1];
            var status = payloadArray[2];

            PersonDataObject person = null;

            if (!string.IsNullOrEmpty(driverId) && driverId != "0")
            {
                var permissionMaster = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "LevelName == @0", new object[] { (int)PermissionLevelEnum.Master }, skipSecurity: true)).SingleOrDefault();
                var perVehicleMasterAccess = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 && PermissionId == @1", new object[] { vehicle.Id, permissionMaster.Id })).ToList();

                if (perVehicleMasterAccess != null)
                {
                    foreach (var access in perVehicleMasterAccess)
                    {
                        var card = await access.LoadCardAsync(skipSecurity: true);

                        if (card != null && card.Weigand == driverId)
                        {
                            var driver = await card.LoadDriverAsync();

                            if (driver != null)
                            {
                                person = await driver.LoadPersonAsync();

                                break;
                            }
                        }
                    }
                }
            }

            var vorSetting = _serviceProvider.GetRequiredService<VORSettingHistoryDataObject>();
            if (status == "0")
            {
                // get the current vor setting
                vorSetting = (await _dataFacade.VORSettingHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0 and EndDateTime == null", new object[] { vehicle.Id })).SingleOrDefault();
                if (vorSetting != null)
                {
                    vorSetting.EndDateTime = DataUtils.HexToUtcTime(timeStamp);
                    vorSetting.Status = VORStatusEnum.VORFinished;
                }
            } else if (status == "1")
            {
                vorSetting = (await _dataFacade.VORSettingHistoryDataProvider.GetCollectionAsync(null, "VehicleId == @0 and EndDateTime == null", new object[] { vehicle.Id })).SingleOrDefault();
                if (vorSetting == null)
                {
                    vorSetting = _serviceProvider.GetRequiredService<VORSettingHistoryDataObject>();
                    vorSetting.VehicleId = vehicle.Id;
                    if (person != null)
                    {
                        vorSetting.PersonId = person.Id;
                    }
                    vorSetting.StartDateTime = DataUtils.HexToUtcTime(timeStamp);
                    vorSetting.EndDateTime = null;
                    vorSetting.Status = VORStatusEnum.VOREnabled;
                } else // update the existing vor setting
                {
                    if (person != null)
                    {
                      vorSetting.PersonId = person.Id;
                    }
                    vorSetting.StartDateTime = DataUtils.HexToUtcTime(timeStamp);
                    vorSetting.EndDateTime = null;
                    vorSetting.Status = VORStatusEnum.VOREnabled;
                }
            }
            try
            {
                if(vorSetting != null)
                {
                    await _dataFacade.VORSettingHistoryDataProvider.SaveAsync(vorSetting);
                }
            }
            catch (Exception)
            {
                throw new GOServerException("Error in saving VORSettingHistory");
            }

            // update the vehicle VorStatus to true if status is 1 else false
            try
            {
                var vorStatus = status == "1";
                if (otherSettings == null)
                {
                    otherSettings = _serviceProvider.GetRequiredService<VehicleOtherSettingsDataObject>();
                }

                otherSettings.VORStatus = vorStatus;
                otherSettings.VORStatusConfirmed = true;
                await _dataFacade.VehicleOtherSettingsDataProvider.SaveAsync(otherSettings);
            }
            catch (Exception)
            {
                throw new GOServerException("Error in updating Vehicle");
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }
    }
}
