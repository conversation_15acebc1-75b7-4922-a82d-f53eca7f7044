// 002 - customer.cy.js

describe("002 - Customer (Create and Edit)", () => {
    let tempCompanyName, cypressFirstName, cypressLastName;

    before(() => {
        // Load test data from fixture
        cy.fixture('testData').then((testData) => {
            tempCompanyName = testData.tempCompanyName;
            cypressFirstName = testData.cypressFirstName;
            cypressLastName = testData.cypressLastName;
        });
    });

    beforeEach(() => {
        // Use the centralized login function from the support file
        cy.login();

        // Intercept the specific API call for dealer list before Step 2
        cy.intercept('/dataset/api/dealer/list*').as('getDealerList');

        // Intercept the API call for country list before interacting with the second dropdown
        cy.intercept('/dataset/api/country/list*').as('getCountryList');

        // Wait for the page to load by checking a key element
        cy.get("#nav-accordion-8735218d-3aeb-4563-bccb-8cdfcdf1188f > li:nth-of-type(2) span").should('exist').should('be.visible');
    });

    it("Should create a customer with Cypress company name", () => {
        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company else if not found continue and create
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(tempCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // CHECK IF THE COMPANY IS FOUND OR NOT
        cy.get('body').then($body => {
            // Check if the no-data-message exists and is visible
            if ($body.find('.no-data-message > span:visible').length > 0) {
                // No customer data found, create a new one
                cy.get('.no-data-message > span')
                    .should('exist')
                    .should('be.visible')
                    .and('contain', 'No Customer data available');

        // Create a new company
        cy.get('.topGridCommands > :nth-child(1) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();

        // **Wait for the API call to complete before interacting with the dropdown**
        cy.wait('@getDealerList').then((interception) => {
            cy.log('Dealer list API completed:', interception);
        });
                // Select items from the first lookup dropdown
        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsDealerVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper`)
            .should('exist')
            .should('be.visible')
            .click();

        // Select the first item from the dropdown
        cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']")
            .should('exist')
            .should('be.visible')
            .click();

        // **Wait for the country list API before interacting with the second dropdown**
                cy.wait('@getCountryList').then((interception) => {
                    cy.log('Country list API completed:', interception);
                });
                // Interact with the second lookup dropdown
        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsCountryVisible()"] > .form-field-control-container > .lookup-field > div.formEditLookupFieldControl > .lookupFieldWrapper > .ui-treeautocomplete-comboboxwrapper > .ui-treeautocomplete-wrapper`)
            .should('exist')
            .should('be.visible')
            .click();

        // Select the first item from the second dropdown
        cy.get("html > body > [data-test-id='lookup_wrapper'] > li:nth-of-type(1) > [data-test-id='lookup_item']")
            .should('exist')
            .should('be.visible')
            .eq(1)
            .click();

        // Fill in the fields for the customer creation form
        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsAddessVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .type("Test Address");

        cy.get(`[data-bind="'visible':StatusData.DisplayMode() == 'edit' && StatusData.IsCompanyNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
                    .type(tempCompanyName);

                cy.get('.save')
                    .should('exist')
                    .should('be.visible')
                    .click();

                cy.wait(1000);

                // Search for the company
                cy.get('.filterTextInputCustom')
                    .should('exist')
                    .should('be.visible')
                    .clear()
                    .type(tempCompanyName);

                cy.wait(1000);

                cy.get('.filterTextInputCustom').type('{enter}');
                cy.wait(1000);

                cy.get('td[data-bind="jqStopBubble: \'a\'"]')
                    .should('exist')
                    .should('be.visible')
                    .first()  // Select the first matching element if multiple exist
                    .click();

                cy.get(':nth-child(2) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();
            } else {
                // Customer data found, select the customer
                cy.get('td[data-bind="jqStopBubble: \'a\'"]')
                    .should('exist')
                    .should('be.visible')
                    .first()  // Select the first matching element if multiple exist
                    .click();

                cy.get(':nth-child(2) > .command-button')
                    .should('exist')
                    .should('be.visible')
                    .click();
            }
        });
    });

    it("Should edit a customer with Cypress company name", () => {

        // Step 1: Open the customer menu
        cy.get(`[data-bind="'enable' : navigation.isCustomersEnabled(), 'visible' : navigation.isCustomersVisible()"] > .nav-link`)
            .should('exist')
            .should('be.visible')
            .click();

        // Search for the company
        cy.get('.filterTextInputCustom')
            .should('exist')
            .should('be.visible')
            .type(tempCompanyName);

        cy.wait(1000);

        cy.get('.filterTextInputCustom').type('{enter}');
        cy.wait(1000);

        // Check if the company is found or not
        cy.get('body').then($body => {
            if ($body.find('.no-data-message > span:visible').length > 0) {
                cy.log('Company not found after creation');
            } else {
                cy.log('Company found after creation');
            }
        });

        // Select the customer
        cy.get('td[data-bind="jqStopBubble: \'a\'"]')
            .should('exist')
            .should('be.visible')
            .first()  // Select the first matching element if multiple exist
            .click();   

        // Edit the company
        cy.get(':nth-child(2) > .command-button')
            .should('exist')
            .should('be.visible')
            .click();       
        
        // Enable the edit mode
        cy.get('.btn-group > .edit')
            .should('exist')
            .should('be.visible')
            .click();
        
        // Update the company name
        cy.get(`[data-bind="'visible':CustomerForm1FormViewModel.StatusData.DisplayMode() == 'edit' && CustomerForm1FormViewModel.StatusData.IsCompanyNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('be.visible')
            .clear()
            .type(tempCompanyName + "-Updated");

        // Check if create contact button exists, then create contact only if it does
        cy.get('body').then($body => {
            const contactButton = $body.find('.optional-relation-command-zone > .pointer');
            if (contactButton.length > 0 && contactButton.is(':visible')) {
                // Create contact only if the button exists and is visible
                cy.get('.optional-relation-command-zone > .pointer')
                    .should('exist')
                    .should('be.visible')
                    .click();
            } else {
                cy.log('Contact creation button not found or not visible - skipping contact creation');
            }
        });

        // Enter first name
        cy.get(`[data-bind="'visible':ContactPersonInformationFormViewModel.StatusData.DisplayMode() == 'edit' && ContactPersonInformationFormViewModel.StatusData.IsFirstNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type(cypressFirstName);
        
        // Enter last name
        cy.get(`[data-bind="'visible':ContactPersonInformationFormViewModel.StatusData.DisplayMode() == 'edit' && ContactPersonInformationFormViewModel.StatusData.IsLastNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type(cypressLastName);

        // Enter phone number
        cy.get(`[data-bind="'visible':ContactPersonInformationFormViewModel.StatusData.DisplayMode() == 'edit' && ContactPersonInformationFormViewModel.StatusData.IsPhoneNoVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type("1234");

        // Enter email
        cy.get(`[data-bind="'visible':ContactPersonInformationFormViewModel.StatusData.DisplayMode() == 'edit' && ContactPersonInformationFormViewModel.StatusData.IsEmailVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type("<EMAIL>");

        // Enter address
        cy.get(`[data-bind="'visible':ContactPersonInformationFormViewModel.StatusData.DisplayMode() == 'edit' && ContactPersonInformationFormViewModel.StatusData.IsAddressVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type("Test Address");
            

        // Save the changes
        cy.get('.save')
            .should('exist')
            .should('be.visible')
            .click();
        
        
        cy.wait(500);

        // check if the company name is updated
        cy.get(`[data-bind="'css' : { errorControl : !CustomerForm2FormViewModel.CustomerObject().StatusData.isCompanyNameValid() }"] > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .and('contain', tempCompanyName + "-Updated");

        cy.get(`[data-bind="'visible':CustomerForm2FormViewModel.StatusData.DisplayMode() == 'view' && CustomerForm2FormViewModel.StatusData.IsContactPersonInformation_FullNameVisible()"] > [data-bind="'enable' : false"] > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .and('contain', cypressFirstName + " " + cypressLastName);

        //###### EDIT THE COMPANY NAME AGAIN AND PUT THE ORIGINAL COMPANY NAME #######
        // Enable the edit mode
        cy.get('.btn-group > .edit')
            .should('exist')
            .should('be.visible')
            .click();

        cy.get(`[data-bind="'visible':CustomerForm1FormViewModel.StatusData.DisplayMode() == 'edit' && CustomerForm1FormViewModel.StatusData.IsCompanyNameVisible()"] > .form-field-control-container > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .clear()
            .type(tempCompanyName);

        // Save the changes
        cy.get('.save')
            .should('exist')
            .should('be.visible')
            .click();

        cy.wait(500);

        // check if the company name is updated
        cy.get(`[data-bind="'css' : { errorControl : !CustomerForm2FormViewModel.CustomerObject().StatusData.isCompanyNameValid() }"] > :nth-child(1) > .text-field > .form-field-text`)
            .should('exist')
            .should('be.visible')
            .and('contain', tempCompanyName);
        
    });
});
