using System;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using NUnit.Framework;
using System.Linq;
using GenerativeObjects.Practices.ExceptionHandling;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class BroadcastMessageHistoryAPITest : TestBase
    {
        private IBroadcastMessageHistoryAPI _broadcastMessageHistoryAPI;
        private IDataFacade _dataFacade;
        private ILoggingService _logger;
        private IEmailService _emailService;
        private readonly string _testDatabaseName = $"BroadcastMessageHistoryAPITest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _departmentId;
        private Guid _modelId;
        private Guid _siteId;
        private Guid _vehicleId;
        private Guid _alertTypeId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            base.AddServiceRegistrations(services);

            _logger = Substitute.For<ILoggingService>();
            _emailService = Substitute.For<IEmailService>();
            services.AddTransient<ILoggingService>(sp => _logger);
            services.AddTransient<IEmailService>(sp => _emailService);
            services.AddTransient<IBroadcastMessageHistoryAPI, BroadcastMessageHistoryAPI>();
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _broadcastMessageHistoryAPI = _serviceProvider.GetRequiredService<IBroadcastMessageHistoryAPI>();

            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        [SetUp]
        public void SetUp()
        {
            _logger.ClearReceivedCalls();
        }

        private async Task CreateTestDataAsync()
        {
            // Create country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            // Create region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            // Create timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            // Create site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);
            _siteId = site.Id;

            // Create department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            department = await _dataFacade.DepartmentDataProvider.SaveAsync(department);
            _departmentId = department.Id;

            // Create model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            model.Type = ModelTypesEnum.Electric;
            model = await _dataFacade.ModelDataProvider.SaveAsync(model);
            _modelId = model.Id;

            // Create module
            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID1";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = $"test_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}_{_departmentId}";
            module.IsAllocatedToVehicle = true;
            module = await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = Guid.NewGuid();
            vehicle.CustomerId = _customerId;
            vehicle.DepartmentId = _departmentId;
            vehicle.ModelId = _modelId;
            vehicle.SiteId = _siteId;
            vehicle.ModuleId1 = module.Id;  // Set the module ID
            vehicle.HireNo = "Test Vehicle";
            vehicle.SerialNo = "Test Serial No";
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
            _vehicleId = vehicle.Id;

            // Create Broadcast Message Alert type
            var alertType = _serviceProvider.GetRequiredService<AlertDataObject>();
            alertType.Id = Guid.NewGuid();
            alertType.Name = "Broadcast Message Alert";
            alertType.Description = "Alert for broadcast message responses";
            alertType.Subject = "Broadcast Message Alert -";
            alertType.Paragraph1 = "Broadcast Message Alert has been triggered on the following Machine:";
            alertType.Paragraph2 = "Driver has responded to the broadcast message.";
            alertType = await _dataFacade.AlertDataProvider.SaveAsync(alertType);
            _alertTypeId = alertType.Id;
        }

        [Test]
        public async Task StoreBroadcastMessageHistoryAsync_WhenValidPayload_ShouldUpdateHistory()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_001";
            var timestamp = "65FB9C00"; // Hex timestamp for 2024-03-20T10:00:00
            var displayTimestamp = "65FB9C05"; // Hex timestamp for 2024-03-20T10:00:05
            var response = "Yes";

            // Create a broadcast message history record
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = _vehicleId;
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            // Get the auto-generated MessageId
            var messageId = history.MessageId;
            Assert.That(messageId, Is.GreaterThan(0), "Test setup failed: MessageId was not generated");

            var payload = $"MSG_RSP={messageId},{driverId},{timestamp},{displayTimestamp},{response}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Create a card with matching weigand and required fields
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Weigand = driverId;
            card.CardNumber = "TEST123";  // Required field
            card.Active = true;           // Required field
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            // Create a person which will automatically create a driver
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.CustomerId = _customerId;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Get the auto-created driver and associate it with the card
            var driver = await person.LoadDriverAsync();
            driver.Card = card;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(200));
            Assert.That(responseObj.detail, Is.EqualTo("Broadcast message history updated successfully"));

            // Verify history was updated
            var updatedHistory = await _dataFacade.BroadcastMessageHistoryDataProvider.GetAsync(history);
            Assert.That(updatedHistory.Response, Is.EqualTo(response));
            Assert.That(updatedHistory.ResponseTime, Is.EqualTo(DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(timestamp, 16)).UtcDateTime));
            Assert.That(updatedHistory.DisplayTime, Is.EqualTo(DateTimeOffset.FromUnixTimeSeconds(Convert.ToInt64(displayTimestamp, 16)).UtcDateTime));
            Assert.That(updatedHistory.DriverId, Is.EqualTo(driver.Id));
        }

        [Test]
        public async Task StoreBroadcastMessageHistoryAsync_WhenMessageIdIsMissing_ShouldReturnBadRequest()
        {
            // Arrange
            var payload = "MSG_RSP=,12345,65FB9C00,65FB9C05,Yes";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(400));
            Assert.That(responseObj.detail, Is.EqualTo("MessageId is required"));
        }

        [Test]
        public async Task StoreBroadcastMessageHistoryAsync_WhenHistoryNotFound_ShouldReturnNotFound()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_002";
            var payload = "MSG_RSP=999999,12345,65FB9C00,65FB9C05,Yes";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(404));
            Assert.That(responseObj.detail, Is.EqualTo("Broadcast message history not found"));
        }

        [Test]
        public async Task StoreBroadcastMessageHistoryAsync_WhenCardNotFound_ShouldReturnErrorResponse()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_003";
            var timestamp = "65FB9C00"; // Hex timestamp for 2024-03-20T10:00:00
            var displayTimestamp = "65FB9C05"; // Hex timestamp for 2024-03-20T10:00:05
            var response = "Yes";

            // Create a broadcast message history record first
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = _vehicleId;
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            // Get the auto-generated MessageId
            var messageId = history.MessageId;
            Assert.That(messageId, Is.GreaterThan(0), "Test setup failed: MessageId was not generated");

            // Create the payload using the auto-generated MessageId
            var payload = $"MSG_RSP={messageId},{driverId},{timestamp},{displayTimestamp},{response}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Verify the history was created
            var savedHistory = (await _dataFacade.BroadcastMessageHistoryDataProvider.GetCollectionAsync(
                null,
                "MessageId == @0",
                new object[] { messageId }
            )).FirstOrDefault();
            Assert.That(savedHistory, Is.Not.Null, "Test setup failed: History record was not created");

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(500));
            Assert.That(responseObj.detail, Is.EqualTo("Error storing broadcast message history"));

            // Verify both log calls
            _logger.Received(2).LogError(Arg.Is<GOServerException>(e =>
                e.Message == $"Card with Weigand {driverId} not found"));
        }

        [Test]
        [Category("BroadcastMessageAlert")]
        public async Task StoreBroadcastMessageHistoryAsync_WhenValidPayloadAndAlertSubscriptionExists_ShouldSendAlert()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_ALERT_001";
            var timestamp = "65FB9C00"; // Hex timestamp for 2024-03-20T10:00:00
            var displayTimestamp = "65FB9C05"; // Hex timestamp for 2024-03-20T10:00:05
            var response = "Yes";

            // Create a broadcast message history record
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = _vehicleId;
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            var messageId = history.MessageId;
            var payload = $"MSG_RSP={messageId},{driverId},{timestamp},{displayTimestamp},{response}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Create card and driver
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Weigand = driverId;
            card.CardNumber = "TEST123";
            card.Active = true;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.CustomerId = _customerId;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            driver.Card = card;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Create alert subscription
            var alertSubscription = _serviceProvider.GetRequiredService<AlertSubscriptionDataObject>();
            alertSubscription.Id = Guid.NewGuid();
            alertSubscription.AlertId = _alertTypeId;
            alertSubscription.IsActive = true;
            alertSubscription.PersonId = person.Id;
            alertSubscription = await _dataFacade.AlertSubscriptionDataProvider.SaveAsync(alertSubscription);

            // Create vehicle alert subscription
            var vehicleAlertSubscription = _serviceProvider.GetRequiredService<VehicleAlertSubscriptionDataObject>();
            vehicleAlertSubscription.Id = Guid.NewGuid();
            vehicleAlertSubscription.VehicleId = _vehicleId;
            vehicleAlertSubscription.AlertSubscriptionId = alertSubscription.Id;
            vehicleAlertSubscription = await _dataFacade.VehicleAlertSubscriptionDataProvider.SaveAsync(vehicleAlertSubscription);

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(200));

            // Verify email was sent with correct details
            await _emailService.Received(1).SendEmailAsync(Arg.Is<string>(emailJson =>
                VerifyEmailDetails(emailJson, "Broadcast Message Alert", $"{person.FirstName} {person.LastName}", _vehicleId, response)));
        }

        [Test]
        [Category("BroadcastMessageAlert")]
        public async Task StoreBroadcastMessageHistoryAsync_WhenNoAlertSubscription_ShouldNotSendAlert()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_ALERT_002";
            var timestamp = "65FB9C00";
            var displayTimestamp = "65FB9C05";
            var response = "Yes";

            // Create history, card, and driver (similar to previous test)
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = _vehicleId;
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            var messageId = history.MessageId;
            var payload = $"MSG_RSP={messageId},{driverId},{timestamp},{displayTimestamp},{response}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Weigand = driverId;
            card.CardNumber = "TEST123";
            card.Active = true;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.CustomerId = _customerId;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            driver.Card = card;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(200));

            // Verify no email was sent
            await _emailService.DidNotReceive().SendEmailAsync(Arg.Any<string>());
        }

        [Test]
        [Category("BroadcastMessageAlert")]
        public async Task StoreBroadcastMessageHistoryAsync_WhenAlertTypeNotFound_ShouldLogErrorAndContinue()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_ALERT_003";
            var timestamp = "65FB9C00";
            var displayTimestamp = "65FB9C05";
            var response = "Yes";

            // Create a module for the new vehicle
            var newModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            newModule.Id = Guid.NewGuid();
            newModule.Calibration = 100;
            newModule.CCID = "CCID2";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            newModule.FSSSBase = randomNumber * 10000;
            newModule.FSSXMulti = 1;
            newModule.IoTDevice = $"test_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}_{_departmentId}_2";
            newModule.IsAllocatedToVehicle = true;
            newModule = await _dataFacade.ModuleDataProvider.SaveAsync(newModule);

            // Create a new vehicle for this test
            var newVehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            newVehicle.Id = Guid.NewGuid();
            newVehicle.CustomerId = _customerId;
            newVehicle.DepartmentId = _departmentId;
            newVehicle.ModelId = _modelId;
            newVehicle.SiteId = _siteId;
            newVehicle.ModuleId1 = newModule.Id;  // Set the module ID
            newVehicle.HireNo = "Test Vehicle No Alert";
            newVehicle.SerialNo = "Test Serial No Alert";
            newVehicle = await _dataFacade.VehicleDataProvider.SaveAsync(newVehicle);

            // Create history and other necessary data
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = newVehicle.Id; // Use the new vehicle
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            var messageId = history.MessageId;
            var payload = $"MSG_RSP={messageId},{driverId},{timestamp},{displayTimestamp},{response}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Weigand = driverId;
            card.CardNumber = "TEST123";
            card.Active = true;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = "Driver";
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.CustomerId = _customerId;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            driver.Card = card;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(200));

            // Verify no email was sent since there's no alert subscription
            await _emailService.DidNotReceive().SendEmailAsync(Arg.Any<string>());
        }

        [Test]
        [TestCase("0", "OK")]
        [TestCase("1", "Yes")]
        [TestCase("2", "No")]
        [TestCase("3", "Timeout")]
        [TestCase("4", "Logout")]
        [TestCase("5", "No Driver")]
        public async Task StoreBroadcastMessageHistoryAsync_WhenBroadcastMessageExists_ShouldMapResponseCorrectly(string responseCode, string expectedResponse)
        {
            // Arrange
            var driverId = $"TEST_WEIGAND_RESPONSE_{responseCode}";
            var timestamp = "65FB9C00";
            var displayTimestamp = "65FB9C05";

            // Create a broadcast message first
            var broadcastMessage = _serviceProvider.GetRequiredService<BroadcastMessageDataObject>();
            broadcastMessage.Message = "Test broadcast message";
            broadcastMessage.Priority = MessagePriorityEnum.NormalMessage;
            broadcastMessage.ResponseOptions = ResponseOptionsEnum.YesNo;
            broadcastMessage.Timeout = 30;
            broadcastMessage = await _dataFacade.BroadcastMessageDataProvider.SaveAsync(broadcastMessage);

            // Create a broadcast message history record with the broadcast message ID
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = _vehicleId;
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            var payload = $"MSG_RSP={history.MessageId},{driverId},{timestamp},{displayTimestamp},{responseCode}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Create card and driver
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Weigand = driverId;
            card.CardNumber = $"TEST{responseCode}";
            card.Active = true;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = $"Driver{responseCode}";
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.CustomerId = _customerId;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            driver.Card = card;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(200));

            // Verify the response was mapped correctly
            var updatedHistory = await _dataFacade.BroadcastMessageHistoryDataProvider.GetAsync(history);
            Assert.That(updatedHistory.Response, Is.EqualTo(expectedResponse),
                $"Response code '{responseCode}' should map to '{expectedResponse}' but got '{updatedHistory.Response}'");
        }

        [Test]
        public async Task StoreBroadcastMessageHistoryAsync_WhenBroadcastMessageNotFound_ShouldStoreRawResponse()
        {
            // Arrange
            var driverId = "TEST_WEIGAND_RAW_RESPONSE";
            var timestamp = "65FB9C00";
            var displayTimestamp = "65FB9C05";
            var rawResponse = "CustomResponse123";

            // Create a broadcast message history record with a non-existent broadcast message ID
            var history = _serviceProvider.GetRequiredService<BroadcastMessageHistoryDataObject>();
            history.MessageId = 999999; // Non-existent ID
            history.Message = "Test message";
            history.Type = ResponseOptionsEnum.YesNo;
            history.SentTime = DateTime.UtcNow;
            history.VehicleId = _vehicleId;
            history = await _dataFacade.BroadcastMessageHistoryDataProvider.SaveAsync(history);

            var payload = $"MSG_RSP={history.MessageId},{driverId},{timestamp},{displayTimestamp},{rawResponse}";
            var message = JsonConvert.SerializeObject(new { Payload = payload });

            // Create card and driver
            var card = _serviceProvider.GetRequiredService<CardDataObject>();
            card.Weigand = driverId;
            card.CardNumber = "TESTRAW";
            card.Active = true;
            card = await _dataFacade.CardDataProvider.SaveAsync(card);

            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.FirstName = "Test";
            person.LastName = "RawDriver";
            person.SiteId = _siteId;
            person.DepartmentId = _departmentId;
            person.CustomerId = _customerId;
            person = await _dataFacade.PersonDataProvider.SaveAsync(person);

            var driver = await person.LoadDriverAsync();
            driver.Card = card;
            driver = await _dataFacade.DriverDataProvider.SaveAsync(driver);

            // Act
            var result = await _broadcastMessageHistoryAPI.StoreBroadcastMessageHistoryAsync(message);

            // Assert
            Assert.That(result.Result, Is.Not.Null);
            var responseObj = JsonConvert.DeserializeObject<DefaultResponse>(result.Result);
            Assert.That(responseObj.status, Is.EqualTo(200));

            // Verify the raw response was stored
            var updatedHistory = await _dataFacade.BroadcastMessageHistoryDataProvider.GetAsync(history);
            Assert.That(updatedHistory.Response, Is.EqualTo(rawResponse),
                $"When broadcast message is not found, raw response '{rawResponse}' should be stored but got '{updatedHistory.Response}'");
        }

        private bool VerifyEmailDetails(string emailJson, string alertType, string driverName, Guid vehicleId, string response)
        {
            try
            {
                var emailDetail = JsonConvert.DeserializeObject<EmailDetail>(emailJson);
                return emailDetail.Alert == alertType &&
                       emailDetail.DriverName == driverName &&
                       emailDetail.VehicleId == vehicleId &&
                       emailDetail.Details.Response == response;
            }
            catch
            {
                return false;
            }
        }
    }
}