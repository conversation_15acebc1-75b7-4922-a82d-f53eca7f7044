---
type: "always_apply"
---

## Core Development Principles
- **Zero Technical Debt**: Deliver production-ready code that requires no refactoring or cleanup
- **Complete Solution Ownership**: Take full responsibility for the correctness, performance, and maintainability of all generated code
- **Exact Requirement Matching**: Implement solutions that precisely fulfill stated requirements without over-engineering or under-delivering
- **Current Scope Focus**: Address only the immediate problem without adding speculative features or future-proofing
- **DRY/KISS Adherence**: Eliminate code duplication and maintain simplicity in all implementations
- **Minimal, Intuitive Code**: Write the shortest possible code that remains clear and maintainable
- **Developer Experience Priority**: Optimize for code readability and ease of maintenance
- **Root Cause Solutions**: Always identify and fix underlying issues rather than applying surface-level patches
- **Comprehensive Documentation**: Provide detailed summaries that capture all critical implementation details and decisions
- **Pragmatic Simplicity**: Choose the simplest approach that fully solves the problem

## Code Quality Standards
- **No Code Comments**: Write self-explanatory code that doesn't require inline documentation unless for very complicated logic
- **Zero Boilerplate**: Remove all unnecessary scaffolding, imports, and redundant patterns
- **Descriptive Naming**: Use clear, intention-revealing names for variables, functions, and classes
- **Industry Best Practices**: Follow established design patterns and architectural principles appropriate to the technology stack
- **Maximum Reusability**: Structure components and functions for easy reuse across the codebase
- **Performance Optimization**: Write efficient code without compromising readability or maintainability
- **Robust Error Handling**: Implement comprehensive error handling that gracefully manages edge cases and failure scenarios
- **Technology-Specific Standards**: Apply language and framework-specific conventions (e.g., C# naming conventions, JavaScript ES6+ features, SQL optimization techniques)

## Context-Aware Development
- **FleetXQ Architecture Compliance**: Ensure all code aligns with the existing layered architecture (Web/Service/Business/Data layers)
- **Database Integration**: Follow established patterns for SQL Server and NHibernate ORM usage
- **Security Implementation**: Apply existing authentication and authorization patterns consistently
- **Custom Code Integration**: Properly extend generated code using the CustomCode directory structure