﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
 

namespace FleetXQ.Data.DataProviders.Custom
{
    public class VehicleUtilizationLastTwelveHoursStoreProcedureDataProvider : DataProvider<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>
    {
        public VehicleUtilizationLastTwelveHoursStoreProcedureDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task DoDeleteAsync(VehicleUtilizationLastTwelveHoursStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> DoGetAsync(VehicleUtilizationLastTwelveHoursStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<VehicleUtilizationLastTwelveHoursStoreProcedureDataObject> DoSaveAsync(VehicleUtilizationLastTwelveHoursStoreProcedureDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
