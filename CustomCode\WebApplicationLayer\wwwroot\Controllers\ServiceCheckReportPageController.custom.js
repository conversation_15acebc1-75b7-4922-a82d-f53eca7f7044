﻿(function () {

    FleetXQ.Web.Controllers.ServiceCheckReportPageControllerCustom = function (controller) {
        var self = this;
        this.controller = controller;

        this.getDefaultConfiguration = function () {
            var configuration = {};
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            var AllowedSiteIds = self.controller.applicationController.viewModel.security.currentUserClaims().AllowedSiteIds;
            // Remove the curly braces and spaces, then split the string into an array
            var array = AllowedSiteIds.replace(/[{} ]/g, '').split(',');
            // Get the first element of the array, if empty null
            var siteId = array[0] || null;
    
        
            var parameterCount = 0;
        
            if (customerId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + customerId + '" }';
            }
        
            if (siteId != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + siteId + '" }';
            }
            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }
            return configuration;
        };

        this.getConfiguration = function () {
            var configuration = {};
            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;
            configuration.filterPredicate = "CustomerId == @0 && SiteId == @1 && DepartmentId == @2";
            configuration.filterParameters = '[{ "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.CustomerId() ? '"' + currentData.CustomerId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.SiteId() ? '"' + currentData.SiteId() + '"' : 'null') + ' }, { "TypeName" : "System.Guid", "IsNullable" : true, "Value" : ' + (currentData.DepartmentId() ? '"' + currentData.DepartmentId() + '"' : 'null') + ' }]';
            return configuration;
        };

        this.LoadVehicleGridGridViewData = function () {
            var configuration = {
            };

            var currentData = self.controller.DashboardFilterFormViewModel.CurrentObject().Data;

            
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (currentData.CustomerId() == null && customerId != null) {
                var defaultConfig = self.getDefaultConfiguration();
                self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection(defaultConfig);
                return;
            }

            var parameterCount = 0;

            if (currentData.CustomerId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Department.Site.CustomerId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.CustomerId() + '" }';
            }
        
            if (currentData.SiteId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'Department.SiteId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.SiteId() + '" }';
            }
        
            if (currentData.DepartmentId() != null) {
                configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
                configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';
        
                configuration.filterPredicate += 'DepartmentId == @' + parameterCount++;
                configuration.filterParameters += '{ "TypeName" : "System.Guid", "IsNullable" : false, "Value" : "' + currentData.DepartmentId() + '" }';
            }

            // add filterpredicate for ServiceSettingsId != null
            configuration.filterPredicate = configuration.filterPredicate ? configuration.filterPredicate + ' && ' : '';
            configuration.filterParameters =configuration.filterParameters ? configuration.filterParameters + ', ' : '';

            configuration.filterPredicate += 'ServiceSettingsId != null';

            if (configuration.filterParameters) {
                configuration.filterParameters = '[' + configuration.filterParameters + ']';
            }

            self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.exportFilterPredicate = configuration.filterPredicate;
            self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.exportFilterParameters = configuration.filterParameters;
            self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection(configuration);
        };
        

        this.loadInitialGridData = function () {
            var customerId = self.controller.applicationController.viewModel.security.currentUserClaims().CustomerId;
            if (!GO.Filter.hasUrlFilter(self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.FILTER_NAME, self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel)) {
                if (customerId != null) {
                    var defaultConfig = self.getDefaultConfiguration();
                    self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection(defaultConfig);
                    return;
                }
				self.controller.ServiceSettingsForm1ViewModel.VehicleGridGridViewModel.LoadVehicleObjectCollection();
			}
        }


        this.initialize = function () {
            // to avoid to have the message asking to confirm changing page and lose changes (caused at the moment by the dashboard filter that is a view in edit mode)
            self.controller.IsInEditMode = function () {
                return false;
            }

            // if (!sessionStorage.getItem('hasReloaded')) {
            //     // Set the flag before reloading
            //     sessionStorage.setItem('hasReloaded', 'true');
                
            //     // Force a reload after a brief delay to ensure hash is set
            //     window.location.reload();
            // } else {
            //     // Clear the flag for next time
            //     sessionStorage.removeItem('hasReloaded');
            // }

            // We create here the filterData function that will be called by the custom client component FleetXQ.Web.Model.Components.DashboardFilter

            self.controller.DashboardFilterFormViewModel.filterData = function () {
                // Check if user is DealerAdmin
                var userRole = self.controller.applicationController.viewModel.security.currentUserClaims().role;
                if (userRole === 'DealerAdmin') {
                    // For DealerAdmin, verify that a customer is selected
                    var customerId = self.controller.DashboardFilterFormViewModel.CurrentObject().Data.CustomerId();
                    if (!customerId) {
                        self.controller.DashboardFilterFormViewModel.ShowError('Please select a customer');
                        return;
                    }
                }
                
                self.LoadVehicleGridGridViewData();
            };

            // self.loadInitialGridData();
        };
    };

})();