import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock the FleetXQ namespace with a proper structure
global.FleetXQ = {
    Web: {
        Controllers: {}
    }
};

// Create a mock for window before importing the controller
global.window = {
    ApplicationController: {
        viewModel: {
            pageController: {
                PersonFormViewModel: {
                    StatusData: {
                        DisplayMode: vi.fn()
                    }
                }
            }
        }
    }
};

// Import the controller file
require('../../../WebApplicationLayer/wwwroot/Controllers/PersonDetailPagePageController.custom.js');

describe('PersonDetailPagePageController.custom.js', () => {
    let controller;
    let mockWindow;

    beforeEach(() => {
        // Reset mocks
        vi.resetAllMocks();

        // Reset the window mock before each test
        mockWindow = {
            ApplicationController: {
                viewModel: {
                    pageController: {
                        PersonFormViewModel: {
                            StatusData: {
                                DisplayMode: vi.fn()
                            }
                        }
                    }
                }
            }
        };
        global.window = mockWindow;

        // Initialize the controller
        const baseController = {};
        controller = new FleetXQ.Web.Controllers.PersonDetailPagePageControllerCustom(baseController);
        controller.initialize();
    });

    describe('IsInEditMode', () => {
        it('should return true when DisplayMode is edit', () => {
            // Arrange
            mockWindow.ApplicationController.viewModel.pageController.PersonFormViewModel.StatusData.DisplayMode.mockReturnValue('edit');

            // Act
            const result = FleetXQ.Web.Controllers.IsInEditMode();

            // Assert
            expect(result).toBe(true);
        });

        it('should return false when DisplayMode is view', () => {
            // Arrange
            mockWindow.ApplicationController.viewModel.pageController.PersonFormViewModel.StatusData.DisplayMode.mockReturnValue('view');

            // Act
            const result = FleetXQ.Web.Controllers.IsInEditMode();

            // Assert
            expect(result).toBe(false);
        });
    });

    describe('Controller initialization', () => {
        it('should properly initialize the controller', () => {
            // Arrange & Act
            const newController = new FleetXQ.Web.Controllers.PersonDetailPagePageControllerCustom({});
            
            // Assert
            expect(newController).toBeDefined();
            expect(newController.initialize).toBeDefined();
            expect(typeof newController.initialize).toBe('function');
        });

        it('should set up IsInEditMode function after initialization', () => {
            // Assert
            expect(FleetXQ.Web.Controllers.IsInEditMode).toBeDefined();
            expect(typeof FleetXQ.Web.Controllers.IsInEditMode).toBe('function');
        });
    });
});
