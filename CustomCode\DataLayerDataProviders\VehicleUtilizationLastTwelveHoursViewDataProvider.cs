﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq.Expressions;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class VehicleUtilizationLastTwelveHoursViewDataProvider : DataProvider<VehicleUtilizationLastTwelveHoursViewDataObject>
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<VehicleUtilizationLastTwelveHoursViewDataProvider> _logger;

        public VehicleUtilizationLastTwelveHoursViewDataProvider(
            IServiceProvider serviceProvider,
            IDataProviderTransaction transaction,
            IEntityDataProvider entityDataProvider,
            IDataProviderDispatcher<VehicleUtilizationLastTwelveHoursViewDataObject> dispatcher,
            IDataProviderDeleteStrategy dataProviderDeleteStrategy,
            IAutoInclude autoInclude,
            IThreadContext threadContext,
            IDataProviderTransaction dataProviderTransaction,
            IConfiguration configuration,
            ILogger<VehicleUtilizationLastTwelveHoursViewDataProvider> logger)
            : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
            _logger = logger;
        }

        protected override async Task<DataObjectCollection<VehicleUtilizationLastTwelveHoursViewDataObject>> DoGetCollectionAsync(
            LambdaExpression securityFilterExpression,
            string filterPredicate,
            object[] filterArguments,
            string orderByPredicate,
            int pageNumber,
            int pageSize,
            List<string> includes,
            IObjectsDataSet context,
            Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<VehicleUtilizationLastTwelveHoursViewDataObject>();
            result.ObjectsDataSet = context;

            try
            {
                _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Starting DoGetCollectionAsync with filter arguments: {FilterArguments}",
                    filterArguments != null ? string.Join(", ", filterArguments.Select(a => a?.ToString() ?? "null")) : "null");
                _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Filter predicate: {FilterPredicate}", filterPredicate ?? "null");

                // Log all parameters
                if (parameters != null)
                {
                    _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Parameters dictionary contains {Count} items", parameters.Count);
                    foreach (var param in parameters)
                    {
                        _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Parameter Key: {Key}, Value Type: {Type}, Raw Value: {Value}",
                            param.Key,
                            param.Value?.GetType().FullName ?? "null",
                            param.Value);
                    }
                }
                else
                {
                    _logger.LogWarning("[VehicleUtilizationLastTwelveHoursViewDataProvider] Parameters dictionary is null");
                }

                // Get parameters from filter arguments
                DateTime? referenceDate = null;
                Guid? customerId = null;
                Guid? siteId = null;
                Guid? departmentId = null;

                if (filterArguments != null)
                {
                    _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Filter arguments length: {Length}", filterArguments.Length);

                    // Log all filter arguments for debugging
                    for (int i = 0; i < filterArguments.Length; i++)
                    {
                        _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Filter argument {Index}: Type = {Type}, Value = {Value}",
                            i,
                            filterArguments[i]?.GetType().FullName ?? "null",
                            filterArguments[i]?.ToString() ?? "null");
                    }

                    // When only one parameter is passed and it's a DateTime, it's the reference date
                    if (filterArguments.Length == 1 && filterArguments[0] is DateTime)
                    {
                        referenceDate = (DateTime)filterArguments[0];
                        _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Using single date parameter: {Date}", referenceDate);
                    }
                    else
                    {
                        // Process each parameter based on its type
                        for (int i = 0; i < filterArguments.Length; i++)
                        {
                            if (filterArguments[i] == null) continue;

                            // If it's a DateTime, it's our reference date
                            if (filterArguments[i] is DateTime dateValue)
                            {
                                referenceDate = dateValue;
                                _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Found date at index {Index}: {Date}", i, referenceDate);
                            }
                            // If it's a Guid, assign it to the appropriate ID based on order
                            else if (filterArguments[i] is Guid guidValue)
                            {
                                if (customerId == null)
                                {
                                    customerId = guidValue;
                                    _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Found CustomerId at index {Index}: {Id}", i, customerId);
                                }
                                else if (siteId == null)
                                {
                                    siteId = guidValue;
                                    _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Found SiteId at index {Index}: {Id}", i, siteId);
                                }
                                else if (departmentId == null)
                                {
                                    departmentId = guidValue;
                                    _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Found DepartmentId at index {Index}: {Id}", i, departmentId);
                                }
                            }
                            else
                            {
                                _logger.LogWarning("[VehicleUtilizationLastTwelveHoursViewDataProvider] Unexpected parameter type at index {Index}: {Type}", 
                                    i, 
                                    filterArguments[i].GetType().FullName);
                            }
                        }
                    }

                    _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Final parameter values: CustomerId = {CustomerId}, SiteId = {SiteId}, DepartmentId = {DepartmentId}, ReferenceDate = {ReferenceDate}",
                        customerId,
                        siteId,
                        departmentId,
                        referenceDate);
                }

                using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
                {
                    _logger.LogDebug("[VehicleUtilizationLastTwelveHoursViewDataProvider] Opening SQL connection");
                    await connection.OpenAsync();

                    using (var command = new SqlCommand("GetVehicleUtilizationLastTwelveHours", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        // Add parameters in the order expected by the stored procedure
                        command.Parameters.AddWithValue("@CustomerId", customerId.HasValue ? (object)customerId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@SiteId", siteId.HasValue ? (object)siteId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@DepartmentId", departmentId.HasValue ? (object)departmentId.Value : DBNull.Value);
                        command.Parameters.AddWithValue("@ReferenceDate", referenceDate.HasValue ? (object)referenceDate.Value : DBNull.Value);

                        // Log final parameter collection for debugging
                        foreach (SqlParameter param in command.Parameters)
                        {
                            _logger.LogDebug("[VehicleUtilizationLastTwelveHoursViewDataProvider] Parameter {Name}: Type = {Type}, Value = {Value}, SqlDbType = {SqlDbType}", 
                                param.ParameterName,
                                param.Value?.GetType().FullName ?? "null",
                                param.Value?.ToString() ?? "null",
                                param.SqlDbType);
                        }

                        _logger.LogDebug("[VehicleUtilizationLastTwelveHoursViewDataProvider] Executing stored procedure");
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    try
                                    {
                                        var entity = _serviceProvider.GetRequiredService<VehicleUtilizationLastTwelveHoursViewDataObject>();
                                        entity.ObjectsDataSet = context;

                                        // Log column ordinals for debugging
                                        var columnOrdinals = new Dictionary<string, int>
                                        {
                                            { "Id", reader.GetOrdinal("Id") },
                                            { "DealerId", reader.GetOrdinal("DealerId") },
                                            { "CustomerId", reader.GetOrdinal("CustomerId") },
                                            { "SiteId", reader.GetOrdinal("SiteId") },
                                            { "DepartmentId", reader.GetOrdinal("DepartmentId") },
                                            { "NumberOfSessions", reader.GetOrdinal("NumberOfSessions") },
                                            { "TimePeriod", reader.GetOrdinal("TimePeriod") }
                                        };
                                        _logger.LogDebug("[VehicleUtilizationLastTwelveHoursViewDataProvider] Column ordinals: {Ordinals}", columnOrdinals);

                                        // Set Id first
                                        entity.Id = reader.GetGuid(columnOrdinals["Id"]);

                                        // Now mark as not new after Id is set
                                        entity.IsNew = false;

                                        if (!reader.IsDBNull(columnOrdinals["DealerId"]))
                                            entity.DealerId = reader.GetGuid(columnOrdinals["DealerId"]);

                                        if (!reader.IsDBNull(columnOrdinals["CustomerId"]))
                                            entity.CustomerId = reader.GetGuid(columnOrdinals["CustomerId"]);

                                        if (!reader.IsDBNull(columnOrdinals["SiteId"]))
                                            entity.SiteId = reader.GetGuid(columnOrdinals["SiteId"]);

                                        if (!reader.IsDBNull(columnOrdinals["DepartmentId"]))
                                            entity.DepartmentId = reader.GetGuid(columnOrdinals["DepartmentId"]);

                                        if (!reader.IsDBNull(columnOrdinals["NumberOfSessions"]))
                                        {
                                            var value = reader.GetInt32(columnOrdinals["NumberOfSessions"]);
                                            entity.NumberOfSessions = value <= Int16.MaxValue ? (Int16)value : Int16.MaxValue;
                                        }

                                        if (!reader.IsDBNull(columnOrdinals["TimePeriod"]))
                                            entity.TimePeriod = reader.GetString(columnOrdinals["TimePeriod"]);

                                        result.Add(entity);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, "[VehicleUtilizationLastTwelveHoursViewDataProvider] Error mapping data reader to entity");
                                        throw new GOServerException("Error mapping data reader to entity", "Error mapping data reader to entity", ex);
                                    }
                                }
                            }
                            else
                            {
                                _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] No rows returned from stored procedure");
                            }
                        }
                    }
                }

                _logger.LogInformation("[VehicleUtilizationLastTwelveHoursViewDataProvider] Successfully completed DoGetCollectionAsync. Returning {Count} items", result.Count);
                return result;
            }
            catch (SqlException ex)
            {
                _logger.LogError(ex, "[VehicleUtilizationLastTwelveHoursViewDataProvider] SQL error in DoGetCollectionAsync");
                throw new GOServerException("Database error in GetVehicleUtilizationLastTwelveHours", ex.Message, ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[VehicleUtilizationLastTwelveHoursViewDataProvider] Unexpected error in DoGetCollectionAsync");
                throw new GOServerException("Error in GetVehicleUtilizationLastTwelveHours", "An unexpected error occurred", ex);
            }
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task DoDeleteAsync(VehicleUtilizationLastTwelveHoursViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<VehicleUtilizationLastTwelveHoursViewDataObject> DoGetAsync(VehicleUtilizationLastTwelveHoursViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<VehicleUtilizationLastTwelveHoursViewDataObject> DoSaveAsync(VehicleUtilizationLastTwelveHoursViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
