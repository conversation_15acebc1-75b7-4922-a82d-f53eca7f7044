@echo off
:: =============================================================================
:: VuePress Documentation Deploy and Serve Script
:: =============================================================================
:: Usage:
::   deploydocs.cmd              - Build, deploy to IIS and local, then serve
::   deploydocs.cmd --local-only - Build, deploy locally only, then serve
::
:: Description:
::   This script builds VuePress documentation, deploys it to specified locations,
::   and automatically starts a local development server for immediate preview.
:: =============================================================================

setlocal EnableDelayedExpansion

:: Parse command line arguments
SET LOCAL_ONLY=0
IF "%1"=="--local-only" SET LOCAL_ONLY=1

:: Set paths
SET INETPATH=C:\inetpub\wwwroot\GOApps\fleetxq-docs
SET LOCALPATH=%USERPROFILE%\fleetxq-docs-local

echo.
echo ================================
echo  VuePress Documentation Deploy
echo ================================
echo.

:: Change to the TechDoc directory
cd ..\TechDoc
IF %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Could not find TechDoc directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo Current directory: %CD%
echo.

:: Check for Node.js
node --version >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    cd ..\Scripts
    exit /b 1
)

:: Check if package.json exists
IF NOT EXIST package.json (
    echo [ERROR] package.json not found in TechDoc directory
    echo Current directory: %CD%
    pause
    cd ..\Scripts
    exit /b 1
)

:: Install dependencies if needed
IF NOT EXIST node_modules (
    echo Installing npm dependencies...
    call npm install
    IF !ERRORLEVEL! NEQ 0 (
        echo [ERROR] Failed to install dependencies
        pause
        cd ..\Scripts
        exit /b 1
    )
) ELSE (
    echo Dependencies already installed, skipping npm install...
)

echo.
echo Building VuePress documentation...
call npm run docs:build

IF %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Build failed! Please check the errors above.
    pause
    cd ..\Scripts
    exit /b %ERRORLEVEL%
)

:: Check if build output exists
IF NOT EXIST docs\.vuepress\dist (
    echo [ERROR] Build directory not found at docs\.vuepress\dist
    echo The build might have failed silently
    pause
    cd ..\Scripts
    exit /b 1
)

echo Build completed successfully!
echo.

:: Return to Scripts directory for deployment
cd ..\Scripts

:: Deploy built files
IF %LOCAL_ONLY%==1 (
    echo Deploying documentation locally only...
    IF NOT EXIST "%LOCALPATH%" mkdir "%LOCALPATH%"
    robocopy "..\TechDoc\docs\.vuepress\dist" "%LOCALPATH%" *.* /S /R:5 /W:2 /NFL /NDL /PURGE
    echo Local deployment completed at: %LOCALPATH%
) ELSE (
    echo Deploying documentation to IIS and local...
    
    :: Deploy to IIS
    IF NOT EXIST "%INETPATH%" mkdir "%INETPATH%"
    robocopy "..\TechDoc\docs\.vuepress\dist" "%INETPATH%" *.* /S /R:5 /W:2 /NFL /NDL /PURGE
    echo IIS deployment completed at: %INETPATH%
    
    :: Also deploy locally
    IF NOT EXIST "%LOCALPATH%" mkdir "%LOCALPATH%"
    robocopy "..\TechDoc\docs\.vuepress\dist" "%LOCALPATH%" *.* /S /R:5 /W:2 /NFL /NDL /PURGE
    echo Local deployment completed at: %LOCALPATH%
)

echo.
echo ================================
echo  Starting Local Development Server
echo ================================
echo.
echo Your documentation will be available at: http://localhost:3000
echo.
echo -------------------------------------------------------
echo To stop the server and close this window:
echo 1. Press Ctrl+C
echo 2. When asked "Terminate batch job (Y/N)?", type Y and press Enter
echo -------------------------------------------------------
echo.
echo Current directory: %CD%
echo Server starting now...
echo.

:: Navigate to local deployment and serve
cd "%LOCALPATH%"
npx serve

:: Return to Scripts directory when server stops
cd %~dp0