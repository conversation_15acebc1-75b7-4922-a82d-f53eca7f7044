---
alwaysApply: false
---
# Prompt Enhancement System Instructions

## Trigger Activation
When the user inputs a prompt starting with **"enhance:"** followed by their initial prompt, activate the enhancement system.

## System Flow

### 1. Input Recognition
- Detect the keyword trigger: `enhance: {initial prompt}`
- Extract the core prompt after the "enhance:" prefix
- Identify the domain/context (e.g., debugging, feature development, system analysis)

### 2. Context Analysis Phase

#### A. Problem Domain Identification
- **Technical Stack Detection**: Identify mentioned technologies, frameworks, APIs, or tools
- **Issue Classification**: Determine if it's a bug, feature request, integration issue, performance problem, etc.
- **Scope Assessment**: Understand if it's frontend, backend, full-stack, database, or infrastructure related

#### B. Implicit Context Extraction
- **Error Pattern Recognition**: Look for keywords indicating failure modes (e.g., "not working", "still broken", "fails")
- **Previous Attempt Indicators**: Identify phrases suggesting prior efforts ("still", "despite", "after trying")
- **System Component Mapping**: Infer involved components from minimal descriptions

#### C. Knowledge Gap Analysis
- Identify what information is missing but likely relevant
- Determine what questions a developer would need answered
- Recognize unstated assumptions that need verification

### 3. Enhancement Rules

#### Rule 1: Problem Decomposition
Transform vague issues into specific, investigatable components:
- Break down "X doesn't work" into:
  - What specific functionality is failing?
  - What are the expected vs. actual behaviors?
  - What are the potential failure points in the system flow?

#### Rule 2: Technical Specification Expansion
For any mentioned technical element:
- **APIs**: Include endpoint paths, HTTP methods, request/response formats
- **Features**: Specify user interactions, data flow, and system boundaries
- **Errors**: Detail error messages, logs, stack traces needed

#### Rule 3: Investigation Pathway Generation
Create a structured debugging approach:
1. **Symptom Documentation**: What exactly is observed?
2. **Reproduction Steps**: How to consistently trigger the issue?
3. **Isolation Strategy**: How to narrow down the root cause?
4. **Verification Method**: How to confirm the fix works?

#### Rule 4: Layer-Based Analysis
For full-stack issues, systematically address each layer:
- **Frontend Layer**: UI components, event handlers, state management, API calls
- **Network Layer**: Request formation, headers, payload structure, response handling
- **Backend Layer**: Route handling, middleware, business logic, data processing
- **Data Layer**: Database queries, data models, persistence logic
- **Integration Layer**: Third-party services, external APIs, authentication

#### Rule 5: Action-Oriented Structuring
Transform passive descriptions into active tasks:
- Replace "The search doesn't work" with "Investigate why search parameters are not being processed"
- Convert "API fails" to "Debug the API endpoint by checking [specific items]"

### 4. Output Generation Template

```
[ENHANCED PROMPT]

## Problem Statement
[Detailed description of the issue with technical context]

## Current Situation Analysis
[What's happening vs. what should happen]

## Investigation Areas

### 1. [Primary Component] Investigation
**Objective**: [What to verify in this component]
**Check Points**:
- [Specific item to examine]
- [Method or file to review]
- [Configuration to validate]

### 2. [Secondary Component] Investigation  
**Objective**: [What to verify in this component]
**Check Points**:
- [Specific item to examine]
- [Method or file to review]
- [Configuration to validate]

### 3. End-to-End Validation
**Test Flow**:
1. [First step to test]
2. [Second step to test]
3. [Validation of results]

## Success Criteria
- [ ] [Specific measurable outcome]
- [ ] [Another measurable outcome]
- [ ] [Final validation point]

## Potential Root Causes
1. [Most likely cause based on symptoms]
2. [Secondary possibility]
3. [Less likely but worth checking]

## Recommended Fix Approach
1. [First action to take]
2. [Follow-up action]
3. [Verification step]
```

### 5. Enhancement Patterns by Problem Type

#### A. Debugging Issues
- Include systematic elimination steps
- Add logging/monitoring points
- Specify exact error reproduction steps
- Detail expected vs. actual behavior comparison

#### B. Integration Problems
- Map data flow between systems
- Identify authentication/authorization checkpoints
- Specify protocol requirements (REST, GraphQL, WebSocket)
- Include timeout and retry considerations

#### C. Performance Issues
- Add metrics collection points
- Specify baseline vs. current performance
- Include profiling methodology
- Detail resource utilization checks

#### D. Feature Implementation
- Expand acceptance criteria
- Include edge cases and error handling
- Specify backward compatibility requirements
- Add testing strategy

### 6. Context Inference Guidelines

When limited information is provided, infer context using these patterns:

#### Technical Stack Inference
- "API" → Likely REST/HTTP, need to check endpoints, methods, headers
- "search" → Involves query parameters, filtering logic, result pagination
- "doesn't work" → Need error messages, console logs, network traces
- "payload" → Data serialization, content-type headers, request body structure

#### Temporal Context
- "still" → Previous attempts made, need different approach
- "again" → Regression issue, check recent changes
- "suddenly" → Environmental change, check configurations/dependencies

#### Scope Expansion
- Single feature mention → Consider full user journey
- One component → Check integrated components
- Client issue → Verify server-side handling
- Data problem → Check entire CRUD cycle

### 7. Quality Checklist for Enhanced Prompts

- **Specificity**: Replaces vague terms with technical specifics
- **Completeness**: Addresses all system layers involved
- **Actionability**: Every section has clear, executable steps
- **Measurability**: Includes success criteria and validation methods
- **Structure**: Organized in logical investigation sequence
- **Context**: Incorporates inferred technical and business context
- **Efficiency**: Prioritizes most likely issues first
- **Documentation**: Includes what to log/record during investigation

## Example Transformations

### Example 1: Basic Bug Report
**Input**: "enhance: the login is broken"

**Output**: 
"The user authentication flow is failing at one or more points in the login process. Investigation needed for:

1. **Frontend Authentication Flow**
   - Verify login form validation and error handling
   - Check credential payload structure in authentication request
   - Examine token storage mechanism (localStorage/sessionStorage/cookies)
   - Review redirect logic post-authentication

2. **Backend Authentication Processing**
   - Validate endpoint `/api/auth/login` is receiving credentials
   - Check password hashing and comparison logic
   - Verify JWT/session token generation
   - Examine user lookup in database

3. **Session Management**
   - Confirm token validation on protected routes
   - Check token expiration handling
   - Verify refresh token mechanism if applicable

Testing checklist:
- [ ] Valid credentials return success response with token
- [ ] Invalid credentials return appropriate error message
- [ ] Token is properly stored and sent with subsequent requests
- [ ] Protected routes are accessible with valid token
- [ ] Session persistence works across page refreshes"

### Example 2: Performance Issue
**Input**: "enhance: the dashboard is slow"

**Output**:
"Dashboard performance degradation requires systematic analysis across multiple layers:

1. **Frontend Rendering Analysis**
   - Profile React/Vue/Angular component render cycles
   - Identify unnecessary re-renders or state updates
   - Check for memory leaks in event listeners
   - Analyze bundle size and code splitting opportunities

2. **API Performance Audit**
   - Measure response times for dashboard data endpoints
   - Check for N+1 query problems in data fetching
   - Analyze payload sizes and consider pagination
   - Review caching strategy for frequently accessed data

3. **Database Query Optimization**
   - Examine query execution plans for dashboard queries
   - Verify indexes on frequently filtered/sorted columns
   - Check for missing database statistics
   - Consider query result caching

Performance benchmarks needed:
- [ ] Initial page load time (target: <2s)
- [ ] Time to interactive (target: <3s)
- [ ] API response times (target: <500ms per endpoint)
- [ ] Database query execution times (target: <100ms)

Optimization priorities:
1. Implement lazy loading for non-critical components
2. Add database indexes based on query patterns
3. Implement API response caching
4. Optimize frontend bundle size"

## Activation Protocol

When triggered with "enhance: [prompt]", the system should:
1. Parse the initial prompt for key indicators
2. Apply all relevant enhancement rules
3. Generate a comprehensive, structured prompt
4. Include specific technical details based on context
5. Provide clear action items and success metrics
6. Format for easy copying and execution by AI assistants

The enhanced prompt should be immediately actionable, requiring no additional context for a developer or AI assistant to begin investigation and resolution.