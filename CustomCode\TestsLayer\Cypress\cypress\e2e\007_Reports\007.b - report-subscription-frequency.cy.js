describe("007.b - Report Subscription Frequency Test", () => {
    beforeEach(() => {
        // Prevent uncaught exceptions from failing tests
        Cypress.on('uncaught:exception', (err, runnable) => {
            console.log('Uncaught exception:', err.message);
            return false;
        });
        cy.login();
    });

    it("verifies report subscription frequency dropdown functionality", () => {
        // Wait after login and click Reports menu
        cy.wait(5000); // Wait longer after login
        cy.get("[data-test-id='90b729ff-9db1-46e2-96d2-1e86b8db7278']", { timeout: 30000 })
            .first()
            .click({ force: true });

        // Log the click attempt
        cy.log('Clicked Reports menu');

        // Wait for reports to load and click specific report
        cy.wait(2000);
        cy.get("[data-test-id='bd7c9600-de15-4523-8c48-f042a9b57a88']")
            .should('be.visible')
            .first()
            .click({ force: true });

        // Click Subscribe button - using first() to ensure single element
        cy.contains('button', 'Subscribe')
            .should('be.visible')
            .first()
            .click({ force: true });

        // Verify frequency dropdown functionality
        cy.get("[data-test-id='edit_3a15d4ce-a8ef-4ff1-aec6-90c2deac2ff5']")
            .should('be.visible')
            .should('not.be.disabled')
            .first()  // Ensure we're working with a single select element
            .then($select => {
                // Get all options first
                cy.wrap($select)
                    .find('option')
                    .then($options => {
                        // Verify that there are exactly 3 options (Daily, Weekly, Monthly)
                        expect($options.length).to.equal(3);

                        // Verify option values and texts
                        const expectedOptions = [
                            { value: '0', text: 'Daily' },
                            { value: '1', text: 'Weekly' },
                            { value: '2', text: 'Monthly' }
                        ];

                        $options.each((index, option) => {
                            expect(option.value).to.equal(expectedOptions[index].value);
                            expect(option.text).to.equal(expectedOptions[index].text);
                        });

                        // Select each option and verify it can be selected
                        expectedOptions.forEach(opt => {
                            cy.get("[data-test-id='edit_3a15d4ce-a8ef-4ff1-aec6-90c2deac2ff5']")
                                .first()  // Ensure we're working with a single select element
                                .select(opt.value)
                                .should('have.value', opt.value);
                        });
                    });
            });
    });
}); 