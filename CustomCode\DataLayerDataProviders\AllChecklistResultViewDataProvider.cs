﻿////////////////////////////////////////////////////////////////////////////////////////////
// This is Generated Code
// You should not modify this code as it may be overwritten. Use Partial classes instead
// Generated By Generative Objects  
////////////////////////////////////////////////////////////////////////////////////////////
using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Threading;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using System.Threading.Tasks;
using System.Data;
using System.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.DependencyInjection;
using DocumentFormat.OpenXml.Wordprocessing;
using NHibernate.Hql.Ast.ANTLR.Util;

namespace FleetXQ.Data.DataProviders.Custom
{
    public class AllChecklistResultViewDataProvider : DataProvider<AllChecklistResultViewDataObject>
    {
        protected readonly IConfiguration _configuration;
        public AllChecklistResultViewDataProvider(IServiceProvider serviceProvider, IDataProviderTransaction transaction, IEntityDataProvider entityDataProvider, IDataProviderDispatcher<AllChecklistResultViewDataObject> dispatcher, IDataProviderDeleteStrategy dataProviderDeleteStrategy, IAutoInclude autoInclude, IThreadContext threadContext, IDataProviderTransaction dataProviderTransaction, IConfiguration configuration) : base(serviceProvider, transaction, entityDataProvider, dispatcher, dataProviderDeleteStrategy, autoInclude, threadContext, dataProviderTransaction)
        {
            _configuration = configuration;
        }

        protected override async Task<int> DoCountAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (var connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (var command = new SqlCommand("GetAllChecklistResults", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;

                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasResultType)
                    {
                        command.Parameters.Add(new SqlParameter("@ResultType", SqlDbType.Int) { Value = filterArguments[filter.ResultTypeParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.AddWithValue("@ReturnTotalCount", 1);

                    connection.Open();
                    int totalCount = (int)await command.ExecuteScalarAsync();
                    return totalCount;
                }
            }
        }
        //

        protected override async Task DoDeleteAsync(AllChecklistResultViewDataObject entity, LambdaExpression securityFilterExpression, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }

        protected override async Task<AllChecklistResultViewDataObject> DoGetAsync(AllChecklistResultViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetAllChecklistResults", connection))
                {
                    AllChecklistResultViewDataObject result = null;

                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@ChecklistResultId", SqlDbType.UniqueIdentifier) { Value = entity.ChecklistResultId });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                result = _serviceProvider.GetRequiredService<AllChecklistResultViewDataObject>();
                                result.IsNew = false;

                                // Assuming the stored procedure returns Id, TimeSlot, NumberOfRedImpacts, and NumberOfAmberImpacts
                                result.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                result.ChecklistResultId = reader.GetGuid(reader.GetOrdinal("ChecklistResultId"));
                                result.CheckComplete = Convert.ToBoolean(reader.GetInt32(reader.GetOrdinal("CheckComplete")));
                                result.CompletionTime = reader.GetString(reader.GetOrdinal("CompletionTime"));
                                result.TimezoneAdjustedChecklistStartTime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedChecklistStartTime"));
                                context.AddObject(result);
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get AllChecklistResultViewDataObject data", "Unable to get AllChecklistResultViewDataObject data", ex);
                    }
                }
            }
        }

        protected override async Task<DataObjectCollection<AllChecklistResultViewDataObject>> DoGetCollectionAsync(LambdaExpression securityFilterExpression, string filterPredicate, object[] filterArguments, string orderByPredicate, int pageNumber, int pageSize, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            var result = new DataObjectCollection<AllChecklistResultViewDataObject>();
            result.ObjectsDataSet = context;

            var filter = PredicateParser.ParseFilterPredicate(filterPredicate);

            using (SqlConnection connection = new SqlConnection(_configuration["MainConnectionString"]))
            {
                using (SqlCommand command = new SqlCommand("GetAllChecklistResults", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    if (filter.HasCustomerId)
                    {
                        command.Parameters.Add(new SqlParameter("@CustomerId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.CustomerIdParameterNumber] });
                    }

                    if (filter.HasSiteId)
                    {
                        command.Parameters.Add(new SqlParameter("@SiteId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.SiteIdParameterNumber] });
                    }

                    if (filter.HasDepartmentId)
                    {
                        command.Parameters.Add(new SqlParameter("@DepartmentId", SqlDbType.UniqueIdentifier) { Value = filterArguments[filter.DepartmentIdParameterNumber] });
                    }

                    if (filter.HasStartDate)
                    {
                        command.Parameters.Add(new SqlParameter("@StartDate", SqlDbType.DateTime) { Value = filterArguments[filter.StartDateParameterNumber] });
                    }
                    if (filter.HasEndDate)
                    {
                        command.Parameters.Add(new SqlParameter("@EndDate", SqlDbType.DateTime) { Value = filterArguments[filter.EndDateParameterNumber] });
                    }
                    if (filter.HasResultType)
                    {
                        command.Parameters.Add(new SqlParameter("@ResultType", SqlDbType.Int) { Value = filterArguments[filter.ResultTypeParameterNumber] });
                    }
                    if (filter.HasMultiSearch)
                    {
                        command.Parameters.Add(new SqlParameter("@MultiSearch", SqlDbType.NVarChar) { Value = filterArguments[filter.MultiSearchParameterNumber] });
                    }

                    command.Parameters.Add(new SqlParameter("@PageIndex", SqlDbType.Int) { Value = pageNumber - 1 });
                    command.Parameters.Add(new SqlParameter("@PageSize", SqlDbType.Int) { Value = pageSize });

                    try
                    {
                        await connection.OpenAsync();
                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            if (reader.HasRows)
                            {
                                while (await reader.ReadAsync())
                                {
                                    var entity = _serviceProvider.GetRequiredService<AllChecklistResultViewDataObject>();
                                    entity.IsNew = false;

                                    // Assuming the stored procedure returns Id, TimeSlot, NumberOfRedImpacts, and NumberOfAmberImpacts
                                    entity.Id = reader.GetGuid(reader.GetOrdinal("Id"));
                                    entity.ChecklistResultId = reader.GetGuid(reader.GetOrdinal("ChecklistResultId"));
                                    entity.CheckComplete = Convert.ToBoolean(reader.GetInt32(reader.GetOrdinal("CheckComplete")));
                                    entity.CompletionTime = reader.GetString(reader.GetOrdinal("CompletionTime"));
                                    entity.TimezoneAdjustedChecklistStartTime = reader.GetDateTime(reader.GetOrdinal("TimezoneAdjustedChecklistStartTime"));
                                    entity.DealerId = reader.GetGuid(reader.GetOrdinal("DealerId"));
                                    entity.HasCriticalQuestions = reader.GetInt32(reader.GetOrdinal("HasCriticalQuestions")) == 1;
                                    result.Add(entity);
                                }
                            }
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        throw new GOServerException("Unable to get IncompletedChecklist data", "Unable to get IncompletedChecklist data", ex);
                    }
                }
            }
        }

        protected override async Task<AllChecklistResultViewDataObject> DoSaveAsync(AllChecklistResultViewDataObject entity, LambdaExpression securityFilterExpression, List<string> includes, IObjectsDataSet context, Dictionary<string, object> parameters)
        {
            throw new NotImplementedException();
        }
    }
}
