import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('UpdateQuestionsOrderFormViewModelCustom', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/Vehicle/UpdateQuestionsOrderFormViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create base view model with required properties
        viewModel = {
            PreOperationalChecklistsListViewModel: {
                getSourceCollection: vi.fn()
            },
            ShowError: vi.fn()
        };

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.UpdateQuestionsOrderFormViewModelCustom(viewModel);
    });

    describe('onBeforeSave', () => {
        it('should return true when there are no questions', () => {
            // Setup
            viewModel.PreOperationalChecklistsListViewModel.getSourceCollection.mockReturnValue(null);

            // Execute
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.ShowError).not.toHaveBeenCalled();
        });

        it('should return true when there are no duplicate order numbers among active questions', () => {
            // Setup
            const questions = [
                { Data: { Order: ko.observable(1), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(2), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(3), Active: ko.observable(true) } }
            ];
            viewModel.PreOperationalChecklistsListViewModel.getSourceCollection.mockReturnValue(questions);

            // Execute
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.ShowError).not.toHaveBeenCalled();
        });

        it('should return false and show error when duplicate order numbers are found among active questions', () => {
            // Setup
            const questions = [
                { Data: { Order: ko.observable(1), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(2), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(1), Active: ko.observable(true) } },  // Duplicate order
                { Data: { Order: ko.observable(3), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(2), Active: ko.observable(true) } }   // Another duplicate order
            ];
            viewModel.PreOperationalChecklistsListViewModel.getSourceCollection.mockReturnValue(questions);

            // Execute
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(false);
            expect(viewModel.ShowError).toHaveBeenCalledWith(
                "Cannot save: Multiple active questions have the same order number(s): 1, 2",
                "Validation Error"
            );
        });

        it('should ignore duplicate order numbers among inactive questions', () => {
            // Setup
            const questions = [
                { Data: { Order: ko.observable(1), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(2), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(1), Active: ko.observable(false) } },  // Inactive duplicate
                { Data: { Order: ko.observable(3), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(2), Active: ko.observable(false) } }   // Inactive duplicate
            ];
            viewModel.PreOperationalChecklistsListViewModel.getSourceCollection.mockReturnValue(questions);

            // Execute
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.ShowError).not.toHaveBeenCalled();
        });

        it('should handle empty questions array', () => {
            // Setup
            viewModel.PreOperationalChecklistsListViewModel.getSourceCollection.mockReturnValue([]);

            // Execute
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.ShowError).not.toHaveBeenCalled();
        });

        it('should handle mix of active and inactive questions with unique order numbers', () => {
            // Setup
            const questions = [
                { Data: { Order: ko.observable(1), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(1), Active: ko.observable(false) } },  // Inactive with same order
                { Data: { Order: ko.observable(2), Active: ko.observable(true) } },
                { Data: { Order: ko.observable(2), Active: ko.observable(false) } },  // Inactive with same order
                { Data: { Order: ko.observable(3), Active: ko.observable(true) } }
            ];
            viewModel.PreOperationalChecklistsListViewModel.getSourceCollection.mockReturnValue(questions);

            // Execute
            const result = customViewModel.onBeforeSave();

            // Assert
            expect(result).toBe(true);
            expect(viewModel.ShowError).not.toHaveBeenCalled();
        });
    });
}); 