﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// PersonAPI Component
	///  
	/// </summary>
    public partial class PersonAPI : BaseServerComponent, IPersonAPI 
    {
		public PersonAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> SoftDeleteAsync(Guid personId, Dictionary<string, object> parameters = null)
        {
            var person = _serviceProvider.GetService<PersonDataObject>();
            person.Id = personId;

            person = await _dataFacade.PersonDataProvider.GetAsync(person);

            if (person == null)
            {
                throw new GOServerException($"unknow person id {personId}");
            }

            // Set driver to inactive if person is a driver
            if (person.DriverId != null)
            {
                var driver = await person.LoadDriverAsync(skipSecurity: true);
                if (driver != null)
                {
                    driver.Active = false;
                    await _dataFacade.DriverDataProvider.SaveAsync(driver);
                }
            }

            person.IsActiveDriver = false;
            person.Supervisor = false;

            var goUserId = person.GOUserId;
            person.DeletedAtUtc = DateTime.UtcNow;
            person.GOUserId = null;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Delete the associated GOUser
            if (goUserId.HasValue)
            {
                var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
                goUser.Id = goUserId.Value;

                goUser = await _dataFacade.GOUserDataProvider.GetAsync(goUser);

                await _dataFacade.GOUserDataProvider.DeleteAsync(goUser);
            }

            return new ComponentResponse<bool>(true);
        }
    }
}
