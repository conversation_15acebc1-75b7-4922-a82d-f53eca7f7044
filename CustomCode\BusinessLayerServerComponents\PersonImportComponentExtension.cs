﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{

    public class PersonImportComponentExtension : IImportExportComponentExtension<PersonImportSection0Component,
        PersonDataObject>
    {
        private readonly IDataFacade dataFacade;
        public PersonImportComponentExtension(IDataFacade dataFacade)
        {     
            this.dataFacade = dataFacade;
        }

        public void Init(IImportExportComponent<PersonDataObject> importExportComponent)
        {
            importExportComponent.OnAfterImportDataRowAsync += OnAfterImportDataRowAsync;
        }

        private async Task OnAfterImportDataRowAsync(OnAfterImportDataRowEventArgs<PersonDataObject> arg)
        {
            var customerName = arg.DataRow[PersonImportSection0Component.COL_CUSTOMER].ToString();
            var siteName = arg.DataRow[PersonImportSection0Component.COL_SITE].ToString();
            var departmentName = arg.DataRow[PersonImportSection0Component.COL_DEPARTMENT].ToString();

            var customer = (await dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { customerName })).SingleOrDefault();

            if (customer == null)
            {
                return;
            }

            await customer.LoadSitesAsync();

            var site = customer.Sites.FirstOrDefault(x => x.Name == siteName);

            if (site == null)
            {
                return;
            }

            await site.LoadDepartmentItemsAsync();

            var department = site.DepartmentItems.FirstOrDefault(x => x.Name == departmentName);

            if (department == null)
            {
                return;
            }

            arg.Entity.Customer = customer;
            arg.Entity.Site = site;
            arg.Entity.Department = department;
        }
    }
}