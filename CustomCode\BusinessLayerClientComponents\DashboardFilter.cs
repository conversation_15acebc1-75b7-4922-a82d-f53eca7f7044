﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.ORMSupportClasses;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// DashboardFilter Component
	///  
	/// </summary>
    public partial class DashboardFilter : IDashboardFilter 
    {
		/// <summary>
        /// Clear Method
		///  
		  /// </summary>
		public Task<ComponentResponse<System.Boolean>> ClearAsync(Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return Task.FromResult(new ComponentResponse<bool>(default(System.Boolean))); 
		}
		
		/// <summary>
        /// Filter Method
		///  
		  /// </summary>
		public Task<ComponentResponse<System.Boolean>> FilterAsync(Dictionary<string, object> parameters = null) 
		{
			// TODO: This is a custom component - Implementation should be provided
			return Task.FromResult(new ComponentResponse<System.Boolean>(default(System.Boolean))); 
		}
		
		public void Dispose()
		{
		}

	}
}
