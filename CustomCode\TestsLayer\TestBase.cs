﻿using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using FleetXQ.Features.Security;
using FleetXQ.ServiceLayer;
using GenerativeObjects.Infrastructure.Database;
using GenerativeObjects.Infrastructure.Database.SqlServer;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.Settings;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Azure.Storage.Blobs;

namespace FleetXQ.Tests.Common
{
    [TestFixture]
    public abstract class TestBase
    {
        protected IServiceProvider _serviceProvider;
        protected IDatabaseFactory _databaseFactory;
        protected Mock<IAuthentication> _authenticationMock;
        protected Mock<IAuthorizations> _authorizationsMock;
        protected Mock<IVehicleUtils> _vehicleUtilsMock;
        protected Mock<IStorageClientFactory> _storageClientFactoryMock;

        /// <summary>
        /// ServiceScope matching the scope of one unit test
        /// </summary>
        protected IServiceScope _serviceScope;
        protected IConfiguration _configuration;

        [SetUp]
        public void Setup()
        {
            // Initialize a different scope for each test
            var serviceScopeFactory = _serviceProvider.GetRequiredService<IServiceScopeFactory>();
            _serviceScope = serviceScopeFactory.CreateScope();
        }

        [TearDown]
        public void TearDown()
        {
            // Test finished => dispose the scope
            _serviceScope.Dispose();
            _serviceScope = null;
        }

        [OneTimeSetUp]
        public virtual void OneTimeSetUp()
        {
            var services = new ServiceCollection();
            services.AddGenerativeObjectsSupport();
            services.AddTransient<IDatabaseFactory, SqlServerDatabaseFactory>();
            services.AddHttpContextAccessor();

            _configuration = new ConfigurationBuilder()
                .AddJsonFile(Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, "../../../../../TestsLayer/appsettings.json")), optional: false)
                .AddJsonFile(Path.GetFullPath(Path.Combine(AppContext.BaseDirectory, "../../../../../TestsLayer/appsettings.Development.json")), optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(_configuration);

            var deviceMessageHandlerMock = new Mock<IDeviceMessageHandler>();
            services.AddSingleton<IDeviceMessageHandler>(deviceMessageHandlerMock.Object);

            var loggingServiceMock = new Mock<ILoggingService>();
            services.AddSingleton<ILoggingService>(loggingServiceMock.Object);

            // Create a mock that implements both interfaces
            var webHostEnvironment = new Mock<IWebHostEnvironment>();
            var hostingEnvironmentMock = webHostEnvironment.As<IHostingEnvironment>();
            services.AddSingleton<IWebHostEnvironment>(webHostEnvironment.Object);
            services.AddSingleton<IHostingEnvironment>(hostingEnvironmentMock.Object);

            // Initialize authentication and authorization mocks
            _authenticationMock = new Mock<IAuthentication>();
            _authorizationsMock = new Mock<IAuthorizations>();
            services.AddSingleton<IAuthentication>(_authenticationMock.Object);
            services.AddSingleton<IAuthorizations>(_authorizationsMock.Object);
            _vehicleUtilsMock = new Mock<IVehicleUtils>();
            services.AddSingleton<IVehicleUtils>(_vehicleUtilsMock.Object);

            // Initialize storage client factory mock
            _storageClientFactoryMock = new Mock<IStorageClientFactory>();
            services.AddSingleton<IStorageClientFactory>(_storageClientFactoryMock.Object);

            // Setup default mock behavior for storage client factory
            var blobClientMock = new Mock<BlobClient>();
            blobClientMock.SetupGet(x => x.Uri)
                .Returns(new Uri("https://teststorage.blob.core.windows.net/testcontainer/testblob"));

            _storageClientFactoryMock.Setup(x => x.CreateBlobClient(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(blobClientMock.Object);

            var httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            services.AddSingleton<IHttpContextAccessor>(httpContextAccessorMock.Object);
            services.AddSingleton<Mock<IHttpContextAccessor>>(httpContextAccessorMock);

            // Can be overriden on test classes 
            _authenticationMock.Setup(a => a.GetCurrentUserClaimsAsync())
                .ReturnsAsync(new UserClaims());

            _authorizationsMock.Setup(a => a.CanReadAsync(It.IsAny<IDataObject>(), It.IsAny<UserClaims>(), It.IsAny<ValueWrapper<string>>(), It.IsAny<ValueWrapper<SecurityPredicate>>()))
                .ReturnsAsync(PermissionLevel.Authorized);

            _authorizationsMock.Setup(a => a.CheckWriteAuthorizationsOnDataSetAsync(It.IsAny<ObjectsDataSet>(), It.IsAny<UserClaims>(), It.IsAny<Dictionary<string, object>>(), It.IsAny<ValueWrapper<string>>()))
                .ReturnsAsync(PermissionLevel.Authorized);

            AddServiceRegistrations(services);

            _serviceProvider = services.BuildServiceProvider();

            _databaseFactory = _serviceProvider.GetRequiredService<IDatabaseFactory>();
            _databaseFactory.ConnectionString = _configuration["DatabaseServerConnectionString"];

            var settingsProvider = _serviceProvider.GetRequiredService<ISettingsProvider>();
            settingsProvider["LazyLoadingEnabled"] = false;
            settingsProvider["RootNamespace"] = "FleetXQ";
            settingsProvider["StorageProvider"] = "FileSystemStorageProvider";
        }

        protected void CreateTestDatabase(string databaseName)
        {
            // First create the database
            _databaseFactory.CreateDatabase(databaseName);

            // and then run the script to create the schema

            string sqlFileRootPath = "../../../../../../Sql";

            string sqlFilePath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "CreateSchemaScript.sql"));
            string sqlFileCustomPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "CreateViews.custom.sql"));

            if (!File.Exists(sqlFilePath))
            {
                throw new GOServerException($"Cannot find script file \"{sqlFilePath}\"");
            }

            if (!File.Exists(sqlFileCustomPath))
            {
                throw new GOServerException($"Cannot find script file \"{sqlFileCustomPath}\"");
            }

            var createFleetXQScriptTextBuilder = new StringBuilder();
            createFleetXQScriptTextBuilder.Append(System.IO.File.ReadAllText(sqlFilePath));

            createFleetXQScriptTextBuilder.AppendLine();
            createFleetXQScriptTextBuilder.Append(File.ReadAllText(sqlFileCustomPath));

            _databaseFactory.RunSqlScript(databaseName, createFleetXQScriptTextBuilder.ToString());

            // now provide the connection string to the test database
            _configuration["MainConnectionString"] = String.Format(_configuration["DatabaseConnectionStringTemplate"], databaseName);

            string oneOffScriptsPath = "../../../../../../Scripts/OneOffScripts";
            string seedPermissionsPath = Path.GetFullPath(Path.Combine(oneOffScriptsPath, "SeedPermissions.sql"));
            if (!File.Exists(seedPermissionsPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedPermissionsPath}\"");
            }
            _databaseFactory.RunSqlScript(databaseName, File.ReadAllText(seedPermissionsPath));
        }

        protected void DeleteTestDatabase(string databaseName)
        {
            if (_databaseFactory.DoesDatabaseExists(databaseName))
            {
                _databaseFactory.DeleteDatabase(databaseName);
            }
        }

        protected virtual void AddServiceRegistrations(ServiceCollection services)
        {
        }

    }
}
