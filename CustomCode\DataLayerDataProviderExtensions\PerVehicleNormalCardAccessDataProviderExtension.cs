using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using System.Threading;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    /// <summary>
    /// DataProviderExtension for PerVehicleNormalCardAccess with race condition protection
    /// </summary>
    public class PerVehicleNormalCardAccessDataProviderExtension : IDataProviderExtension<PerVehicleNormalCardAccessDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;
        private ILogger<PerVehicleNormalCardAccessDataProviderExtension> _logger;

        public PerVehicleNormalCardAccessDataProviderExtension(
            IServiceProvider serviceProvider,
            IDataFacade dataFacade)
        {
            _serviceProvider = serviceProvider;
            _dataFacade = dataFacade;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeDelete += OnBeforeDeleteAsync;
        }

        private async Task OnBeforeDeleteAsync(OnBeforeDeleteEventArgs e)
        {
            var instance = e.Entity as PerVehicleNormalCardAccessDataObject;
            if (instance == null) return;

            try
            {
                // Initialize logger if not already done
                _logger ??= _serviceProvider.GetService<ILogger<PerVehicleNormalCardAccessDataProviderExtension>>();

                // Add logging for troubleshooting
                _logger?.LogDebug("Processing OnBeforeDelete for PerVehicleNormalCardAccess Id: {Id}, VehicleId: {VehicleId}, CardId: {CardId}",
                    instance.Id, instance.VehicleId, instance.CardId);

                // Use a timeout for the existence check to prevent hanging
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // 30 second timeout

                try
                {
                    // Check if the record still exists before attempting deletion
                    // This helps prevent race conditions where another process already deleted it
                    var existingRecord = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetAsync(instance, skipSecurity: true);

                    if (existingRecord == null)
                    {
                        _logger?.LogWarning("PerVehicleNormalCardAccess record Id: {Id} already deleted by another process. Skipping deletion to prevent race condition.", instance.Id);

                        // Mark as handled to prevent the framework from attempting the delete
                        // This prevents the "expected 1 row, got 0" error
                        e.IsHandled = true;
                        return;
                    }

                    _logger?.LogDebug("PerVehicleNormalCardAccess record Id: {Id} exists, proceeding with deletion", instance.Id);
                }
                catch (OperationCanceledException) when (cts.Token.IsCancellationRequested)
                {
                    _logger?.LogWarning("Timeout occurred while checking existence of PerVehicleNormalCardAccess Id: {Id}. Allowing deletion to proceed with caution.", instance.Id);
                    // Don't set e.IsHandled = true here, let the delete proceed but it may fail gracefully
                }
            }
            catch (Exception ex)
            {
                // Initialize logger if not already done for error handling
                _logger ??= _serviceProvider.GetService<ILogger<PerVehicleNormalCardAccessDataProviderExtension>>();

                // For concurrent deletion scenarios, log but don't fail the operation
                if (IsRaceConditionError(ex))
                {
                    _logger?.LogWarning("Race condition detected during OnBeforeDelete for PerVehicleNormalCardAccess Id: {Id}. Record likely already deleted by concurrent process: {Message}",
                        instance?.Id, ex.Message);

                    // Mark as handled to prevent further processing
                    e.IsHandled = true;
                    return;
                }

                _logger?.LogError(ex, "Error in OnBeforeDelete for PerVehicleNormalCardAccess Id: {Id}: {Message}",
                    instance?.Id, ex.Message);
                throw;
            }
        }

        /// <summary>
        /// Determines if an exception is likely due to a race condition or timeout
        /// </summary>
        private bool IsRaceConditionError(Exception ex)
        {
            var message = ex.Message?.ToLowerInvariant() ?? string.Empty;
            var innerMessage = ex.InnerException?.Message?.ToLowerInvariant() ?? string.Empty;
            var exceptionType = ex.GetType().Name.ToLowerInvariant();

            // Check for NHibernate StaleStateException
            if (exceptionType.Contains("stalestateexception") ||
                message.Contains("stale") ||
                message.Contains("batch update returned unexpected row count"))
            {
                return true;
            }

            // Check for timeout exceptions
            if (exceptionType.Contains("timeout") ||
                message.Contains("timeout") ||
                message.Contains("the wait operation timed out") ||
                innerMessage.Contains("timeout"))
            {
                return true;
            }

            // Check for other race condition indicators
            return message.Contains("row count") ||
                   message.Contains("already deleted") ||
                   message.Contains("not found") ||
                   message.Contains("batch command") ||
                   message.Contains("sql not available") ||
                   message.Contains("pervehiclenormalcardaccessdeletehandler") ||
                   message.Contains("could not execute batch command") ||
                   innerMessage.Contains("row count") ||
                   innerMessage.Contains("batch command") ||
                   innerMessage.Contains("sql not available") ||
                   innerMessage.Contains("could not execute batch command");
        }
    }
}