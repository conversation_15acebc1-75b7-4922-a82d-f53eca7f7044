stateDiagram-v2
    [*] --> AccessRequest : Access Request Initiated
    
    AccessRequest --> Validation : Validate Request
    Validation --> AccessRequest : Validation Failed
    Validation --> PermissionCheck : Validation Passed
    
    PermissionCheck --> AccessRequest : Insufficient Permissions
    PermissionCheck --> CardAssignment : Permissions Valid
    
    CardAssignment --> AccessCreation : Card Assigned
    CardAssignment --> AccessRequest : Assignment Failed
    
    AccessCreation --> IoTSync : Access Created
    AccessCreation --> AccessRequest : Creation Failed
    
    IoTSync --> Active : Sync Successful
    IoTSync --> Active : Sync Failed (Non-blocking)
    
    Active --> AccessRevoked : Revoke Access
    Active --> [*] : Access Expired
    
    AccessRevoked --> [*] : Cleanup Complete
    
    note right of AccessRequest
        Driver requests vehicle access
        System validates request
        Checks business rules
    end note
    
    note right of Validation
        Validates driver eligibility
        Checks vehicle availability
        Verifies site permissions
    end note
    
    note right of PermissionCheck
        Checks driver permissions
        Validates license status
        Confirms access levels
    end note
    
    note right of CardAssignment
        Assigns access card
        Links to vehicle/model
        Sets permission levels
    end note
    
    note right of AccessCreation
        Creates access records
        Database transactions
        Audit trail creation
    end note
    
    note right of IoTSync
        Syncs to vehicle device
        Updates access lists
        Real-time activation
    end note
    
    note right of Active
        Access is active
        Driver can use vehicle
        Real-time monitoring
    end note 