using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSubstitute;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using FleetXQ.BusinessLayer.Tasks;
using System.Dynamic;
using Newtonsoft.Json;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.ServiceLayer;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    public class MockExportJobStatus : ExportJobStatusDataObject
    {
        private readonly Guid _id;
        private readonly GOTaskStatusEnum _taskStatus;
        private readonly string _exportedFile;
        private readonly string _exportedFileInternalName;

        public MockExportJobStatus(Guid id, GOTaskStatusEnum taskStatus, string exportedFile, string exportedFileInternalName)
            : base(Substitute.For<IServiceProvider>())
        {
            _id = id;
            _taskStatus = taskStatus;
            _exportedFile = exportedFile;
            _exportedFileInternalName = exportedFileInternalName;
        }

        public override Guid Id => _id;
        public override GOTaskStatusEnum TaskStatus => _taskStatus;
        public override string ExportedFile => _exportedFile;
        public override string ExportedFileInternalName => _exportedFileInternalName;
    }

    // Mock implementations for report export components
    public class MockImpactReportExportComponent : IImpactReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "ImpactReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockGeneralProductivityReportExportComponent : IGeneralProductivityReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "GeneralProductivityReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockProficiencyReportExportComponent : IProficiencyReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "ProficiencyReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockMachineUnlockReportExportComponent : IMachineUnlockReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "MachineUnlockReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockVehicleCurrentStatusReportExportComponent : IVehicleCurrentStatusReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "VehicleCurrentStatusReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockDriverCurrentStatusReportExportComponent : IDriverCurrentStatusReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "DriverCurrentStatusReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockPreOpChecklistReportExportComponent : IPreOpChecklistReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "PreOpChecklistReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockServiceCheckReportExportComponent : IServiceCheckReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "ServiceCheckReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockDriverAccessAbuseReportExportComponent : IDriverAccessAbuseReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "DriverAccessAbuseReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockLicenseExpiryReportExportComponent : ILicenseExpiryReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "LicenseExpiryReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockSynchronizationStatusReportExportComponent : ISynchronizationStatusReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "SynchronizationStatusReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockVORReportExportComponent : IVORReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "VORReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    public class MockVehicleCalibrationReportExportComponent : IVehicleCalibrationReportExportComponent
    {
        public object ComponentClass => throw new NotImplementedException();

        public async System.Threading.Tasks.Task ExportAsync(FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> task, string filterPredicate, string filterParameters, Dictionary<string, object> parameters = null)
        {
            task.DataObject.TaskStatus = GOTaskStatusEnum.Complete;
            task.DataObject.ExportedFile = "VehicleCalibrationReport.xlsx";
            task.DataObject.ExportedFileInternalName = $"exports/{Guid.NewGuid()}.xlsx";
            await task.SaveProgressAsync();
        }
    }

    [TestFixture]
    public class EmailServiceTest : TestBase
    {
        private IEmailService _emailService;
        private IDataFacade _dataFacade;
        private IAuthentication _authentication;
        private IServicePath _servicePath;
        private readonly string _testDatabaseName = $"EmailServiceTest-{Guid.NewGuid()}";
        private IVehicleCurrentStatusReportExportComponent _mockExportComponent;
        private FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject> _mockTask;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            _authentication = Substitute.For<IAuthentication>();
            services.AddTransient<IAuthentication>(sp => _authentication);

            // Mock IServicePath
            _servicePath = Substitute.For<IServicePath>();
            _servicePath.WebRootPath().Returns(Path.Combine(Path.GetTempPath(), "FleetXQTest"));
            services.AddTransient<IServicePath>(sp => _servicePath);

            // Create and register mock export component
            _mockExportComponent = Substitute.For<IVehicleCurrentStatusReportExportComponent>();
            services.AddTransient<IVehicleCurrentStatusReportExportComponent>(sp => _mockExportComponent);
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _emailService = _serviceProvider.GetRequiredService<IEmailService>();

            // Create the test directory if it doesn't exist
            var testDir = _servicePath.WebRootPath();
            if (!Directory.Exists(testDir))
            {
                Directory.CreateDirectory(testDir);
            }

            CreateTestDatabase(_testDatabaseName);

            // Run the seed script after database creation
            string sqlFileRootPath = "../../../../../../Scripts/OneOffScripts";
            string seedReportTypesPath = Path.GetFullPath(Path.Combine(sqlFileRootPath, "SeedReportTypes.sql"));
            if (!File.Exists(seedReportTypesPath))
            {
                throw new GOServerException($"Cannot find script file \"{seedReportTypesPath}\"");
            }
            _databaseFactory.RunSqlScript(_testDatabaseName, File.ReadAllText(seedReportTypesPath));

            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            // Clean up test directory
            var testDir = _servicePath.WebRootPath();
            if (Directory.Exists(testDir))
            {
                Directory.Delete(testDir, true);
            }

            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            // Create region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            // Create timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            // Create site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.Name = "Test Site";
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Active = true;
            site.UnlockSetting = true;
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create GOUser
            var goUser = _serviceProvider.GetRequiredService<GOUserDataObject>();
            goUser.Id = Guid.NewGuid();
            goUser.UserName = "testuser";
            goUser.EmailAddress = "<EMAIL>";
            goUser.Password = "password123";
            await _dataFacade.GOUserDataProvider.SaveAsync(goUser);

            // Create person
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = Guid.NewGuid();
            person.FirstName = "Test";
            person.LastName = "Person";
            person.SiteId = site.Id;
            person.DepartmentId = department.Id;
            person.CustomerId = customer.Id;
            person.IsActiveDriver = true;
            person.IsDriver = true;
            person.NormalDriverAccess = true;
            person.VehicleAccess = true;
            person.GOUserId = goUser.Id;
            await _dataFacade.PersonDataProvider.SaveAsync(person);

            // Setup authentication mock
            var userClaims = new UserClaims { UserId = goUser.Id };
            _authentication.GetCurrentUserClaimsAsync().Returns(userClaims);
        }

        [Test]
        public async Task SendReportEmails_WithValidSubscription_PassesCorrectFilterPredicateAndParameters()
        {
            // Arrange
            var reportType = 10; // Current Status Report from seed script
            var reportTypeObject = (await _dataFacade.ReportTypeDataProvider.GetCollectionAsync(null, "ReportType == @0", new object[] { reportType })).First();

            // Create a report subscription
            var reportSubscription = _serviceProvider.GetRequiredService<ReportSubscriptionDataObject>();
            reportSubscription.Id = Guid.NewGuid();
            reportSubscription.Subject = "Test Report Subscription";
            reportSubscription.StartDate = DateTime.UtcNow.AddDays(-1); // Started yesterday
            reportSubscription.ReportTypeId = reportTypeObject.Id;
            reportSubscription.Frequency = 0; // Daily
            reportSubscription.ReportGenerationTime = HourOfDayEnum.Eight; // 8 AM
            reportSubscription.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            reportSubscription.SiteId = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).First().Id;
            reportSubscription.DepartmentId = (await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null)).First().Id;
            reportSubscription.ReportStartTime = DateTime.UtcNow.AddDays(-7); // Last 7 days
            reportSubscription.ReportEndTime = DateTime.UtcNow;
            reportSubscription.PersonId = (await _dataFacade.PersonDataProvider.GetCollectionAsync(null)).First().Id;

            await _dataFacade.ReportSubscriptionDataProvider.SaveAsync(reportSubscription);

            // Expected filter predicate
            var expectedFilterParts = new List<string>
            {
                $"CustomerId == @0",
                $"SiteId == @1",
                $"DepartmentId == @2",
                $"StartDate == @3",
                $"EndDate == @4"
            };

            var expectedFilterPredicate = string.Join(" && ", expectedFilterParts);

            // Act
            var response = await _emailService.SendReportEmailsAsync();

            // Assert
            Assert.That(response.Result, Is.True, "SendReportEmails should return true for valid subscription");

            // Verify the export component was called with correct parameters
            await _mockExportComponent.Received().ExportAsync(
                Arg.Any<FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject>>(),
                Arg.Is<string>(p => p == expectedFilterPredicate),
                Arg.Is<string>(p => ValidateFilterParameters(p)),
                Arg.Any<Dictionary<string, object>>()
            );
        }

        private bool ValidateFilterParameters(string json)
        {
            var parameters = JsonConvert.DeserializeObject<List<ParameterInfo>>(json);
            return parameters != null &&
                   parameters.Count == 5 &&
                   parameters.All(p => !p.IsNullable) &&
                   parameters[0].TypeName == "System.Guid" &&
                   parameters[1].TypeName == "System.Guid" &&
                   parameters[2].TypeName == "System.Guid" &&
                   parameters[3].TypeName == "System.DateTime" &&
                   parameters[4].TypeName == "System.DateTime";
        }

        [Test]
        public async Task SendReportEmails_WithNoSubscriptions_ReturnsFalse()
        {
            // Act
            var response = await _emailService.SendReportEmailsAsync();

            // Assert
            Assert.That(response.Result, Is.False, "SendReportEmails should return false when no subscriptions exist");

            // Verify export component was not called
            await _mockExportComponent.DidNotReceive().ExportAsync(
                Arg.Any<FleetXQ.BusinessLayer.Tasks.Task<ExportJobStatusDataObject>>(),
                Arg.Any<string>(),
                Arg.Any<string>(),
                (Dictionary<string, object>)Arg.Any<Dictionary<string, object>>()
            );
        }

        private class ParameterInfo
        {
            public string TypeName { get; set; }
            public bool IsNullable { get; set; }
            public string Value { get; set; }
        }
    }
}