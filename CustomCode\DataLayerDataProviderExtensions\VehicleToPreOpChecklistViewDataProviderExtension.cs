﻿using DocumentFormat.OpenXml.Drawing.Charts;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer;
using GenerativeObjects.Practices.LayerSupportClasses.DataLayer.Extensions;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Org.BouncyCastle.Asn1;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.Data.DataProvidersExtensions.Custom
{
    public class VehicleToPreOpChecklistViewDataProviderExtension : IDataProviderExtension<VehicleToPreOpChecklistViewDataObject>
    {
        private readonly IDataFacade _dataFacade;
        private readonly IServiceProvider _serviceProvider;


        public VehicleToPreOpChecklistViewDataProviderExtension(IServiceProvider serviceProvider, IDataFacade dataFacade)
        {
            _dataFacade = dataFacade;
            _serviceProvider = serviceProvider;
        }

        public void Init(IDataProviderExtensionProvider dataProvider)
        {
            dataProvider.OnBeforeSave += OnBeforeSaveAsync;
            dataProvider.OnBeforeDelete += OnBeforeDeleteAsync;
        }

        private async Task OnBeforeDeleteAsync(OnBeforeDeleteEventArgs e)
        {
            if (e.IsDry)
            {
                e.IsHandled = true;
                return;
            }

            var vehicletopreop = e.Entity as VehicleToPreOpChecklistViewDataObject;
            var question = (await vehicletopreop.LoadPreOperationalChecklistAsync()).Clone(recursive: false) as PreOperationalChecklistDataObject;
            question.Active = false;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question);

            e.IsHandled = true;
        }

        private async Task OnBeforeSaveAsync(OnBeforeSaveEventArgs e)
        {
            var vehicletopreop = e.Entity as VehicleToPreOpChecklistViewDataObject;
            var vehicle = await vehicletopreop.LoadVehicleAsync();

            var vehicleToPreops = await vehicle.LoadVehicleToPreOpCheckilstItemsAsync();
            var question = (await vehicletopreop.LoadPreOperationalChecklistAsync()).Clone(recursive: false) as PreOperationalChecklistDataObject;
            var questions = new List<PreOperationalChecklistDataObject>();

            foreach (var vehicleToPreop in vehicleToPreops?.Where(v => !v.IsNew).ToList() ?? new List<VehicleToPreOpChecklistViewDataObject>())
            {
                var preop = await vehicleToPreop.LoadPreOperationalChecklistAsync();
                questions.Add(preop);
            }

            if (questions?.Any(q => q.Order == question.Order && q.Id != question.Id && q.Active) == true)
            {
                short nextOrder = 1;
                if (questions.Any())
                {
                    nextOrder = (short)(questions.Where(q => q.Active).Max(x => x.Order) + 1);
                }

                question.Order = nextOrder;
            }

            if (vehicletopreop.IsNew)
            {
                //TODO: what if more than one department checklist ? for now only working for one department checklist
                var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "DepartmentId == @0 and ModelId == @1", new object[] { vehicletopreop.Vehicle.DepartmentId, vehicletopreop.Vehicle.ModelId })).FirstOrDefault();

                if (departmentChecklist == null)
                {
                    // no department checklist : create it 
                    departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                    departmentChecklist.DepartmentId = vehicletopreop.Vehicle.DepartmentId;
                    departmentChecklist.ModelId = vehicletopreop.Vehicle.ModelId;
                    departmentChecklist.Id = Guid.NewGuid();
                    departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);
                }

                // [FXQ-2372] This relationship is added to enable us to add a subform in Vehicle Checklist, to update the Department Checklist directly 
                // from the Vehicle page. Because GO doesn't allow to create a subform on a subrelated field. The relationship is enforced 
                // between Vehicle and DepartmentChecklist in VehicleToPreOpChecklistViewDataProviderExtension.OnBeforeSave
                var vehicleToUpdate = _serviceProvider.GetRequiredService<VehicleDataObject>();
                vehicleToUpdate.Id = vehicle.Id;
                vehicleToUpdate = await _dataFacade.VehicleDataProvider.GetAsync(vehicleToUpdate);
                vehicleToUpdate.DepartmentChecklistId = departmentChecklist.Id;
                await _dataFacade.VehicleDataProvider.SaveAsync(vehicleToUpdate);

                question.SiteChecklistId = departmentChecklist.Id;
            }

            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(question);

            // Get the module to sync the checklist
            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
            if (module?.IoTDevice != null)
            {
                // Get the device twin handler from service provider if it's not already injected
                var deviceTwinHandler = _serviceProvider.GetRequiredService<IDeviceTwinHandler>();
                await deviceTwinHandler.SyncChecklistToVehicle(module.IoTDevice);
            }

            e.Result = e.Entity;
        }
    }
}
