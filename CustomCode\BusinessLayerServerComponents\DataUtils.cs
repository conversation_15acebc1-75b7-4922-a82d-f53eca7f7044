﻿using NHibernate.Cfg.MappingSchema;
using System;
using System.Collections.Generic;
using System.IO.Compression;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FleetXQ.Data.DataObjects;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class DataUtils
    {
        public static DateTime HexToUtcTime(string hexTimestamp)
        {
            long unixTimestamp = Convert.ToInt64(hexTimestamp, 16);

            // Create a DateTimeOffset object from the Unix timestamp
            DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(unixTimestamp);

            // Convert to UTC time
            return dateTimeOffset.UtcDateTime;
        }

        public static string UtcTimeToHex(DateTime utcTime)
        {
            // Convert to DateTimeOffset
            DateTimeOffset dateTimeOffset = new DateTimeOffset(utcTime);

            // Get Unix timestamp in seconds
            long unixTimestamp = dateTimeOffset.ToUnixTimeSeconds();

            // Convert to hex string
            return unixTimestamp.ToString("X");
        }

        public static int ConvertHexToInt(string hexString)
        {
            // Remove any "0x" prefix from the hex string
            if (hexString.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                hexString = hexString.Substring(2);

            // Convert the hexadecimal string to an integer
            int result = Convert.ToInt32(hexString, 16);
            return result;
        }

        public static ulong ConvertHexToLong(string hexString)
        {
            // Remove any "0x" prefix from the hex string
            if (hexString.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                hexString = hexString.Substring(2);

            // for Uint64
            ulong value = ulong.Parse(hexString, System.Globalization.NumberStyles.HexNumber);
            return value;
        }

        public static string GetLocaleString(LocaleEnum localeEnum)
        {
            var localeString = localeEnum switch
            {
                LocaleEnum.EnglishUnitedStates => "en-US",
                LocaleEnum.EnglishUnitedKingdom => "en-GB",
                _ => "en-US"
            };

            return localeString;
        }

        public static double ConvertHexToDouble(string hexString)
        {
            // Remove any "0x" prefix from the hex string
            if (hexString.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                hexString = hexString.Substring(2);

            // Parse the hexadecimal string to a long
            long longValue = long.Parse(hexString, System.Globalization.NumberStyles.HexNumber);

            // Convert the long value to a double using BitConverter
            double doubleValue = BitConverter.Int64BitsToDouble(longValue);
            return doubleValue;
        }

        private static byte[] Compress(string str)
        {
            byte[] originalData = System.Text.Encoding.UTF8.GetBytes(str);
            byte[] compressedData;
            using (MemoryStream ms = new MemoryStream())
            {
                // Calculate uncompressed data size (4 bytes, big-endian)
                uint uncompressedSize = (uint)originalData.Length;
                byte[] sizeBytes = BitConverter.GetBytes(uncompressedSize);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(sizeBytes); // Ensure big-endian order
                ms.Write(sizeBytes, 0, sizeBytes.Length);

                // Write zlib header (2 bytes, hardcoded)
                ms.WriteByte(0x78);
                ms.WriteByte(0x9C);

                using (DeflateStream deflateStream = new DeflateStream(ms, CompressionMode.Compress, true))
                {
                    deflateStream.Write(originalData, 0, originalData.Length);
                }

                // Write Adler-32 checksum (4 bytes)
                uint adler = Adler32(originalData);
                byte[] adlerBytes = BitConverter.GetBytes(adler);
                if (BitConverter.IsLittleEndian)
                    Array.Reverse(adlerBytes); // Ensure big-endian order
                ms.Write(adlerBytes, 0, adlerBytes.Length);

                compressedData = ms.ToArray();
            }
            return compressedData;
        }

        private static List<string> SplitBase64String(string str)
        {
            int chunkSize = 4096;
            var chunks = new List<string>();
            for (int i = 0; i < str.Length; i += chunkSize)
            {
                if (i + chunkSize > str.Length) chunkSize = str.Length - i;
                chunks.Add(str.Substring(i, chunkSize));
            }
            return chunks;
        }
        public static object CompressAndSplit(List<object> toConvert)
        {
            var json = JsonConvert.SerializeObject(toConvert);

            // Compress
            byte[] compressedData = Compress(json);

            // Convert to Base64
            string base64String = Convert.ToBase64String(compressedData);

            // Split into chunks
            return new { data = SplitBase64String(base64String) };
        }
        public static string Decompress(byte[] bytes)
        {
            using var msi = new MemoryStream(bytes);
            using var mso = new MemoryStream();
            using (var gs = new GZipStream(msi, CompressionMode.Decompress))
            {
                gs.CopyTo(mso);
            }
            return Encoding.UTF8.GetString(mso.ToArray());
        }

        public static object JoinBase64String(List<string> chunks)
        {
            return string.Join("", chunks);
        }

        // Adler-32 checksum calculation
        static uint Adler32(byte[] data)
        {
            const uint MOD_ADLER = 65521;
            uint a = 1, b = 0;
            for (int index = 0; index < data.Length; ++index)
            {
                a = (a + data[index]) % MOD_ADLER;
                b = (b + a) % MOD_ADLER;
            }
            return (b << 16) | a;
        }

        // convert unix timestamp to datetime
        public static DateTime UnixTimeStampToDateTime(long unixTimeStamp)
        {
            // Unix timestamp is seconds past epoch
            System.DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
            dtDateTime = dtDateTime.AddSeconds(unixTimeStamp).ToLocalTime();
            return dtDateTime;
        }


    }

}
