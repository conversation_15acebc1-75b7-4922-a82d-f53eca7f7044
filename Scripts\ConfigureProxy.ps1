# Corporate Proxy Configuration for NuGet
# Run this script if you need to configure proxy settings

param(
    [Parameter(Mandatory=$false)]
    [string]$ProxyUrl = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ProxyUsername = "",
    
    [Parameter(Mandatory=$false)]
    [string]$ProxyPassword = "",
    
    [Parameter(Mandatory=$false)]
    [string]$NoProxyHosts = "localhost,127.0.0.1,.company.com"
)

Write-Host "Configuring NuGet proxy settings..." -ForegroundColor Yellow

if ([string]::IsNullOrEmpty($ProxyUrl)) {
    Write-Host "No proxy URL provided. Current proxy configuration:" -ForegroundColor Green
    netsh winhttp show proxy
    
    Write-Host "`nTo configure proxy, run this script with parameters:" -ForegroundColor Cyan
    Write-Host "  .\ConfigureProxy.ps1 -ProxyUrl 'http://proxy.company.com:8080' -ProxyUsername 'domain\username' -ProxyPassword 'password'" -ForegroundColor White
    return
}

# Configure NuGet proxy settings
Write-Host "Setting NuGet proxy configuration..." -ForegroundColor Green

dotnet nuget config set http_proxy $ProxyUrl
if (![string]::IsNullOrEmpty($ProxyUsername)) {
    dotnet nuget config set http_proxy.user $ProxyUsername
}
if (![string]::IsNullOrEmpty($ProxyPassword)) {
    dotnet nuget config set http_proxy.password $ProxyPassword
}
dotnet nuget config set https_proxy $ProxyUrl
dotnet nuget config set no_proxy $NoProxyHosts

Write-Host "✅ Proxy configuration completed!" -ForegroundColor Green

# Test connectivity
Write-Host "Testing connectivity..." -ForegroundColor Yellow
try {
    dotnet nuget list source
    Write-Host "✅ NuGet sources accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ Error accessing NuGet sources: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nProxy configuration summary:" -ForegroundColor Cyan
Write-Host "  HTTP Proxy: $ProxyUrl" -ForegroundColor White
Write-Host "  HTTPS Proxy: $ProxyUrl" -ForegroundColor White
Write-Host "  No Proxy: $NoProxyHosts" -ForegroundColor White
if (![string]::IsNullOrEmpty($ProxyUsername)) {
    Write-Host "  Username: $ProxyUsername" -ForegroundColor White
}
