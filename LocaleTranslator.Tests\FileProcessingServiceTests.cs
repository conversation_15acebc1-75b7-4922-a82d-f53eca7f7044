using LocaleTranslator.Models;
using LocaleTranslator.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;

namespace LocaleTranslator.Tests;

[TestFixture]
public class FileProcessingServiceTests
{
    private Mock<ClaudeTranslationService> _mockTranslationService;
    private Mock<ILogger<FileProcessingService>> _mockLogger;
    private FileProcessingService _service;
    private string _testDataPath;

    [SetUp]
    public void Setup()
    {
        _mockTranslationService = new Mock<ClaudeTranslationService>(Mock.Of<HttpClient>(), Mock.Of<ILogger<ClaudeTranslationService>>());
        _mockLogger = new Mock<ILogger<FileProcessingService>>();
        // Use real configuration for robust testing
        var config = new ConfigurationBuilder().AddInMemoryCollection(new Dictionary<string, string>()).Build();
        _service = new FileProcessingService(
            _mockTranslationService.Object,
            _mockLogger.Object
        );
        _testDataPath = Path.Combine(TestContext.CurrentContext.TestDirectory, "TestData", "FileProcessing");
        Directory.CreateDirectory(_testDataPath);
    }

    [TearDown]
    public void TearDown()
    {
        if (Directory.Exists(_testDataPath))
            Directory.Delete(_testDataPath, true);
    }

    [Test]
    public async Task DiscoverJsonFilesAsync_ReturnsOnlyJsonFiles()
    {
        var jsonFile = Path.Combine(_testDataPath, "file1.json");
        var txtFile = Path.Combine(_testDataPath, "file2.txt");
        await File.WriteAllTextAsync(jsonFile, "{}");
        await File.WriteAllTextAsync(txtFile, "not json");

        var files = await _service.DiscoverJsonFilesAsync(_testDataPath);
        Assert.That(files.Count, Is.EqualTo(1));
        Assert.That(files[0], Does.EndWith("file1.json"));
    }

    [Test]
    public async Task ProcessFilesAsync_EmptyDirectory_ReturnsNoResults()
    {
        var options = new TranslationOptions
        {
            SourceLocalesPath = _testDataPath,
            TargetLocalesPath = _testDataPath,
            TargetLanguage = "French"
        };
        var results = await _service.ProcessFilesAsync(options);
        Assert.That(results.TotalFiles, Is.EqualTo(0));
    }

    [Test]
    public async Task ProcessFilesAsync_InvalidJsonFile_ReportsFailure()
    {
        var invalidFile = Path.Combine(_testDataPath, "bad.json");
        await File.WriteAllTextAsync(invalidFile, "{invalid json}");
        var options = new TranslationOptions
        {
            SourceLocalesPath = _testDataPath,
            TargetLocalesPath = _testDataPath,
            TargetLanguage = "French"
        };
        var results = await _service.ProcessFilesAsync(options);
        Assert.That(results.TotalFiles, Is.EqualTo(1));
        Assert.That(results.FailedFiles, Is.EqualTo(1));
        Assert.That(results.Results[0].ErrorMessage, Does.Contain("Invalid JSON"));
    }

    [Test]
    public async Task ProcessFilesAsync_TargetExistsWithDifferentKeys_Retranslates()
    {
        var sourceDir = Path.Combine(_testDataPath, "src");
        var targetDir = Path.Combine(_testDataPath, "tgt");
        Directory.CreateDirectory(sourceDir);
        Directory.CreateDirectory(targetDir);

        var sourceFile = Path.Combine(sourceDir, "test.json");
        await File.WriteAllTextAsync(sourceFile, @"{""a"": ""hello"", ""b"": ""world""}");

        var targetFile = Path.Combine(targetDir, "test.json");
        await File.WriteAllTextAsync(targetFile, @"{""a"": ""bonjour"", ""c"": ""monde""}");

        var options = new TranslationOptions
        {
            SourceLocalesPath = sourceDir,
            TargetLocalesPath = targetDir,
            TargetLanguage = "French"
        };

        // Create a FileProcessingService without translation service for this test
        // The logic should still detect key differences and attempt translation
        _service = new FileProcessingService(
            null, // No translation service - will cause failure but test key difference detection
            _mockLogger.Object
        );

        var results = await _service.ProcessFilesAsync(options);

        Assert.That(results.TotalFiles, Is.EqualTo(1));
        Assert.That(results.FailedFiles, Is.EqualTo(1)); // Will fail due to no translation service
        Assert.That(results.Results[0].ErrorMessage, Does.Contain("Translation service is not available"));
        // We can't test RetranslatedDueToKeyDifferences without a working translation service
        // but we can test that key differences are detected in the CompareFileKeysAsync test
    }

    [Test]
    public async Task ProcessFilesAsync_TargetExistsWithMatchingKeys_Skips()
    {
        // Create source file with keys "a" and "b"
        var sourceFile = Path.Combine(_testDataPath, "test.json");
        await File.WriteAllTextAsync(sourceFile, @"{""a"": ""hello"", ""b"": ""world""}");

        // Create target file with same keys but different values
        var targetFile = Path.Combine(_testDataPath, "test.json");
        await File.WriteAllTextAsync(targetFile, @"{""a"": ""bonjour"", ""b"": ""monde""}");

        var options = new TranslationOptions
        {
            SourceLocalesPath = _testDataPath,
            TargetLocalesPath = _testDataPath,
            TargetLanguage = "French"
        };

        var results = await _service.ProcessFilesAsync(options);

        Assert.That(results.TotalFiles, Is.EqualTo(1));
        Assert.That(results.SkippedFiles, Is.EqualTo(1));
        Assert.That(results.RetranslatedFiles, Is.EqualTo(0));
        Assert.That(results.Results[0].Skipped, Is.True);
        Assert.That(results.Results[0].SkipReason, Does.Contain("matching keys"));
    }

    [Test]
    public async Task KeyComparison_DetectsMissingKeys_Correctly()
    {
        // Create source file with more keys than target
        var sourceFile = Path.Combine(_testDataPath, "source.json");
        await File.WriteAllTextAsync(sourceFile, @"{""a"": ""hello"", ""b"": ""world"", ""c"": ""test""}");

        // Create target file with fewer keys
        var targetFile = Path.Combine(_testDataPath, "target.json");
        await File.WriteAllTextAsync(targetFile, @"{""a"": ""bonjour"", ""b"": ""monde""}");

        var keyDifference = await _service.CompareFileKeysAsync(sourceFile, targetFile, "test.json");

        Assert.That(keyDifference.HasDifferences, Is.True);
        Assert.That(keyDifference.MissingKeys.Count, Is.EqualTo(1));
        Assert.That(keyDifference.MissingKeys[0], Is.EqualTo("c"));
        Assert.That(keyDifference.ExtraKeys.Count, Is.EqualTo(0));
    }

    [Test]
    public async Task KeyComparison_DetectsExtraKeys_Correctly()
    {
        // Create source file with fewer keys than target
        var sourceFile = Path.Combine(_testDataPath, "source.json");
        await File.WriteAllTextAsync(sourceFile, @"{""a"": ""hello"", ""b"": ""world""}");

        // Create target file with more keys
        var targetFile = Path.Combine(_testDataPath, "target.json");
        await File.WriteAllTextAsync(targetFile, @"{""a"": ""bonjour"", ""b"": ""monde"", ""c"": ""test""}");

        var keyDifference = await _service.CompareFileKeysAsync(sourceFile, targetFile, "test.json");

        Assert.That(keyDifference.HasDifferences, Is.True);
        Assert.That(keyDifference.MissingKeys.Count, Is.EqualTo(0));
        Assert.That(keyDifference.ExtraKeys.Count, Is.EqualTo(1));
        Assert.That(keyDifference.ExtraKeys[0], Is.EqualTo("c"));
    }

    [Test]
    public async Task KeyComparison_DetectsNestedKeyDifferences_Correctly()
    {
        // Create source file with nested structure
        var sourceFile = Path.Combine(_testDataPath, "source.json");
        await File.WriteAllTextAsync(sourceFile, @"{""fields"": {""a"": ""hello"", ""b"": ""world"", ""c"": ""test""}}");

        // Create target file with missing nested key
        var targetFile = Path.Combine(_testDataPath, "target.json");
        await File.WriteAllTextAsync(targetFile, @"{""fields"": {""a"": ""bonjour"", ""b"": ""monde""}}");

        var keyDifference = await _service.CompareFileKeysAsync(sourceFile, targetFile, "test.json");

        Assert.That(keyDifference.HasDifferences, Is.True);
        Assert.That(keyDifference.MissingKeys.Count, Is.EqualTo(1));
        Assert.That(keyDifference.MissingKeys[0], Is.EqualTo("fields.c"));
        Assert.That(keyDifference.ExtraKeys.Count, Is.EqualTo(0));
    }

    [Test]
    public async Task KeyComparison_WithActualCustomerFiles_DetectsDifferences()
    {
        // Create source file similar to the actual Customer.json structure
        var sourceFile = Path.Combine(_testDataPath, "source.json");
        var sourceContent = @"{
            ""entityName"": ""Customer"",
            ""entityNamePlural"": ""Customers"",
            ""fields"": {
                ""AccessGroupItems"": {""displayName"": ""Access group Items""},
                ""Active"": {""displayName"": ""Active""},
                ""CompanyName"": {""displayName"": ""Customer Name""},
                ""CustomerFeatureSubscription"": {""displayName"": ""CustomerFeatureSubscription""},
                ""DashboardDriverCardStoreProcedureItems"": {""displayName"": ""Dashboard Driver Card Store Procedure Items""}
            }
        }";
        await File.WriteAllTextAsync(sourceFile, sourceContent);

        // Create target file with missing keys
        var targetFile = Path.Combine(_testDataPath, "target.json");
        var targetContent = @"{
            ""entityName"": ""Kustomer"",
            ""entityNamePlural"": ""Mga Kustomer"",
            ""fields"": {
                ""AccessGroupItems"": {""displayName"": ""Mga Item ng Access Group""},
                ""Active"": {""displayName"": ""Aktibo""},
                ""CompanyName"": {""displayName"": ""Pangalan ng Kustomer""}
            }
        }";
        await File.WriteAllTextAsync(targetFile, targetContent);

        var keyDifference = await _service.CompareFileKeysAsync(sourceFile, targetFile, "Customer.json");

        Assert.That(keyDifference.HasDifferences, Is.True);
        Assert.That(keyDifference.MissingKeys.Count, Is.GreaterThan(0));
        Assert.That(keyDifference.MissingKeys, Does.Contain("fields.CustomerFeatureSubscription"));
        Assert.That(keyDifference.MissingKeys, Does.Contain("fields.DashboardDriverCardStoreProcedureItems"));
    }
}