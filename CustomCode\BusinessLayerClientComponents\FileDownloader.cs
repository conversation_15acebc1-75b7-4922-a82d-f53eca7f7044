﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Client
{
    /// <summary>
	/// FileDownloader Component
	///  
	/// </summary>
    public partial class FileDownloader : IFileDownloader 
    {	
		public void Dispose()
		{
		}

        public async Task<ComponentResponse<bool>> DownloadFileAsync(string fileName, string fileUrl, Dictionary<string, object> parameters = null)
        {
            return new ComponentResponse<bool>(default(bool));
        }
    }
}
