-- =============================================
-- Bulk Import Vehicles Using Temporary Tables
-- =============================================

-- Step 1: Initialize Vehicle Import Session
-- Creates temporary table for bulk data loading
CREATE OR ALTER PROCEDURE [dbo].[BeginVehicleImport]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Create session-specific temporary table for vehicles
    CREATE TABLE #ImportVehicles (
        VIN NVARCHAR(50) NULL,
        HireNo NVARCHAR(100) NOT NULL,
        ModelName NVARCHAR(100) NOT NULL,
        SerialNumber NVARCHAR(100) NULL,
        RegistrationNumber NVARCHAR(50) NULL,
        DepartmentName NVARCHAR(100) NOT NULL,
        SiteName NVARCHAR(100) NOT NULL,
        CustomerName NVARCHAR(100) NOT NULL,
        Active BIT DEFAULT 1,
        YearManufactured INT NULL,
        -- Add indexes for performance during merge
        INDEX IX_ImportVehicles_HireNo NONCLUSTERED (HireNo),
        INDEX IX_ImportVehicles_VIN NONCLUSTERED (VIN),
        INDEX IX_ImportVehicles_Lookup NONCLUSTERED (CustomerName, SiteName, DepartmentName, ModelName)
    );
    
    -- Store session metadata
    INSERT INTO ImportSession (Id, EntityType, Status, StartedAt)
    VALUES (@ImportSessionId, 'Vehicle', 'InProgress', GETUTCDATE());
    
    SELECT 'Vehicle import session initialized' AS Message;
END
GO

-- Step 2: Process Vehicle Import Data
-- Validates and merges data from temporary table to production
CREATE OR ALTER PROCEDURE [dbo].[ProcessVehicleImport]
    @ImportSessionId UNIQUEIDENTIFIER,
    @AllowUpdates BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    DECLARE @ProcessedCount INT = 0;
    DECLARE @ErrorCount INT = 0;
    DECLARE @ValidationErrors TABLE (
        RowNumber INT,
        HireNo NVARCHAR(100),
        ErrorMessage NVARCHAR(500)
    );
    
    -- Check if temp table exists
    IF OBJECT_ID('tempdb..#ImportVehicles') IS NULL
    BEGIN
        RAISERROR('Import session not initialized. Call BeginVehicleImport first.', 16, 1);
        RETURN;
    END
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Add row numbers for error reporting
        ALTER TABLE #ImportVehicles ADD RowNumber INT IDENTITY(1,1);
        
        -- Validation 1: Check for duplicate HireNo in import data
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT 
            MIN(RowNumber),
            HireNo,
            'Duplicate HireNo in import data: ' + HireNo
        FROM #ImportVehicles
        GROUP BY HireNo
        HAVING COUNT(*) > 1;
        
        -- Validation 2: Check for duplicate VIN in import data (if provided)
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT 
            MIN(RowNumber),
            HireNo,
            'Duplicate VIN in import data: ' + VIN
        FROM #ImportVehicles
        WHERE VIN IS NOT NULL AND VIN != ''
        GROUP BY VIN
        HAVING COUNT(*) > 1;
        
        -- Validation 3: Check for required fields
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT RowNumber, HireNo, 'HireNo is required'
        FROM #ImportVehicles
        WHERE HireNo IS NULL OR LTRIM(RTRIM(HireNo)) = '';
        
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT RowNumber, HireNo, 'ModelName is required'
        FROM #ImportVehicles
        WHERE ModelName IS NULL OR LTRIM(RTRIM(ModelName)) = '';
        
        -- Validation 4: Check for valid Customer/Site/Department combinations
        WITH HierarchyValidation AS (
            SELECT 
                i.RowNumber,
                i.HireNo,
                i.CustomerName,
                i.SiteName,
                i.DepartmentName,
                i.ModelName,
                c.Id AS CustomerId,
                s.Id AS SiteId,
                d.Id AS DepartmentId,
                m.Id AS ModelId
            FROM #ImportVehicles i
            LEFT JOIN dbo.Customer c ON c.CompanyName = i.CustomerName AND c.Active = 1
            LEFT JOIN dbo.Site s ON s.Name = i.SiteName AND s.CustomerId = c.Id AND s.Active = 1
            LEFT JOIN dbo.Department d ON d.Name = i.DepartmentName AND d.SiteId = s.Id AND d.Active = 1
            LEFT JOIN dbo.Model m ON m.Name = i.ModelName AND m.Active = 1
        )
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT RowNumber, HireNo, 
            CASE 
                WHEN CustomerId IS NULL THEN 'Invalid Customer: ' + ISNULL(CustomerName, 'NULL')
                WHEN SiteId IS NULL THEN 'Invalid Site: ' + ISNULL(SiteName, 'NULL') + ' for Customer: ' + CustomerName
                WHEN DepartmentId IS NULL THEN 'Invalid Department: ' + ISNULL(DepartmentName, 'NULL') + ' for Site: ' + SiteName
                WHEN ModelId IS NULL THEN 'Invalid Model: ' + ISNULL(ModelName, 'NULL')
            END
        FROM HierarchyValidation
        WHERE CustomerId IS NULL OR SiteId IS NULL OR DepartmentId IS NULL OR ModelId IS NULL;
        
        -- Validation 5: Check for existing HireNo conflicts
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT i.RowNumber, i.HireNo, 'HireNo already exists: ' + i.HireNo
        FROM #ImportVehicles i
        INNER JOIN dbo.Vehicle v ON v.HireNo = i.HireNo
        WHERE @AllowUpdates = 0;
        
        -- Validation 6: Check for existing VIN conflicts
        INSERT INTO @ValidationErrors (RowNumber, HireNo, ErrorMessage)
        SELECT i.RowNumber, i.HireNo, 'VIN already exists: ' + i.VIN
        FROM #ImportVehicles i
        INNER JOIN dbo.Vehicle v ON v.VIN = i.VIN
        WHERE i.VIN IS NOT NULL AND i.VIN != '' AND @AllowUpdates = 0;
        
        -- If validation errors exist, return them and rollback
        IF EXISTS (SELECT 1 FROM @ValidationErrors)
        BEGIN
            SELECT * FROM @ValidationErrors ORDER BY RowNumber;
            SET @ErrorCount = @@ROWCOUNT;
            
            UPDATE ImportSession 
            SET Status = 'Failed', 
                EndedAt = GETUTCDATE(),
                ErrorMessage = 'Validation failed with ' + CAST(@ErrorCount AS NVARCHAR(10)) + ' errors'
            WHERE Id = @ImportSessionId;
            
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Create lookup table for valid hierarchy and models
        CREATE TABLE #ValidLookups (
            HireNo NVARCHAR(100),
            CustomerId UNIQUEIDENTIFIER,
            SiteId UNIQUEIDENTIFIER,
            DepartmentId UNIQUEIDENTIFIER,
            ModelId UNIQUEIDENTIFIER,
            INDEX IX_HireNo (HireNo)
        );
        
        INSERT INTO #ValidLookups
        SELECT 
            i.HireNo,
            c.Id,
            s.Id,
            d.Id,
            m.Id
        FROM #ImportVehicles i
        INNER JOIN dbo.Customer c ON c.CompanyName = i.CustomerName AND c.Active = 1
        INNER JOIN dbo.Site s ON s.Name = i.SiteName AND s.CustomerId = c.Id AND s.Active = 1
        INNER JOIN dbo.Department d ON d.Name = i.DepartmentName AND d.SiteId = s.Id AND d.Active = 1
        INNER JOIN dbo.Model m ON m.Name = i.ModelName AND m.Active = 1;
        
        -- Perform the merge operation
        WITH SourceData AS (
            SELECT 
                NEWID() AS NewId,
                i.VIN,
                i.HireNo,
                i.SerialNumber,
                i.RegistrationNumber,
                i.Active,
                i.YearManufactured,
                l.CustomerId,
                l.SiteId,
                l.DepartmentId,
                l.ModelId,
                @ImportSessionId AS ImportSessionId,
                GETUTCDATE() AS ImportDate
            FROM #ImportVehicles i
            INNER JOIN #ValidLookups l ON i.HireNo = l.HireNo
        )
        MERGE dbo.Vehicle AS Target
        USING SourceData AS Source ON Target.HireNo = Source.HireNo
        WHEN NOT MATCHED THEN
            INSERT (
                Id, VIN, HireNo, SerialNumber, RegistrationNumber, Active, 
                YearManufactured, SiteId, DepartmentId, ModelId, 
                ImportSessionId, CreatedDate
            )
            VALUES (
                Source.NewId, Source.VIN, Source.HireNo, Source.SerialNumber, 
                Source.RegistrationNumber, Source.Active, Source.YearManufactured,
                Source.SiteId, Source.DepartmentId, Source.ModelId,
                Source.ImportSessionId, Source.ImportDate
            )
        WHEN MATCHED AND @AllowUpdates = 1 THEN
            UPDATE SET 
                VIN = Source.VIN,
                SerialNumber = Source.SerialNumber,
                RegistrationNumber = Source.RegistrationNumber,
                Active = Source.Active,
                YearManufactured = Source.YearManufactured,
                SiteId = Source.SiteId,
                DepartmentId = Source.DepartmentId,
                ModelId = Source.ModelId,
                LastModifiedDate = Source.ImportDate;
        
        SET @ProcessedCount = @@ROWCOUNT;
        
        -- Update import session
        UPDATE ImportSession 
        SET Status = 'Completed', 
            EndedAt = GETUTCDATE(),
            ProcessedCount = @ProcessedCount
        WHERE Id = @ImportSessionId;
        
        COMMIT TRANSACTION;
        
        -- Return success summary
        SELECT 
            @ProcessedCount AS ProcessedRows,
            (SELECT COUNT(*) FROM #ImportVehicles) AS InputRows,
            'Import completed successfully' AS Message;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        -- Log error
        UPDATE ImportSession 
        SET Status = 'Failed', 
            EndedAt = GETUTCDATE(),
            ErrorMessage = ERROR_MESSAGE()
        WHERE Id = @ImportSessionId;
        
        -- Re-raise the error
        THROW;
    END CATCH
END
GO

-- Step 3: Combined Driver-Vehicle Import
-- For scenarios where drivers and vehicles are imported together
CREATE OR ALTER PROCEDURE [dbo].[ProcessDriverVehicleAssignment]
    @ImportSessionId UNIQUEIDENTIFIER
AS
BEGIN
    SET NOCOUNT ON;
    SET XACT_ABORT ON;
    
    -- Create temporary table for driver-vehicle relationships
    CREATE TABLE #DriverVehicleAssignments (
        DriverExternalId NVARCHAR(50) NOT NULL,
        VehicleHireNo NVARCHAR(100) NOT NULL,
        AssignmentType NVARCHAR(20) DEFAULT 'Primary', -- Primary, Secondary, etc.
        EffectiveDate DATETIME DEFAULT GETUTCDATE(),
        INDEX IX_Assignment (DriverExternalId, VehicleHireNo)
    );
    
    -- This procedure assumes the temp table is populated externally
    -- or through SqlBulkCopy
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Validate that drivers and vehicles exist
        DECLARE @ValidationErrors TABLE (
            DriverExternalId NVARCHAR(50),
            VehicleHireNo NVARCHAR(100),
            ErrorMessage NVARCHAR(500)
        );
        
        INSERT INTO @ValidationErrors
        SELECT a.DriverExternalId, a.VehicleHireNo, 'Driver not found: ' + a.DriverExternalId
        FROM #DriverVehicleAssignments a
        LEFT JOIN dbo.Driver d ON d.ExternalId = a.DriverExternalId
        WHERE d.Id IS NULL;
        
        INSERT INTO @ValidationErrors
        SELECT a.DriverExternalId, a.VehicleHireNo, 'Vehicle not found: ' + a.VehicleHireNo
        FROM #DriverVehicleAssignments a
        LEFT JOIN dbo.Vehicle v ON v.HireNo = a.VehicleHireNo
        WHERE v.Id IS NULL;
        
        IF EXISTS (SELECT 1 FROM @ValidationErrors)
        BEGIN
            SELECT * FROM @ValidationErrors;
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- Create assignments
        INSERT INTO dbo.DriverVehicleAssignment (
            Id, DriverId, VehicleId, AssignmentType, EffectiveDate, 
            ImportSessionId, CreatedDate
        )
        SELECT 
            NEWID(),
            d.Id,
            v.Id,
            a.AssignmentType,
            a.EffectiveDate,
            @ImportSessionId,
            GETUTCDATE()
        FROM #DriverVehicleAssignments a
        INNER JOIN dbo.Driver d ON d.ExternalId = a.DriverExternalId
        INNER JOIN dbo.Vehicle v ON v.HireNo = a.VehicleHireNo;
        
        COMMIT TRANSACTION;
        
        SELECT @@ROWCOUNT AS AssignmentsCreated, 'Assignments completed successfully' AS Message;
        
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO
