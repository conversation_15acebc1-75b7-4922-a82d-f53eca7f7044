<!--
// This is Custom Code
// Override of the generated partial view to include filter
-->
<div data-bind="'visible': {DATABINDROOT}StatusData.IsFilterVisible">
    {#false,PersonToPerVehicleMasterAccessViewFilter,Filter,,PersonToPerVehicleMasterAccessView\Filters\PersonToPerVehicleMasterAccessViewFilterPartialView.html#}
</div>
<div class="uiContainer list-container" id="{VIEWNAME}-{DISPLAYMODE}"
    data-bind="'css': { 'busy': {DATABINDROOT}StatusData.IsBusy(), 'disabled': !{DATABINDROOT}StatusData.IsEnabled() || {DATABINDROOT}StatusData.IsBusy() }"
    data-test-id="1730190a-3719-4d87-bbf3-a2595141c89c">
    <div id="{VIEWNAME}Control-{DISPLAYMODE}">
        <div class="hideElt" data-bind="css: { hideElt : false }">
            <h2 data-bind="visible: {DATABINDROOT}StatusData.ShowTitle(), i18n: {DATABINDROOT}StatusData.Title()"></h2>
            <div class="no-data-message"
                data-bind="'visible' : {DATABINDROOT}StatusData.IsEmpty() && !{DATABINDROOT}StatusData.IsBusy(), i18n: 'entities/PersonToPerVehicleNormalAccessView/lists/PersonToPerVehicleMasterAccessViewList:messages.noDataMessage'">
            </div>
            <div class="loading-content"
                data-bind="'visible' : {DATABINDROOT}StatusData.IsEmpty() && {DATABINDROOT}StatusData.IsBusy()">
                <span class="loading-content-image"></span>
                <span data-bind="text: FleetXQ.Web.Messages.loadingMessage"></span>
            </div>
            <div class="uiContainer list-item-container">
                <div class="row"
                    data-bind="'visible' : !{DATABINDROOT}StatusData.IsEmpty(), 'foreach' : { data: {DATABINDROOT}viewModelCollection, as: '{LISTNAME}KOData' }"
                    id="{VIEWNAME}Data-{DISPLAYMODE}">
                    <div class="col-4 uiContainer">
                        <div>
                            {#false,PersonToPerVehicleMasterAccessViewForm,Form,,PersonToPerVehicleNormalAccessView\PersonToPerVehicleMasterAccessViewFormPartialView.html#}
                        </div>
                    </div>
                </div>
            </div>
            <div class="gridCommandContainer" data-bind="'css': { 'border': !{DATABINDROOT}StatusData.IsEmpty() }">
                <div class="d-flex">
                    <div class="grid-count-label" data-bind="'visible' : !{DATABINDROOT}StatusData.IsEmpty()">
                        <span class="titleText">Items : </span>
                        <span data-bind="text: GO.FormatNumberWithSpaces({DATABINDROOT}totalCollection())"></span>
                    </div>
                    <div class="--horizontalList"
                        data-bind="'visible': !{DATABINDROOT}StatusData.IsEmpty() && {DATABINDROOT}StatusData.IsEnabled() && {DATABINDROOT}totalPageNumber() > 1">
                        <div id="{PARENT}-{DATABINDROOTWITHOUTDOT}list-paginator-widget-{DISPLAYMODE}">
                            <ul class="grid-pagination">
                                <li class="page-numbers page-controls"
                                    data-bind="safeAttr: { 'data-pagenumber': 0 }, css: { 'invisible': ({DATABINDROOT}pageNumber() < 1 + 2) }, click: function(data, event){{DATABINDROOT}paginationSettings.methods.paginationClickHandler(data, event)}">
                                    <svg class="bi seek-first-page-icon" width="16" height="16">
                                        <use xlink:href="Styles\Images\bootstrap-icons.svg#chevron-bar-left"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" />
                                    </svg>
                                </li>
                                <li class="page-numbers page-controls"
                                    data-bind="safeAttr: { 'data-pagenumber': {DATABINDROOT}pageNumber() - 1 }, css: { 'invisible': {DATABINDROOT}pageNumber() < 1 }, click: function(data, event){{DATABINDROOT}paginationSettings.methods.paginationClickHandler(data, event)}">
                                    <svg class="bi seek-prev-page-icon" width="12" height="12">
                                        <use xlink:href="Styles\Images\bootstrap-icons.svg#chevron-left"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" />
                                    </svg>
                                </li>
                                <div data-bind="foreach: {DATABINDROOT}pageItems()">
                                    <li data-bind="safeAttr: { 'data-pagenumber': pagenumber }, css: { currentPage: domClass == true }, text: text, click: function(data, event){{PARENTDATABINDROOT}paginationSettings.methods.paginationClickHandler(data, event)}"
                                        class="page-numbers"></li>
                                </div>
                                <li class="page-numbers page-controls"
                                    data-bind="safeAttr: { 'data-pagenumber': parseInt({DATABINDROOT}pageNumber(), 10) + parseInt(1, 10) }, css: { 'invisible': {DATABINDROOT}pageNumber() >= ({DATABINDROOT}totalPageNumber() - 1) }, click: function(data, event){{DATABINDROOT}paginationSettings.methods.paginationClickHandler(data, event)} ">
                                    <svg class="bi seek-next-page-icon" width="12" height="12">
                                        <use xlink:href="Styles\Images\bootstrap-icons.svg#chevron-right"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" />
                                    </svg>
                                </li>
                                <li class="page-numbers page-controls"
                                    data-bind="safeAttr: { 'data-pagenumber': {DATABINDROOT}totalPageNumber() - 1 }, css: { 'invisible': {DATABINDROOT}pageNumber() >= ({DATABINDROOT}totalPageNumber() - (1 + 2)) }, click: function(data, event){{DATABINDROOT}paginationSettings.methods.paginationClickHandler(data, event)}">
                                    <svg class="bi seek-end-page-icon" width="16" height="16">
                                        <use xlink:href="Styles\Images\bootstrap-icons.svg#chevron-bar-right"
                                            xmlns:xlink="http://www.w3.org/1999/xlink" />
                                    </svg>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</div>