using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace FleetXQFunctionService.Services
{
    public interface ICircuitBreakerService
    {
        Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string operationName);
        bool IsOpen { get; }
    }

    public class CircuitBreakerService : ICircuitBreakerService
    {
        private readonly ILogger<CircuitBreakerService> _logger;
        private readonly int _failureThreshold;
        private readonly TimeSpan _timeout;
        private readonly TimeSpan _retryTimePeriod;

        private int _failureCount;
        private DateTime _lastFailureTime;
        private CircuitState _state;

        public CircuitBreakerService(ILogger<CircuitBreakerService> logger)
        {
            _logger = logger;
            _failureThreshold = 5; // Open circuit after 5 consecutive failures
            _timeout = TimeSpan.FromMinutes(2); // Keep circuit open for 2 minutes
            _retryTimePeriod = TimeSpan.FromSeconds(30); // Allow one test after 30 seconds
            _state = CircuitState.Closed;
        }

        public bool IsOpen => _state == CircuitState.Open;

        public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation, string operationName)
        {
            if (_state == CircuitState.Open)
            {
                if (DateTime.UtcNow - _lastFailureTime < _timeout)
                {
                    _logger.LogWarning("Circuit breaker is OPEN for {OperationName}. Failing fast.", operationName);
                    throw new InvalidOperationException($"Circuit breaker is open for {operationName}");
                }

                // Try to transition to half-open
                _state = CircuitState.HalfOpen;
                _logger.LogInformation("Circuit breaker transitioning to HALF-OPEN for {OperationName}", operationName);
            }

            try
            {
                var result = await operation();

                // Success - reset circuit breaker
                if (_state == CircuitState.HalfOpen)
                {
                    _state = CircuitState.Closed;
                    _failureCount = 0;
                    _logger.LogInformation("Circuit breaker closed for {OperationName} after successful operation", operationName);
                }

                return result;
            }
            catch (Exception ex)
            {
                _failureCount++;
                _lastFailureTime = DateTime.UtcNow;

                _logger.LogError(ex, "Operation {OperationName} failed. Failure count: {FailureCount}",
                    operationName, _failureCount);

                if (_failureCount >= _failureThreshold || _state == CircuitState.HalfOpen)
                {
                    _state = CircuitState.Open;
                    _logger.LogWarning("Circuit breaker OPENED for {OperationName} after {FailureCount} failures",
                        operationName, _failureCount);
                }

                throw;
            }
        }

        private enum CircuitState
        {
            Closed,
            Open,
            HalfOpen
        }
    }
}