﻿using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
// Added for bulk deletion optimization
using System.Data;
using System.Data.SqlClient;
using NHibernate.Util;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace FleetXQ.BusinessLayer.Components.Server
{

    /// <summary>
	/// VehicleAccessUtilities Component
	///  
	/// </summary>
    public partial class VehicleAccessUtilities : BaseServerComponent, IVehicleAccessUtilities
    {
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        private readonly ILoggingService _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;

        private readonly IAuthentication _authentication;

        public VehicleAccessUtilities(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade, IDeviceMessageHandler deviceMessageHandler, ILoggingService logger, IServiceScopeFactory serviceScopeFactory, IAuthentication authentication) : base(serviceProvider, configuration, dataFacade)
        {
            _deviceMessageHandler = deviceMessageHandler;
            _logger = logger;
            _serviceScopeFactory = serviceScopeFactory;
            _authentication = authentication;
        }

        /////////////////////////////////////////////////////////////////////////////////////
        // New methods for updating site, department, model and vehicle accesses in batch
        /////////////////////////////////////////////////////////////////////////////////////

        public async Task<ComponentResponse<bool>> CopyUserVehicleAccessAsync(Guid personId, Guid[] driverIds, Dictionary<string, object> parameters = null)
        {
            var person = await GetPersonAsync(personId);

            foreach (var driverId in driverIds)
            {
                var driver = _serviceProvider.GetRequiredService<DriverDataObject>();
                driver.Id = driverId;
                driver = await _dataFacade.DriverDataProvider.GetAsync(driver, includes: new List<string> {
                    "Card",
                    "Card.SiteVehicleNormalCardAccessItems",
                    "Card.DepartmentVehicleNormalCardAccessItems",
                    "Card.ModelVehicleNormalCardAccessItems",
                    "Card.PerVehicleNormalCardAccessItems" });

                await driver.LoadCardAsync(skipSecurity: true);

                if (driver.Card == null)
                {
                    continue;
                }

                // Delete current accesses
                foreach (var item in driver.Card.SiteVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.DepartmentVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.ModelVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                foreach (var item in driver.Card.PerVehicleNormalCardAccessItems)
                {
                    item.IsMarkedForDeletion = true;
                }

                //Copy accesses
                foreach (var item in person.Driver.Card.SiteVehicleNormalCardAccessItems)
                {
                    var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
                    siteAccess.SiteId = item.SiteId;
                    siteAccess.PermissionId = item.PermissionId;

                    driver.Card.SiteVehicleNormalCardAccessItems.Add(siteAccess);
                }

                foreach (var item in person.Driver.Card.DepartmentVehicleNormalCardAccessItems)
                {
                    var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                    deptAccess.DepartmentId = item.DepartmentId;
                    deptAccess.PermissionId = item.PermissionId;
                    driver.Card.DepartmentVehicleNormalCardAccessItems.Add(deptAccess);
                }

                foreach (var item in person.Driver.Card.ModelVehicleNormalCardAccessItems)
                {
                    var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    modelAccess.ModelId = item.ModelId;
                    modelAccess.DepartmentId = item.DepartmentId;
                    modelAccess.PermissionId = item.PermissionId;
                    driver.Card.ModelVehicleNormalCardAccessItems.Add(modelAccess);
                }

                foreach (var item in person.Driver.Card.PerVehicleNormalCardAccessItems)
                {
                    var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    vehicleAccess.VehicleId = item.VehicleId;
                    vehicleAccess.PermissionId = item.PermissionId;

                    driver.Card.PerVehicleNormalCardAccessItems.Add(vehicleAccess);
                }

                await _dataFacade.DriverDataProvider.SaveAsync(driver, skipSecurity: true);
            }

            return new ComponentResponse<System.Boolean>(true);
        }

        public async Task<ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>> CreateOnDemandAccessesAsync(Guid[] cardIds, Guid vehicleId, Dictionary<string, object> parameters)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);

            if (vehicle == null)
            {
                return new ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>(new DataObjectCollection<PerVehicleNormalCardAccessDataObject>());
            }

            var permissionOnDemandUser = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)PermissionLevelEnum.OnDemandUser }, skipSecurity: true)).SingleOrDefault();

            var cardIdsToAdd = new List<Guid>();

            var accessList = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var cardId in cardIds)
            {
                var card = _serviceProvider.GetRequiredService<CardDataObject>();
                card.Id = cardId;
                card = await _dataFacade.CardDataProvider.GetAsync(card);

                if (card == null)
                {
                    continue;
                }

                await card.LoadPerVehicleNormalCardAccessItemsAsync();

                var existingPerVehicleAccess = card.PerVehicleNormalCardAccessItems.FirstOrDefault(a => a.VehicleId == vehicleId && a.PermissionId == permissionOnDemandUser.Id);
                if (existingPerVehicleAccess == null)
                {
                    existingPerVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    existingPerVehicleAccess.VehicleId = vehicleId;
                    existingPerVehicleAccess.PermissionId = permissionOnDemandUser.Id;
                    card.PerVehicleNormalCardAccessItems.Add(existingPerVehicleAccess);

                    await _dataFacade.CardDataProvider.SaveAsync(card, skipSecurity: true);
                }

                accessList.Add(existingPerVehicleAccess);
            }

            // Start sync in background
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            _ = Task.Run(async () =>
            {
                var syncStart = DateTime.UtcNow;
                try
                {
                    using var scope = _serviceScopeFactory.CreateScope();
                    var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                    var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                    await SyncDriversInScopeAsync(new List<Guid> { vehicleId }, scopedDataFacade, scopedDeviceTwinHandler, scope.ServiceProvider, currentUserId);

                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogInformation($"[PERF] SyncDriversAsync (background - on-demand create) completed in {syncDuration}ms for vehicle {vehicleId}");
                }
                catch (Exception ex)
                {
                    var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                    _logger?.LogError(ex, $"[PERF] SyncDriversAsync (background - on-demand create) failed after {syncDuration}ms: {ex.Message}");
                }
            });

            return new ComponentResponse<DataObjectCollection<PerVehicleNormalCardAccessDataObject>>(new DataObjectCollection<PerVehicleNormalCardAccessDataObject>(accessList));
        }

        public async Task<ComponentResponse<bool>> DeleteOnDemandAccessAsync(Guid accessId, Dictionary<string, object> parameters = null)
        {
            var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            access.Id = accessId;
            access = await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetAsync(access);

            if (access != null)
            {
                access.IsMarkedForDeletion = true;

                await _dataFacade.PerVehicleNormalCardAccessDataProvider.DeleteAsync(access);

                // Start sync in background
                var userClaims = await _authentication.GetCurrentUserClaimsAsync();
                var currentUserId = userClaims?.UserId;

                _ = Task.Run(async () =>
                {
                    var syncStart = DateTime.UtcNow;
                    try
                    {
                        using var scope = _serviceScopeFactory.CreateScope();
                        var scopedDataFacade = scope.ServiceProvider.GetRequiredService<IDataFacade>();
                        var scopedDeviceTwinHandler = scope.ServiceProvider.GetRequiredService<IDeviceTwinHandler>();

                        await SyncDriversInScopeAsync(new List<Guid> { access.VehicleId }, scopedDataFacade, scopedDeviceTwinHandler, scope.ServiceProvider, currentUserId);

                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogInformation($"[PERF] SyncDriversAsync (background - on-demand delete) completed in {syncDuration}ms for vehicle {access.VehicleId}");
                    }
                    catch (Exception ex)
                    {
                        var syncDuration = (DateTime.UtcNow - syncStart).TotalMilliseconds;
                        _logger?.LogError(ex, $"[PERF] SyncDriversAsync (background - on-demand delete) failed after {syncDuration}ms: {ex.Message}");
                    }
                });

                return new ComponentResponse<bool>(true);
            }

            return new ComponentResponse<bool>(false);
        }

        /// <summary>
        /// GetAccessesForDepartments - Calculates department access based on site selections
        /// 
        /// DEACTIVATED VIA TAB NAVIGATION LOCK:
        /// This method contains the original cascading logic but is effectively deactivated because
        /// tab navigation is locked during edit mode. Users cannot navigate between tabs until they save,
        /// which prevents this method from being called during editing sessions.
        /// 
        /// This approach prevents data integrity issues with pagination while maintaining the original
        /// business logic for when it's safe to execute (post-save scenarios).
        /// 
        /// Original purpose: When user selects sites, automatically calculate which departments 
        /// should be accessible and return them for UI display.
        /// </summary>
        public async Task<ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>> GetAccessesForDepartmentsAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                _logger?.LogWarning($"[GetAccessesForDepartmentsAsync] No permission found for level {permissionLevel}. Returning empty collection.");
                var emptyDataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                var emptyCollection = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = emptyDataset };
                return new ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>(emptyCollection);
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var departmentAccessCollection = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            // Original cascading logic: For each selected site, find all departments in that site
            foreach (var siteAccess in personToSiteAccesses)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.Id = siteAccess.SiteId;
                site = await _dataFacade.SiteDataProvider.GetAsync(site, includes: new List<string> { "DepartmentItems" }, skipSecurity: true);
                if (site == null)
                {
                    continue;
                }

                foreach (var department in site.DepartmentItems)
                {
                    var deptAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();

                    deptAccess.DepartmentId = department.Id;
                    deptAccess.DepartmentName = department.Name;
                    deptAccess.PermissionId = permission.Id;
                    deptAccess.PersonId = personId;
                    deptAccess.HasAccess = true;
                    deptAccess.IsNew = false; // Set to false to prevent subscription handlers from running

                    // Properly add the object to the dataset's internal structure
                    dataset.AddObject(deptAccess);
                    departmentAccessCollection.Add(deptAccess);
                }
            }

            return new ComponentResponse<DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>>(departmentAccessCollection);
        }

        /// <summary>
        /// GetAccessesForModels - Calculates model access based on department selections
        /// 
        /// DEACTIVATED VIA TAB NAVIGATION LOCK:
        /// This method contains the original cascading logic but is effectively deactivated because
        /// tab navigation is locked during edit mode. Users cannot navigate between tabs until they save,
        /// which prevents this method from being called during editing sessions.
        /// 
        /// This approach prevents data integrity issues with pagination while maintaining the original
        /// business logic for when it's safe to execute (post-save scenarios).
        /// 
        /// Original purpose: When user selects departments, automatically calculate which vehicle models
        /// should be accessible and return them for UI display.
        /// </summary>
        public async Task<ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>> GetAccessesForModelsAsync(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                _logger?.LogWarning($"[GetAccessesForModelsAsync] No permission found for level {permissionLevel}. Returning empty collection.");
                var emptyDataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                var emptyCollection = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = emptyDataset };
                return new ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>(emptyCollection);
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var modelAccessCollection = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            // Original cascading logic: For each selected department, find all unique models in that department
            var allModels = new List<(ModelDataObject model, DepartmentDataObject department)>();

            foreach (var deptAccess in personToDepartmentAccesses)
            {
                var models = new List<ModelDataObject>();
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = deptAccess.DepartmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" });

                if (department == null)
                {
                    continue;
                }

                var vehicles = department.Vehicles;

                foreach (var vehicle in vehicles)
                {
                    var model = await vehicle.LoadModelAsync();
                    if (model != null && !models.Exists(x => x.Id == model.Id))
                    {
                        models.Add(model);
                    }
                }

                // Add models with their department context
                foreach (var model in models)
                {
                    // Avoid duplicates across departments
                    if (!allModels.Any(x => x.model.Id == model.Id && x.department.Id == department.Id))
                    {
                        allModels.Add((model, department));
                    }
                }
            }

            // Create access objects for all models
            foreach (var (model, department) in allModels)
            {
                var access = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                access.ModelId = model.Id;
                access.ModelName = $"{department.Name} : {model.Name}";
                access.PermissionId = permission.Id;
                access.DepartmentId = department.Id;
                access.PersonId = personId;
                access.HasAccess = true;
                access.IsNew = false; // Set to false to prevent subscription handlers from running

                // Properly add the object to the dataset's internal structure
                dataset.AddObject(access);
                modelAccessCollection.Add(access);
            }

            return new ComponentResponse<DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>>(modelAccessCollection);
        }

        /// <summary>
        /// GetAccessesForVehicles - Calculates vehicle access based on department and model selections
        /// 
        /// DEACTIVATED VIA TAB NAVIGATION LOCK:
        /// This method contains the original cascading logic but is effectively deactivated because
        /// tab navigation is locked during edit mode. Users cannot navigate between tabs until they save,
        /// which prevents this method from being called during editing sessions.
        /// 
        /// This approach prevents data integrity issues with pagination while maintaining the original
        /// business logic for when it's safe to execute (post-save scenarios).
        /// 
        /// Original purpose: When user selects departments and models, automatically calculate which vehicles
        /// should be accessible and return them for UI display.
        /// </summary>
        public async Task<ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>> GetAccessesForVehiclesAsync(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            Guid personId,
            int permissionLevel,
            Dictionary<string, object> parameters = null)
        {
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { (int)permissionLevel })).SingleOrDefault();

            if (permission == null)
            {
                _logger?.LogWarning($"[GetAccessesForVehiclesAsync] No permission found for level {permissionLevel}. Returning empty collection.");
                var emptyDataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
                var emptyCollection = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = emptyDataset };
                return new ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>(emptyCollection);
            }

            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();
            var vehicleAccessCollection = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            // Original cascading logic: Find all vehicles that match both department and model criteria
            var allVehicles = new List<VehicleDataObject>();

            foreach (var deptAccess in personToDepartmentAccesses)
            {
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = deptAccess.DepartmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" });

                if (department == null)
                {
                    continue;
                }

                var modelIds = personToModelAccesses.Select(a => a.ModelId).ToList();

                foreach (var vehicle in department.Vehicles)
                {
                    if (!allVehicles.Exists(x => x.Id == vehicle.Id) && modelIds.Contains(vehicle.ModelId))
                    {
                        allVehicles.Add(vehicle);
                    }
                }
            }

            // Create access objects for all vehicles
            foreach (var vehicle in allVehicles)
            {
                var access = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                access.VehicleId = vehicle.Id;
                access.HireNo = vehicle.HireNo;
                access.PermissionId = permission.Id;
                access.PersonId = personId;
                access.HasAccess = true;
                access.IsNew = false; // Set to false to prevent subscription handlers from running

                // Properly add the object to the dataset's internal structure
                dataset.AddObject(access);
                vehicleAccessCollection.Add(access);
            }

            return new ComponentResponse<DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>>(vehicleAccessCollection);
        }

        public async Task<ComponentResponse<bool>> UpdateAccessesForPersonAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses,
            Guid personId,
            System.Int32 PermissionLevel,
            System.Boolean cascadeAddPermission,
            Dictionary<string, object> parameters = null)
        {
            return await UpdateAccessesForPersonInternalAsync(personToSiteAccesses, personToDepartmentAccesses, personToModelAccesses, personToVehicleAccesses, personId, cascadeAddPermission, parameters);
        }

        /// <summary>
        /// Internal method that performs the actual user access update processing
        /// This method is called by the Azure Function to process queued messages
        /// </summary>
        public async Task<ComponentResponse<bool>> UpdateAccessesForPersonInternalAsync(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> personToSiteAccesses,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> personToDepartmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> personToModelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> personToVehicleAccesses,
            Guid personId,
            bool cascadeAddPermission,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from queue message)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateAccessesForPersonInternalAsync using permissionLevel: {permissionLevel} for person {personId}");

            // HACK: Define dummy PersonId marker used by client to indicate empty collections
            // TODO: Remove this hack when MapDataSetToJSON can handle empty datasets properly
            var dummyPersonId = new Guid("00000000-0000-0000-0000-000000000001");

            // Log all incoming permission IDs to understand the context
            var allIncomingPermissionIds = new List<Guid>();
            if (personToSiteAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToSiteAccesses.Select(a => a.PermissionId));
            if (personToDepartmentAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToDepartmentAccesses.Select(a => a.PermissionId));
            if (personToModelAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToModelAccesses.Select(a => a.PermissionId));
            if (personToVehicleAccesses?.Any() == true)
                allIncomingPermissionIds.AddRange(personToVehicleAccesses.Select(a => a.PermissionId));

            var incomingPermissionLevels = allIncomingPermissionIds
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .Distinct()
                .OrderBy(level => level)
                .ToList();

            _logger?.LogInformation($"[PERF] Incoming permission levels detected: [{string.Join(", ", incomingPermissionLevels)}]");

            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Get current user ID for tracking
            var currentUserClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = currentUserClaims?.UserId;

            // Track permission level context globally across all access types for better fallback logic
            // Initialize with incoming permission levels to provide strong context hint
            var globalPermissionContext = new HashSet<int>(incomingPermissionLevels);

            // Sites
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingSiteAccesses;
            try
            {
                existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.SiteId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate site access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                    .GroupBy(access => (access.SiteId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate site access records. " +
                    $"Created dictionary with {existingSiteAccesses.Count} unique entries for PersonId: {personId}");
            }

            // HACK: Filter out dummy objects (marker PersonId = 00000000-0000-0000-0000-000000000001) before processing
            // See https://github.com/generative-objects-org/go-meta-lowcode/issues/351
            // TODO: Remove this hack when MapDataSetToJSON can handle empty datasets properly
            var filteredSiteAccesses = personToSiteAccesses?.Where(obj => obj.PersonId != dummyPersonId).ToList();
            var (sitesToAdd, sitesToRemove) = filteredSiteAccesses?.Any() == true
                ? await CategorizeAccessUpdates(new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject>(filteredSiteAccesses), existingSiteAccesses, person)
                : (new List<PersonToSiteVehicleNormalAccessViewDataObject>(), new List<PersonToSiteVehicleNormalAccessViewDataObject>());

            // Capture permission levels from successful site access resolution
            if (personToSiteAccesses?.Any() == true)
            {
                var sitePermissionLevels = personToSiteAccesses
                    .Select(a => a.PermissionId)
                    .Distinct()
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value);

                foreach (var level in sitePermissionLevels)
                {
                    globalPermissionContext.Add(level);
                }
            }

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process site additions with cascading
            foreach (var siteAccess in sitesToAdd)
            {
                var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
                card.SiteVehicleNormalCardAccessItems.Add(newAccess);

                // CASCADE: Add access to all departments, models, and vehicles in this site (only if cascading is enabled)
                if (cascadeAddPermission)
                {
                    var vehicleAccesses = await AddAccessForDepartmentsOfSiteAsync(
                        card,
                        siteAccess.SiteId,
                        siteAccess.PermissionId);

                    if (vehicleAccesses.Any())
                    {
                        updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                    }
                }
            }

            // Process site removals with cascading
            foreach (var siteAccess in sitesToRemove)
            {
                existingSiteAccesses[(siteAccess.SiteId, siteAccess.PermissionId)].IsMarkedForDeletion = true;

                // CASCADE: Remove access from all departments, models, and vehicles in this site
                var vehicleAccesses = await RemoveAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Departments
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingDepartmentAccesses;
            try
            {
                existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.DepartmentId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate department access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                    .GroupBy(access => (access.DepartmentId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate department access records. " +
                    $"Created dictionary with {existingDepartmentAccesses.Count} unique entries for PersonId: {personId}");
            }

            // HACK: Filter out dummy objects (marker PersonId = 00000000-0000-0000-0000-000000000001) before processing
            // See https://github.com/generative-objects-org/go-meta-lowcode/issues/351
            // TODO: Remove this hack when MapDataSetToJSON can handle empty datasets properly
            var filteredDepartmentAccesses = personToDepartmentAccesses?.Where(obj => obj.PersonId != dummyPersonId).ToList();
            var (departmentsToAdd, departmentsToRemove) = filteredDepartmentAccesses?.Any() == true
                ? await CategorizeAccessUpdates(new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject>(filteredDepartmentAccesses), existingDepartmentAccesses, person, globalPermissionContext)
                : (new List<PersonToDepartmentVehicleNormalAccessViewDataObject>(), new List<PersonToDepartmentVehicleNormalAccessViewDataObject>());

            // Capture permission levels from successful department access resolution
            if (personToDepartmentAccesses?.Any() == true)
            {
                var deptPermissionLevels = personToDepartmentAccesses
                    .Select(a => a.PermissionId)
                    .Distinct()
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value);

                foreach (var level in deptPermissionLevels)
                {
                    globalPermissionContext.Add(level);
                }
            }

            // Process department additions with cascading
            foreach (var deptAccess in departmentsToAdd)
            {
                var newAccess = CreateDepartmentAccess(deptAccess.DepartmentId, deptAccess.PermissionId);
                card.DepartmentVehicleNormalCardAccessItems.Add(newAccess);

                // CASCADE: Add access to all models and vehicles in this department (only if cascading is enabled)
                if (cascadeAddPermission)
                {
                    var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                    await AddAccessForModelsOfDepartmentAsync(
                        card,
                        department,
                        deptAccess.PermissionId);

                    var vehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(
                        card,
                        department,
                        deptAccess.PermissionId);

                    if (vehicleAccesses.Any())
                    {
                        updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                    }
                }
            }

            // Process department removals with cascading
            foreach (var deptAccess in departmentsToRemove)
            {
                existingDepartmentAccesses[(deptAccess.DepartmentId, deptAccess.PermissionId)]
                    .IsMarkedForDeletion = true;

                // CASCADE: Remove access from all models and vehicles in this department
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                await RemoveAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                    card,
                    deptAccess.DepartmentId,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Models  
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingModelAccesses;
            try
            {
                existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.ModelId, access.DepartmentId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate model access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                    .GroupBy(access => (access.ModelId, access.DepartmentId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate model access records. " +
                    $"Created dictionary with {existingModelAccesses.Count} unique entries for PersonId: {personId}");
            }

            // HACK: Filter out dummy objects (marker PersonId = 00000000-0000-0000-0000-000000000001) before processing
            // See https://github.com/generative-objects-org/go-meta-lowcode/issues/351
            // TODO: Remove this hack when MapDataSetToJSON can handle empty datasets properly
            var filteredModelAccesses = personToModelAccesses?.Where(obj => obj.PersonId != dummyPersonId).ToList();
            var (modelsToAdd, modelsToRemove) = filteredModelAccesses?.Any() == true
                ? await CategorizeAccessUpdates(new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject>(filteredModelAccesses), existingModelAccesses, person, globalPermissionContext)
                : (new List<PersonToModelVehicleNormalAccessViewDataObject>(), new List<PersonToModelVehicleNormalAccessViewDataObject>());

            // Process model additions with cascading
            foreach (var modelAccess in modelsToAdd)
            {
                var newAccess = CreateModelAccess(
                    modelAccess.ModelId,
                    modelAccess.DepartmentId,
                    modelAccess.PermissionId);

                card.ModelVehicleNormalCardAccessItems.Add(newAccess);

                // CASCADE: Add access to all vehicles of this model (only if cascading is enabled)
                if (cascadeAddPermission)
                {
                    _logger?.LogInformation($"[CASCADE] Adding model access for ModelId: {modelAccess.ModelId} - cascading to vehicles");
                    var vehicleAccesses = await AddAccessForVehiclesOfModelAsync(
                        card,
                        modelAccess.ModelId,
                        modelAccess.PermissionId);

                    _logger?.LogInformation($"[CASCADE] Model access cascade added {vehicleAccesses.Count()} vehicle accesses for ModelId: {modelAccess.ModelId}");
                    if (vehicleAccesses.Any())
                    {
                        updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                    }
                }
            }

            // Process model removals with cascading
            foreach (var modelAccess in modelsToRemove)
            {
                var key = (modelAccess.ModelId, modelAccess.DepartmentId, modelAccess.PermissionId);
                existingModelAccesses[key].IsMarkedForDeletion = true;

                // CASCADE: Remove access from all vehicles of this model
                _logger?.LogInformation($"[CASCADE] Removing model access for ModelId: {modelAccess.ModelId} - cascading to vehicles");
                var vehicleAccesses = await RemoveAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                _logger?.LogInformation($"[CASCADE] Model access cascade removed {vehicleAccesses.Count()} vehicle accesses for ModelId: {modelAccess.ModelId}");
                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Vehicles
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingVehicleAccesses;
            try
            {
                existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                    .ToDictionary(
                        access => (access.VehicleId, access.PermissionId),
                        access => access
                    );
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate vehicle access records detected for PersonId: {personId}. " +
                    $"Using GroupBy to handle duplicates and taking the first occurrence. Key details: {argEx.Message}");

                // Handle duplicates by grouping and taking the first occurrence of each key
                existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                    .GroupBy(access => (access.VehicleId, access.PermissionId))
                    .ToDictionary(
                        group => group.Key,
                        group => group.First()
                    );

                _logger?.LogInformation($"[PERF] Successfully resolved duplicate vehicle access records. " +
                    $"Created dictionary with {existingVehicleAccesses.Count} unique entries for PersonId: {personId}");
            }

            // HACK: Filter out dummy objects (marker PersonId = 00000000-0000-0000-0000-000000000001) before processing
            // See https://github.com/generative-objects-org/go-meta-lowcode/issues/351
            // TODO: Remove this hack when MapDataSetToJSON can handle empty datasets properly
            var filteredVehicleAccesses = personToVehicleAccesses?.Where(obj => obj.PersonId != dummyPersonId).ToList();
            var (vehiclesToAdd, vehiclesToRemove) = filteredVehicleAccesses?.Any() == true
                ? await CategorizeAccessUpdates(new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject>(filteredVehicleAccesses), existingVehicleAccesses, person, globalPermissionContext)
                : (new List<PersonToPerVehicleNormalAccessViewDataObject>(), new List<PersonToPerVehicleNormalAccessViewDataObject>());

            foreach (var vehicleAccess in vehiclesToAdd)
            {
                var newAccess = CreateVehicleAccess(
                    vehicleAccess.VehicleId,
                    vehicleAccess.PermissionId);

                card.PerVehicleNormalCardAccessItems.Add(newAccess);
                updatedPerVehicleAccesses.Add(newAccess);
            }

            foreach (var vehicleAccess in vehiclesToRemove)
            {
                var key = (vehicleAccess.VehicleId, vehicleAccess.PermissionId);
                var existingAccess = existingVehicleAccesses[key];
                existingAccess.IsMarkedForDeletion = true;
                updatedPerVehicleAccesses.Add(existingAccess);
            }

            // Save changes and sync drivers
            try
            {
                await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
                _logger?.LogInformation($"[PERF] Successfully saved person access changes for PersonId: {personId}");
            }
            catch (ArgumentException argEx) when (argEx.Message.Contains("An item with the same key has already been added"))
            {
                _logger?.LogWarning($"[PERF] Duplicate key detected during person access save for PersonId: {personId}. " +
                    $"This indicates data integrity issues with duplicate access records. Skipping access save but continuing with sync operations. " +
                    $"Key details: {argEx.Message}");

                // Continue with sync operations even if save failed due to duplicate keys
            }
            catch (Exception saveEx)
            {
                _logger?.LogError(saveEx, $"[PERF] Unexpected error during person access save for PersonId: {personId}: {saveEx.Message}");
                throw; // Re-throw other exceptions as they indicate more serious issues
            }

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();

                // Use the reusable method to queue sync messages in background
                QueueVehicleSyncMessagesInBackground(
                    vehicleIds,
                    personId,
                    person.CustomerId,
                    currentUserId,
                    permissionLevel,
                    $"UserAccessUpdate_Level{permissionLevel}",
                    correlationId,
                    "UserAccessUpdate",
                    false); // Use IServiceProvider.CreateScope() for this method
            }

            _logger?.LogInformation($"[PERF] Internal user access update processing completed for person {personId}");
            return new ComponentResponse<bool>(true);
        }

        /// <summary>
        /// Serializes access collection to JSON string
        /// </summary>
        /// <typeparam name="T">The type of access data object</typeparam>
        /// <param name="collection">The collection to serialize</param>
        /// <returns>JSON string representation of the collection</returns>
        private string SerializeAccessCollection<T>(DataObjectCollection<T> collection) where T : class, IDataObject
        {
            if (collection == null || collection.Count == 0)
            {
                return string.Empty;
            }

            try
            {
                var items = collection.Cast<T>().ToList();
                // Use Newtonsoft.Json which handles complex object graphs better
                return JsonConvert.SerializeObject(items, new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    TypeNameHandling = TypeNameHandling.Auto,
                    NullValueHandling = NullValueHandling.Ignore
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[PERF] Failed to serialize access collection of type {typeof(T).Name}");
                return string.Empty;
            }
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleDepartmentAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updatedPersonToDepartmentAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleDepartmentAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            // Create lookup for existing department accesses - filter by permission level
            var existingDepartmentAccesses = card.DepartmentVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.DepartmentId, access.PermissionId),
                    access => access
                );

            // Categorize department access updates - simplified without guessing
            var (departmentsToAdd, departmentsToRemove) = await CategorizeAccessUpdatesSimplified(
                updatedPersonToDepartmentAccesses,
                existingDepartmentAccesses,
                person,
                permissionLevel);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var deptAccess in departmentsToAdd)
            {
                // Create and add department access
                var newAccess = CreateDepartmentAccess(deptAccess.DepartmentId, deptAccess.PermissionId);
                card.DepartmentVehicleNormalCardAccessItems.Add(newAccess);

                // Load department data
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                // Process model and vehicle access
                await AddAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Process removals
            foreach (var deptAccess in departmentsToRemove)
            {
                // Mark existing access for deletion
                existingDepartmentAccesses[(deptAccess.DepartmentId, deptAccess.PermissionId)]
                    .IsMarkedForDeletion = true;

                // Load department data
                var department = await GetDepartmentAsync(deptAccess.DepartmentId);

                // Remove model and vehicle access
                await RemoveAccessForModelsOfDepartmentAsync(
                    card,
                    department,
                    deptAccess.PermissionId);

                var vehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                    card,
                    deptAccess.DepartmentId,
                    deptAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle in background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Create a new scope for the background task to avoid disposed service issues
                        using var scope = _serviceProvider.CreateScope();
                        var backgroundVehicleSyncQueueService = scope.ServiceProvider.GetRequiredService<IVehicleSyncQueueService>();

                        for (int i = 0; i < vehicleIds.Count; i++)
                        {
                            var syncMessage = new VehicleSyncMessage
                            {
                                VehicleId = vehicleIds[i],
                                PersonId = personId,
                                CustomerId = person.CustomerId,
                                InitiatedByUserId = currentUserId,
                                CreatedAt = DateTime.UtcNow,
                                CorrelationId = correlationId,
                                Priority = "Normal",
                                SyncReason = $"DepartmentAccessUpdate_Level{permissionLevel}",
                                VehicleSequence = i + 1,
                                TotalVehicles = vehicleIds.Count,
                                PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                            };

                            await backgroundVehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                        }
                        _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (department access update - Level {permissionLevel}) with correlation {correlationId}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"[PERF] Failed to queue vehicle sync messages for person {personId} with correlation {correlationId}: {ex.Message}");
                    }
                });

                // Log immediately that background queuing was initiated
                _logger?.LogInformation($"[SIMPLIFIED] Background queuing initiated for {vehicleIds.Count} vehicle sync messages for person {personId} (department access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleModelAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updateModelAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleModelAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Get current user ID from claims
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            // Create lookup for existing model accesses - filter by permission level
            var existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.ModelId, access.DepartmentId, access.PermissionId),
                    access => access
                );

            // Categorize model access updates - simplified without guessing
            var (modelsToAdd, modelsToRemove) = await CategorizeModelAccessUpdatesSimplified(
                updateModelAccesses,
                existingModelAccesses,
                person,
                permissionLevel);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var modelAccess in modelsToAdd)
            {
                // Create and add model access
                var newAccess = CreateModelAccess(
                    modelAccess.ModelId,
                    modelAccess.DepartmentId,
                    modelAccess.PermissionId);

                card.ModelVehicleNormalCardAccessItems.Add(newAccess);

                // Add vehicle access
                var vehicleAccesses = await AddAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Process removals
            foreach (var modelAccess in modelsToRemove)
            {
                // Mark existing access for deletion
                var key = (modelAccess.ModelId, modelAccess.DepartmentId, modelAccess.PermissionId);
                existingModelAccesses[key].IsMarkedForDeletion = true;

                // Remove vehicle access
                var vehicleAccesses = await RemoveAccessForVehiclesOfModelAsync(
                    card,
                    modelAccess.ModelId,
                    modelAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle in background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Create a new scope for the background task to avoid disposed service issues
                        using var scope = _serviceProvider.CreateScope();
                        var backgroundVehicleSyncQueueService = scope.ServiceProvider.GetRequiredService<IVehicleSyncQueueService>();

                        for (int i = 0; i < vehicleIds.Count; i++)
                        {
                            var syncMessage = new VehicleSyncMessage
                            {
                                VehicleId = vehicleIds[i],
                                PersonId = personId,
                                CustomerId = person.CustomerId,
                                InitiatedByUserId = currentUserId,
                                CreatedAt = DateTime.UtcNow,
                                CorrelationId = correlationId,
                                Priority = "Normal",
                                SyncReason = $"ModelAccessUpdate_Level{permissionLevel}",
                                VehicleSequence = i + 1,
                                TotalVehicles = vehicleIds.Count,
                                PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                            };

                            await backgroundVehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                        }
                        _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (model access update - Level {permissionLevel}) with correlation {correlationId}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"[PERF] Failed to queue vehicle sync messages for person {personId} with correlation {correlationId}: {ex.Message}");
                    }
                });

                // Log immediately that background queuing was initiated
                _logger?.LogInformation($"[SIMPLIFIED] Background queuing initiated for {vehicleIds.Count} vehicle sync messages for person {personId} (model access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehiclePerVehicleAccessesForPersonAsync(
            Guid personId,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updatePerVehicleAccesses,
            Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehiclePerVehicleAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing vehicle accesses - filter by permission level
            var existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.VehicleId, access.PermissionId),
                    access => access
                );

            // Categorize vehicle access updates - simplified without guessing
            var (vehiclesToAdd, vehiclesToRemove) = await CategorizeVehicleAccessUpdatesSimplified(
                updatePerVehicleAccesses,
                existingVehicleAccesses,
                person,
                permissionLevel);

            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            foreach (var vehicleAccess in vehiclesToAdd)
            {
                var newAccess = CreateVehicleAccess(
                    vehicleAccess.VehicleId,
                    vehicleAccess.PermissionId);

                card.PerVehicleNormalCardAccessItems.Add(newAccess);
                updatedPerVehicleAccesses.Add(newAccess);
            }

            // Process removals
            foreach (var vehicleAccess in vehiclesToRemove)
            {
                var key = (vehicleAccess.VehicleId, vehicleAccess.PermissionId);
                var existingAccess = existingVehicleAccesses[key];
                existingAccess.IsMarkedForDeletion = true;
                updatedPerVehicleAccesses.Add(existingAccess);
            }

            // Save changes and sync drivers
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle in background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Create a new scope for the background task to avoid disposed service issues
                        using var scope = _serviceProvider.CreateScope();
                        var backgroundVehicleSyncQueueService = scope.ServiceProvider.GetRequiredService<IVehicleSyncQueueService>();

                        for (int i = 0; i < vehicleIds.Count; i++)
                        {
                            var syncMessage = new VehicleSyncMessage
                            {
                                VehicleId = vehicleIds[i],
                                PersonId = personId,
                                CustomerId = person.CustomerId,
                                InitiatedByUserId = currentUserId,
                                CreatedAt = DateTime.UtcNow,
                                CorrelationId = correlationId,
                                Priority = "Normal",
                                SyncReason = $"VehicleAccessUpdate_Level{permissionLevel}",
                                VehicleSequence = i + 1,
                                TotalVehicles = vehicleIds.Count,
                                PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                            };

                            await backgroundVehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                        }
                        _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (vehicle access update - Level {permissionLevel}) with correlation {correlationId}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"[PERF] Failed to queue vehicle sync messages for person {personId} with correlation {correlationId}: {ex.Message}");
                    }
                });

                // Log immediately that background queuing was initiated
                _logger?.LogInformation($"[SIMPLIFIED] Background queuing initiated for {vehicleIds.Count} vehicle sync messages for person {personId} (vehicle access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        public async Task<ComponentResponse<bool>> UpdateVehicleSiteAccessesForPersonAsync(
                                                    Guid personId,
    DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updatedPersonToSiteAccesses,
Dictionary<string, object> parameters = null)
        {
            // Extract permission level from parameters (passed from JavaScript)
            var permissionLevel = 3; // Default to normal driver
            if (parameters != null && parameters.ContainsKey("permissionLevel"))
            {
                permissionLevel = Convert.ToInt32(parameters["permissionLevel"]);
            }

            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleSiteAccessesForPersonAsync using permissionLevel: {permissionLevel}");

            // Get person data
            var person = await GetPersonAsync(personId);
            var card = person.Driver.Card;

            // Create lookup for existing site accesses - filter by permission level
            var existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToDictionary(
                    access => (access.SiteId, access.PermissionId),
                    access => access
                );

            // Categorize site access updates - simplified without guessing
            var (sitesToAdd, sitesToRemove) = await CategorizeSiteAccessUpdatesSimplified(
                updatedPersonToSiteAccesses,
                existingSiteAccesses,
                person,
                permissionLevel);

            // Process all access changes and collect vehicle updates
            var updatedPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process additions
            var additionsStart = DateTime.UtcNow;
            foreach (var siteAccess in sitesToAdd)
            {
                var newAccess = CreateSiteAccess(siteAccess.SiteId, siteAccess.PermissionId);
                card.SiteVehicleNormalCardAccessItems.Add(newAccess);

                var vehicleAccesses = await AddAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }
            var additionsDuration = (DateTime.UtcNow - additionsStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] Site additions took {additionsDuration}ms for {sitesToAdd.Count} sites");

            // Process removals
            var removalsStart = DateTime.UtcNow;
            foreach (var siteAccess in sitesToRemove)
            {
                // Mark existing access for deletion
                existingSiteAccesses[(siteAccess.SiteId, siteAccess.PermissionId)].IsMarkedForDeletion = true;

                var vehicleAccesses = await RemoveAccessForDepartmentsOfSiteAsync(
                    card,
                    siteAccess.SiteId,
                    siteAccess.PermissionId);

                if (vehicleAccesses.Any())
                {
                    updatedPerVehicleAccesses.AddRange(vehicleAccesses);
                }
            }
            var removalsDuration = (DateTime.UtcNow - removalsStart).TotalMilliseconds;
            _logger?.LogInformation($"[PERF] Site removals took {removalsDuration}ms for {sitesToRemove.Count} sites");

            // Save changes and sync drivers
            var saveStart = DateTime.UtcNow;
            await _dataFacade.PersonDataProvider.SaveAsync(person, skipSecurity: true);
            var saveDuration = (DateTime.UtcNow - saveStart).TotalMilliseconds;
            _logger?.LogInformation($"[SIMPLIFIED] PersonDataProvider.SaveAsync took {saveDuration}ms");
            _logger?.LogInformation($"[SIMPLIFIED] UpdateVehicleSiteAccessesForPersonAsync completed for person {personId}");

            // Queue sync operations instead of running them directly
            var userClaims = await _authentication.GetCurrentUserClaimsAsync();
            var currentUserId = userClaims?.UserId;

            if (currentUserId.HasValue && updatedPerVehicleAccesses.Any())
            {
                var vehicleIds = updatedPerVehicleAccesses.Select(x => x.VehicleId).Distinct().ToList();
                var correlationId = Guid.NewGuid().ToString();
                var vehicleSyncQueueService = _serviceProvider.GetRequiredService<IVehicleSyncQueueService>();

                // Use the specific permission level passed from JavaScript
                var permissionLevels = new List<int> { permissionLevel };

                // Send individual message for each vehicle in background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Create a new scope for the background task to avoid disposed service issues
                        using var scope = _serviceProvider.CreateScope();
                        var backgroundVehicleSyncQueueService = scope.ServiceProvider.GetRequiredService<IVehicleSyncQueueService>();

                        for (int i = 0; i < vehicleIds.Count; i++)
                        {
                            var syncMessage = new VehicleSyncMessage
                            {
                                VehicleId = vehicleIds[i],
                                PersonId = personId,
                                CustomerId = person.CustomerId,
                                InitiatedByUserId = currentUserId,
                                CreatedAt = DateTime.UtcNow,
                                CorrelationId = correlationId,
                                Priority = "Normal",
                                SyncReason = $"SiteAccessUpdate_Level{permissionLevel}",
                                VehicleSequence = i + 1,
                                TotalVehicles = vehicleIds.Count,
                                PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                            };

                            await backgroundVehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                        }
                        _logger?.LogInformation($"[SIMPLIFIED] {vehicleIds.Count} vehicle sync messages queued for person {personId} (site access update - Level {permissionLevel}) with correlation {correlationId}");
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"[PERF] Failed to queue vehicle sync messages for person {personId} with correlation {correlationId}: {ex.Message}");
                    }
                });

                // Log immediately that background queuing was initiated
                _logger?.LogInformation($"[SIMPLIFIED] Background queuing initiated for {vehicleIds.Count} vehicle sync messages for person {personId} (site access update - Level {permissionLevel}) with correlation {correlationId}");
            }

            return new ComponentResponse<bool>(true);
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForDepartmentsOfSiteAsync(
            CardDataObject card,
            Guid siteId,
            Guid permissionId)
        {
            // Get site with departments in a single query
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = siteId;
            site = await _dataFacade.SiteDataProvider.GetAsync(
                site,
                includes: new List<string> { "DepartmentItems" },
                skipSecurity: true
            ) ?? throw new GOServerException($"Site {siteId} not found");

            var allPerVehicleAccesses = new List<PerVehicleNormalCardAccessDataObject>();

            // Process departments sequentially to avoid MARS issues
            foreach (var department in site.DepartmentItems)
            {
                // Create and add department access
                var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
                deptAccess.DepartmentId = department.Id;
                deptAccess.PermissionId = permissionId;
                card.DepartmentVehicleNormalCardAccessItems.Add(deptAccess);

                // Execute operations sequentially
                await AddAccessForModelsOfDepartmentAsync(card, department, permissionId);
                var perVehicleAccesses = await AddAccessForVehiclesOfDepartmentAsync(card, department, permissionId);

                if (perVehicleAccesses.Any())
                {
                    allPerVehicleAccesses.AddRange(perVehicleAccesses);
                }
            }

            return allPerVehicleAccesses;
        }

        private async System.Threading.Tasks.Task AddAccessForModelsOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
        {
            var modelIds = (await department.LoadVehiclesAsync(skipSecurity: true))
                .Where(v => v.DeletedAtUtc == null)
                .Select(v => v.ModelId)
                .ToHashSet();

            var existingAccess = card.ModelVehicleNormalCardAccessItems
               .Where(a => a.DepartmentId == department.Id && a.PermissionId == permissionId)
               .Select(a => a.ModelId)
               .ToHashSet();

            var modelsNeedingAccess = modelIds.Except(existingAccess);

            var accessesToAdd = modelsNeedingAccess
                .Select(modelId =>
                {
                    var access = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
                    access.ModelId = modelId;
                    access.PermissionId = permissionId;
                    access.DepartmentId = department.Id;
                    return access;
                })
                .ToList();

            if (accessesToAdd.Any())
            {
                card.ModelVehicleNormalCardAccessItems.AddRange(accessesToAdd);
            }
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfDepartmentAsync(CardDataObject card, DepartmentDataObject department, Guid permissionId)
        {
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, $"DepartmentId == @0 and DeletedAtUtc == null", new object[] { department.Id }, skipSecurity: true);

            // Create all access items at once using LINQ
            var perVehicleAccessList = vehicles
                .Select(vehicle =>
                {
                    var access = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    access.VehicleId = vehicle.Id;
                    access.PermissionId = permissionId;
                    return access;
                })
                .ToList();

            // Add all items to the card's collection in one batch
            if (perVehicleAccessList.Any())
            {
                card.PerVehicleNormalCardAccessItems.AddRange(perVehicleAccessList);
            }

            return perVehicleAccessList;
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> AddAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
        {
            var departments = (await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true)).Select(a => a.DepartmentId).ToList();
            _logger?.LogInformation($"[CASCADE] Found {departments.Count} departments with access for model cascade");

            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, $"ModelId == @0 and @1.Contains(outerIt.DepartmentId) and DeletedAtUtc == null", new object[] { modelId, departments }, skipSecurity: true);
            _logger?.LogInformation($"[CASCADE] Found {vehicles.Count()} vehicles for ModelId: {modelId}");

            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

            foreach (var vehicle in vehicles)
            {
                var hasAccess = card.PerVehicleNormalCardAccessItems.Where(a => a.VehicleId == vehicle.Id && a.PermissionId == permissionId).Any();
                _logger?.LogInformation($"[CASCADE] Vehicle {vehicle.Id} already has access: {hasAccess}");

                if (!hasAccess)
                {
                    var perVehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
                    perVehicleAccess.VehicleId = vehicle.Id;
                    perVehicleAccess.PermissionId = permissionId;

                    card.PerVehicleNormalCardAccessItems.Add(perVehicleAccess);
                    perVehicleAccessList.Add(perVehicleAccess);
                    _logger?.LogInformation($"[CASCADE] Added vehicle access for VehicleId: {vehicle.Id}");
                }
            }

            _logger?.LogInformation($"[CASCADE] AddAccessForVehiclesOfModelAsync completed - added {perVehicleAccessList.Count} new vehicle accesses");

            return perVehicleAccessList;
        }

        private async Task<(List<PersonToSiteVehicleNormalAccessViewDataObject> ToAdd,
                                                List<PersonToSiteVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person)
        {
            var toAdd = new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToSiteVehicleNormalAccessViewDataObject>();

            // Handle null or empty updates collection (for non-current tabs)
            if (updates?.Any() != true)
            {
                _logger?.LogInformation($"[PERF] Site access - No updates provided, returning empty lists");
                return (toAdd, toRemove);
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid SiteId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.SiteId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                }
            }

            // Load existing view items
            await person.LoadPersonToSiteVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Site access - Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] Site access - HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");

                // Try to determine the intended permission level from context
                // Check if we're dealing with a "clear all normal access" scenario
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Site access - Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Analyze what types of accesses we're trying to remove to determine the target level
                var removalPermissionIds = updates.Where(u => !u.HasAccess).Select(u => u.PermissionId).Distinct().ToList();
                var removalLevels = removalPermissionIds
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .Distinct()
                    .ToList();

                if (removalLevels.Any())
                {
                    // If we can determine levels from the removal updates, use those
                    foreach (var level in removalLevels)
                    {
                        updatedPermissionLevels.Add(level);
                    }
                    _logger?.LogInformation($"[PERF] Site access - Determined target levels from removal updates: [{string.Join(", ", removalLevels)}]");
                }
                else
                {
                    // Fallback: analyze existing accesses to determine most likely target
                    // Check if we have supervisor accesses (Level 1) - if so, we might be updating supervisor access
                    // Otherwise, default to normal access (Level 3)
                    if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                    {
                        // Both supervisor and normal access exist - be more conservative
                        // Only target the level that has the most accesses matching our removal pattern
                        var level1Count = existingLevels.GetValueOrDefault(1, 0);
                        var level3Count = existingLevels.GetValueOrDefault(3, 0);

                        // Default to Level 3 (normal) unless Level 1 significantly dominates
                        if (level1Count > level3Count * 2)
                        {
                            updatedPermissionLevels.Add(1);
                            _logger?.LogInformation($"[PERF] Site access - Detected supervisor access update intent - targeting Level 1 permissions");
                        }
                        else
                        {
                            updatedPermissionLevels.Add(3);
                            _logger?.LogInformation($"[PERF] Site access - Detected normal access update intent - targeting Level 3 permissions");
                        }
                    }
                    else if (existingLevels.ContainsKey(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Site access - Only supervisor access exists - targeting Level 1 permissions");
                    }
                    else if (existingLevels.ContainsKey(3))
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Site access - Only normal access exists - targeting Level 3 permissions");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] Site access - Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Always use partial update mode: only apply changes for items in current page
            // Preserve existing accesses not in the current page to avoid deleting data from other pages
            _logger?.LogInformation($"[PERF] Site access - Partial update mode: preserving existing accesses not in current page");

            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            HashSet<int> globalPermissionContext)
        {
            var toAdd = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates for departments - Updates count: {updates.Count}, Existing accesses count: {existingAccesses.Count}");

            // Log all input departments and their HasAccess status
            foreach (var update in updates)
            {
                _logger?.LogInformation($"[PERF] Input department - DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}");
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid DepartmentId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.DepartmentId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[PERF] Processing department update - DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[PERF] Added to ADD list - DepartmentId: {update.DepartmentId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[PERF] Added to REMOVE list - DepartmentId: {update.DepartmentId}");
                }
            }

            // Load existing view items
            await person.LoadPersonToDepartmentVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Department access - Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] Department access - HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");

                // Try to determine the intended permission level from context
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Department access - Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Analyze what types of accesses we're trying to remove to determine the target level
                var removalPermissionIds = updates.Where(u => !u.HasAccess).Select(u => u.PermissionId).Distinct().ToList();
                var removalLevels = removalPermissionIds
                    .Select(pid => GetPermissionLevelFromId(pid))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .Distinct()
                    .ToList();

                if (removalLevels.Any())
                {
                    // If we can determine levels from the removal updates, use those
                    foreach (var level in removalLevels)
                    {
                        updatedPermissionLevels.Add(level);
                    }
                    _logger?.LogInformation($"[PERF] Department access - Determined target levels from removal updates: [{string.Join(", ", removalLevels)}]");
                }
                else
                {
                    // Fallback: analyze existing accesses to determine most likely target
                    // Check if we have supervisor accesses (Level 1) - if so, we might be updating supervisor access
                    // Otherwise, default to normal access (Level 3)
                    if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                    {
                        // Both supervisor and normal access exist - be more conservative
                        var level1Count = existingLevels.GetValueOrDefault(1, 0);
                        var level3Count = existingLevels.GetValueOrDefault(3, 0);

                        // Default to Level 3 (normal) unless Level 1 significantly dominates
                        if (level1Count > level3Count * 2)
                        {
                            updatedPermissionLevels.Add(1);
                            _logger?.LogInformation($"[PERF] Department access - Detected supervisor access update intent - targeting Level 1 permissions");
                        }
                        else
                        {
                            updatedPermissionLevels.Add(3);
                            _logger?.LogInformation($"[PERF] Department access - Detected normal access update intent - targeting Level 3 permissions");
                        }
                    }
                    else if (existingLevels.ContainsKey(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Department access - Only supervisor access exists - targeting Level 1 permissions");
                    }
                    else if (existingLevels.ContainsKey(3))
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Department access - Only normal access exists - targeting Level 3 permissions");
                    }
                }
            }

            _logger?.LogInformation($"[PERF] Department access - Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // PARTIAL UPDATE: Skip removing existing accesses not in the update  
            // With the new partial save approach, we only process explicitly provided updates
            // and preserve all other existing accesses to maintain data consistency across paginated views
            _logger?.LogInformation($"[PERF] Department access - Using partial update mode, preserving existing accesses not in update");

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates department results - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToModelVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToModelVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            HashSet<int> globalPermissionContext)
        {
            var toAdd = new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToModelVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates for models - Updates count: {updates.Count}, Existing accesses count: {existingAccesses.Count}");

            // Log all input models and their HasAccess status
            foreach (var update in updates)
            {
                _logger?.LogInformation($"[PERF] Input model - ModelId: {update.ModelId}, DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}");
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid ModelId, Guid DepartmentId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.ModelId, update.DepartmentId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[PERF] Processing model update - ModelId: {update.ModelId}, DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[PERF] Added to ADD list - ModelId: {update.ModelId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[PERF] Added to REMOVE list - ModelId: {update.ModelId}");
                }
            }

            // Load existing view items
            await person.LoadPersonToModelVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] Model access - HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");
                _logger?.LogInformation($"[PERF] Model access - Global permission context: [{string.Join(", ", globalPermissionContext)}]");

                // Try to determine the intended permission level from context
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Model access - Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Fallback: analyze existing accesses to determine most likely target
                // Check global permission context first - if site/department resolved to Level 1, prefer Level 1
                if (globalPermissionContext.Contains(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Model access - Using global permission context (Level 1 detected): targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                {
                    // Both supervisor and normal access exist - be more conservative
                    var level1Count = existingLevels.GetValueOrDefault(1, 0);
                    var level3Count = existingLevels.GetValueOrDefault(3, 0);

                    // Default to Level 3 (normal) unless Level 1 significantly dominates OR global context suggests Level 1
                    if (level1Count > level3Count * 2 || globalPermissionContext.Contains(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Model access - Detected supervisor access update intent - targeting Level 1 permissions");
                    }
                    else
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Model access - Detected normal access update intent - targeting Level 3 permissions");
                    }
                }
                else if (existingLevels.ContainsKey(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Model access - Only supervisor access exists - targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(3))
                {
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Model access - Only normal access exists - targeting Level 3 permissions");
                }
                else
                {
                    // Final fallback - default to Level 3 if no context available
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Model access - No existing accesses found, defaulting to Level 3 permissions");
                }
            }

            _logger?.LogInformation($"[PERF] Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // PARTIAL UPDATE: Skip removing existing accesses not in the update
            // With the new partial save approach, we only process explicitly provided updates
            // and preserve all other existing accesses to maintain data consistency across paginated views
            _logger?.LogInformation($"[PERF] Model access - Using partial update mode, preserving existing accesses not in update");

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates model results - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        private async Task<(List<PersonToPerVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToPerVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdates(
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            HashSet<int> globalPermissionContext)
        {
            var toAdd = new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToPerVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates for vehicles - Updates count: {updates.Count}, Existing accesses count: {existingAccesses.Count}");

            // Log all input vehicles and their HasAccess status
            foreach (var update in updates)
            {
                _logger?.LogInformation($"[PERF] Input vehicle - VehicleId: {update.VehicleId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HireNo: {update.HireNo}");
            }

            // Log all existing accesses
            foreach (var existing in existingAccesses)
            {
                _logger?.LogInformation($"[PERF] Existing access - VehicleId: {existing.Key.VehicleId}, PermissionId: {existing.Key.PermissionId}");
            }

            // Create a set of keys from updates for quick lookup
            var updateKeys = new HashSet<(Guid VehicleId, Guid PermissionId)>();
            foreach (var update in updates)
            {
                var key = (update.VehicleId, update.PermissionId);
                updateKeys.Add(key);

                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[PERF] Processing update - VehicleId: {update.VehicleId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[PERF] Added to ADD list - VehicleId: {update.VehicleId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[PERF] Added to REMOVE list - VehicleId: {update.VehicleId}");
                }
            }

            // Load existing view items
            await person.LoadPersonToPerVehicleNormalAccessViewItemsAsync();

            // Only remove existing accesses for permission levels that are being updated
            // This prevents interference between supervisor (Level 1) and regular (Level 3) access
            var updatedPermissionLevels = updates
                .Select(u => u.PermissionId)
                .Distinct()
                .Select(pid => GetPermissionLevelFromId(pid))
                .Where(level => level.HasValue)
                .Select(level => level.Value)
                .ToHashSet();

            _logger?.LogInformation($"[PERF] Updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Special case: if we have updates with HasAccess=false but no permission levels resolved,
            // we need to determine which permission level we're trying to remove
            // Look at the typical permission IDs to determine the intended permission level
            var hasRemovalUpdates = updates.Any(u => !u.HasAccess);

            if (hasRemovalUpdates && !updatedPermissionLevels.Any())
            {
                _logger?.LogInformation($"[PERF] HasRemovalUpdates but no permission levels resolved. Analyzing removal intent...");
                _logger?.LogInformation($"[PERF] Vehicle access - Global permission context: [{string.Join(", ", globalPermissionContext)}]");

                // Try to determine the intended permission level from context
                // Check if we're dealing with a "clear all normal access" scenario
                // by looking at the most common permission levels in existing accesses
                var existingLevels = existingAccesses.Values
                    .Select(access => GetPermissionLevelFromId(access.PermissionId))
                    .Where(level => level.HasValue)
                    .Select(level => level.Value)
                    .GroupBy(level => level)
                    .ToDictionary(g => g.Key, g => g.Count());

                _logger?.LogInformation($"[PERF] Existing permission level distribution: {string.Join(", ", existingLevels.Select(kvp => $"Level {kvp.Key}: {kvp.Value} accesses"))}");

                // Fallback: analyze existing accesses to determine most likely target
                // Check global permission context first - if site/department/model resolved to Level 1, prefer Level 1
                if (globalPermissionContext.Contains(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Vehicle access - Using global permission context (Level 1 detected): targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(1) && existingLevels.ContainsKey(3))
                {
                    // Both supervisor and normal access exist - be more conservative
                    var level1Count = existingLevels.GetValueOrDefault(1, 0);
                    var level3Count = existingLevels.GetValueOrDefault(3, 0);

                    // Default to Level 3 (normal) unless Level 1 significantly dominates OR global context suggests Level 1
                    if (level1Count > level3Count * 2 || globalPermissionContext.Contains(1))
                    {
                        updatedPermissionLevels.Add(1);
                        _logger?.LogInformation($"[PERF] Vehicle access - Detected supervisor access update intent - targeting Level 1 permissions");
                    }
                    else
                    {
                        updatedPermissionLevels.Add(3);
                        _logger?.LogInformation($"[PERF] Vehicle access - Detected normal access update intent - targeting Level 3 permissions");
                    }
                }
                else if (existingLevels.ContainsKey(1))
                {
                    updatedPermissionLevels.Add(1);
                    _logger?.LogInformation($"[PERF] Vehicle access - Only supervisor access exists - targeting Level 1 permissions");
                }
                else if (existingLevels.ContainsKey(3))
                {
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Vehicle access - Only normal access exists - targeting Level 3 permissions");
                }
                else
                {
                    // Final fallback - default to Level 3 if no context available
                    updatedPermissionLevels.Add(3);
                    _logger?.LogInformation($"[PERF] Vehicle access - No existing accesses found, defaulting to Level 3 permissions");
                }
            }

            _logger?.LogInformation($"[PERF] Final updated permission levels: [{string.Join(", ", updatedPermissionLevels)}]");

            // Now that UI sends complete view state (both checked and unchecked records),
            // we no longer need to automatically remove missing records - only process explicit changes
            _logger?.LogInformation($"[SIMPLIFIED] Processing only explicit changes from UI - no auto-removal of missing records");

            _logger?.LogInformation($"[PERF] CategorizeAccessUpdates results - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        private DepartmentVehicleNormalCardAccessDataObject CreateDepartmentAccess(
            Guid departmentId,
            Guid permissionId)
        {
            var deptAccess = _serviceProvider.GetRequiredService<DepartmentVehicleNormalCardAccessDataObject>();
            deptAccess.DepartmentId = departmentId;
            deptAccess.PermissionId = permissionId;
            return deptAccess;
        }

        private ModelVehicleNormalCardAccessDataObject CreateModelAccess(
            Guid modelId,
            Guid departmentId,
            Guid permissionId)
        {
            var modelAccess = _serviceProvider.GetRequiredService<ModelVehicleNormalCardAccessDataObject>();
            modelAccess.ModelId = modelId;
            modelAccess.DepartmentId = departmentId;
            modelAccess.PermissionId = permissionId;
            return modelAccess;
        }

        private SiteVehicleNormalCardAccessDataObject CreateSiteAccess(Guid siteId, Guid permissionId)
        {
            var siteAccess = _serviceProvider.GetRequiredService<SiteVehicleNormalCardAccessDataObject>();
            siteAccess.SiteId = siteId;
            siteAccess.PermissionId = permissionId;
            return siteAccess;
        }
        private PerVehicleNormalCardAccessDataObject CreateVehicleAccess(
            Guid vehicleId,
            Guid permissionId)
        {
            var vehicleAccess = _serviceProvider.GetRequiredService<PerVehicleNormalCardAccessDataObject>();
            vehicleAccess.VehicleId = vehicleId;
            vehicleAccess.PermissionId = permissionId;
            return vehicleAccess;
        }

        private async Task<DepartmentDataObject> GetDepartmentAsync(Guid departmentId)
        {
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = departmentId;
            return await _dataFacade.DepartmentDataProvider.GetAsync(department, skipSecurity: true)
                ?? throw new GOServerException($"Department {departmentId} not found");
        }
        private async System.Threading.Tasks.Task<PersonDataObject> GetPersonAsync(Guid personId)
        {
            var person = _serviceProvider.GetRequiredService<PersonDataObject>();
            person.Id = personId;

            person = await _dataFacade.PersonDataProvider.GetAsync(person, includes: new List<string> {
                "Driver.Card",
                    "Driver.Card.SiteVehicleNormalCardAccessItems",
                    "Driver.Card.DepartmentVehicleNormalCardAccessItems",
                    "Driver.Card.ModelVehicleNormalCardAccessItems",
                    "Driver.Card.PerVehicleNormalCardAccessItems" }, skipSecurity: true);

            if (person == null || person.Driver == null || person.Driver.Card == null)
            {
                throw new GOServerException("Something went wrong. Input should be a person who is a driver and has an access card. One of those is not happening");
            }

            return person;
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForDepartmentsOfSiteAsync(
            CardDataObject card,
            Guid siteId,
            Guid permissionId)
        {
            // Load all department access items first
            var deptAccessItems = await card.LoadDepartmentVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Filter by permissionId first to reduce unnecessary department loads
            var relevantAccesses = deptAccessItems
                .Where(access => access.PermissionId == permissionId)
                .ToList();

            if (!relevantAccesses.Any())
            {
                return Enumerable.Empty<PerVehicleNormalCardAccessDataObject>();
            }

            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>();

            // Process relevant accesses sequentially
            foreach (var deptAccess in relevantAccesses)
            {
                var department = await deptAccess.LoadDepartmentAsync(skipSecurity: true);

                if (department.SiteId == siteId)
                {
                    // Mark for deletion
                    deptAccess.IsMarkedForDeletion = true;

                    // Remove vehicle access first as it depends on department existence
                    var perVehicleAccesses = await RemoveAccessForVehiclesOfDepartmentAsync(
                        card,
                        deptAccess.DepartmentId,
                        permissionId);

                    if (perVehicleAccesses.Any())
                    {
                        perVehicleAccessList.AddRange(perVehicleAccesses);
                    }

                    // Remove model access last as it might reference vehicles
                    await RemoveAccessForModelsOfDepartmentAsync(
                        card,
                        department,
                        permissionId);
                }
            }

            return perVehicleAccessList;
        }
        private async Task RemoveAccessForModelsOfDepartmentAsync(
            CardDataObject card,
            DepartmentDataObject department,
            Guid permissionId)
        {
            // Load data sequentially to avoid MARS issues
            var site = await department.LoadSiteAsync();
            await site.LoadDepartmentItemsAsync(skipSecurity: true);

            // Get current department's models - optimized to filter at database level
            var currentDeptVehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(null, $"DepartmentId == @0 and DeletedAtUtc == null", new object[] { department.Id }, skipSecurity: true);
            var currentDeptModelIds = currentDeptVehicles
                .Select(v => v.ModelId)
                .ToHashSet();

            // For cascade removal, we should remove ALL model accesses for this department
            // regardless of whether the models exist in other departments too
            _logger?.LogInformation($"[CASCADE] Removing model access for department {department.Id} - found {currentDeptModelIds.Count} models");

            // Create lookup for existing access items
            var existingAccesses = card.ModelVehicleNormalCardAccessItems
                .Where(a => a.DepartmentId == department.Id && a.PermissionId == permissionId)
                .ToLookup(a => a.ModelId);

            // Mark items for deletion for ALL models in this department
            var deletedCount = 0;
            foreach (var modelId in currentDeptModelIds)
            {
                var access = existingAccesses[modelId].SingleOrDefault();
                if (access != null)
                {
                    access.IsMarkedForDeletion = true;
                    deletedCount++;
                    _logger?.LogInformation($"[CASCADE] Marked model access for deletion: ModelId={modelId}, DepartmentId={department.Id}, PermissionId={permissionId}");
                }
            }

            _logger?.LogInformation($"[CASCADE] Department model access removal completed - marked {deletedCount} model accesses for deletion out of {currentDeptModelIds.Count} models in department {department.Id}");
        }

        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForVehiclesOfDepartmentAsync(
            CardDataObject card,
            Guid departmentId,
            Guid permissionId)
        {
            // OPTIMIZED: Use bulk deletion instead of individual IsMarkedForDeletion pattern
            try
            {
                return await OptimizedRemoveAccessForVehiclesOfDepartmentAsync(card, departmentId, permissionId);
            }
            catch (Exception ex)
            {
                _logger?.LogWarning($"[PERF] Optimized deletion failed, falling back to original method: {ex.Message}");

                // Fallback to original implementation
                var perVehicleAccessList = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

                var relevantAccesses = perVehicleAccessList
                    .Where(access => access.PermissionId == permissionId)
                    .ToList();

                if (!relevantAccesses.Any())
                {
                    return Enumerable.Empty<PerVehicleNormalCardAccessDataObject>();
                }

                var markedForDeletion = new List<PerVehicleNormalCardAccessDataObject>();

                // Load vehicles sequentially to avoid MARS issues
                foreach (var access in relevantAccesses)
                {
                    var vehicle = await access.LoadVehicleAsync(skipSecurity: true);
                    if (vehicle.DepartmentId == departmentId)
                    {
                        access.IsMarkedForDeletion = true;
                        markedForDeletion.Add(access);
                    }
                }

                return markedForDeletion;
            }
        }

        private async System.Threading.Tasks.Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> RemoveAccessForVehiclesOfModelAsync(CardDataObject card, Guid modelId, Guid permissionId)
        {
            var allAccessItems = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);
            var perVehicleAccessList = new List<PerVehicleNormalCardAccessDataObject>(); // Assuming AccessItem is the type of the items in the collection

            foreach (var accessItem in allAccessItems)
            {
                var vehicle = await accessItem.LoadVehicleAsync(skipSecurity: true);
                if (vehicle.ModelId == modelId && accessItem.PermissionId == permissionId)
                {
                    perVehicleAccessList.Add(accessItem);
                }
            }

            foreach (var perVehicleAccess in perVehicleAccessList)
            {
                perVehicleAccess.IsMarkedForDeletion = true;
            }

            return perVehicleAccessList;
        }

        /// <summary>
        /// Helper method to get permission level from permission ID
        /// Uses a simple cache to avoid repeated database calls
        /// </summary>
        private static readonly Dictionary<Guid, int?> _permissionLevelCache = new Dictionary<Guid, int?>();
        private static readonly object _permissionLevelCacheLock = new object();

        private int? GetPermissionLevelFromId(Guid permissionId)
        {
            lock (_permissionLevelCacheLock)
            {
                if (_permissionLevelCache.TryGetValue(permissionId, out var cachedLevel))
                {
                    return cachedLevel;
                }

                try
                {
                    // Since this is called from async context but we need to avoid deadlock,
                    // we'll use a simple pattern that works within the current async flow
                    var permission = _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { permissionId }, skipSecurity: true).Result?.FirstOrDefault();
                    var level = permission?.LevelName;
                    var levelInt = level.HasValue ? (int)level.Value : (int?)null;
                    _permissionLevelCache[permissionId] = levelInt;
                    return levelInt;
                }
                catch
                {
                    _permissionLevelCache[permissionId] = null;
                    return null;
                }
            }
        }

        private async Task SyncDriversInScopeAsync(List<Guid> vehicleIds, IDataFacade scopedDataFacade, IDeviceTwinHandler scopedDeviceTwinHandler, IServiceProvider serviceProvider, Guid? userId = null)
        {
            _logger?.LogInformation($"[PERF] SyncDriversInScopeAsync called with {vehicleIds.Count} vehicle IDs");
            if (vehicleIds.Any())
            {
                var iotDevices = new List<string>();

                if (userId == null)
                {
                    return;
                }

                foreach (var vehicleId in vehicleIds)
                {
                    try
                    {
                        // Reload vehicle data in the new scope
                        var vehicle = serviceProvider.GetRequiredService<VehicleDataObject>();
                        vehicle.Id = vehicleId;
                        vehicle = await scopedDataFacade.VehicleDataProvider.GetAsync(vehicle, skipSecurity: true);

                        if (vehicle != null)
                        {
                            var module = await vehicle.LoadModuleAsync(skipSecurity: true);
                            if (module != null && !string.IsNullOrEmpty(module.IoTDevice))
                            {
                                if (!iotDevices.Contains(module.IoTDevice))
                                {
                                    iotDevices.Add(module.IoTDevice);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, $"Failed to load vehicle {vehicleId} for sync: {ex.Message}");
                    }
                }

                if (iotDevices.Any())
                {
                    foreach (var iotDevice in iotDevices)
                    {
                        try
                        {
                            _logger?.LogInformation($"Syncing driver to vehicle {iotDevice} for user {userId}");
                            await scopedDeviceTwinHandler.SyncDriverToVehicle(iotDevice, userId.Value);
                        }
                        catch (Exception ex)
                        {
                            _logger?.LogError(ex, $"Failed to sync device {iotDevice}: {ex.Message}");
                        }
                    }
                }
            }
        }

        // SIMPLIFIED: New method that doesn't guess permission levels
        private async Task<(List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToDepartmentVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeAccessUpdatesSimplified(
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid DepartmentId, Guid PermissionId), DepartmentVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeAccessUpdatesSimplified for departments - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToDepartmentVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.DepartmentId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing department - DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - DepartmentId: {update.DepartmentId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - DepartmentId: {update.DepartmentId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToDepartmentVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToDepartmentVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid DepartmentId, Guid PermissionId)>(updates.Select(u => (u.DepartmentId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.DepartmentId, existingItem.PermissionId);

                    // If this existing access is not in our updates, it should be removed
                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                        removeUpdate.DepartmentId = existingItem.DepartmentId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing access - DepartmentId: {existingItem.DepartmentId}, PermissionId: {existingItem.PermissionId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        // SIMPLIFIED: Model access updates without permission guessing
        private async Task<(List<PersonToModelVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToModelVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeModelAccessUpdatesSimplified(
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid ModelId, Guid DepartmentId, Guid PermissionId), ModelVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToModelVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeModelAccessUpdatesSimplified - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToModelVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping model update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.ModelId, update.DepartmentId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing model - ModelId: {update.ModelId}, DepartmentId: {update.DepartmentId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - ModelId: {update.ModelId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - ModelId: {update.ModelId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToModelVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToModelVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid ModelId, Guid DepartmentId, Guid PermissionId)>(updates.Select(u => (u.ModelId, u.DepartmentId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.ModelId, existingItem.DepartmentId, existingItem.PermissionId);

                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                        removeUpdate.ModelId = existingItem.ModelId;
                        removeUpdate.DepartmentId = existingItem.DepartmentId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing model access - ModelId: {existingItem.ModelId}, DepartmentId: {existingItem.DepartmentId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Model access final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        // SIMPLIFIED: Vehicle access updates without permission guessing
        private async Task<(List<PersonToPerVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToPerVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeVehicleAccessUpdatesSimplified(
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid VehicleId, Guid PermissionId), PerVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToPerVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeVehicleAccessUpdatesSimplified - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToPerVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping vehicle update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.VehicleId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing vehicle - VehicleId: {update.VehicleId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - VehicleId: {update.VehicleId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - VehicleId: {update.VehicleId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToPerVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToPerVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid VehicleId, Guid PermissionId)>(updates.Select(u => (u.VehicleId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.VehicleId, existingItem.PermissionId);

                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                        removeUpdate.VehicleId = existingItem.VehicleId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing vehicle access - VehicleId: {existingItem.VehicleId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Vehicle access final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }

        // SIMPLIFIED: Site access updates without permission guessing
        private async Task<(List<PersonToSiteVehicleNormalAccessViewDataObject> ToAdd,
                List<PersonToSiteVehicleNormalAccessViewDataObject> ToRemove)>
        CategorizeSiteAccessUpdatesSimplified(
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> updates,
            Dictionary<(Guid SiteId, Guid PermissionId), SiteVehicleNormalCardAccessDataObject> existingAccesses,
            PersonDataObject person,
            int targetPermissionLevel)
        {
            var toAdd = new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var toRemove = new List<PersonToSiteVehicleNormalAccessViewDataObject>();

            _logger?.LogInformation($"[SIMPLIFIED] CategorizeSiteAccessUpdatesSimplified - Target permission level: {targetPermissionLevel}");

            // Load existing view items
            await person.LoadPersonToSiteVehicleNormalAccessViewItemsAsync();

            // Process updates - only affect the target permission level
            foreach (var update in updates)
            {
                var updatePermissionLevel = GetPermissionLevelFromId(update.PermissionId);
                if (updatePermissionLevel != targetPermissionLevel)
                {
                    _logger?.LogInformation($"[SIMPLIFIED] Skipping site update with permission level {updatePermissionLevel}, target is {targetPermissionLevel}");
                    continue;
                }

                var key = (update.SiteId, update.PermissionId);
                var hasExisting = existingAccesses.ContainsKey(key);

                _logger?.LogInformation($"[SIMPLIFIED] Processing site - SiteId: {update.SiteId}, PermissionId: {update.PermissionId}, HasAccess: {update.HasAccess}, HasExisting: {hasExisting}");

                if (update.HasAccess && !hasExisting)
                {
                    toAdd.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to ADD list - SiteId: {update.SiteId}");
                }
                else if (!update.HasAccess && hasExisting)
                {
                    toRemove.Add(update);
                    _logger?.LogInformation($"[SIMPLIFIED] Added to REMOVE list - SiteId: {update.SiteId}");
                }
            }

            // Only remove existing accesses that match the target permission level
            var existingViewItems = person.PersonToSiteVehicleNormalAccessViewItems?.ToList() ?? new List<PersonToSiteVehicleNormalAccessViewDataObject>();
            var updateKeys = new HashSet<(Guid SiteId, Guid PermissionId)>(updates.Select(u => (u.SiteId, u.PermissionId)));

            foreach (var existingItem in existingViewItems)
            {
                var existingPermissionLevel = GetPermissionLevelFromId(existingItem.PermissionId);
                if (existingPermissionLevel == targetPermissionLevel)
                {
                    var key = (existingItem.SiteId, existingItem.PermissionId);

                    if (!updateKeys.Contains(key))
                    {
                        var removeUpdate = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                        removeUpdate.SiteId = existingItem.SiteId;
                        removeUpdate.PermissionId = existingItem.PermissionId;
                        removeUpdate.HasAccess = false;

                        toRemove.Add(removeUpdate);
                        _logger?.LogInformation($"[SIMPLIFIED] Auto-removing existing site access - SiteId: {existingItem.SiteId}");
                    }
                }
            }

            _logger?.LogInformation($"[SIMPLIFIED] Site access final result - ToAdd: {toAdd.Count}, ToRemove: {toRemove.Count}");
            return (toAdd, toRemove);
        }



        public async Task<ComponentResponse<bool>> SelectAllAndSaveAsync(int tabIndex, bool cascadeSave, int permissionLevel, Guid personId, Dictionary<string, object> parameters = null)
        {
            try
            {
                var person = await GetPersonAsync(personId);
                var card = person.Driver.Card;

                // Get permission ID for the specified level
                var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel }, skipSecurity: true)).SingleOrDefault();
                if (permission == null)
                {
                    _logger?.LogWarning($"[SelectAllAndSaveAsync] No permission found for level {permissionLevel}");
                    return new ComponentResponse<bool>(false);
                }

                // Calculate complete access collections based on tab index
                var (siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses) = await CalculateCompleteAccessesForSelectAllAsync(
                    tabIndex, card, person, permissionLevel, permission.Id);

                // Use the existing internal method to process all access updates
                var result = await UpdateAccessesForPersonInternalAsync(
                    siteAccesses,
                    departmentAccesses,
                    modelAccesses,
                    vehicleAccesses,
                    personId,
                    cascadeSave,
                    new Dictionary<string, object> { { "permissionLevel", permissionLevel } });

                _logger?.LogInformation($"[SelectAllAndSaveAsync] Successfully selected all for tab {tabIndex}, person {personId}");

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[SelectAllAndSaveAsync] Failed to select all for tab {tabIndex}: {ex.Message}");
                throw;
            }
        }

        public async Task<ComponentResponse<bool>> DeselectAllAndSaveAsync(int tabIndex, int permissionLevel, Guid personId, Dictionary<string, object> parameters = null)
        {
            try
            {
                var person = await GetPersonAsync(personId);
                var card = person.Driver.Card;

                // Get permission ID for the specified level
                var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, $"LevelName == @0", new object[] { permissionLevel }, skipSecurity: true)).SingleOrDefault();
                if (permission == null)
                {
                    _logger?.LogWarning($"[DeselectAllAndSaveAsync] No permission found for level {permissionLevel}");
                    return new ComponentResponse<bool>(false);
                }

                // Calculate complete access collections based on tab index (all will be empty for deselection)
                var (siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses) = await CalculateCompleteAccessesForDeselectAllAsync(
                    tabIndex, card, person, permissionLevel, permission.Id);

                // Use the existing internal method to process all access updates
                var result = await UpdateAccessesForPersonInternalAsync(
                    siteAccesses,
                    departmentAccesses,
                    modelAccesses,
                    vehicleAccesses,
                    personId,
                    false, // No cascading for deselection
                    new Dictionary<string, object> { { "permissionLevel", permissionLevel } });

                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, $"[DeselectAllAndSaveAsync] Failed to deselect all for tab {tabIndex}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Calculates complete access collections for Select All operations
        /// </summary>
        private async Task<(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> siteAccesses,
                          DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses,
                          DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
                          DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)>
        CalculateCompleteAccessesForSelectAllAsync(int tabIndex, CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId)
        {
            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            var siteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var modelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var vehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            switch (tabIndex)
            {
                case 0: // Sites - Select all sites and optionally cascade
                    await CalculateAllSiteAccessesAsync(card, person, permissionLevel, permissionId, siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses);
                    break;

                case 1: // Departments - Select all departments from existing sites and optionally cascade
                    await CalculateAllDepartmentAccessesAsync(card, person, permissionLevel, permissionId, departmentAccesses, modelAccesses, vehicleAccesses);
                    break;

                case 2: // Models - Select all models from existing departments and optionally cascade
                    await CalculateAllModelAccessesAsync(card, person, permissionLevel, permissionId, modelAccesses, vehicleAccesses);
                    break;

                case 3: // Vehicles - Select all vehicles from existing models
                    await CalculateAllVehicleAccessesAsync(card, person, permissionLevel, permissionId, vehicleAccesses);
                    break;
            }

            return (siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses);
        }

        /// <summary>
        /// Calculates complete access collections for Deselect All operations
        /// </summary>
        private async Task<(DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> siteAccesses,
                          DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses,
                          DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
                          DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)>
        CalculateCompleteAccessesForDeselectAllAsync(int tabIndex, CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId)
        {
            var dataset = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            var siteAccesses = new DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var departmentAccesses = new DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var modelAccesses = new DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };
            var vehicleAccesses = new DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> { ObjectsDataSet = dataset };

            switch (tabIndex)
            {
                case 0: // Sites - Deselect all sites (cascade will remove departments, models, vehicles)
                    await CalculateEmptySiteAccessesAsync(card, person, permissionLevel, permissionId, siteAccesses);
                    break;

                case 1: // Departments - Deselect all departments (cascade will remove models, vehicles)
                    await CalculateEmptyDepartmentAccessesAsync(card, person, permissionLevel, permissionId, departmentAccesses);
                    break;

                case 2: // Models - Deselect all models (cascade will remove vehicles)
                    await CalculateEmptyModelAccessesAsync(card, person, permissionLevel, permissionId, modelAccesses);
                    break;

                case 3: // Vehicles - Deselect all vehicles
                    await CalculateEmptyVehicleAccessesAsync(card, person, permissionLevel, permissionId, vehicleAccesses);
                    break;
            }

            return (siteAccesses, departmentAccesses, modelAccesses, vehicleAccesses);
        }

        /// <summary>
        /// Calculates all site accesses and optionally cascades to departments, models, and vehicles
        /// </summary>
        private async Task CalculateAllSiteAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> siteAccesses,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
        {
            // Get all available sites for the customer
            var customerId = person.CustomerId;
            var allSites = await _dataFacade.SiteDataProvider.GetCollectionAsync(null, "CustomerId == @0", new object[] { customerId }, skipSecurity: true);

            var existingSiteIds = card.SiteVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.SiteId)
                .ToHashSet();

            foreach (var site in allSites)
            {
                // Skip if site already has access at this permission level
                if (existingSiteIds.Contains(site.Id))
                    continue;

                var siteAccess = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                siteAccess.SiteId = site.Id;
                siteAccess.PermissionId = permissionId;
                siteAccess.PersonId = person.Id;
                siteAccess.HasAccess = true;
                siteAccess.IsNew = false;

                siteAccesses.Add(siteAccess);
            }
        }

        /// <summary>
        /// Calculates all department accesses and optionally cascades to models and vehicles
        /// </summary>
        private async Task CalculateAllDepartmentAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
        {
            // Get all departments from existing site accesses
            var siteIds = card.SiteVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.SiteId)
                .ToList();

            var existingDeptIds = card.DepartmentVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.DepartmentId)
                .ToHashSet();

            foreach (var siteId in siteIds)
            {
                var site = _serviceProvider.GetRequiredService<SiteDataObject>();
                site.Id = siteId;
                site = await _dataFacade.SiteDataProvider.GetAsync(site, includes: new List<string> { "DepartmentItems" }, skipSecurity: true);

                foreach (var department in site.DepartmentItems)
                {
                    if (!existingDeptIds.Contains(department.Id))
                    {
                        var deptAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                        deptAccess.DepartmentId = department.Id;
                        deptAccess.DepartmentName = department.Name;
                        deptAccess.PermissionId = permissionId;
                        deptAccess.PersonId = person.Id;
                        deptAccess.HasAccess = true;
                        deptAccess.IsNew = false;

                        departmentAccesses.Add(deptAccess);
                        existingDeptIds.Add(department.Id);
                    }
                }
            }
        }

        /// <summary>
        /// Calculates all model accesses and optionally cascades to vehicles
        /// </summary>
        private async Task CalculateAllModelAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
        {
            var existingModelDeptCombos = card.ModelVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => (access.DepartmentId, access.ModelId))
                .ToHashSet();

            var departmentIds = card.DepartmentVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.DepartmentId)
                .ToList();

            foreach (var departmentId in departmentIds)
            {
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = departmentId;
                department = await _dataFacade.DepartmentDataProvider.GetAsync(department, includes: new List<string> { "Vehicles" }, skipSecurity: true);

                var modelIds = department.Vehicles.Select(v => v.ModelId).Distinct();
                foreach (var modelId in modelIds)
                {
                    if (!existingModelDeptCombos.Contains((departmentId, modelId)))
                    {
                        var modelAccess = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                        modelAccess.ModelId = modelId;
                        modelAccess.ModelName = await GetModelNameAsync(modelId);
                        modelAccess.DepartmentId = departmentId;
                        modelAccess.PermissionId = permissionId;
                        modelAccess.PersonId = person.Id;
                        modelAccess.HasAccess = true;
                        modelAccess.IsNew = false;

                        modelAccesses.Add(modelAccess);
                        existingModelDeptCombos.Add((departmentId, modelId));
                    }
                }
            }
        }

        /// <summary>
        /// Calculates all vehicle accesses
        /// </summary>
        private async Task CalculateAllVehicleAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
        {
            var existingVehicleIds = card.PerVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.VehicleId)
                .ToHashSet();

            // Get all model IDs from ModelVehicleNormalCardAccessItems
            var modelIds = card.ModelVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.ModelId)
                .ToHashSet();

            // Get all department IDs from DepartmentVehicleNormalCardAccessItems
            var departmentIds = card.DepartmentVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .Select(access => access.DepartmentId)
                .ToHashSet();

            // Get all vehicles under the departments
            var vehiclesUnderDepartments = new List<VehicleDataObject>();
            if (departmentIds.Any())
            {
                var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                    null, 
                    "@0.Contains(outerIt.DepartmentId) and DeletedAtUtc == null", 
                    new object[] { departmentIds.ToList() }, 
                    skipSecurity: true);
                vehiclesUnderDepartments.AddRange(vehicles);
            }

            // For each vehicle, check if its model is in the modelIds
            foreach (var vehicle in vehiclesUnderDepartments)
            {
                // Skip if vehicle already has access
                if (existingVehicleIds.Contains(vehicle.Id))
                    continue;

                // Check if the vehicle's model is in the allowed model IDs
                if (modelIds.Contains(vehicle.ModelId))
                {
                    var vehicleAccess = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                    vehicleAccess.VehicleId = vehicle.Id;
                    vehicleAccess.HireNo = vehicle.HireNo;
                    vehicleAccess.PermissionId = permissionId;
                    vehicleAccess.PersonId = person.Id;
                    vehicleAccess.HasAccess = true;
                    vehicleAccess.IsNew = false;

                    vehicleAccesses.Add(vehicleAccess);
                    existingVehicleIds.Add(vehicle.Id);
                }
            }
        }

        /// <summary>
        /// Calculates empty site accesses (for deselection)
        /// </summary>
        private async Task CalculateEmptySiteAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToSiteVehicleNormalAccessViewDataObject> siteAccesses)
        {
            // For deselection, we need to mark all existing site accesses as HasAccess = false
            var existingSiteAccesses = card.SiteVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToList();

            foreach (var existingAccess in existingSiteAccesses)
            {
                var siteAccess = _serviceProvider.GetRequiredService<PersonToSiteVehicleNormalAccessViewDataObject>();
                siteAccess.SiteId = existingAccess.SiteId;
                siteAccess.PermissionId = existingAccess.PermissionId;
                siteAccess.PersonId = person.Id;
                siteAccess.HasAccess = false;
                siteAccess.IsNew = false;

                siteAccesses.Add(siteAccess);
            }
        }

        /// <summary>
        /// Calculates empty department accesses (for deselection)
        /// </summary>
        private async Task CalculateEmptyDepartmentAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToDepartmentVehicleNormalAccessViewDataObject> departmentAccesses)
        {
            var existingDeptAccesses = card.DepartmentVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToList();

            foreach (var existingAccess in existingDeptAccesses)
            {
                var deptAccess = _serviceProvider.GetRequiredService<PersonToDepartmentVehicleNormalAccessViewDataObject>();
                deptAccess.DepartmentId = existingAccess.DepartmentId;
                deptAccess.DepartmentName = await GetDepartmentNameAsync(existingAccess.DepartmentId);
                deptAccess.PermissionId = existingAccess.PermissionId;
                deptAccess.PersonId = person.Id;
                deptAccess.HasAccess = false;
                deptAccess.IsNew = false;

                departmentAccesses.Add(deptAccess);
            }
        }

        /// <summary>
        /// Calculates empty model accesses (for deselection)
        /// </summary>
        private async Task CalculateEmptyModelAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToModelVehicleNormalAccessViewDataObject> modelAccesses)
        {
            var existingModelAccesses = card.ModelVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToList();

            foreach (var existingAccess in existingModelAccesses)
            {
                var modelAccess = _serviceProvider.GetRequiredService<PersonToModelVehicleNormalAccessViewDataObject>();
                modelAccess.ModelId = existingAccess.ModelId;
                modelAccess.ModelName = await GetModelNameAsync(existingAccess.ModelId);
                modelAccess.DepartmentId = existingAccess.DepartmentId;
                modelAccess.PermissionId = existingAccess.PermissionId;
                modelAccess.PersonId = person.Id;
                modelAccess.HasAccess = false;
                modelAccess.IsNew = false;

                modelAccesses.Add(modelAccess);
            }
        }

        /// <summary>
        /// Calculates empty vehicle accesses (for deselection)
        /// </summary>
        private async Task CalculateEmptyVehicleAccessesAsync(CardDataObject card, PersonDataObject person, int permissionLevel, Guid permissionId,
            DataObjectCollection<PersonToPerVehicleNormalAccessViewDataObject> vehicleAccesses)
        {
            var existingVehicleAccesses = card.PerVehicleNormalCardAccessItems
                .Where(access => GetPermissionLevelFromId(access.PermissionId) == permissionLevel)
                .ToList();

            foreach (var existingAccess in existingVehicleAccesses)
            {
                var vehicleAccess = _serviceProvider.GetRequiredService<PersonToPerVehicleNormalAccessViewDataObject>();
                vehicleAccess.VehicleId = existingAccess.VehicleId;
                vehicleAccess.HireNo = await GetVehicleHireNoAsync(existingAccess.VehicleId);
                vehicleAccess.PermissionId = existingAccess.PermissionId;
                vehicleAccess.PersonId = person.Id;
                vehicleAccess.HasAccess = false;
                vehicleAccess.IsNew = false;

                vehicleAccesses.Add(vehicleAccess);
            }
        }

        // Helper methods for getting names and details
        private async Task<string> GetModelNameAsync(Guid modelId)
        {
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = modelId;
            model = await _dataFacade.ModelDataProvider.GetAsync(model, skipSecurity: true);
            return model?.Name ?? "Unknown Model";
        }

        private async Task<string> GetDepartmentNameAsync(Guid departmentId)
        {
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = departmentId;
            department = await _dataFacade.DepartmentDataProvider.GetAsync(department, skipSecurity: true);
            return department?.Name ?? "Unknown Department";
        }

        private async Task<string> GetVehicleHireNoAsync(Guid vehicleId)
        {
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Id = vehicleId;
            vehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle, skipSecurity: true);
            return vehicle?.HireNo ?? "Unknown Vehicle";
        }

        private void QueueVehicleSyncMessagesInBackground(
            List<Guid> vehicleIds,
            Guid personId,
            Guid customerId,
            Guid? currentUserId,
            int permissionLevel,
            string syncReason,
            string correlationId,
            string methodName,
            bool useServiceScopeFactory = true)
        {
            if (vehicleIds == null || !vehicleIds.Any())
                return;

            // Use the specific permission level passed from the method
            var permissionLevels = new List<int> { permissionLevel };

            // Queue the sync messages in background to avoid blocking the main request
            _ = Task.Run(async () =>
            {
                try
                {
                    // Create a new scope for the background task to avoid disposed service issues
                    using var scope = useServiceScopeFactory
                        ? _serviceScopeFactory.CreateScope()
                        : _serviceProvider.CreateScope();
                    var backgroundVehicleSyncQueueService = scope.ServiceProvider.GetRequiredService<IVehicleSyncQueueService>();

                    for (int i = 0; i < vehicleIds.Count; i++)
                    {
                        var syncMessage = new VehicleSyncMessage
                        {
                            VehicleId = vehicleIds[i],
                            PersonId = personId,
                            CustomerId = customerId,
                            InitiatedByUserId = currentUserId,
                            CreatedAt = DateTime.UtcNow,
                            CorrelationId = correlationId,
                            Priority = "Normal",
                            SyncReason = syncReason,
                            VehicleSequence = i + 1,
                            TotalVehicles = vehicleIds.Count,
                            PermissionLevels = permissionLevels.Any() ? permissionLevels : null
                        };
                        await backgroundVehicleSyncQueueService.SendVehicleSyncMessageAsync(syncMessage);
                    }
                    _logger?.LogInformation($"[{methodName}] {vehicleIds.Count} vehicle sync messages queued for vehicle access operations (Level {permissionLevel}) with correlation {correlationId}");
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, $"[{methodName}] Failed to queue vehicle sync messages for vehicle access operations with correlation {correlationId}: {ex.Message}");
                }
            });

            // Log immediately that background queuing was initiated
            _logger?.LogInformation($"[{methodName}] Background queuing initiated for {vehicleIds.Count} vehicle sync messages for vehicle access operations (Level {permissionLevel}) with correlation {correlationId}");
        }

        /// <summary>
        /// Optimized bulk deletion for PerVehicleNormalCardAccess using direct SQL
        /// Replaces the slow individual IsMarkedForDeletion = true pattern
        /// </summary>
        private async Task BulkDeletePerVehicleAccessAsync(List<Guid> accessIds)
        {
            if (!accessIds.Any()) return;

            var configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            var connectionString = configuration["MainConnectionString"];

            if (string.IsNullOrEmpty(connectionString))
            {
                _logger?.LogWarning("[PERF] MainConnectionString not configured, falling back to individual deletes");
                return; // Fall back to individual deletion
            }

            const int chunkSize = 1000; // SQL Server parameter limit safety
            var totalDeleted = 0;

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        for (int i = 0; i < accessIds.Count; i += chunkSize)
                        {
                            var chunk = accessIds.Skip(i).Take(chunkSize).ToList();

                            var paramPlaceholders = string.Join(",", chunk.Select((id, index) => $"@id{index}"));
                            var sql = $"DELETE FROM [PerVehicleNormalCardAccess] WHERE Id IN ({paramPlaceholders})";

                            using (var command = new SqlCommand(sql, connection, transaction))
                            {
                                for (int j = 0; j < chunk.Count; j++)
                                {
                                    command.Parameters.Add(new SqlParameter($"@id{j}", SqlDbType.UniqueIdentifier) { Value = chunk[j] });
                                }

                                var deletedCount = await command.ExecuteNonQueryAsync();
                                totalDeleted += deletedCount;
                            }
                        }

                        transaction.Commit();
                        _logger?.LogInformation($"[PERF] Bulk deleted {totalDeleted} PerVehicleNormalCardAccess records in {Math.Ceiling((double)accessIds.Count / chunkSize)} SQL operations instead of {accessIds.Count} individual ORM calls");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        _logger?.LogError(ex, $"[PERF] Bulk deletion failed: {ex.Message}");
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// Optimized removal that uses collection manipulation + bulk SQL instead of IsMarkedForDeletion
        /// </summary>
        private async Task<IEnumerable<PerVehicleNormalCardAccessDataObject>> OptimizedRemoveAccessForVehiclesOfDepartmentAsync(
            CardDataObject card,
            Guid departmentId,
            Guid permissionId)
        {
            // Load all vehicle access items in one go
            var perVehicleAccessList = await card.LoadPerVehicleNormalCardAccessItemsAsync(skipSecurity: true);

            // Filter to only relevant items based on permissionId first
            var relevantAccesses = perVehicleAccessList
                .Where(access => access.PermissionId == permissionId)
                .ToList();

            if (!relevantAccesses.Any())
            {
                return Enumerable.Empty<PerVehicleNormalCardAccessDataObject>();
            }

            var accessesToDelete = new List<PerVehicleNormalCardAccessDataObject>();
            var accessIdsToDelete = new List<Guid>();

            // Load vehicles and collect items to delete
            foreach (var access in relevantAccesses)
            {
                var vehicle = await access.LoadVehicleAsync(skipSecurity: true);
                if (vehicle.DepartmentId == departmentId)
                {
                    accessesToDelete.Add(access);
                    accessIdsToDelete.Add(access.Id);
                }
            }

            if (accessesToDelete.Any())
            {
                try
                {
                    // Use bulk SQL deletion instead of individual IsMarkedForDeletion
                    await BulkDeletePerVehicleAccessAsync(accessIdsToDelete);

                    // Remove from collection to reflect the deletion
                    foreach (var access in accessesToDelete)
                    {
                        card.PerVehicleNormalCardAccessItems.Remove(access);
                    }

                    _logger?.LogInformation($"[PERF] Optimized removal of {accessesToDelete.Count} vehicle accesses for department {departmentId}");
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"[PERF] Bulk deletion failed, falling back to individual IsMarkedForDeletion for {accessesToDelete.Count} items: {ex.Message}");

                    // Fallback to original individual marking
                    foreach (var access in accessesToDelete)
                    {
                        access.IsMarkedForDeletion = true;
                    }
                }
            }

            return accessesToDelete;
        }

    }
}
