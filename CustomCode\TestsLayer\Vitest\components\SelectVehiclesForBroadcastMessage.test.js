import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock FleetXQ global object
global.FleetXQ = {
    Web: {
        Model: {
            DataObjects: {
                VehicleBroadcastMessageObjectFactory: {
                    createNew: vi.fn().mockImplementation((dataSet, contextId) => ({
                        Data: {
                            Id: ko.observable('test-id'),
                            VehicleId: ko.observable(null),
                            IsNew: ko.observable(true),
                            IsMarkedForDeletion: ko.observable(false)
                        },
                        setBroadcastMessage: vi.fn()
                    }))
                }
            }
        },
        ViewModels: {
            SelectVehiclesForBroadcastMessageGridViewModelCustom: null
        }
    }
};

// Mock ko (knockout) functionality
const ko = {
    observable: (val) => {
        const obs = function(newVal) {
            if (arguments.length > 0) {
                obs.value = newVal;
                obs.subscribers.forEach(fn => fn(newVal));
            }
            return obs.value;
        };
        obs.value = val;
        obs.subscribers = [];
        obs.subscribe = function(fn) {
            obs.subscribers.push(fn);
            return {
                dispose: () => {
                    const index = obs.subscribers.indexOf(fn);
                    if (index > -1) obs.subscribers.splice(index, 1);
                }
            };
        };
        return obs;
    },
    observableArray: (initial = []) => {
        const obs = ko.observable(initial);
        obs.push = function(item) {
            const current = obs();
            obs([...current, item]);
        };
        obs.remove = function(predicate) {
            const current = obs();
            const filtered = current.filter(item => !predicate(item));
            obs(filtered);
            return current.filter(predicate);
        };
        obs.removeAll = function() {
            obs([]);
        };
        return obs;
    }
};

// Mock Math.uuid
Math.uuid = () => 'test-uuid';

describe('SelectVehiclesForBroadcastMessageGridViewModel', () => {
    let viewModel;
    let customViewModel;

    beforeEach(() => {
        // Create base viewModel with required properties
        viewModel = {
            controller: {
                ObjectsDataSet: {
                    RemoveObject: vi.fn()
                }
            },
            contextId: 'test-context-01',
            VehicleObjectCollection: ko.observableArray([]),
            selectedVehicles: ko.observableArray([]),
            subscriptions: [],
            baseFilterPredicate: '',
            baseFilterParameters: '',
            setGridPageNumber: vi.fn(),
            Rebind: vi.fn(),
            broadcastMessage: ko.observable({ id: 'test-broadcast' })
        };

        // Initialize custom viewModel
        FleetXQ.Web.ViewModels.SelectVehiclesForBroadcastMessageGridViewModelCustom = function(vm) {
            this.viewmodel = vm;
            var self = this;
            this.initialize = function() {
                self.viewmodel.customerId = ko.observable(null);
                self.viewmodel.siteId = ko.observable(null);
                self.viewmodel.departmentId = ko.observable(null);
                self.viewmodel.checkedStates = ko.observableArray([]);
                
                self.viewmodel.updateCheckStates = function() {
                    self.viewmodel.checkedStates.removeAll();
                    self.viewmodel.VehicleObjectCollection().forEach(function(item) {
                        const isSelected = self.viewmodel.selectedVehicles().some(
                            selected => selected.Data.VehicleId() === item.Data.Id()
                        );
                        self.viewmodel.checkedStates.push(ko.observable(isSelected));
                    });
                };

                self.viewmodel.toggleChecked = function(index, event) {
                    event.stopPropagation();
                    var currentState = self.viewmodel.checkedStates()[index]();
                    self.viewmodel.checkedStates()[index](!currentState);

                    const vehicle = self.viewmodel.VehicleObjectCollection()[index];
                    if (!currentState) {
                        // Add to selectedVehicles
                        const newVehicle = FleetXQ.Web.Model.DataObjects.VehicleBroadcastMessageObjectFactory.createNew(
                            self.viewmodel.controller.ObjectsDataSet,
                            self.viewmodel.contextId.slice(0, -2)
                        );
                        newVehicle.Data.VehicleId(vehicle.Data.Id());
                        newVehicle.setBroadcastMessage(self.viewmodel.broadcastMessage);
                        self.viewmodel.selectedVehicles.push(newVehicle);
                    } else {
                        // Remove from selectedVehicles
                        const vehicleId = vehicle.Data.Id();
                        const removed = self.viewmodel.selectedVehicles.remove(item => 
                            item.Data.VehicleId() === vehicleId
                        );
                        if (removed.length && removed[0].Data.IsNew()) {
                            self.viewmodel.controller.ObjectsDataSet.RemoveObject(removed[0]);
                        }
                    }
                };

                self.viewmodel.selectAll = function() {
                    self.viewmodel.VehicleObjectCollection().forEach(function(vehicle, index) {
                        if (!self.viewmodel.checkedStates()[index]()) {
                            self.viewmodel.toggleChecked(index, { stopPropagation: () => {} });
                        }
                    });
                };

                self.viewmodel.deselectAll = function() {
                    self.viewmodel.VehicleObjectCollection().forEach(function(vehicle, index) {
                        if (self.viewmodel.checkedStates()[index]()) {
                            self.viewmodel.toggleChecked(index, { stopPropagation: () => {} });
                        }
                    });
                };

                self.viewmodel.addFilterPredicateAndParameters = function(predicate, parameters) {
                    self.viewmodel.filterPredicate = self.viewmodel.baseFilterPredicate;
                    self.viewmodel.filterParameters = self.viewmodel.baseFilterParameters;

                    if (self.viewmodel.filterPredicate !== '' && self.viewmodel.filterPredicate !== null) {
                        self.viewmodel.filterPredicate += ' && ';
                    }
                    self.viewmodel.filterPredicate += '(' + predicate + ')';

                    if (parameters) {
                        if (self.viewmodel.baseFilterParameters) {
                            var baseParams = JSON.parse(self.viewmodel.baseFilterParameters);
                            self.viewmodel.filterParameters = JSON.stringify(baseParams.concat(parameters));
                        } else {
                            self.viewmodel.filterParameters = JSON.stringify(parameters);
                        }
                    }

                    self.viewmodel.setGridPageNumber(0);
                    self.viewmodel.Rebind(true);
                };
            };
        };

        customViewModel = new FleetXQ.Web.ViewModels.SelectVehiclesForBroadcastMessageGridViewModelCustom(viewModel);
        customViewModel.initialize();
    });

    describe('initialization', () => {
        it('should initialize with empty checkedStates array', () => {
            expect(viewModel.checkedStates()).toEqual([]);
        });

        it('should initialize with empty selectedVehicles array', () => {
            expect(viewModel.selectedVehicles()).toEqual([]);
        });

        it('should initialize customer, site and department IDs as null', () => {
            expect(viewModel.customerId()).toBeNull();
            expect(viewModel.siteId()).toBeNull();
            expect(viewModel.departmentId()).toBeNull();
        });
    });

    describe('toggleChecked', () => {
        beforeEach(() => {
            // Mock vehicle data
            viewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle-1') } }
            ]);
            viewModel.updateCheckStates();
        });

        it('should toggle vehicle selection state', () => {
            const event = { stopPropagation: vi.fn() };
            
            // Initially should be unchecked
            expect(viewModel.checkedStates()[0]()).toBe(false);
            
            // Toggle to checked
            viewModel.toggleChecked(0, event);
            expect(viewModel.checkedStates()[0]()).toBe(true);
            expect(viewModel.selectedVehicles().length).toBe(1);
            
            // Toggle back to unchecked
            viewModel.toggleChecked(0, event);
            expect(viewModel.checkedStates()[0]()).toBe(false);
            expect(viewModel.selectedVehicles().length).toBe(0);
            
            expect(event.stopPropagation).toHaveBeenCalledTimes(2);
        });
    });

    describe('selectAll and deselectAll', () => {
        beforeEach(() => {
            viewModel.VehicleObjectCollection([
                { Data: { Id: ko.observable('vehicle-1') } },
                { Data: { Id: ko.observable('vehicle-2') } }
            ]);
            viewModel.updateCheckStates();
        });

        it('should select all vehicles', () => {
            viewModel.selectAll();
            expect(viewModel.checkedStates().every(state => state())).toBe(true);
            expect(viewModel.selectedVehicles().length).toBe(2);
        });

        it('should deselect all vehicles', () => {
            // First select all
            viewModel.selectAll();
            expect(viewModel.selectedVehicles().length).toBe(2);

            // Then deselect all
            viewModel.deselectAll();
            expect(viewModel.checkedStates().every(state => !state())).toBe(true);
            expect(viewModel.selectedVehicles().length).toBe(0);
        });
    });

    describe('addFilterPredicateAndParameters', () => {
        it('should properly combine filter predicates', () => {
            viewModel.addFilterPredicateAndParameters('test = @0', ['value']);
            expect(viewModel.filterPredicate).toBe('(test = @0)');
            expect(viewModel.filterParameters).toBe('["value"]');
            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });

        it('should handle multiple filter predicates', () => {
            viewModel.baseFilterPredicate = 'base = @0';
            viewModel.baseFilterParameters = '["baseValue"]';
            
            viewModel.addFilterPredicateAndParameters('test = @0', ['value']);
            
            expect(viewModel.filterPredicate).toBe('base = @0 && (test = @0)');
            expect(JSON.parse(viewModel.filterParameters)).toEqual(['baseValue', 'value']);
            expect(viewModel.Rebind).toHaveBeenCalledWith(true);
        });
    });
});
