﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using Microsoft.Extensions.Configuration;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.Data.DataObjects.Custom;
using GenerativeObjects.Practices.ExceptionHandling;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json;
using System.ComponentModel;
using Microsoft.Extensions.DependencyInjection;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using System.Text.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;
using FleetXQ.Data.DataProviders.Database;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// DriverAuthenticationAPI Component
	/// Driver access verification 
	/// </summary>
    public partial class DriverAuthenticationAPI : BaseServerComponent, IDriverAuthenticationAPI
    {
        const string ALERT_UNAUTHORIZE_DRIVER_ACCESS = "Unauthorized Driver Access";
        const string ALERT_UNKNOWN_DRIVER = "Unknown Driver Access Alert";
        private readonly IDataFacade _dataFacade;
        private readonly IModuleValidator _moduleValidator;
        private readonly IDeviceMessageHandler _deviceMessageHandler;
        public DriverAuthenticationAPI(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade, IModuleValidator moduleValidator, IDeviceMessageHandler deviceMessageHandler) : base(provider, configuration, dataFacade)
        {
            _dataFacade = dataFacade;
            _moduleValidator = moduleValidator;
            _deviceMessageHandler = deviceMessageHandler;
        }

        /// <summary>
        /// VerifyDriverAccess Method
        /// Returns string result with acknowledgement if driver was AUTH or DENY 
        /// </summary>
        /// <param name="Message">JSON Message</param>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.String>> VerifyDriverAccessAsync(System.String Message, Dictionary<string, object> parameters = null)
        {

            PayloadDataObject payloadObject = JsonConvert.DeserializeObject<PayloadDataObject>(Message);
            //"payload":“CARD=9a4,64CC6BC4”
            CardPayloadDataObject cardPayloadObject = handlePayload(payloadObject.Payload);

            var module = (await _dataFacade.ModuleDataProvider.GetCollectionAsync(null, "IoTDevice == @0", new object[] { payloadObject.IoTDeviceId })).SingleOrDefault();

            if (module == null)
            {
                throw new GOServerException($"unknow module id {payloadObject.IoTDeviceId}");
            }

            var vehicle = await module.LoadVehicleAsync();
            if (vehicle == null)
            {
                throw new GOServerException($"Module is not assign to any vehicle {payloadObject.IoTDeviceId}");
            }

            var department = await vehicle.LoadDepartmentAsync();
            var site = await department.LoadSiteAsync();
            var model = await vehicle.LoadModelAsync();

            var card = (await _dataFacade.CardDataProvider.GetCollectionAsync(null, "Weigand == @0 and SiteId == @1", new object[] { cardPayloadObject.CardId, site.Id })).SingleOrDefault();
            if (card == null)
            {
                await SendUnAuthorizedDriverAccessAlertAsync(module, card, ALERT_UNKNOWN_DRIVER);
                throw new GOServerException($"unknow card id {cardPayloadObject.CardId}");
            }

            //check if card and driver are active
            bool isDriverAllowed = true;
            string errorMessage = "";
            if (!card.Active)
            {
                isDriverAllowed = false;
                errorMessage = $"Card {cardPayloadObject.CardId} is deactivated";
            }

            var driver = await card.LoadDriverAsync();
            if (driver == null)
            {
                isDriverAllowed = false;
                errorMessage = $"Card {cardPayloadObject.CardId} is not assign to any driver";
                throw new GOServerException(errorMessage);
            }

            if (!driver.Active)
            {
                isDriverAllowed = false;
                errorMessage = $"Driver {(await driver.LoadPersonAsync()).FullName} is not active";
            }

            if (!isDriverAllowed)
            {
                await SendUnAuthorizedDriverAccessAlertAsync(module, card, ALERT_UNAUTHORIZE_DRIVER_ACCESS);
                throw new GOServerException(errorMessage);
            }



            var vehicleNormalAccess = (await _dataFacade.PerVehicleNormalCardAccessDataProvider.GetCollectionAsync(null, "VehicleId == @0 && CardId == @1", new object[] { vehicle.Id, card.Id })).ToList();

            //check if any from the above access has value or count is greater than 0 else throw exception
            if (vehicleNormalAccess == null || vehicleNormalAccess.Count == 0)
            {
                await SendUnAuthorizedDriverAccessAlertAsync(module, card, ALERT_UNAUTHORIZE_DRIVER_ACCESS);
                throw new GOServerException($"Card {cardPayloadObject.CardId} is not assign to vehicle {vehicle.Module.IoTDevice}");

            }

            // get one entry from vehicleNormalAccess
            var vehicleNormalAccessItem = vehicleNormalAccess.FirstOrDefault();
            //check permissionId from vehicleNormalAccessItem if in Permission
            var permission = (await _dataFacade.PermissionDataProvider.GetCollectionAsync(null, "Id == @0", new object[] { vehicleNormalAccessItem.PermissionId })).SingleOrDefault();
            if (permission == null && permission.LevelName != PermissionLevelEnum.NormalDriver && permission.LevelName != PermissionLevelEnum.Master)
            {
                await SendUnAuthorizedDriverAccessAlertAsync(module, card, ALERT_UNAUTHORIZE_DRIVER_ACCESS);
                throw new GOServerException($"Card {cardPayloadObject.CardId} doesn't have the correct permission for {vehicle.Module.IoTDevice}");
            }

            //check license
            var item = await vehicleNormalAccessItem.LoadCardAsync();
            var person = await (await item.LoadDriverAsync()).LoadPersonAsync();
            if (person != null)
            {
                var licenceAllowed = true;
                if (person.LicenseActive)
                {
                    var generalLicence = await (await person.LoadDriverAsync()).LoadGeneralLicenceAsync();

                    // check if ExpiryDate is expired
                    if (generalLicence != null && generalLicence.ExpiryDate < DateTime.Now)
                    {
                        licenceAllowed = false;
                    }
                    // question here... if there is a license per model, should the driver then not be allowed for other models?
                    var modelLicence = await (await person.LoadDriverAsync(skipSecurity: true)).LoadLicensesByModelAsync(skipSecurity: true);
                    foreach (var licence in modelLicence)
                    {
                        if (licence.ModelId == model.Id && licence.ExpiryDate > DateTime.Now)
                        {
                            licenceAllowed = true;
                            break;
                        }
                        else
                        {
                            licenceAllowed = false;
                        }
                    }

                }
                if (item.Active && (await person.LoadDriverAsync(skipSecurity: true)).Active && licenceAllowed)
                {

                    //responsePayload.Payload = "IDAUTH=" + cardPayloadObject.CardId;
                }
                else
                {
                    await SendUnAuthorizedDriverAccessAlertAsync(module, card, ALERT_UNAUTHORIZE_DRIVER_ACCESS);
                    throw new GOServerException($"Card {cardPayloadObject.CardId} doesn't have the license for {vehicle.Module.IoTDevice}");
                }
            }

            return new ComponentResponse<string>(JsonConvert.SerializeObject(new DefaultResponse(200, "Success")));
        }

        private async Task SendUnAuthorizedDriverAccessAlertAsync(ModuleDataObject module, CardDataObject card, string alertType)
        {
            EmailDetail emailDetail = new EmailDetail();
            emailDetail.TimeStamp = DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss") + " - UTC";
            emailDetail.Alert = alertType;
            var person = card == null ? null : (await card.LoadDriverAsync().Result.LoadPersonAsync());
            emailDetail.DriverName = person == null ? "Unknown Driver" : person.FirstName + " " + person.LastName;
            emailDetail.VehicleId = (await module.LoadVehicleAsync()).Id;

            IEmailService emailService = _serviceProvider.GetRequiredService<IEmailService>();
            await emailService.SendEmailAsync(JsonConvert.SerializeObject(emailDetail));
        }

        private CardPayloadDataObject handlePayload(String payloadObj)
        {
            CardPayloadDataObject cardPayload = new();

            string[] parts = payloadObj.Split('=');

            if (parts.Length == 2 && parts[0] == "CARD")
            {
                string[] values = parts[1].Split(',');

                if (values.Length == 2)
                {
                    cardPayload.CardId = values[0];
                    cardPayload.ModuleHexTimeStamp = values[1];
                }
                else
                {
                    Console.WriteLine("Invalid input format.");
                }
            }
            else
            {
                Console.WriteLine("Invalid input format.");
            }

            return cardPayload;
        }
    }

    internal class CardJsonResponse
    {
        public string MessageId { get; set; }
        public string SessionId { get; set; }
        public string Type { get; set; }
        public string Payload { get; set; }
    }
}
