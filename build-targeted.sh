#!/bin/bash

# FleetXQ Targeted Build Script (Bash)
# This script performs a clean build of specific projects in the correct order
# Usage: ./build-targeted.sh [configuration] [run]
#   configuration - Debug or Release (default: Debug)
#   run          - Start the web application after building

set -e  # Exit on any error

# Parse arguments
CONFIGURATION="Debug"
RUN_APP=false

for arg in "$@"; do
    case $arg in
        Debug|Release)
            CONFIGURATION="$arg"
            ;;
        run)
            RUN_APP=true
            ;;
        *)
            echo "Unknown argument: $arg"
            echo "Usage: $0 [Debug|Release] [run]"
            exit 1
            ;;
    esac
done

VERBOSITY="minimal"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
CYAN='\033[0;36m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Helper functions
print_step() {
    echo -e "${CYAN}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

# Main script
echo -e "${CYAN}========================================${NC}"
echo -e "${CYAN}FleetXQ Targeted Build Script${NC}"
if [ "$RUN_APP" = true ]; then
    echo -e "${CYAN}(Build and Run Mode)${NC}"
else
    echo -e "${CYAN}(Build Only Mode)${NC}"
fi
echo -e "${CYAN}Configuration: $CONFIGURATION${NC}"
echo -e "${CYAN}========================================${NC}"
echo ""

# Define paths
SOLUTION_FILE="FleetXQ.sln"
CONSTRUCT_VIEWS_PROJECT="GeneratedCode/ConstructViews/FleetXQ.ConstructViews.csproj"
WEB_APP_PROJECT="GeneratedCode/WebApplicationLayer/FleetXQ.Application.Web.csproj"

# Check if files exist
if [ ! -f "$SOLUTION_FILE" ]; then
    print_error "Solution file not found: $SOLUTION_FILE"
    exit 1
fi

if [ ! -f "$CONSTRUCT_VIEWS_PROJECT" ]; then
    print_error "ConstructViews project not found: $CONSTRUCT_VIEWS_PROJECT"
    exit 1
fi

if [ ! -f "$WEB_APP_PROJECT" ]; then
    print_error "Web Application project not found: $WEB_APP_PROJECT"
    exit 1
fi

# Step 1: Check .NET CLI
print_step "[1/5] Checking .NET CLI availability..."
if ! command -v dotnet &> /dev/null; then
    print_error ".NET CLI is not available. Please install .NET SDK."
    exit 1
fi
DOTNET_VERSION=$(dotnet --version)
print_success ".NET CLI is available (version: $DOTNET_VERSION)"
echo ""

# Step 2: Clean the solution
print_step "[2/5] Cleaning solution..."
if ! dotnet clean "$SOLUTION_FILE" --configuration "$CONFIGURATION" --verbosity "$VERBOSITY"; then
    print_error "Failed to clean solution"
    exit 1
fi
print_success "Solution cleaned successfully."
echo ""

# Step 3: Restore packages
print_step "[3/5] Restoring NuGet packages..."
if ! dotnet restore "$SOLUTION_FILE" --verbosity "$VERBOSITY"; then
    print_error "Failed to restore NuGet packages"
    exit 1
fi
print_success "NuGet packages restored successfully."
echo ""

# Step 4: Build ConstructViews project
print_step "[4/5] Building FleetXQ.ConstructViews project..."
if ! dotnet build "$CONSTRUCT_VIEWS_PROJECT" --configuration "$CONFIGURATION" --no-restore --verbosity "$VERBOSITY"; then
    print_error "Failed to build FleetXQ.ConstructViews project"
    exit 1
fi
print_success "FleetXQ.ConstructViews project built successfully."
echo ""

# Step 5: Build Web Application project
print_step "[5/5] Building FleetXQ.Application.Web project..."
if ! dotnet build "$WEB_APP_PROJECT" --configuration "$CONFIGURATION" --no-restore --verbosity "$VERBOSITY"; then
    print_error "Failed to build FleetXQ.Application.Web project"
    exit 1
fi
print_success "FleetXQ.Application.Web project built successfully."
echo ""

# Success message
echo -e "${GREEN}========================================${NC}"
echo -e "${GREEN}BUILD COMPLETED SUCCESSFULLY!${NC}"
echo -e "${GREEN}========================================${NC}"
echo ""
print_step "Built projects:"
print_step "  1. $CONSTRUCT_VIEWS_PROJECT"
print_step "  2. $WEB_APP_PROJECT"
echo ""

# Step 6: Run the web application if requested
if [ "$RUN_APP" = true ]; then
    print_step "[6/6] Starting web application..."
    print_step "Web application will be available at:"
    print_step "  - https://localhost:5001 (HTTPS)"
    print_step "  - http://localhost:5000 (HTTP)"
    print_warning "Press Ctrl+C to stop the application"
    echo ""

    # Change to the web project directory and run
    WEB_PROJECT_DIR=$(dirname "$WEB_APP_PROJECT")
    cd "$WEB_PROJECT_DIR"
    dotnet run --configuration "$CONFIGURATION" --urls "https://localhost:5001;http://localhost:5000" --no-build
fi

exit 0
