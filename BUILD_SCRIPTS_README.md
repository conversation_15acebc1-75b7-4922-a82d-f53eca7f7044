# FleetXQ Targeted Build Scripts

This directory contains targeted build scripts for the FleetXQ project that perform a clean build of specific projects in the correct order.

## Quick Start

**To build and run the web application in debug mode:**

```powershell
# PowerShell (Recommended)
.\build-targeted.ps1 -Run
```

```cmd
# Windows Command Prompt
build-targeted.cmd run
```

```bash
# Linux/macOS
./build-targeted.sh Debug run
```

## Available Scripts

### 1. Windows Batch Script (`build-targeted.cmd`)
**Usage:**
```cmd
# Build only (Debug configuration)
build-targeted.cmd

# Build and run the web application
build-targeted.cmd run

# Build in Release configuration
build-targeted.cmd release

# Build and run in Release configuration
build-targeted.cmd run release
```

### 2. PowerShell Script (`build-targeted.ps1`) - **RECOMMENDED**
**Usage:**
```powershell
# Default (Debug configuration, build only)
.\build-targeted.ps1

# Build and run the web application
.\build-targeted.ps1 -Run

# Release configuration
.\build-targeted.ps1 -Configuration Release

# Build and run with custom URLs
.\build-targeted.ps1 -Run -Urls "https://localhost:7001;http://localhost:7000"

# Verbose output
.\build-targeted.ps1 -Verbose

# Build, run, and verbose output
.\build-targeted.ps1 -Run -Verbose

# Release with verbose output and run
.\build-targeted.ps1 -Configuration Release -Run -Verbose
```

### 3. Bash Script (`build-targeted.sh`)
**Usage:**
```bash
# Default (Debug configuration, build only)
./build-targeted.sh

# Build and run the web application
./build-targeted.sh Debug run

# Release configuration
./build-targeted.sh Release

# Build and run in Release configuration
./build-targeted.sh Release run
```

## What These Scripts Do

All scripts perform the following steps in order:

1. **Check .NET CLI availability** - Verifies that the .NET SDK is installed
2. **Clean solution** - Runs `dotnet clean` on the entire solution
3. **Restore NuGet packages** - Runs `dotnet restore` to download dependencies
4. **Build ConstructViews project** - Builds `GeneratedCode\ConstructViews\FleetXQ.ConstructViews.csproj`
5. **Build Web Application project** - Builds `GeneratedCode\WebApplicationLayer\FleetXQ.Application.Web.csproj`
6. **Run Web Application** *(PowerShell script only, when `-Run` flag is used)* - Starts the web application in debug mode

## PowerShell Script Additional Features

The PowerShell script (`build-targeted.ps1`) includes additional functionality:

- **`-Run` flag**: After building, automatically starts the web application
- **`-Urls` parameter**: Customize the URLs the web application listens on (default: `https://localhost:5001;http://localhost:5000`)
- **`-Verbose` flag**: Shows more detailed build output
- **`-Configuration` parameter**: Specify build configuration (Debug/Release, default: Debug)

### Running the Web Application

When using the `-Run` flag:
- The web application will start automatically after a successful build
- Default URLs: `https://localhost:5001` (HTTPS) and `http://localhost:5000` (HTTP)
- Press `Ctrl+C` to stop the application
- The script will show you the URLs where the application is available

## Build Order Importance

The build order is critical because:
- The `FleetXQ.ConstructViews` project creates an executable that is used by the Web Application project's post-build event
- The Web Application project has a dependency on the ConstructViews executable being available

## Error Handling

All scripts include comprehensive error handling:
- Exit immediately if any step fails
- Display clear error messages
- Return appropriate exit codes for CI/CD integration

## Requirements

- .NET 7.0 SDK or later
- All project files must be present in their expected locations

## Troubleshooting

If the build fails:
1. Ensure .NET SDK is installed and accessible via command line
2. Check that all project files exist in the expected locations
3. Verify that you have sufficient permissions to build the projects
4. Check the detailed error output for specific issues

## Integration with CI/CD

These scripts are designed to work well with CI/CD pipelines:
- Return proper exit codes (0 for success, 1 for failure)
- Provide clear, parseable output
- Support different build configurations
