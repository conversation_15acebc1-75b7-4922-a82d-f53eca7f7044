﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// AlertHistoryAPI Component
	///  
	/// </summary>
    public partial class AlertHistoryAPI : BaseServerComponent, IAlertHistoryAPI 
    {
		public AlertHistoryAPI(IServiceProvider serviceProvider, IConfiguration configuration, IDataFacade dataFacade) : base(serviceProvider, configuration, dataFacade)
		{
		}

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> AcknowledgeAsync(Guid alertHistoryId, Dictionary<string, object> parameters = null)
        {
            var alertHistory = _serviceProvider.GetRequiredService<AlertHistoryDataObject>();
            alertHistory.Id = alertHistoryId;
            alertHistory = await _dataFacade.AlertHistoryDataProvider.GetAsync(alertHistory, parameters: parameters);
            if (alertHistory == null)
            {
                return new ComponentResponse<bool>(false);
            }

            alertHistory.IsAcknowledged = true;
            await _dataFacade.AlertHistoryDataProvider.SaveAsync(alertHistory, parameters: parameters);

            return new ComponentResponse<bool>(true);
        }

        public async System.Threading.Tasks.Task<ComponentResponse<bool>> ResolveAsync(Guid alertHistoryId, Dictionary<string, object> parameters = null)
        {
            var alertHistory = _serviceProvider.GetRequiredService<AlertHistoryDataObject>();
            alertHistory.Id = alertHistoryId;
            alertHistory = await _dataFacade.AlertHistoryDataProvider.GetAsync(alertHistory, parameters: parameters);
            if (alertHistory == null)
            {
                return new ComponentResponse<bool>(false);
            }

            alertHistory.IsResolved = true;
            await _dataFacade.AlertHistoryDataProvider.SaveAsync(alertHistory, parameters: parameters);

            return new ComponentResponse<bool>(true);
        }
    }
}
