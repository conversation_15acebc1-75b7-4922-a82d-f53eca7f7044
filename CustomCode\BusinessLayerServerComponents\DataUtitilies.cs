﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Tasks;
using GenerativeObjects.Practices.ORMSupportClasses;
using System.Reflection;
using GenerativeObjects.Practices.ExceptionHandling;
using DocumentFormat.OpenXml.Bibliography;
using System.IO;
using System.ComponentModel;
using VDS.RDF;
using FleetXQ.Data.DataProviders.Database;
using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System.Security.AccessControl;

namespace FleetXQ.BusinessLayer.Components.Server
{
    /// <summary>
	/// DataUtitilies Component
	///  
	/// </summary>
    public partial class DataUtitilies : BaseServerComponent, IDataUtitilies
    {
        const int NumberOfSessions = 1000;
        const int batchsize = 100; // create in batches of 100, otherwise too memory intensive
        const string CustomerName = "Demo Customer";
        const int NumberOfDays = 3 * 30; // generate data for last 3 months
        const int MinSessionTimeMinutes = 15;
        const int MaxSessionTimeMinutes = 3 * 60;
        const int HourStartDay = 8;
        const int HourEndDay = 19;
        const int MinImpactValue = 50000;
        const int MaxImpactValue = 400000;
        const int CurrentFSSSBase = 20000;
        const int ProbabilityOneImpactPerSession = 10; // percentage probability to have one impact in a session 
        const int ProbabilityTwoImpactsPerSession = 7;
        const int ProbabilityThreeImpactsPerSession = 5;

        public DataUtitilies(IServiceProvider provider, IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
        {
        }

        /// <summary>
        /// GenerateDemoData Method
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> GenerateDemoDataAsync(string customerName, string timezoneName, Dictionary<string, object> parameters = null)
        {
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { CustomerName })).SingleOrDefault();

            if (customer == null)
            {
                throw new GOServerException($"Customer '{CustomerName}' not found");
            }

            var IoTHubIds1 = new string[] { "test_00000001", "test_00000002", "test_00000003", "test_00000004", "test_00000005", "test_00000006", "test_00000007", "test_00000008", "test_00000009", "test_00000010" };
            var IoTHubIds2 = new string[] { "test_00000011", "test_00000012", "test_00000013", "test_00000014", "test_00000015", "test_00000016", "test_00000017", "test_00000018", "test_00000019", "test_00000020" };
            var IoTHubIds3 = new string[] { "test_00000021", "test_00000022", "test_00000023", "test_00000024", "test_00000025", "test_00000026", "test_00000027", "test_00000028", "test_00000029", "test_00000030" };
            var VehicleHireNos1 = new string[] { "VH1", "VH2", "VH3", "VH4", "VH5", "VH6", "VH7", "VH8", "VH9", "VH10" };
            var VehicleHireNos2 = new string[] { "VH11", "VH12", "VH13", "VH14", "VH15", "VH16", "VH17", "VH18", "VH19", "VH20" };
            var VehicleHireNos3 = new string[] { "VH21", "VH22", "VH23", "VH24", "VH25", "VH26", "VH27", "VH28", "VH29", "VH30" };
            var VehicleSerialNos1 = new string[] { "VS1", "VS2", "VS3", "VS4", "VS5", "VS6", "VS7", "VS8", "VS9", "VS10" };
            var VehicleSerialNos2 = new string[] { "VS11", "VS12", "VS13", "VS14", "VS15", "VS16", "VS17", "VS18", "VS19", "VS20" };
            var VehicleSerialNos3 = new string[] { "VS21", "VS22", "VS23", "VS24", "VS25", "VS26", "VS27", "VS28", "VS29", "VS30" };
            var PersonFirstName1 = new string[] { "John", "Peter", "Paul", "Mark", "Luke", "Matthew", "James", "Jude", "Simon", "Andrew" };
            var PersonFirstName2 = new string[] { "Mary", "Elizabeth", "Anna", "Ruth", "Esther", "Sarah", "Rebecca", "Leah", "Rachel", "Deborah" };
            var PersonFirstName3 = new string[] { "David", "Solomon", "Elijah", "Elisha", "Isaiah", "Jeremiah", "Ezekiel", "Daniel", "Hosea", "Joel" };
            var PersonLastName1 = new string[] { "Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor" };
            var PersonLastName2 = new string[] { "Anderson", "Thomas", "Jackson", "White", "Harris", "Martin", "Thompson", "Garcia", "Martinez", "Robinson" };
            var PersonLastName3 = new string[] { "Clark", "Rodriguez", "Lewis", "Lee", "Walker", "Hall", "Allen", "Young", "Hernandez", "King" };

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.Name = "Chippendale";
            site.Active = true;
            // get Australia/Sydney timezone from Timezone
            var timezone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null, "TimezoneName == @0", new object[] { "Australia/Sydney" })).SingleOrDefault();
            site.CustomerId = customer.Id;
            site.TimezoneId = timezone.Id;
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            var departmentNames1 = new string[] { "Warehouse", "Logistics", "Production" };

            // create 3 departments for each site
            for (int j = 0; j < 3; j++)
            {
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = Guid.NewGuid();
                department.Name = departmentNames1[j];
                department.SiteId = site.Id;
                await _dataFacade.DepartmentDataProvider.SaveAsync(department);

                // get only 3 models
                var Models = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null)).Take(3).ToList();

                // create 10 vehicles for each department
                for (int k = 0; k < 10; k++)
                {
                    var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                    vehicle.Id = Guid.NewGuid();
                    vehicle.CustomerId = customer.Id;
                    vehicle.SiteId = site.Id;
                    vehicle.DepartmentId = department.Id;
                    vehicle.IDLETimer = 300;
                    vehicle.OnHire = true;
                    vehicle.ImpactLockout = true;
                    // set random modelId with index rand 0 to 2
                    vehicle.ModelId = Models[k % 3].Id;
                    if (j == 0)
                    {
                        vehicle.HireNo = VehicleHireNos1[k];
                        vehicle.SerialNo = VehicleSerialNos1[k];
                    }
                    else if (j == 1)
                    {
                        vehicle.HireNo = VehicleHireNos2[k];
                        vehicle.SerialNo = VehicleSerialNos2[k];
                    }
                    else
                    {
                        vehicle.HireNo = VehicleHireNos3[k];
                        vehicle.SerialNo = VehicleSerialNos3[k];
                    }

                    // create a module for the vehicle
                    var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                    module.Id = Guid.NewGuid();
                    module.Calibration = 100;
                    module.CCID = "CCID" + j + k;
                    // set FSSSBASE random from 100000 to 200000 in increment of 10000
                    module.FSSSBase = 100000 + (k * 10000);
                    module.FSSXMulti = 1;
                    if (j == 0)
                        module.IoTDevice = IoTHubIds1[k];
                    else if (j == 1)
                        module.IoTDevice = IoTHubIds2[k];
                    else
                        module.IoTDevice = IoTHubIds3[k];
                    module.IsAllocatedToVehicle = true;
                    await _dataFacade.ModuleDataProvider.SaveAsync(module);

                    vehicle.ModuleId1 = module.Id;
                    await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
                }

                // // create 10 persons for each department
                for (int k = 0; k < 10; k++)
                {
                    var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                    person.Id = Guid.NewGuid();
                    person.CustomerId = customer.Id;
                    person.SiteId = site.Id;
                    person.DepartmentId = department.Id;
                    if (j == 0)
                    {
                        person.FirstName = PersonFirstName1[k];
                        person.LastName = PersonLastName1[k];
                    }
                    else if (j == 1)
                    {
                        person.FirstName = PersonFirstName2[k];
                        person.LastName = PersonLastName2[k];
                    }
                    else
                    {
                        person.FirstName = PersonFirstName3[k];
                        person.LastName = PersonLastName3[k];
                    }
                    person.IsDriver = true;
                    person.IsActiveDriver = true;
                    await _dataFacade.PersonDataProvider.SaveAsync(person);

                    // create a card for the driver of the person
                    // var driver = person.LoadDriver();
                    // var card = _serviceProvider.GetRequiredService<CardDataObject>();
                    // card.Id = Guid.NewGuid();
                    // // Facility Code is random between 1 to 254 in string
                    // card.FacilityCode = (k % 254 + 1).ToString();
                    // // Card Number is random between 100001 to 675899 in string
                    // card.CardNumber = (100000 + k).ToString();
                    // card.Active = true;
                    // card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
                    // card.Type = card.Type.AsEnumerable().First(x => x.ToString() == "CardID");
                    // driver.CardDetailsId = card.Id;
                    // _dataFacade.DriverDataProvider.Save(driver);
                    // // save it
                    // _dataFacade.CardDataProvider.Save(card);

                }


            }

            // get all departments
            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null);
            // get all models
            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(null);

            // create DepartmentChecklist for each department and model
            foreach (var department in departments)
            {
                foreach (var model in models)
                {
                    var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                    departmentChecklist.Id = Guid.NewGuid();
                    departmentChecklist.ModelId = model.Id;
                    departmentChecklist.DepartmentId = department.Id;
                    await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);
                }
            }

            // get all DepartmentChecklist
            var departmentChecklists = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync()).ToArray();

            var questions = new string[] { "Do the brakes work properly ?", "Is the steering operating correctly?" };
            // create 2 PreOperationalChecklist for each DepartmentChecklist
            foreach (var departmentChecklist in departmentChecklists)
            {
                for (int i = 0; i < 2; i++)
                {
                    var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                    preOperationalChecklist.Id = Guid.NewGuid();
                    preOperationalChecklist.SiteChecklistId = departmentChecklist.Id;
                    preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
                    preOperationalChecklist.Question = questions[i];
                    preOperationalChecklist.ExpectedAnswer = true;
                    preOperationalChecklist.Critical = true;
                    // order is i + 1 as short
                    preOperationalChecklist.Order = (short)(i + 1);
                    await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist);
                }
            }

            // get all vehicles
            var vehicles = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null)).ToArray();
            var IOFieldIgnition = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "0" })).SingleOrDefault();
            var IOFieldSEAT = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "SEAT" })).SingleOrDefault();
            var IOFieldHYDR = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "HYDL" })).SingleOrDefault();
            var IOFieldTRACK = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "TRACK" })).SingleOrDefault();
            // get drivers where customer Name is Demo Customer
            var drivers = (await _dataFacade.DriverDataProvider.GetCollectionAsync(null, "Person.Customer.CompanyName == @0", new object[] { CustomerName })).ToArray();
            // for each vehicle create 2 sessions each with 1 impact and 4 sessiondetails and 1 ChecklistResult and 2 ChecklistDetail
            foreach (var vehicle in vehicles)
            {
                var vehicleIndex = Array.IndexOf(vehicles, vehicle);
                for (int i = 0; i < 2; i++)
                {
                    var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                    session.Id = Guid.NewGuid();
                    session.VehicleId = vehicle.Id;
                    // get random drivers index from 0 to 25
                    var randomDriverIndex = new Random().Next(0, 25);
                    session.DriverId = drivers[randomDriverIndex].Id;
                    var random1To30 = new Random().Next(1, 30);
                    var random1To24 = new Random().Next(1, 24);
                    // session StartTime is random from 1 to 30 days ago

                    session.StartTime = DateTime.Now.AddDays(random1To30).AddHours(random1To24);
                    // sessionEndTime is 1 hour after StartTime
                    session.EndTime = session.StartTime.AddHours(1);
                    session.isVOR = false;

                    await _dataFacade.SessionDataProvider.SaveAsync(session);
             
                    var sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldIgnition.Id;
                    // Usage is random from 2500 to 2800 as string
                    sessionDetail.Usage = new Random().Next(2500, 2800).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldSEAT.Id;
                    // Usage is random from 1500 to 2400 as string 
                    sessionDetail.Usage = new Random().Next(1500, 2400).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldHYDR.Id;
                    // Usage is random from 700 to 1300 as string
                    sessionDetail.Usage = new Random().Next(700, 1300).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldTRACK.Id;
                    // Usage is random from 500 to 1400 as string
                    sessionDetail.Usage = new Random().Next(500, 1400).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
                    checklistResult.Id = Guid.NewGuid();
                    checklistResult.SessionId1 = session.Id;
                    // StartTime is 15 minutes after session start time
                    checklistResult.StartTime = session.StartTime.AddMinutes(15);
                    // EndTime is 17 minutes after session start time if vehicle index in vehicles is divisible by 10 else null
                    checklistResult.EndTime = vehicleIndex % 10 == 0 ? null : session.StartTime.AddMinutes(17);
                    await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

                    // get DepartmentChecklist for the vehicle
                    var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "DepartmentId == @0 and ModelId == @1", new object[] { vehicle.DepartmentId, vehicle.ModelId })).SingleOrDefault();

                    var preopchecklists = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, "SiteChecklistId == @0", new object[] { departmentChecklist.Id })).ToArray();
                    foreach (var preopchecklist in preopchecklists)
                    {
                        var checklistDetail = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
                        checklistDetail.Id = Guid.NewGuid();
                        checklistDetail.ChecklistResultId = checklistResult.Id;
                        checklistDetail.PreOperationalChecklistId = preopchecklist.Id;
                        // Answer is false if vehicle index in vehicles is divisible by 5 else true 
                        checklistDetail.Answer = vehicleIndex % 5 == 0 ? false : true;
                        await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetail);
                    }

                    var impact = _serviceProvider.GetRequiredService<ImpactDataObject>();
                    impact.Id = Guid.NewGuid();
                    impact.SessionId = session.Id;
                    // ImpactDateTime is 10 to 40 minutes after session start time
                    impact.ImpactDateTime = session.StartTime.AddMinutes(new Random().Next(10, 40));
                    // Threshold is the FSSSBase of the module
                    impact.Threshold = (await vehicle.LoadModuleAsync()).FSSSBase;
                    // ImpactValue is random from 200000 to 3000000
                    impact.ShockValue = new Random().Next(200000, 3000000);
                    await _dataFacade.ImpactDataProvider.SaveAsync(impact);
                }
            }
            return new ComponentResponse<System.Boolean>(true);
        }

        /// <summary>
        /// GenerateDemoData Method for C&B
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> GenerateDemoDataCBAsyncAsync(Dictionary<string, object> parameters = null)
        {
            var companyName = "C&B Equipment Demo";
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { companyName })).SingleOrDefault();

            if (customer == null)
            {
                throw new GOServerException($"Customer '{CustomerName}' not found");
            }

            var IoTHubIds1 = new string[] { "new_00000001", "new_00000002", "new_00000003", "new_00000004", "new_00000005", "new_00000006", "new_00000007", "new_00000008", "new_00000009", "new_00000010" };
            var IoTHubIds2 = new string[] { "new_00000011", "new_00000012", "new_00000013", "new_00000014", "new_00000015", "new_00000016", "new_00000017", "new_00000018", "new_00000019", "new_00000020" };
            var IoTHubIds3 = new string[] { "new_00000021", "new_00000022", "new_00000023", "new_00000024", "new_00000025", "new_00000026", "new_00000027", "new_00000028", "new_00000029", "new_00000030" };
            var VehicleHireNos1 = new string[] { "VH101", "VH102", "VH103", "VH104", "VH105", "VH106", "VH107", "VH108", "VH109", "VH110" };
            var VehicleHireNos2 = new string[] { "VH111", "VH112", "VH113", "VH114", "VH115", "VH116", "VH117", "VH118", "VH119", "VH120" };
            var VehicleHireNos3 = new string[] { "VH121", "VH122", "VH123", "VH124", "VH125", "VH126", "VH127", "VH128", "VH129", "VH130" };
            var VehicleSerialNos1 = new string[] { "VS101", "VS102", "VS103", "VS104", "VS105", "VS106", "VS107", "VS108", "VS109", "VS110" };
            var VehicleSerialNos2 = new string[] { "VS111", "VS112", "VS113", "VS114", "VS115", "VS116", "VS117", "VS118", "VS119", "VS120" };
            var VehicleSerialNos3 = new string[] { "VS121", "VS122", "VS123", "VS124", "VS125", "VS126", "VS127", "VS128", "VS129", "VS130" };
            var PersonFirstName1 = new string[] { "Alice", "Bob", "Charlie", "Daisy", "Ethan", "Fiona", "George", "Hannah", "Ivan", "Jenny" };
            var PersonFirstName2 = new string[] { "Kara", "Liam", "Mona", "Nathan", "Olivia", "Peter", "Quincy", "Rachel", "Steve", "Tina" };
            var PersonFirstName3 = new string[] { "Ursula", "Victor", "Wendy", "Xander", "Yvonne", "Zack", "Amy", "Brian", "Cathy", "Dennis" };
            var PersonLastName1 = new string[] { "Adams", "Baker", "Clark", "Dixon", "Evans", "Frank", "Green", "Harris", "Ivy", "Johnson" };
            var PersonLastName2 = new string[] { "King", "Lee", "Moore", "Nash", "Owens", "Perry", "Quinn", "Reed", "Stone", "Turner" };
            var PersonLastName3 = new string[] { "Upton", "Vance", "Wright", "Xenon", "Young", "Zimmer", "Allen", "Brown", "Carter", "Davis" };

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.Name = "Test Main";
            site.Active = true;
            // get Australia/Sydney timezone from Timezone
            var timezone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null, "TimezoneName == @0", new object[] { "Australia/Sydney" })).SingleOrDefault();
            site.CustomerId = customer.Id;
            site.TimezoneId = timezone.Id;
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            var departmentNames1 = new string[] { "Warehouse", "Logistics", "Production" };

            // create 3 departments for each site
            for (int j = 0; j < 3; j++)
            {
                var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
                department.Id = Guid.NewGuid();
                department.Name = departmentNames1[j];
                department.SiteId = site.Id;
                await _dataFacade.DepartmentDataProvider.SaveAsync(department);

                // get only 3 models
                var Models = (await _dataFacade.ModelDataProvider.GetCollectionAsync(null)).Take(3).ToList();

                // create 10 vehicles for each department
                for (int k = 0; k < 10; k++)
                {
                    var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
                    vehicle.Id = Guid.NewGuid();
                    vehicle.CustomerId = customer.Id;
                    vehicle.SiteId = site.Id;
                    vehicle.DepartmentId = department.Id;
                    vehicle.IDLETimer = 300;
                    vehicle.OnHire = true;
                    vehicle.ImpactLockout = true;
                    // set random modelId with index rand 0 to 2
                    vehicle.ModelId = Models[k % 3].Id;
                    if (j == 0)
                    {
                        vehicle.HireNo = VehicleHireNos1[k];
                        vehicle.SerialNo = VehicleSerialNos1[k];
                    }
                    else if (j == 1)
                    {
                        vehicle.HireNo = VehicleHireNos2[k];
                        vehicle.SerialNo = VehicleSerialNos2[k];
                    }
                    else
                    {
                        vehicle.HireNo = VehicleHireNos3[k];
                        vehicle.SerialNo = VehicleSerialNos3[k];
                    }

                    // create a module for the vehicle
                    var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
                    module.Id = Guid.NewGuid();
                    module.Calibration = 100;
                    module.CCID = "CCID" + j + k;
                    // set FSSSBASE random from 100000 to 200000 in increment of 10000
                    module.FSSSBase = 100000 + (k * 10000);
                    module.FSSXMulti = 1;
                    if (j == 0)
                        module.IoTDevice = IoTHubIds1[k];
                    else if (j == 1)
                        module.IoTDevice = IoTHubIds2[k];
                    else
                        module.IoTDevice = IoTHubIds3[k];
                    module.IsAllocatedToVehicle = true;
                    await _dataFacade.ModuleDataProvider.SaveAsync(module);

                    vehicle.ModuleId1 = module.Id;
                    await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);
                }

                // // create 10 persons for each department
                for (int k = 0; k < 10; k++)
                {
                    var person = _serviceProvider.GetRequiredService<PersonDataObject>();
                    person.Id = Guid.NewGuid();
                    person.CustomerId = customer.Id;
                    person.SiteId = site.Id;
                    person.DepartmentId = department.Id;
                    if (j == 0)
                    {
                        person.FirstName = PersonFirstName1[k];
                        person.LastName = PersonLastName1[k];
                    }
                    else if (j == 1)
                    {
                        person.FirstName = PersonFirstName2[k];
                        person.LastName = PersonLastName2[k];
                    }
                    else
                    {
                        person.FirstName = PersonFirstName3[k];
                        person.LastName = PersonLastName3[k];
                    }
                    person.IsDriver = true;
                    person.IsActiveDriver = true;
                    await _dataFacade.PersonDataProvider.SaveAsync(person);

                    // create a card for the driver of the person
                    // var driver = person.LoadDriver();
                    // var card = _serviceProvider.GetRequiredService<CardDataObject>();
                    // card.Id = Guid.NewGuid();
                    // // Facility Code is random between 1 to 254 in string
                    // card.FacilityCode = (k % 254 + 1).ToString();
                    // // Card Number is random between 100001 to 675899 in string
                    // card.CardNumber = (100000 + k).ToString();
                    // card.Active = true;
                    // card.KeypadReader = card.KeypadReader.AsEnumerable().First(x => x.ToString() == "Rosslare");
                    // card.Type = card.Type.AsEnumerable().First(x => x.ToString() == "CardID");
                    // driver.CardDetailsId = card.Id;
                    // _dataFacade.DriverDataProvider.Save(driver);
                    // // save it
                    // _dataFacade.CardDataProvider.Save(card);

                }
            }

            // get all departments
            var departments = await _dataFacade.DepartmentDataProvider.GetCollectionAsync(null);
            // get all models
            var models = await _dataFacade.ModelDataProvider.GetCollectionAsync(null);

            // create DepartmentChecklist for each department and model
            foreach (var department in departments)
            {
                foreach (var model in models)
                {
                    var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
                    departmentChecklist.Id = Guid.NewGuid();
                    departmentChecklist.ModelId = model.Id;
                    departmentChecklist.DepartmentId = department.Id;
                    await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);
                }
            }

            // get all DepartmentChecklist
            var departmentChecklists = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync()).ToArray();

            var questions = new string[] { "Do the brakes work properly ?", "Is the steering operating correctly?" };
            // create 2 PreOperationalChecklist for each DepartmentChecklist
            foreach (var departmentChecklist in departmentChecklists)
            {
                for (int i = 0; i < 2; i++)
                {
                    var preOperationalChecklist = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
                    preOperationalChecklist.Id = Guid.NewGuid();
                    preOperationalChecklist.SiteChecklistId = departmentChecklist.Id;
                    preOperationalChecklist.AnswerType = preOperationalChecklist.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
                    preOperationalChecklist.Question = questions[i];
                    preOperationalChecklist.ExpectedAnswer = true;
                    preOperationalChecklist.Critical = true;
                    // order is i + 1 as short
                    preOperationalChecklist.Order = (short)(i + 1);
                    await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preOperationalChecklist);
                }
            }

            // get all vehicles
            var vehicles = (await _dataFacade.VehicleDataProvider.GetCollectionAsync(null)).ToArray();
            var IOFieldIgnition = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "0" })).SingleOrDefault();
            var IOFieldSEAT = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "SEAT" })).SingleOrDefault();
            var IOFieldHYDR = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "HYDL" })).SingleOrDefault();
            var IOFieldTRACK = (await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null, "Name == @0", new object[] { "TRACK" })).SingleOrDefault();
            // get drivers where customer Name is Demo Customer
            var drivers = (await _dataFacade.DriverDataProvider.GetCollectionAsync(null, "Person.Customer.CompanyName == @0", new object[] { CustomerName })).ToArray();
            // for each vehicle create 2 sessions each with 1 impact and 4 sessiondetails and 1 ChecklistResult and 2 ChecklistDetail
            foreach (var vehicle in vehicles)
            {
                var vehicleIndex = Array.IndexOf(vehicles, vehicle);
                for (int i = 0; i < 2; i++)
                {
                    var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                    session.Id = Guid.NewGuid();
                    session.VehicleId = vehicle.Id;
                    // get random drivers index from 0 to 25
                    var randomDriverIndex = new Random().Next(0, 25);
                    session.DriverId = drivers[randomDriverIndex].Id;
                    var random1To30 = new Random().Next(1, 30);
                    var random1To24 = new Random().Next(1, 24);
                    // session StartTime is random from 1 to 30 days ago

                    session.StartTime = DateTime.Now.AddDays(random1To30).AddHours(random1To24);
                    // sessionEndTime is 1 hour after StartTime
                    session.EndTime = session.StartTime.AddHours(1);
                    session.isVOR = false;

                    await _dataFacade.SessionDataProvider.SaveAsync(session);
             
                    var sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldIgnition.Id;
                    // Usage is random from 2500 to 2800 as string
                    sessionDetail.Usage = new Random().Next(2500, 2800).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldSEAT.Id;
                    // Usage is random from 1500 to 2400 as string 
                    sessionDetail.Usage = new Random().Next(1500, 2400).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldHYDR.Id;
                    // Usage is random from 700 to 1300 as string
                    sessionDetail.Usage = new Random().Next(700, 1300).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    sessionDetail = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetail.Id = Guid.NewGuid();
                    sessionDetail.SessionId = session.Id;
                    sessionDetail.IOFIELDId = IOFieldTRACK.Id;
                    // Usage is random from 500 to 1400 as string
                    sessionDetail.Usage = new Random().Next(500, 1400).ToString();
                    await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessionDetail);

                    var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
                    checklistResult.Id = Guid.NewGuid();
                    checklistResult.SessionId1 = session.Id;
                    // StartTime is 15 minutes after session start time
                    checklistResult.StartTime = session.StartTime.AddMinutes(15);
                    // EndTime is 17 minutes after session start time if vehicle index in vehicles is divisible by 10 else null
                    checklistResult.EndTime = vehicleIndex % 10 == 0 ? null : session.StartTime.AddMinutes(17);
                    await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResult);

                    // get DepartmentChecklist for the vehicle
                    var departmentChecklist = (await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null, "DepartmentId == @0 and ModelId == @1", new object[] { vehicle.DepartmentId, vehicle.ModelId })).SingleOrDefault();

                    var preopchecklists = (await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null, "SiteChecklistId == @0", new object[] { departmentChecklist.Id })).ToArray();
                    foreach (var preopchecklist in preopchecklists)
                    {
                        var checklistDetail = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
                        checklistDetail.Id = Guid.NewGuid();
                        checklistDetail.ChecklistResultId = checklistResult.Id;
                        checklistDetail.PreOperationalChecklistId = preopchecklist.Id;
                        // Answer is false if vehicle index in vehicles is divisible by 5 else true 
                        checklistDetail.Answer = vehicleIndex % 5 == 0 ? false : true;
                        await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetail);
                    }

                    var impact = _serviceProvider.GetRequiredService<ImpactDataObject>();
                    impact.Id = Guid.NewGuid();
                    impact.SessionId = session.Id;
                    // ImpactDateTime is 10 to 40 minutes after session start time
                    impact.ImpactDateTime = session.StartTime.AddMinutes(new Random().Next(10, 40));
                    // Threshold is the FSSSBase of the module
                    impact.Threshold = (await vehicle.LoadModuleAsync()).FSSSBase;
                    // ImpactValue is random from 200000 to 3000000
                    impact.ShockValue = new Random().Next(200000, 3000000);
                    await _dataFacade.ImpactDataProvider.SaveAsync(impact);
                }
            }
            return new ComponentResponse<System.Boolean>(true);
        }

        /// <summary>
        /// GenerateTestDataForSessions Method
        /// </summary>
        /// <returns></returns>
        public async System.Threading.Tasks.Task<ComponentResponse<System.Boolean>> GenerateTestDataForSessionsAsync(Dictionary<string, object> parameters = null)
        {
            var randomGenerator = new Random();

            // First load all required data
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null, "CompanyName == @0", new object[] { CustomerName })).SingleOrDefault();

            if (customer == null)
            {
                throw new GOServerException($"Customer '{CustomerName}' not found");
            }

            var site = (await customer.LoadSitesAsync()).First();

            if (site == null)
            {
                throw new GOServerException($"No site found for this customer");
            }

            await site.LoadDepartmentItemsAsync();

            foreach (var department in site.DepartmentItems)
            {
                await department.LoadVehiclesAsync();
            }

            // Load all drivers. TODO : see how to load only relevant drivers, for current customer / site / deparment
            var drivers = await _dataFacade.DriverDataProvider.GetCollectionAsync(null);


            // First delete all existing impact and session data
            var impacts = await _dataFacade.ImpactDataProvider.GetCollectionAsync(null);
            foreach (var impact in impacts)
            {
                impact.IsMarkedForDeletion = true;
            }

            if (impacts.Any())
            {
                await _dataFacade.ImpactDataProvider.SaveAsync(impacts.First());
            }

            var sessiondetails = await _dataFacade.SessionDetailsDataProvider.GetCollectionAsync(null);
            foreach (var sessiondetail in sessiondetails)
            {
                sessiondetail.IsMarkedForDeletion = true;
            }

            if (sessiondetails.Any())
            {
                await _dataFacade.SessionDetailsDataProvider.SaveAsync(sessiondetails.First());
            }

            var iofields = await _dataFacade.IOFIELDDataProvider.GetCollectionAsync(null);
            foreach (var iofield in iofields)
            {
                iofield.IsMarkedForDeletion = true;
            }

            if (iofields.Any())
            {
                await _dataFacade.IOFIELDDataProvider.SaveAsync(iofields.First());
            }

            var sessions = await _dataFacade.SessionDataProvider.GetCollectionAsync(null);
            foreach (var session in sessions)
            {
                session.IsMarkedForDeletion = true;
            }

            if (sessions.Any())
            {
                await _dataFacade.SessionDataProvider.SaveAsync(sessions.First());
            }

            // Delete all preop check data
            var checklistDetails = await _dataFacade.ChecklistDetailDataProvider.GetCollectionAsync(null);
            foreach (var checklistDetail in checklistDetails)
            {
                checklistDetail.IsMarkedForDeletion = true;
            }

            if (checklistDetails.Any())
            {
                await _dataFacade.ChecklistDetailDataProvider.SaveAsync(checklistDetails.First());
            }


            var checklistResults = await _dataFacade.ChecklistResultDataProvider.GetCollectionAsync(null);
            foreach (var checklistResult in checklistResults)
            {
                checklistResult.IsMarkedForDeletion = true;
            }

            if (checklistResults.Any())
            {
                await _dataFacade.ChecklistResultDataProvider.SaveAsync(checklistResults.First());
            }

            var preopcheclists = await _dataFacade.PreOperationalChecklistDataProvider.GetCollectionAsync(null);
            foreach (var preopcheclist in preopcheclists)
            {
                preopcheclist.IsMarkedForDeletion = true;
            }

            if (preopcheclists.Any())
            {
                await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preopcheclists.First());
            }



            // Create the pre-operational checklist
            // Create a collection to hold all the sessions, so that we can call save only once (more performant)
            var preopcheckCollection = new DataObjectCollection<PreOperationalChecklistDataObject>();
            preopcheckCollection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

            // create enum for AnswerType


            // TODO: Generate different Answer Types once we have more options in enum
            var preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Do the brakes work properly ?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Is the steering operating correctly?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Are the hydraulics operating correctly ?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Is the horn functioning correctly?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Is there oil dripping on the floor ?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Is there any structural damage?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Are the wheels/ tires worn or damaged?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Are the mast/ chains OK?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "All lights working ?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Fork arms free from damage?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            preopcheck = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            preopcheck.Id = Guid.NewGuid();
            preopcheck.AnswerType = preopcheck.AnswerType.AsEnumerable().First(x => x.ToString() == "YesNo");
            preopcheck.Question = "Is the truck safe to operate?";
            preopcheck.ExpectedAnswer = true;
            preopcheck.Critical = true;
            preopcheckCollection.Add(preopcheck);

            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(preopcheckCollection.First());

            //preopcheckCollection = _dataFacade.PreOperationalChecklistDataProvider.GetCollection(null);



            for (int batch = 0; batch < NumberOfSessions / batchsize; batch++)
            {
                // Create a collection to hold all the sessions, so that we can call save only once (more performant)
                var sessionCollection = new DataObjectCollection<SessionDataObject>();
                sessionCollection.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

                // Now start to create session data with impacts.
                for (int i = 0; i < NumberOfSessions / batchsize; i++)
                {
                    // _serviceProvider.GetRequiredService because classes should be created through dependency injection
                    var session = _serviceProvider.GetRequiredService<SessionDataObject>();
                    session.Id = Guid.NewGuid();

                    // Add the session to the collection
                    sessionCollection.Add(session);

                    // TODO => when associating a driver to a session / vehicle, we should verify or create a authorized vehicle access for it. Here it just gets a random driver
                    // TODO => DriverID is not correct, should be a FK to Driver table, not standalone string
                    session.DriverId = drivers.ElementAt(randomGenerator.Next(0, drivers.Count())).Id;

                    var department = site.DepartmentItems.ElementAt(randomGenerator.Next(0, site.DepartmentItems.Count()));

                    // TODO => when associating a vehicle to a session / vehicle, we should verify or create a authorized vehicle access for it. Here it just gets a random vehicle
                    // TODO => VehicleID is not correct, should be a FK to Vehicle table, not standalone string
                    session.VehicleId = department.Vehicles.ElementAt(randomGenerator.Next(0, department.Vehicles.Count())).Id;

                    // Get random day in last 3 months
                    session.StartTime = DateTime.Now.Date.AddDays(-randomGenerator.Next(NumberOfDays));
                    // Random time between HourStartDay and HourEndDay
                    session.StartTime += new TimeSpan(randomGenerator.Next(HourStartDay, HourEndDay), randomGenerator.Next(0, 59), randomGenerator.Next(0, 59));

                    // Random session duration between MinSessionTimeMinutes min to MaxSessionTimeMinutes hours
                    session.EndTime = session.StartTime.AddMinutes(randomGenerator.Next(MinSessionTimeMinutes, MaxSessionTimeMinutes));

                    int sessionDurationInMinutes = (int)(session.EndTime.Value - session.StartTime).TotalMinutes;

                    // Add impacts to the session
                    var probability = randomGenerator.Next(0, 100);
                    var numberOfImpacts = probability <= ProbabilityThreeImpactsPerSession ? 3 : probability <= ProbabilityTwoImpactsPerSession ? 2 : probability <= ProbabilityOneImpactPerSession ? 1 : 0;

                    for (int j = 0; j < numberOfImpacts; j++)
                    {
                        var impact = _serviceProvider.GetRequiredService<ImpactDataObject>();
                        impact.Id = Guid.NewGuid();
                        impact.ShockValue = randomGenerator.Next(MinImpactValue, MaxImpactValue);
                        /*impact.CurrentFSSSBase = CurrentFSSSBase;*/
                        impact.Threshold = 1;
                        impact.ImpactDateTime = session.StartTime.AddMinutes(randomGenerator.Next(1, sessionDurationInMinutes));

                        session.Impacts.Add(impact);
                    }

                    // Add Session details for logged hours and seat hours
                    string loggedHours = (sessionDurationInMinutes * 60).ToString("#.##");
                    string seatHours = ((sessionDurationInMinutes * 60) * randomGenerator.Next(85, 100) / 100).ToString("#.##");

                    var sessionDetails = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetails.Id = Guid.NewGuid();
                    sessionDetails.Usage = loggedHours;

                    session.SessionDetailsItems.Add(sessionDetails);

                    var iofield = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
                    iofield.Id = Guid.NewGuid();
                    iofield.Name = "0";
                    iofield.Measurement = loggedHours;
                    iofield.CANBUS = false;
                    iofield.IOType = "";
                    iofield.Description = "";

                    sessionDetails.IOFIELD = iofield;

                    sessionDetails = _serviceProvider.GetRequiredService<SessionDetailsDataObject>();
                    sessionDetails.Id = Guid.NewGuid();
                    sessionDetails.Usage = seatHours;

                    session.SessionDetailsItems.Add(sessionDetails);

                    iofield = _serviceProvider.GetRequiredService<IOFIELDDataObject>();
                    iofield.Id = Guid.NewGuid();
                    iofield.Name = "1";
                    iofield.Measurement = seatHours;
                    iofield.CANBUS = false;
                    iofield.IOType = "";
                    iofield.Description = "";


                    sessionDetails.IOFIELD = iofield;

                    // Add preoperational check for session
                    var checklistResult = _serviceProvider.GetRequiredService<ChecklistResultDataObject>();
                    checklistResult.Id = Guid.NewGuid();
                    checklistResult.ObjectsDataSet = _serviceProvider.GetRequiredService<IObjectsDataSet>();

                    /*session.ChecklistResult = checklistResult;

                    session.ChecklistResult.StartTime = session.StartTime; */

                    bool cheklistFinished = randomGenerator.Next(1, 100) < 96;

                    if (cheklistFinished)
                    {
                        checklistResult.EndTime = session.StartTime.AddSeconds(randomGenerator.Next(60, 180));

                        foreach (var question in preopcheckCollection)
                        {
                            var goodAnswer = randomGenerator.Next(1, 100) < 95;
                            var answer = _serviceProvider.GetRequiredService<ChecklistDetailDataObject>();
                            answer.Answer = goodAnswer ? question.ExpectedAnswer : !question.ExpectedAnswer;
                            answer.PreOperationalChecklistId = question.Id;
                            checklistResult.ChecklistAnswerDetails.Add(answer);
                        }
                    }
                }

                // Save all the sessions and related data. To do this we save the first session object, it will automatically bring all the dataset with it
                await _dataFacade.SessionDataProvider.SaveAsync(sessionCollection.First());
            }

            return new ComponentResponse<System.Boolean>(true);
        }
    }
}
