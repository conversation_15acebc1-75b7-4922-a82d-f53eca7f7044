// 001-dashboard.cy.js

describe("001 - Dashboard Test", () => {
    beforeEach(() => {
        // Use the centralized login function from support file
        cy.login();

        // Intercept the correct API call for the customer list
        cy.intercept('/dataset/api/customer/list*').as('getCustomerList');

        // Intercept the API call that updates the banner count (driver license expiry view API)
        cy.intercept('/dataset/api/driverlicenseexpiryview/list*').as('getBannerData');
    });

    it("tests 001 - Dashboard Test", () => {
        // Wait for the customer list API to complete before interacting with the dropdown
        cy.wait('@getCustomerList').then((interception) => {
            cy.log('Customer list API completed:', interception);
        });

        // Verify if the lookup input is visible and clickable after the API call
        cy.get("#DashboardFilterFormControl-DashboardFilterFormData > div > div:nth-of-type(1) [data-test-id='lookup_input']")
            .should('be.visible') // Ensure the lookup input is visible
            .should('be.enabled') // Ensure it is clickable
            .click();

        // Debugging log to check if dropdown items are visible
        cy.get("li > [data-test-id='lookup_item']")
            .should('have.length.greaterThan', 0) // Ensure at least one item is available in the dropdown
            .then((items) => {
                cy.log('Number of items found in the dropdown:', items.length); // Log the number of items found
            });

        // Click the first item once it is available
        cy.get("li:nth-of-type(1) > [data-test-id='lookup_item']")
            .should('be.visible') // Ensure the first item is visible
            .click();

        // Capture the banner count before clicking the filter
        cy.get(".banner-count")
            .should('be.visible') // Ensure the banner count is visible
            .invoke('text').then((initialCount) => {
                cy.log('Initial count:', initialCount); // Log the initial count

                // Click the filter button when it becomes visible and clickable
                cy.get("[data-test-id='\\34 6dbeefc-6048-40da-825c-89aa25171461']")
                    .should('be.visible') // Ensure the filter button is visible
                    .should('be.enabled') // Ensure it is clickable
                    .click();

                // Wait for the banner data API to complete after the filter button is clicked
                cy.wait('@getBannerData').then((interception) => {
                    cy.log('Banner data API completed:', interception);
                });

                // Wait for the banner count to update by checking its text changes
                cy.get(".banner-count", { timeout: 10000 })
                    .should('be.visible') // Ensure the banner count is still visible
                    .invoke('text').then((newCount) => {
                        cy.log('New count:', newCount); // Log the new count

                        // Assert that the count has changed
                        expect(initialCount).not.to.eq(newCount);
                    });
            });
    });
});
