using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Threading.Tasks;
using System.Linq;
using DocumentFormat.OpenXml.Bibliography;
using Moq;
using FleetXQ.BusinessLayer.Components.Server.Custom;
using FleetXQ.Feature.Security.Common;
using FleetXQ.Data.DataProvidersExtensions.Custom;
using GenerativeObjects.Practices.LayerSupportClasses.Features.Security.Common;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    [TestFixture]
    public class VehicleDataProviderExtensionTest : TestBase
    {
        private IDataFacade _dataFacade;
        private readonly string _testDatabaseName = $"VehicleDataProviderExtensionTest-{Guid.NewGuid()}";
        private Mock<IFirmwareUpdate> _mockFirmwareUpdate;
        private Mock<IDeviceTwinHandler> _mockDeviceTwinHandler;
        private Mock<IVehicleAPI> _mockVehicleAPI;
        private Mock<IAuthentication> _mockAuthentication;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            _mockFirmwareUpdate = new Mock<IFirmwareUpdate>();
            _mockDeviceTwinHandler = new Mock<IDeviceTwinHandler>();
            _mockVehicleAPI = new Mock<IVehicleAPI>();
            _mockAuthentication = new Mock<IAuthentication>();

            services.AddSingleton(_mockFirmwareUpdate.Object);
            services.AddSingleton(_mockDeviceTwinHandler.Object);
            services.AddSingleton(_mockVehicleAPI.Object);
            services.AddSingleton(_mockAuthentication.Object);
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();

            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;

            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;

            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;

            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();

            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            // Create test site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site";
            await _dataFacade.SiteDataProvider.SaveAsync(site);
        }

        [Test]
        public async Task OnBeforeSave_NewVehicle_CreatesDepartmentChecklist()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID1";
            // set FSSSBASE random from 100000 to 200000 in increment of 10000
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000001" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle New";
            vehicle.SerialNo = "Test Serial No New";

            // Act
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var savedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(savedVehicle, Is.Not.Null);
            Assert.That(savedVehicle.DepartmentChecklistId, Is.Not.Null, "Vehicle should have a department checklist linked");

            var departmentChecklistToGet = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklistToGet.Id = savedVehicle.DepartmentChecklistId.Value;
            var departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(departmentChecklistToGet);
            Assert.That(departmentChecklist, Is.Not.Null, "Department checklist should exist");
            Assert.That(departmentChecklist.DepartmentId, Is.EqualTo(department.Id), "Department checklist should be linked to correct department");
            Assert.That(departmentChecklist.ModelId, Is.EqualTo(model.Id), "Department checklist should be linked to correct model");

            // Verify no duplicate department checklist was created
            var departmentChecklists = await _dataFacade.DepartmentChecklistDataProvider.GetCollectionAsync(null,
                "DepartmentId == @0 and ModelId == @1",
                new object[] { department.Id, model.Id },
                skipSecurity: true);
            Assert.That(departmentChecklists.Count(), Is.EqualTo(1), "Only one department checklist should exist for this department and model combination");
        }

        [Test]
        public async Task OnBeforeSave_NewVehicle_LinksExistingDepartmentChecklist()
        {
            // Arrange
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID2";
            // set FSSSBASE random from 100000 to 200000 in increment of 10000
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000002" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.DepartmentId = department.Id;
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle New2";
            vehicle.SerialNo = "Test Serial No New2";

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var savedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(savedVehicle, Is.Not.Null);
            Assert.That(savedVehicle.DepartmentChecklistId, Is.Not.Null, "Vehicle should have a department checklist linked");
            Assert.That(savedVehicle.DepartmentChecklistId, Is.EqualTo(departmentChecklist.Id));
        }

        [Test]
        public async Task OnBeforeSave_DepartmentChanged_UpdatesDepartmentChecklistId()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create original department
            var originalDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            originalDepartment.Id = Guid.NewGuid();
            originalDepartment.SiteId = site.Id;
            originalDepartment.Name = "Original Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(originalDepartment);

            // Create new department
            var newDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            newDepartment.Id = Guid.NewGuid();
            newDepartment.SiteId = site.Id;
            newDepartment.Name = "New Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(newDepartment);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID3";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000003" + originalDepartment.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create original department checklist
            var originalDepartmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            originalDepartmentChecklist.DepartmentId = originalDepartment.Id;
            originalDepartmentChecklist.ModelId = model.Id;
            originalDepartmentChecklist.Id = Guid.NewGuid();
            originalDepartmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(originalDepartmentChecklist);

            // Create a vehicle with original department
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = originalDepartment.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Department Change";
            vehicle.SerialNo = "Test Serial Department Change";

            // First save to create the vehicle
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Verify initial state
            Assert.That(vehicle.DepartmentChecklistId, Is.Not.Null);
            Assert.That(vehicle.DepartmentChecklistId, Is.EqualTo(originalDepartmentChecklist.Id));

            // Act - Change department
            vehicle.DepartmentId = newDepartment.Id;
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var updatedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(updatedVehicle, Is.Not.Null);
            Assert.That(updatedVehicle.DepartmentChecklistId, Is.Not.Null, "Vehicle should have a department checklist linked");
            Assert.That(updatedVehicle.DepartmentChecklistId, Is.Not.EqualTo(originalDepartmentChecklist.Id),
                "Department checklist ID should have changed after department change");

            // Verify the new department checklist is linked to the new department
            var newDepartmentChecklistToGet = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            newDepartmentChecklistToGet.Id = updatedVehicle.DepartmentChecklistId.Value;
            var newDepartmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(newDepartmentChecklistToGet);

            Assert.That(newDepartmentChecklist, Is.Not.Null);
            Assert.That(newDepartmentChecklist.DepartmentId, Is.EqualTo(newDepartment.Id));
            Assert.That(newDepartmentChecklist.ModelId, Is.EqualTo(model.Id));
        }

        [Test]
        public async Task OnBeforeSave_SiteChanged_UpdatesDepartmentChecklistId()
        {
            // Arrange
            var originalSite = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(originalSite, Is.Not.Null, "Test data setup failed: No site found.");

            // Create a new site
            var customer = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First();
            var timezone = (await _dataFacade.TimezoneDataProvider.GetCollectionAsync(null)).First();

            var newSite = _serviceProvider.GetRequiredService<SiteDataObject>();
            newSite.Id = Guid.NewGuid();
            newSite.CustomerId = customer.Id;
            newSite.TimezoneId = timezone.Id;
            newSite.Name = "New Test Site";
            await _dataFacade.SiteDataProvider.SaveAsync(newSite);

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create department for original site
            var originalDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            originalDepartment.Id = Guid.NewGuid();
            originalDepartment.SiteId = originalSite.Id;
            originalDepartment.Name = "Original Site Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(originalDepartment);

            // Create department for new site
            var newDepartment = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            newDepartment.Id = Guid.NewGuid();
            newDepartment.SiteId = newSite.Id;
            newDepartment.Name = "New Site Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(newDepartment);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID4";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000004" + originalDepartment.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create original department checklist
            var originalDepartmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            originalDepartmentChecklist.DepartmentId = originalDepartment.Id;
            originalDepartmentChecklist.ModelId = model.Id;
            originalDepartmentChecklist.Id = Guid.NewGuid();
            originalDepartmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(originalDepartmentChecklist);

            // Create a vehicle with original site and department
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = customer.Id;
            vehicle.DepartmentId = originalDepartment.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = originalSite.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Site Change";
            vehicle.SerialNo = "Test Serial Site Change";

            // First save to create the vehicle
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Verify initial state
            Assert.That(vehicle.DepartmentChecklistId, Is.Not.Null);
            Assert.That(vehicle.DepartmentChecklistId, Is.EqualTo(originalDepartmentChecklist.Id));

            // Act - Change site and department
            vehicle.SiteId = newSite.Id;
            vehicle.DepartmentId = newDepartment.Id;
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var updatedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(updatedVehicle, Is.Not.Null);
            Assert.That(updatedVehicle.DepartmentChecklistId, Is.Not.Null, "Vehicle should have a department checklist linked");
            Assert.That(updatedVehicle.DepartmentChecklistId, Is.Not.EqualTo(originalDepartmentChecklist.Id),
                "Department checklist ID should have changed after site change");

            // Verify the new department checklist is linked to the new department
            var newDepartmentChecklistToGet = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            newDepartmentChecklistToGet.Id = updatedVehicle.DepartmentChecklistId.Value;
            var newDepartmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(newDepartmentChecklistToGet);

            Assert.That(newDepartmentChecklist, Is.Not.Null);
            Assert.That(newDepartmentChecklist.DepartmentId, Is.EqualTo(newDepartment.Id));
            Assert.That(newDepartmentChecklist.ModelId, Is.EqualTo(model.Id));
        }

        [Test]
        public async Task OnBeforeSave_ModelChanged_UpdatesDepartmentChecklistId()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create original model
            var originalModel = _serviceProvider.GetRequiredService<ModelDataObject>();
            originalModel.Id = Guid.NewGuid();
            originalModel.Name = "Original Model";
            originalModel.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(originalModel);

            // Create new model
            var newModel = _serviceProvider.GetRequiredService<ModelDataObject>();
            newModel.Id = Guid.NewGuid();
            newModel.Name = "New Model";
            newModel.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(newModel);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID5";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000005" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create original department checklist
            var originalDepartmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            originalDepartmentChecklist.DepartmentId = department.Id;
            originalDepartmentChecklist.ModelId = originalModel.Id;
            originalDepartmentChecklist.Id = Guid.NewGuid();
            originalDepartmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(originalDepartmentChecklist);

            // Create a vehicle with original model
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = originalModel.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Model Change";
            vehicle.SerialNo = "Test Serial Model Change";

            // First save to create the vehicle
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Verify initial state
            Assert.That(vehicle.DepartmentChecklistId, Is.Not.Null);
            Assert.That(vehicle.DepartmentChecklistId, Is.EqualTo(originalDepartmentChecklist.Id));

            // Act - Change model
            vehicle.ModelId = newModel.Id;
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var updatedVehicle = await _dataFacade.VehicleDataProvider.GetAsync(vehicle);
            Assert.That(updatedVehicle, Is.Not.Null);
            Assert.That(updatedVehicle.DepartmentChecklistId, Is.Not.Null, "Vehicle should have a department checklist linked");
            Assert.That(updatedVehicle.DepartmentChecklistId, Is.Not.EqualTo(originalDepartmentChecklist.Id),
                "Department checklist ID should have changed after model change");

            // Verify the new department checklist is linked to the new model
            var newDepartmentChecklistToGet = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            newDepartmentChecklistToGet.Id = updatedVehicle.DepartmentChecklistId.Value;
            var newDepartmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(newDepartmentChecklistToGet);

            Assert.That(newDepartmentChecklist, Is.Not.Null);
            Assert.That(newDepartmentChecklist.DepartmentId, Is.EqualTo(department.Id));
            Assert.That(newDepartmentChecklist.ModelId, Is.EqualTo(newModel.Id));
        }

        [Test]
        public async Task OnAfterSave_NewVehicle_CreatesVehicleDiagnostic()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID6";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000006" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Diagnostic";
            vehicle.SerialNo = "Test Serial Diagnostic";

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var vehicleDiagnostic = await vehicle.LoadVehicleDiagnosticAsync();
            Assert.That(vehicleDiagnostic, Is.Not.Null, "Vehicle diagnostic should be created");
            Assert.That(vehicleDiagnostic.VehicleId, Is.EqualTo(vehicle.Id), "Vehicle diagnostic should be linked to the correct vehicle");
        }

        [Test]
        public async Task OnAfterSave_NewVehicle_CreatesVehicleOtherSettings()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID7";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000007" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Settings";
            vehicle.SerialNo = "Test Serial Settings";

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var vehicleOtherSettings = await vehicle.LoadVehicleOtherSettingsAsync();
            Assert.That(vehicleOtherSettings, Is.Not.Null, "Vehicle other settings should be created");
            Assert.That(vehicleOtherSettings.VORStatusConfirmed, Is.True, "VORStatusConfirmed should be set to true by default");
        }

        [Test]
        public async Task OnAfterSave_NewVehicle_UpdatesModuleStatus()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID8";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000008" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Module";
            vehicle.SerialNo = "Test Serial Module";

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var updatedModule = await vehicle.LoadModuleAsync(skipSecurity: true);
            Assert.That(updatedModule, Is.Not.Null, "Module should be loaded");
            Assert.That(updatedModule.Status, Is.EqualTo(ModuleStatusEnum.Assigned), "Module status should be set to Assigned");
            Assert.That(updatedModule.DealerId, Is.EqualTo((await vehicle.LoadCustomerAsync()).DealerId), "Module dealer ID should be set to vehicle's customer dealer ID");
        }

        [Test]
        public async Task OnAfterSave_NewVehicle_SyncsDeviceSettings()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID9";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000009" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Sync";
            vehicle.SerialNo = "Test Serial Sync";

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            // For a new vehicle, SyncGeneralSettings should be called at least once
            _mockDeviceTwinHandler.Verify(x => x.SyncGeneralSettings(module.IoTDevice), Times.AtLeastOnce, "Device settings should be synced exactly once for a new vehicle");
        }

        [Test]
        public async Task OnAfterSave_NewVehicle_CreatesCustomerModel()
        {
            // Arrange
            var site = (await _dataFacade.SiteDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(site, Is.Not.Null, "Test data setup failed: No site found.");

            var dealer = (await _dataFacade.DealerDataProvider.GetCollectionAsync(null)).FirstOrDefault();
            Assert.That(dealer, Is.Not.Null, "Test data setup failed: No dealer found.");

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var module = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module.Id = Guid.NewGuid();
            module.Calibration = 100;
            module.CCID = "CCID10";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module.FSSSBase = randomNumber * 10000;
            module.FSSXMulti = 1;
            module.IoTDevice = "test_00000010" + department.Id;
            module.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module);

            // Create a new vehicle
            var vehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle.Initialize(Guid.NewGuid());
            vehicle.CustomerId = (await _dataFacade.CustomerDataProvider.GetCollectionAsync(null)).First().Id;
            vehicle.DepartmentId = department.Id;
            vehicle.ModelId = model.Id;
            vehicle.SiteId = site.Id;
            vehicle.ModuleId1 = module.Id;
            vehicle.HireNo = "Test Vehicle Customer Model";
            vehicle.SerialNo = "Test Serial Customer Model";

            // Act
            vehicle = await _dataFacade.VehicleDataProvider.SaveAsync(vehicle);

            // Assert
            var customer = await vehicle.LoadCustomerAsync();
            var customerModels = await customer.LoadCustomerModelItemsAsync();
            Assert.That(customerModels, Is.Not.Null, "Customer models should be loaded");
            Assert.That(customerModels.Any(cm => cm.CustomerId == vehicle.CustomerId && cm.ModelId == vehicle.ModelId), Is.True, "Customer model should be created");
        }

        [Test]
        public async Task OnBeforeGetCollection_ExcludesDeletedVehicles()
        {
            // Arrange
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site 1";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create module for active vehicle
            var activeModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            activeModule.Id = Guid.NewGuid();
            activeModule.Calibration = 100;
            activeModule.CCID = "CCID11";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            activeModule.FSSSBase = randomNumber * 10000;
            activeModule.FSSXMulti = 1;
            activeModule.IoTDevice = "test_00000011" + department.Id;
            activeModule.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(activeModule);

            // Create module for deleted vehicle
            var deletedModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            deletedModule.Id = Guid.NewGuid();
            deletedModule.Calibration = 100;
            deletedModule.CCID = "CCID12";
            randomNumber = random.Next(10, 21);
            deletedModule.FSSSBase = randomNumber * 10000;
            deletedModule.FSSXMulti = 1;
            deletedModule.IoTDevice = "test_00000012" + department.Id;
            deletedModule.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(deletedModule);

            // Create active vehicle
            var activeVehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            activeVehicle.Id = Guid.NewGuid();
            activeVehicle.CustomerId = customer.Id;
            activeVehicle.DepartmentId = department.Id;
            activeVehicle.ModelId = model.Id;
            activeVehicle.SiteId = site.Id;
            activeVehicle.ModuleId1 = activeModule.Id;
            activeVehicle.HireNo = "Test Vehicle Active";
            activeVehicle.SerialNo = "Test Serial Active";
            await _dataFacade.VehicleDataProvider.SaveAsync(activeVehicle);

            // Create deleted vehicle
            var deletedVehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            deletedVehicle.Id = Guid.NewGuid();
            deletedVehicle.CustomerId = customer.Id;
            deletedVehicle.DepartmentId = department.Id;
            deletedVehicle.ModelId = model.Id;
            deletedVehicle.SiteId = site.Id;
            deletedVehicle.ModuleId1 = deletedModule.Id;
            deletedVehicle.HireNo = "Test Vehicle Deleted";
            deletedVehicle.SerialNo = "Test Serial Deleted";
            deletedVehicle.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.VehicleDataProvider.SaveAsync(deletedVehicle);

            // Act
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                null, "SiteId == @0", new object[] { site.Id });

            // Assert
            Assert.That(vehicles.Count(), Is.EqualTo(1), "Should only return active vehicles");
            Assert.That(vehicles.First().Id, Is.EqualTo(activeVehicle.Id), "Should return the active vehicle");
        }

        [Test]
        public async Task OnBeforeGetCollection_CombinesWithExistingFilter()
        {
            // Arrange
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.Id = Guid.NewGuid();
            site.CustomerId = customer.Id;
            site.TimezoneId = timeZone.Id;
            site.Name = "Test Site 2";
            site = await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create test department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.SiteId = site.Id;
            department.Name = "Test Department";
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create test model
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.DealerId = dealer.Id;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            // Create module for vehicle1
            var module1 = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module1.Id = Guid.NewGuid();
            module1.Calibration = 100;
            module1.CCID = "CCID13";
            Random random = new Random();
            int randomNumber = random.Next(10, 21);
            module1.FSSSBase = randomNumber * 10000;
            module1.FSSXMulti = 1;
            module1.IoTDevice = "test_00000013" + department.Id;
            module1.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module1);

            // Create module for vehicle2
            var module2 = _serviceProvider.GetRequiredService<ModuleDataObject>();
            module2.Id = Guid.NewGuid();
            module2.Calibration = 100;
            module2.CCID = "CCID14";
            randomNumber = random.Next(10, 21);
            module2.FSSSBase = randomNumber * 10000;
            module2.FSSXMulti = 1;
            module2.IoTDevice = "test_00000014" + department.Id;
            module2.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(module2);

            // Create module for deleted vehicle
            var deletedModule = _serviceProvider.GetRequiredService<ModuleDataObject>();
            deletedModule.Id = Guid.NewGuid();
            deletedModule.Calibration = 100;
            deletedModule.CCID = "CCID15";
            randomNumber = random.Next(10, 21);
            deletedModule.FSSSBase = randomNumber * 10000;
            deletedModule.FSSXMulti = 1;
            deletedModule.IoTDevice = "test_00000015" + department.Id;
            deletedModule.IsAllocatedToVehicle = true;
            await _dataFacade.ModuleDataProvider.SaveAsync(deletedModule);

            // Create vehicles with different hire numbers
            var vehicle1 = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle1.Id = Guid.NewGuid();
            vehicle1.CustomerId = customer.Id;
            vehicle1.DepartmentId = department.Id;
            vehicle1.ModelId = model.Id;
            vehicle1.SiteId = site.Id;
            vehicle1.ModuleId1 = module1.Id;
            vehicle1.HireNo = "Test Vehicle 1";
            vehicle1.SerialNo = "Test Serial 1";
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle1);

            var vehicle2 = _serviceProvider.GetRequiredService<VehicleDataObject>();
            vehicle2.Id = Guid.NewGuid();
            vehicle2.CustomerId = customer.Id;
            vehicle2.DepartmentId = department.Id;
            vehicle2.ModelId = model.Id;
            vehicle2.SiteId = site.Id;
            vehicle2.ModuleId1 = module2.Id;
            vehicle2.HireNo = "Test Vehicle 2";
            vehicle2.SerialNo = "Test Serial 2";
            await _dataFacade.VehicleDataProvider.SaveAsync(vehicle2);

            var deletedVehicle = _serviceProvider.GetRequiredService<VehicleDataObject>();
            deletedVehicle.Id = Guid.NewGuid();
            deletedVehicle.CustomerId = customer.Id;
            deletedVehicle.DepartmentId = department.Id;
            deletedVehicle.ModelId = model.Id;
            deletedVehicle.SiteId = site.Id;
            deletedVehicle.ModuleId1 = deletedModule.Id;
            deletedVehicle.HireNo = "Test Vehicle 1";
            deletedVehicle.SerialNo = "Test Serial Deleted";
            deletedVehicle.DeletedAtUtc = DateTime.UtcNow;
            await _dataFacade.VehicleDataProvider.SaveAsync(deletedVehicle);

            // Act
            var vehicles = await _dataFacade.VehicleDataProvider.GetCollectionAsync(
                null, "SiteId == @0 && HireNo == @1", new object[] { site.Id, "Test Vehicle 1" });

            // Assert
            Assert.That(vehicles.Count(), Is.EqualTo(1), "Should only return active vehicles matching the filter");
            Assert.That(vehicles.First().Id, Is.EqualTo(vehicle1.Id), "Should return the active vehicle with matching hire number");
        }
    }
}