using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using FleetXQ.Tests.Common;
using GenerativeObjects.Practices.ExceptionHandling;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NUnit.Framework;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server.Custom.Tests
{
    public class CustomerUtilitiesTest : TestBase
    {
        private IDataFacade _dataFacade;
        private CustomerUtilities _customerUtilities;
        private readonly string _testDatabaseName = $"CustomerUtilitiesTest-{Guid.NewGuid()}";
        private Guid _customerId;
        private Guid _siteId;
        private Guid _departmentId;
        private Guid _modelId;
        private Guid _departmentChecklistId;
        private Guid _checklistTemplateId;

        protected override void AddServiceRegistrations(ServiceCollection services)
        {
            // Add any specific service registrations if needed
        }

        [OneTimeSetUp]
        public async Task OneTimeSetUpAsync()
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            _configuration = _serviceProvider.GetRequiredService<IConfiguration>();
            _customerUtilities = new CustomerUtilities(_serviceProvider, _configuration, _dataFacade);
            CreateTestDatabase(_testDatabaseName);
            await CreateTestDataAsync();
        }

        [OneTimeTearDown]
        public void OneTimeTearDown()
        {
            DeleteTestDatabase(_testDatabaseName);
        }

        private async Task CreateTestDataAsync()
        {
            // Create country
            var country = _serviceProvider.GetRequiredService<CountryDataObject>();
            country.Name = "Australia";
            country.Id = Guid.NewGuid();
            country = await _dataFacade.CountryDataProvider.SaveAsync(country);

            // Create region
            var region = _serviceProvider.GetRequiredService<RegionDataObject>();
            region.Name = "Victoria";
            region.Id = Guid.NewGuid();
            region.Active = true;
            region = await _dataFacade.RegionDataProvider.SaveAsync(region);

            // Create dealer
            var dealer = _serviceProvider.GetRequiredService<DealerDataObject>();
            dealer.Name = "Test dealer";
            dealer.Id = Guid.NewGuid();
            dealer.RegionId = region.Id;
            dealer.Active = true;
            dealer = await _dataFacade.DealerDataProvider.SaveAsync(dealer);

            // Create customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = country.Id;
            customer.DealerId = dealer.Id;
            customer.Active = true;
            customer = await _dataFacade.CustomerDataProvider.SaveAsync(customer);
            _customerId = customer.Id;

            // Create timezone
            var timeZone = _serviceProvider.GetRequiredService<TimezoneDataObject>();
            timeZone.TimezoneName = "AEST";
            timeZone.UTCOffset = 10;
            timeZone.Id = Guid.NewGuid();
            timeZone = await _dataFacade.TimezoneDataProvider.SaveAsync(timeZone);

            // Create site
            var site = _serviceProvider.GetRequiredService<SiteDataObject>();
            site.CustomerId = customer.Id;
            site.Name = "Test Site";
            site.TimezoneId = timeZone.Id;
            site.Id = Guid.NewGuid();
            _siteId = site.Id;
            await _dataFacade.SiteDataProvider.SaveAsync(site);

            // Create department
            var department = _serviceProvider.GetRequiredService<DepartmentDataObject>();
            department.Id = Guid.NewGuid();
            department.Name = "Test Department";
            department.SiteId = site.Id;
            department.CustomerId = customer.Id;
            department.Active = true;
            _departmentId = department.Id;
            await _dataFacade.DepartmentDataProvider.SaveAsync(department);

            // Create checklist template
            var checklistTemplate = _serviceProvider.GetRequiredService<CustomerPreOperationalChecklistTemplateDataObject>();
            checklistTemplate.Id = Guid.NewGuid();
            checklistTemplate.CustomerId = _customerId;
            checklistTemplate.Question = "Test Template Question";
            checklistTemplate.AnswerType = PreopAnswerTypesEnum.YesNo;
            checklistTemplate.ExpectedAnswer = true;
            checklistTemplate.Critical = true;
            checklistTemplate.Active = true;
            checklistTemplate.Order = 1;
            await _dataFacade.CustomerPreOperationalChecklistTemplateDataProvider.SaveAsync(checklistTemplate);
            _checklistTemplateId = checklistTemplate.Id;
        }

        [Test]
        public async Task ApplyChecklistTemplatesAsync_ShouldAddNewQuestion_WhenNoExistingQuestion()
        {
            // Arrange - Create model and department checklist
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            model.Type = ModelTypesEnum.Electric;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = _departmentId;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            // Act
            var result = await _customerUtilities.ApplyChecklistTemplatesAsync(_checklistTemplateId, _customerId, new[] { model.Id });

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify the question was added
            departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(departmentChecklist);
            await departmentChecklist.LoadPreOperationalChecklistsAsync();

            Assert.That(departmentChecklist.PreOperationalChecklists.Count, Is.EqualTo(1));
            var addedQuestion = departmentChecklist.PreOperationalChecklists.First();
            Assert.That(addedQuestion.Question, Is.EqualTo("Test Template Question"));
            Assert.That(addedQuestion.Order, Is.EqualTo(1));
        }

        [Test]
        public async Task ApplyChecklistTemplatesAsync_ShouldNotAddDuplicateQuestion()
        {
            // Arrange - Create model and department checklist
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            model.Type = ModelTypesEnum.Electric;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = _departmentId;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            // Add initial question
            var initialQuestion = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            initialQuestion.Id = Guid.NewGuid();
            initialQuestion.SiteChecklistId = departmentChecklist.Id;
            initialQuestion.Question = "Test Template Question";
            initialQuestion.AnswerType = PreopAnswerTypesEnum.YesNo;
            initialQuestion.ExpectedAnswer = true;
            initialQuestion.Critical = true;
            initialQuestion.Order = 1;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(initialQuestion);

            // Act
            var result = await _customerUtilities.ApplyChecklistTemplatesAsync(_checklistTemplateId, _customerId, new[] { model.Id });

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify no duplicate was added
            departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(departmentChecklist);
            await departmentChecklist.LoadPreOperationalChecklistsAsync();

            Assert.That(departmentChecklist.PreOperationalChecklists.Count, Is.EqualTo(1));
        }

        [Test]
        public async Task ApplyChecklistTemplatesAsync_ShouldIncrementOrder_WhenAddingNewQuestion()
        {
            // Arrange - Create model and department checklist
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            model.Type = ModelTypesEnum.Electric;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = _departmentId;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            // Add initial question
            var initialQuestion = _serviceProvider.GetRequiredService<PreOperationalChecklistDataObject>();
            initialQuestion.Id = Guid.NewGuid();
            initialQuestion.SiteChecklistId = departmentChecklist.Id;
            initialQuestion.Question = "Initial Question";
            initialQuestion.AnswerType = PreopAnswerTypesEnum.YesNo;
            initialQuestion.ExpectedAnswer = true;
            initialQuestion.Critical = true;
            initialQuestion.Order = 1;
            initialQuestion.Active = true;
            await _dataFacade.PreOperationalChecklistDataProvider.SaveAsync(initialQuestion);

            // Act
            var result = await _customerUtilities.ApplyChecklistTemplatesAsync(_checklistTemplateId, _customerId, new[] { model.Id });

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify the new question was added with incremented order
            departmentChecklist = await _dataFacade.DepartmentChecklistDataProvider.GetAsync(departmentChecklist);
            await departmentChecklist.LoadPreOperationalChecklistsAsync();

            Assert.That(departmentChecklist.PreOperationalChecklists.Count, Is.EqualTo(2));
            var newQuestion = departmentChecklist.PreOperationalChecklists.First(x => x.Question == "Test Template Question");
            Assert.That(newQuestion.Order, Is.EqualTo(2));
        }

        [Test]
        public async Task ApplyChecklistTemplatesAsync_ShouldReturnFalse_WhenTemplateNotFound()
        {
            // Arrange - Create model and department checklist
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            model.Type = ModelTypesEnum.Electric;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = _departmentId;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            // Act
            var result = await _customerUtilities.ApplyChecklistTemplatesAsync(Guid.NewGuid(), _customerId, new[] { model.Id });

            // Assert
            Assert.That(result.Result, Is.False);
        }

        [Test]
        public async Task ApplyChecklistTemplatesAsync_ShouldReturnFalse_WhenCustomerNotFound()
        {
            // Arrange - Create model and department checklist
            var model = _serviceProvider.GetRequiredService<ModelDataObject>();
            model.Id = Guid.NewGuid();
            model.Name = "Test Model";
            model.Description = "Test Model Description";
            model.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            model.Type = ModelTypesEnum.Electric;
            await _dataFacade.ModelDataProvider.SaveAsync(model);

            var departmentChecklist = _serviceProvider.GetRequiredService<DepartmentChecklistDataObject>();
            departmentChecklist.Id = Guid.NewGuid();
            departmentChecklist.ModelId = model.Id;
            departmentChecklist.DepartmentId = _departmentId;
            await _dataFacade.DepartmentChecklistDataProvider.SaveAsync(departmentChecklist);

            // Act
            var result = await _customerUtilities.ApplyChecklistTemplatesAsync(_checklistTemplateId, Guid.NewGuid(), new[] { model.Id });

            // Assert
            Assert.That(result.Result, Is.False);
        }

        [Test]
        public async Task CopyFeatureSubscriptionAsync_ShouldCreateNewSubscription_WhenCustomerHasNoSubscription()
        {
            // Arrange - Create dealer feature subscription
            var dealerFeatureSubscription = _serviceProvider.GetRequiredService<DealerFeatureSubscriptionDataObject>();
            dealerFeatureSubscription.Id = Guid.NewGuid();
            dealerFeatureSubscription.Name = "Test Dealer Subscription";
            dealerFeatureSubscription.Description = "Test Description";
            dealerFeatureSubscription.HasAdditionalHardwaresAccess = true;
            dealerFeatureSubscription.IsEnab = true;
            await _dataFacade.DealerFeatureSubscriptionDataProvider.SaveAsync(dealerFeatureSubscription);

            // Create customer without feature subscription
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer No Subscription";
            customer.Id = Guid.NewGuid();
            customer.CountryId = (await _dataFacade.CountryDataProvider.GetCollectionAsync()).First().Id;
            customer.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            customer.Active = true;
            customer.CustomerFeatureSubscriptionId = null; // No existing subscription
            await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            // Act
            var result = await _customerUtilities.CopyFeatureSubscriptionAsync(dealerFeatureSubscription.Id, new[] { customer.Id });

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify new subscription was created
            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer);
            Assert.That(customer.CustomerFeatureSubscriptionId, Is.Not.Null);

            var newSubscription = await _dataFacade.CustomerFeatureSubscriptionDataProvider.GetAsync(
                _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize(customer.CustomerFeatureSubscriptionId.Value));
            
            Assert.That(newSubscription.Name, Is.EqualTo("Test Dealer Subscription"));
            Assert.That(newSubscription.Description, Is.EqualTo("Test Description"));
            Assert.That(newSubscription.HasAdditionalHardwaresAccess, Is.True);
            Assert.That(newSubscription.IsEnabled, Is.True);
            Assert.That(newSubscription.IsTagged, Is.False);
        }

        [Test]
        public async Task CopyFeatureSubscriptionAsync_ShouldUpdateExistingSubscription_WhenCustomerHasSubscription()
        {
            // Arrange - Create dealer feature subscription
            var dealerFeatureSubscription = _serviceProvider.GetRequiredService<DealerFeatureSubscriptionDataObject>();
            dealerFeatureSubscription.Id = Guid.NewGuid();
            dealerFeatureSubscription.Name = "Updated Dealer Subscription";
            dealerFeatureSubscription.Description = "Updated Description";
            dealerFeatureSubscription.HasAdditionalHardwaresAccess = false;
            dealerFeatureSubscription.IsEnab = false;
            await _dataFacade.DealerFeatureSubscriptionDataProvider.SaveAsync(dealerFeatureSubscription);

            // Create existing customer feature subscription
            var existingSubscription = _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>();
            existingSubscription.Id = Guid.NewGuid();
            existingSubscription.Name = "Old Customer Subscription";
            existingSubscription.Description = "Old Description";
            existingSubscription.HasAdditionalHardwaresAccess = true;
            existingSubscription.IsEnabled = true;
            existingSubscription.IsTagged = true;
            await _dataFacade.CustomerFeatureSubscriptionDataProvider.SaveAsync(existingSubscription);

            // Create customer with existing feature subscription
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer With Subscription";
            customer.Id = Guid.NewGuid();
            customer.CountryId = (await _dataFacade.CountryDataProvider.GetCollectionAsync()).First().Id;
            customer.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            customer.Active = true;
            customer.CustomerFeatureSubscriptionId = existingSubscription.Id;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            // Act
            var result = await _customerUtilities.CopyFeatureSubscriptionAsync(dealerFeatureSubscription.Id, new[] { customer.Id });

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify existing subscription was updated (same ID)
            customer = await _dataFacade.CustomerDataProvider.GetAsync(customer);
            Assert.That(customer.CustomerFeatureSubscriptionId, Is.EqualTo(existingSubscription.Id));

            var updatedSubscription = await _dataFacade.CustomerFeatureSubscriptionDataProvider.GetAsync(existingSubscription);
            
            Assert.That(updatedSubscription.Name, Is.EqualTo("Updated Dealer Subscription"));
            Assert.That(updatedSubscription.Description, Is.EqualTo("Updated Description"));
            Assert.That(updatedSubscription.HasAdditionalHardwaresAccess, Is.False);
            Assert.That(updatedSubscription.IsEnabled, Is.False);
        }

        [Test]
        public async Task CopyFeatureSubscriptionAsync_ShouldHandleMultipleCustomers()
        {
            // Arrange - Create dealer feature subscription
            var dealerFeatureSubscription = _serviceProvider.GetRequiredService<DealerFeatureSubscriptionDataObject>();
            dealerFeatureSubscription.Id = Guid.NewGuid();
            dealerFeatureSubscription.Name = "Multi Customer Test";
            dealerFeatureSubscription.Description = "Multi Customer Description";
            dealerFeatureSubscription.HasAdditionalHardwaresAccess = true;
            dealerFeatureSubscription.IsEnab = true;
            await _dataFacade.DealerFeatureSubscriptionDataProvider.SaveAsync(dealerFeatureSubscription);

            // Create multiple customers
            var customer1 = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer1.CompanyName = "Customer 1";
            customer1.Id = Guid.NewGuid();
            customer1.CountryId = (await _dataFacade.CountryDataProvider.GetCollectionAsync()).First().Id;
            customer1.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            customer1.Active = true;
            customer1.CustomerFeatureSubscriptionId = null;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer1);

            var customer2 = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer2.CompanyName = "Customer 2";
            customer2.Id = Guid.NewGuid();
            customer2.CountryId = (await _dataFacade.CountryDataProvider.GetCollectionAsync()).First().Id;
            customer2.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            customer2.Active = true;
            customer2.CustomerFeatureSubscriptionId = null;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer2);

            // Act
            var result = await _customerUtilities.CopyFeatureSubscriptionAsync(dealerFeatureSubscription.Id, new[] { customer1.Id, customer2.Id });

            // Assert
            Assert.That(result.Result, Is.True);

            // Verify both customers got subscriptions
            customer1 = await _dataFacade.CustomerDataProvider.GetAsync(customer1);
            customer2 = await _dataFacade.CustomerDataProvider.GetAsync(customer2);

            Assert.That(customer1.CustomerFeatureSubscriptionId, Is.Not.Null);
            Assert.That(customer2.CustomerFeatureSubscriptionId, Is.Not.Null);
            Assert.That(customer1.CustomerFeatureSubscriptionId, Is.Not.EqualTo(customer2.CustomerFeatureSubscriptionId)); // Different subscriptions

            // Verify both subscriptions have correct values
            var subscription1 = await _dataFacade.CustomerFeatureSubscriptionDataProvider.GetAsync(
                _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize(customer1.CustomerFeatureSubscriptionId.Value));
            var subscription2 = await _dataFacade.CustomerFeatureSubscriptionDataProvider.GetAsync(
                _serviceProvider.GetRequiredService<CustomerFeatureSubscriptionDataObject>().Initialize(customer2.CustomerFeatureSubscriptionId.Value));

            Assert.That(subscription1.Name, Is.EqualTo("Multi Customer Test"));
            Assert.That(subscription2.Name, Is.EqualTo("Multi Customer Test"));
        }

        [Test]
        public async Task CopyFeatureSubscriptionAsync_ShouldThrowException_WhenDealerFeatureSubscriptionNotFound()
        {
            // Arrange - Create customer
            var customer = _serviceProvider.GetRequiredService<CustomerDataObject>();
            customer.CompanyName = "Test Customer";
            customer.Id = Guid.NewGuid();
            customer.CountryId = (await _dataFacade.CountryDataProvider.GetCollectionAsync()).First().Id;
            customer.DealerId = (await _dataFacade.DealerDataProvider.GetCollectionAsync()).First().Id;
            customer.Active = true;
            await _dataFacade.CustomerDataProvider.SaveAsync(customer);

            // Act & Assert
            var exception = Assert.ThrowsAsync<GOServerException>(async () =>
                await _customerUtilities.CopyFeatureSubscriptionAsync(Guid.NewGuid(), new[] { customer.Id }));

            Assert.That(exception.Message, Does.Contain("Dealer feature subscription with id:"));
            Assert.That(exception.Message, Does.Contain("not found"));
        }

        [Test]
        public async Task CopyFeatureSubscriptionAsync_ShouldHandleEmptyCustomerIdsArray()
        {
            // Arrange - Create dealer feature subscription
            var dealerFeatureSubscription = _serviceProvider.GetRequiredService<DealerFeatureSubscriptionDataObject>();
            dealerFeatureSubscription.Id = Guid.NewGuid();
            dealerFeatureSubscription.Name = "Test Subscription";
            dealerFeatureSubscription.Description = "Test Description";
            dealerFeatureSubscription.HasAdditionalHardwaresAccess = true;
            dealerFeatureSubscription.IsEnab = true;
            await _dataFacade.DealerFeatureSubscriptionDataProvider.SaveAsync(dealerFeatureSubscription);

            // Act
            var result = await _customerUtilities.CopyFeatureSubscriptionAsync(dealerFeatureSubscription.Id, new Guid[0]);

            // Assert
            Assert.That(result.Result, Is.True);
        }
    }
} 