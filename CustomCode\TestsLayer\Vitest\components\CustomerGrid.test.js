import { describe, test, expect, beforeEach, vi } from 'vitest';

describe('CustomerGrid Action Buttons', () => {
    let mockCustomer;
    let originalWindow;
    let mockApplicationController;

    beforeEach(() => {
        // Store original window object
        originalWindow = { ...window };

        // Mock customer data
        mockCustomer = {
            Data: {
                Id: vi.fn().mockReturnValue('123'),
                CompanyName: 'Test Company'
            }
        };

        // Enhanced ApplicationController mock to include showConfirmPopup
        mockApplicationController = {
            getRootHashTag: vi.fn().mockReturnValue('root/'),
            showConfirmPopup: vi.fn()
        };

        global.ApplicationController = mockApplicationController;

        // Mock GO.Encoding
        global.GO = {
            Encoding: {
                UrlEncode: vi.fn().mockImplementation(str => encodeURIComponent(str))
            }
        };

        // Mock window.location
        delete window.location;
        window.location = { href: '' };

        // Enhanced sessionStorage mock with all required methods as spies
        const storage = {};
        const sessionStorageMock = {
            setItem: vi.spyOn(Storage.prototype, 'setItem').mockImplementation((key, value) => {
                storage[key] = value;
            }),
            getItem: vi.spyOn(Storage.prototype, 'getItem').mockImplementation(key => storage[key]),
            removeItem: vi.spyOn(Storage.prototype, 'removeItem').mockImplementation(key => {
                delete storage[key];
            }),
            clear: vi.spyOn(Storage.prototype, 'clear').mockImplementation(() => {
                Object.keys(storage).forEach(key => delete storage[key]);
            })
        };

        Object.defineProperty(window, 'sessionStorage', {
            value: sessionStorageMock,
            writable: true
        });

        // Mock FleetXQ.Web.Messages.i18n
        global.FleetXQ = {
            Web: {
                Messages: {
                    i18n: {
                        t: vi.fn((key, params) => {
                            if (key === 'dataSource.confirmDeleteMessage') {
                                return `Are you sure you want to delete this ${params.entity}?`;
                            }
                            if (key === 'dataSource.confirmDeletePopupTitle') {
                                return 'Confirm Deletion';
                            }
                            if (key === 'entities/Customer/Customer:entityName') {
                                return 'Customer';
                            }
                            return key;
                        })
                    }
                }
            }
        };

        // Mock setTimeout
        vi.useFakeTimers();
            
        // Mock document.querySelector
        document.querySelector = vi.fn();
    });

    test('View button should navigate to customer details', () => {
        // Simulate view button click
        const expectedUrl = '#!root/Customers/CustomerDetails/123';
        
        // Execute the view action
        window.location.href = '#!' + ApplicationController.getRootHashTag('CustomerDetails') + 'Customers/CustomerDetails/' + GO.Encoding.UrlEncode(mockCustomer.Data.Id());

        // Assert
        expect(ApplicationController.getRootHashTag).toHaveBeenCalledWith('CustomerDetails');
        expect(GO.Encoding.UrlEncode).toHaveBeenCalledWith('123');
        expect(window.location.href).toBe(expectedUrl);
    });

    test('Edit button should set session storage and navigate to customer details', () => {
        const customerId = mockCustomer.Data.Id();
        const expectedUrl = '#!root/Customers/CustomerDetails/123';

        // Execute the edit action
        sessionStorage.setItem('enterEditMode', 'true');
        sessionStorage.setItem('customerId', customerId);
        window.location.href = '#!' + ApplicationController.getRootHashTag('CustomerDetails') + 'Customers/CustomerDetails/' + GO.Encoding.UrlEncode(customerId);

        // Assert
        expect(window.sessionStorage.setItem).toHaveBeenCalledWith('enterEditMode', 'true');
        expect(window.sessionStorage.setItem).toHaveBeenCalledWith('customerId', '123');
        expect(ApplicationController.getRootHashTag).toHaveBeenCalledWith('CustomerDetails');
        expect(GO.Encoding.UrlEncode).toHaveBeenCalledWith('123');
        expect(window.location.href).toBe(expectedUrl);
    });

    test('Delete button should show confirmation popup with correct messages', () => {
        const viewModel = {
            setIsBusy: vi.fn(),
            contextId: 'test-context',
            onConfirmDelete: vi.fn()
        };

        // Execute delete action
        viewModel.setIsBusy(true);
        mockApplicationController.showConfirmPopup(
            viewModel,
            FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeleteMessage', { 
                entity: FleetXQ.Web.Messages.i18n.t('entities/Customer/Customer:entityName')
            }),
            FleetXQ.Web.Messages.i18n.t('dataSource.confirmDeletePopupTitle'),
            viewModel.onConfirmDelete,
            viewModel.contextId
        );

        // Assert
        expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
        expect(mockApplicationController.showConfirmPopup).toHaveBeenCalledWith(
            viewModel,
            'Are you sure you want to delete this Customer?',
            'Confirm Deletion',
            viewModel.onConfirmDelete,
            'test-context'
        );
        expect(FleetXQ.Web.Messages.i18n.t).toHaveBeenCalledWith('dataSource.confirmDeleteMessage', {
            entity: 'Customer'
        });
        expect(FleetXQ.Web.Messages.i18n.t).toHaveBeenCalledWith('dataSource.confirmDeletePopupTitle');
        expect(FleetXQ.Web.Messages.i18n.t).toHaveBeenCalledWith('entities/Customer/Customer:entityName');
    });

    describe('Auto Edit Mode', () => {
        test('should trigger edit mode when enterEditMode flag is set', () => {
            // Mock sessionStorage getItem to return 'true' for enterEditMode
            window.sessionStorage.getItem.mockImplementation((key) => {
                if (key === 'enterEditMode') return 'true';
                return null;
            });

            // Mock the edit button
            const mockEditButton = {
                click: vi.fn()
            };
            document.querySelector.mockReturnValue(mockEditButton);

            // Execute the auto-edit check
            if (sessionStorage.getItem('enterEditMode') === 'true') {
                setTimeout(() => {
                    const editButton = document.querySelector('[data-test-id="6c4d6842-e8a6-4546-a5ee-ff5dbc501ab8"]');
                    if (editButton) {
                        editButton.click();
                        sessionStorage.removeItem('enterEditMode');
                        sessionStorage.removeItem('customerId');
                    }
                }, 1000);
            }

            // Fast-forward timers
            vi.advanceTimersByTime(1000);

            // Assert
            expect(sessionStorage.getItem).toHaveBeenCalledWith('enterEditMode');
            expect(document.querySelector).toHaveBeenCalledWith('[data-test-id="6c4d6842-e8a6-4546-a5ee-ff5dbc501ab8"]');
            expect(mockEditButton.click).toHaveBeenCalled();
            expect(sessionStorage.removeItem).toHaveBeenCalledWith('enterEditMode');
            expect(sessionStorage.removeItem).toHaveBeenCalledWith('customerId');
        });

        test('should not trigger edit mode when enterEditMode flag is not set', () => {
            // Mock sessionStorage getItem to return null
            window.sessionStorage.getItem.mockReturnValue(null);

            // Mock the edit button
            const mockEditButton = {
                click: vi.fn()
            };
            document.querySelector.mockReturnValue(mockEditButton);

            // Execute the auto-edit check
            if (sessionStorage.getItem('enterEditMode') === 'true') {
                setTimeout(() => {
                    const editButton = document.querySelector('[data-test-id="6c4d6842-e8a6-4546-a5ee-ff5dbc501ab8"]');
                    if (editButton) {
                        editButton.click();
                    }
                }, 1000);
            }

            // Fast-forward timers
            vi.advanceTimersByTime(1000);

            // Assert
            expect(sessionStorage.getItem).toHaveBeenCalledWith('enterEditMode');
            expect(document.querySelector).not.toHaveBeenCalled();
            expect(mockEditButton.click).not.toHaveBeenCalled();
        });

        test('should handle case when edit button is not found', () => {
            // Mock sessionStorage getItem to return 'true'
            window.sessionStorage.getItem.mockReturnValue('true');

            // Mock document.querySelector to return null (button not found)
            document.querySelector.mockReturnValue(null);

            // Execute the auto-edit check
            if (sessionStorage.getItem('enterEditMode') === 'true') {
                setTimeout(() => {
                    const editButton = document.querySelector('[data-test-id="6c4d6842-e8a6-4546-a5ee-ff5dbc501ab8"]');
                    if (editButton) {
                        editButton.click();
                        sessionStorage.removeItem('enterEditMode');
                        sessionStorage.removeItem('customerId');
                    }
                }, 1000);
            }

            // Fast-forward timers
            vi.advanceTimersByTime(1000);

            // Assert
            expect(sessionStorage.getItem).toHaveBeenCalledWith('enterEditMode');
            expect(document.querySelector).toHaveBeenCalledWith('[data-test-id="6c4d6842-e8a6-4546-a5ee-ff5dbc501ab8"]');
            expect(sessionStorage.removeItem).not.toHaveBeenCalled();
        });
    });

    // Cleanup
    afterEach(() => {
        // Restore window object
        window = originalWindow;
        
        // Clear all mocks
        vi.clearAllMocks();
        
        // Restore real timers
        vi.useRealTimers();
    });
}); 