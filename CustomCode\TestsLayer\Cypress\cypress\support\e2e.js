// ***********************************************************
// This example support/e2e.js is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands'

// Alternatively you can use CommonJS syntax:
// require('./commands')

Cypress.on('uncaught:exception', (err, runnable) => {
    // Ignore specific errors
    if (err.message.includes('Unexpected end of JSON input')) {
        return false; // Prevent Cypress from failing the test on this error
    }

    if (err.message.includes('ResizeObserver')) {
        return false; // Prevent Cypress from failing the test on ResizeObserver error
    }

    if (err.message.includes("Cannot set properties of null (setting 'isMemoryOnlyCollection')")) {
        return false; // Prevent Cypress from failing the test on this specific error
    }
    // Returning false here prevents Cypress from failing the test on uncaught exceptions
    if (err.message.includes('Cannot read properties of null')) {
        return false;
    }
    // Add the new error about LoadObject
    if (err.message.includes('Cannot read properties of undefined (reading \'LoadObject\')')) {
        return false; // Prevent Cypress from failing the test on this specific error
    }
    // Let other errors fail the test
    return true;
});

Cypress.Commands.add('login', () => {
    // Intercept API request
    //cy.intercept('/dataset/api/gosecurityprovider/authenticate').as('authenticateRequest');

    // Set higher viewport resolution (e.g., 2560x1440)
    //cy.viewport(2560, 1440);
    cy.viewport(1920, 1080);

    // Visit the login page
    cy.visit("/Membership/Login.html");

    cy.wait(1000);

    // Wait for the username field to be visible and interact with it
    cy.get("[data-test-id='LoginUser_UserName']", { timeout: 10000 })
        .should('be.visible')
        .click()
        .type("Admin");

    // Wait for the login button to be visible and click it
    cy.get("[data-test-id='LoginUser_LoginButton']", { timeout: 10000 })
        .should('be.visible')
        .first()
        .click();

    // Wait for the password field to be visible and interact with it
    cy.get("[data-test-id='LoginUser_Password']", { timeout: 10000 })
        .should('be.visible')
        .type("Admin");

    // Wait for the login button to be visible and click it
    cy.get("[data-test-id='LoginUser_LoginButton']", { timeout: 10000 })
        .should('be.visible')
        .eq(1)
        .click();

    // Wait for API response
    //cy.wait('@authenticateRequest').then((interception) => {
    //    if (interception.response.statusCode !== 200) {
    //        const responseBody = JSON.stringify(interception.response.body, null, 2); // Convert response body to a formatted string
    //        cy.log('API Error:', responseBody); // Log the entire response body as a string
    //    }
    //});
});

Cypress.Commands.add('loginCustomer', () => {
    // Intercept API request
    //cy.intercept('/dataset/api/gosecurityprovider/authenticate').as('authenticateRequest');

    // Set higher viewport resolution (e.g., 2560x1440)
    //cy.viewport(2560, 1440);
    cy.viewport(1920, 1080);

    // Visit the login page
    cy.visit("/Membership/Login.html");

    cy.wait(3000);

    // Wait for the username field to be visible and interact with it
    cy.get("[data-test-id='LoginUser_UserName']", { timeout: 10000 })
        .should('be.visible')
        .click()
        .type("tats");

    // Wait for the login button to be visible and click it
    cy.get("[data-test-id='LoginUser_LoginButton']", { timeout: 10000 })
        .should('be.visible')
        .first()
        .click();

    // Wait for the password field to be visible and interact with it
    cy.get("[data-test-id='LoginUser_Password']", { timeout: 10000 })
        .should('be.visible')
        .type("tats");

    // Wait for the login button to be visible and click it
    cy.get("[data-test-id='LoginUser_LoginButton']", { timeout: 10000 })
        .should('be.visible')
        .eq(1)
        .click();

    // Wait for API response
    //cy.wait('@authenticateRequest').then((interception) => {
    //    if (interception.response.statusCode !== 200) {
    //        const responseBody = JSON.stringify(interception.response.body, null, 2); // Convert response body to a formatted string
    //        cy.log('API Error:', responseBody); // Log the entire response body as a string
    //    }
    //});
});

Cypress.Commands.add('logout', () => {
    cy.get('[data-bind="visible: security == null ? false : security.isLoggedIn(), click: security != null ? security.logOut : null"]')
        .should('be.visible')
        .click();
});
