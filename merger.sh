#!/bin/bash
set -e  # Stop on error (except for merge conflicts we handle manually)

MAIN_BRANCH="main"

echo "Fetching latest from origin..."
git fetch origin

# Save current branch so we can return to it later
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)

# Checkout and update main branch
if [ "$CURRENT_BRANCH" != "$MAIN_BRANCH" ]; then
    echo "Switching to $MAIN_BRANCH..."
    git checkout $MAIN_BRANCH
fi

echo "Pulling latest changes into $MAIN_BRANCH..."
git pull origin $MAIN_BRANCH

# List all local branches except main
BRANCHES=$(git for-each-ref --format='%(refname:short)' refs/heads/ | grep -v "^$MAIN_BRANCH$")

echo
echo "The following local branches will be updated with latest from '$MAIN_BRANCH':"
for BRANCH in $BRANCHES; do
    echo "  - $BRANCH"
done
echo
read -n1 -rsp $'Press any key to start merging...\n'

for BRANCH in $BRANCHES; do
    echo
    echo "============================"
    echo "Processing branch: $BRANCH"
    echo "============================"

    git checkout $BRANCH
    git pull origin $BRANCH

    echo "Merging $MAIN_BRANCH into $BRANCH..."
    BEFORE_MERGE=$(git rev-parse HEAD)

    if ! git merge --no-commit --no-ff $MAIN_BRANCH; then
        echo "Merge conflict on $BRANCH. Skipping."
        git merge --abort
        continue
    fi

    if git diff --quiet --cached; then
        echo "No changes from $MAIN_BRANCH. Skipping commit and push."
        git reset --hard $BEFORE_MERGE  # Clean up the staged state
        continue
    fi

    echo "Committing merge changes..."
    git commit -m "Merge $MAIN_BRANCH into $BRANCH"

    echo "Pushing $BRANCH to origin..."
    git push origin $BRANCH
done

echo
read -n1 -rsp $'\nAll done. Press any key to exit...\n' 