using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using FleetXQ.Data.DataObjects;
using FleetXQ.BusinessLayer.Components.Server;
using FleetXQ.BusinessLayer.Tasks;
using FleetXQ.BusinessLayer.ORMSupportClasses;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.ImportExport.Extensions;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace FleetXQ.BusinessLayer.Components.Server.Custom
{
    public class BroadcastMessageHistoryExportComponentExtension : IImportExportComponentExtension<BroadcastMessageHistoryExportSection0Component, BroadcastMessageHistoryDataObject>
    {
        private readonly IServiceProvider _serviceProvider;
        private IDataFacade _dataFacade;

        public BroadcastMessageHistoryExportComponentExtension(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public void Init(IImportExportComponent<BroadcastMessageHistoryDataObject> component)
        {
            _dataFacade = _serviceProvider.GetRequiredService<IDataFacade>();
            component.OnBeforeExportDataRowAsync += ImportExportComponent_OnBeforeExportDataRowAsync;
        }

        private async Task ImportExportComponent_OnBeforeExportDataRowAsync(OnBeforeExportDataRowEventArgs<BroadcastMessageHistoryDataObject> arg)
        {
            try
            {
                if (arg.Entity?.Driver?.Person == null)
                {
                    // If no driver or person data, leave columns empty
                    return;
                }

                // Load the Person object with its related data
                var personToGet = _serviceProvider.GetRequiredService<PersonDataObject>().Initialize(arg.Entity.Driver.Person.Id);
                var person = await _dataFacade.PersonDataProvider.GetAsync(
                    personToGet,
                    includes: new List<string> { "Customer", "Site", "Department" }
                );

                if (person != null)
                {
                    // Map Customer (Column 1)
                    arg.DataRow[1] = person.Customer?.CompanyName ?? "";

                    // Map Site (Column 2)
                    arg.DataRow[2] = person.Site?.Name ?? "";

                    // Map Department (Column 3)
                    arg.DataRow[3] = person.Department?.Name ?? "";
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't stop the export
                System.Diagnostics.Debug.WriteLine($"Error mapping person data: {ex.Message}");
            }
        }
    }
} 