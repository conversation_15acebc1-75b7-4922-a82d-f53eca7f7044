﻿using FleetXQ.BusinessLayer.ORMSupportClasses;
using FleetXQ.Data.DataObjects;
using GenerativeObjects.Practices.LayerSupportClasses.BusinessLayer.Components;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace FleetXQ.BusinessLayer.Components.Server
{
    public class GO2FASecurityProviderComponent : BaseServerComponent, IGO2FASecurityProviderComponent
    {
        public GO2FASecurityProviderComponent(IServiceProvider provider, Microsoft.Extensions.Configuration.IConfiguration configuration, IDataFacade dataFacade) : base(provider, configuration, dataFacade)
        {
        }

        public Task<ComponentResponse<string>> BeforeSetAuthenticationTokenAsync(GOUserDataObject faUser, Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }

        public Task<ComponentResponse<string>> ValidateOTPAsync(string codeOTP, Guid id, Dictionary<string, object> parameters = null)
        {
            throw new NotImplementedException();
        }
    }
}