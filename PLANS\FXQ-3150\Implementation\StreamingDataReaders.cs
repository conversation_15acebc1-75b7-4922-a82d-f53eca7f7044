using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;

namespace FleetXQ.Tools.BulkImporter
{
    /// <summary>
    /// Streaming data reader for driver import data that implements IDataReader
    /// for SqlBulkCopy without materializing large collections in memory
    /// </summary>
    public class DriverDataReader : IDataReader
    {
        private readonly IEnumerator<DriverImportRow> _enumerator;
        private readonly string[] _columnNames;
        private bool _disposed = false;
        private int _recordsProcessed = 0;

        public DriverDataReader(IEnumerable<DriverImportRow> drivers)
        {
            _enumerator = drivers.GetEnumerator();
            _columnNames = new[]
            {
                "ExternalId", "FirstName", "LastName", "Email", "PhoneNumber",
                "EmployeeNumber", "DepartmentName", "SiteName", "CustomerName", "Active"
            };
        }

        public int RecordsProcessed => _recordsProcessed;

        public bool Read()
        {
            if (_enumerator.MoveNext())
            {
                _recordsProcessed++;
                return true;
            }
            return false;
        }

        public object GetValue(int ordinal)
        {
            var current = _enumerator.Current;
            return ordinal switch
            {
                0 => current.ExternalId ?? (object)DBNull.Value,
                1 => current.FirstName ?? (object)DBNull.Value,
                2 => current.LastName ?? (object)DBNull.Value,
                3 => current.Email ?? (object)DBNull.Value,
                4 => current.PhoneNumber ?? (object)DBNull.Value,
                5 => current.EmployeeNumber ?? (object)DBNull.Value,
                6 => current.DepartmentName ?? (object)DBNull.Value,
                7 => current.SiteName ?? (object)DBNull.Value,
                8 => current.CustomerName ?? (object)DBNull.Value,
                9 => current.Active,
                _ => throw new ArgumentOutOfRangeException(nameof(ordinal))
            };
        }

        public string GetName(int ordinal) => _columnNames[ordinal];
        public int GetOrdinal(string name) => Array.IndexOf(_columnNames, name);
        public Type GetFieldType(int ordinal)
        {
            return ordinal switch
            {
                >= 0 and <= 8 => typeof(string), // String fields
                9 => typeof(bool), // Active field
                _ => throw new ArgumentOutOfRangeException(nameof(ordinal))
            };
        }

        public int FieldCount => _columnNames.Length;
        public object this[int i] => GetValue(i);
        public object this[string name] => GetValue(GetOrdinal(name));

        // Required IDataReader members - most not needed for SqlBulkCopy
        public bool IsDBNull(int i) => GetValue(i) == DBNull.Value;
        public string GetString(int i) => GetValue(i) as string;
        public bool GetBoolean(int i) => (bool)GetValue(i);
        public void Close() => Dispose();
        public DataTable GetSchemaTable() => null;
        public bool NextResult() => false;
        public int Depth => 0;
        public bool IsClosed => _disposed;
        public int RecordsAffected => -1;

        // Not implemented - not needed for SqlBulkCopy
        public byte GetByte(int i) => throw new NotImplementedException();
        public long GetBytes(int i, long fieldOffset, byte[] buffer, int bufferoffset, int length) => throw new NotImplementedException();
        public char GetChar(int i) => throw new NotImplementedException();
        public long GetChars(int i, long fieldoffset, char[] buffer, int bufferoffset, int length) => throw new NotImplementedException();
        public IDataReader GetData(int i) => throw new NotImplementedException();
        public string GetDataTypeName(int i) => throw new NotImplementedException();
        public DateTime GetDateTime(int i) => throw new NotImplementedException();
        public decimal GetDecimal(int i) => throw new NotImplementedException();
        public double GetDouble(int i) => throw new NotImplementedException();
        public float GetFloat(int i) => throw new NotImplementedException();
        public Guid GetGuid(int i) => throw new NotImplementedException();
        public short GetInt16(int i) => throw new NotImplementedException();
        public int GetInt32(int i) => throw new NotImplementedException();
        public long GetInt64(int i) => throw new NotImplementedException();
        public int GetValues(object[] values) => throw new NotImplementedException();

        public void Dispose()
        {
            if (!_disposed)
            {
                _enumerator?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Streaming data reader for vehicle import data
    /// </summary>
    public class VehicleDataReader : IDataReader
    {
        private readonly IEnumerator<VehicleImportRow> _enumerator;
        private readonly string[] _columnNames;
        private bool _disposed = false;
        private int _recordsProcessed = 0;

        public VehicleDataReader(IEnumerable<VehicleImportRow> vehicles)
        {
            _enumerator = vehicles.GetEnumerator();
            _columnNames = new[]
            {
                "VIN", "HireNo", "ModelName", "SerialNumber", "RegistrationNumber",
                "DepartmentName", "SiteName", "CustomerName", "Active", "YearManufactured"
            };
        }

        public int RecordsProcessed => _recordsProcessed;

        public bool Read()
        {
            if (_enumerator.MoveNext())
            {
                _recordsProcessed++;
                return true;
            }
            return false;
        }

        public object GetValue(int ordinal)
        {
            var current = _enumerator.Current;
            return ordinal switch
            {
                0 => current.VIN ?? (object)DBNull.Value,
                1 => current.HireNo ?? (object)DBNull.Value,
                2 => current.ModelName ?? (object)DBNull.Value,
                3 => current.SerialNumber ?? (object)DBNull.Value,
                4 => current.RegistrationNumber ?? (object)DBNull.Value,
                5 => current.DepartmentName ?? (object)DBNull.Value,
                6 => current.SiteName ?? (object)DBNull.Value,
                7 => current.CustomerName ?? (object)DBNull.Value,
                8 => current.Active,
                9 => current.YearManufactured.HasValue ? current.YearManufactured.Value : (object)DBNull.Value,
                _ => throw new ArgumentOutOfRangeException(nameof(ordinal))
            };
        }

        public string GetName(int ordinal) => _columnNames[ordinal];
        public int GetOrdinal(string name) => Array.IndexOf(_columnNames, name);
        public Type GetFieldType(int ordinal)
        {
            return ordinal switch
            {
                >= 0 and <= 7 => typeof(string), // String fields
                8 => typeof(bool), // Active field
                9 => typeof(int), // YearManufactured field
                _ => throw new ArgumentOutOfRangeException(nameof(ordinal))
            };
        }

        public int FieldCount => _columnNames.Length;
        public object this[int i] => GetValue(i);
        public object this[string name] => GetValue(GetOrdinal(name));

        // Required IDataReader members
        public bool IsDBNull(int i) => GetValue(i) == DBNull.Value;
        public string GetString(int i) => GetValue(i) as string;
        public bool GetBoolean(int i) => (bool)GetValue(i);
        public int GetInt32(int i) => (int)GetValue(i);
        public void Close() => Dispose();
        public DataTable GetSchemaTable() => null;
        public bool NextResult() => false;
        public int Depth => 0;
        public bool IsClosed => _disposed;
        public int RecordsAffected => -1;

        // Not implemented - not needed for SqlBulkCopy
        public byte GetByte(int i) => throw new NotImplementedException();
        public long GetBytes(int i, long fieldOffset, byte[] buffer, int bufferoffset, int length) => throw new NotImplementedException();
        public char GetChar(int i) => throw new NotImplementedException();
        public long GetChars(int i, long fieldoffset, char[] buffer, int bufferoffset, int length) => throw new NotImplementedException();
        public IDataReader GetData(int i) => throw new NotImplementedException();
        public string GetDataTypeName(int i) => throw new NotImplementedException();
        public DateTime GetDateTime(int i) => throw new NotImplementedException();
        public decimal GetDecimal(int i) => throw new NotImplementedException();
        public double GetDouble(int i) => throw new NotImplementedException();
        public float GetFloat(int i) => throw new NotImplementedException();
        public Guid GetGuid(int i) => throw new NotImplementedException();
        public short GetInt16(int i) => throw new NotImplementedException();
        public long GetInt64(int i) => throw new NotImplementedException();
        public int GetValues(object[] values) => throw new NotImplementedException();

        public void Dispose()
        {
            if (!_disposed)
            {
                _enumerator?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Generic streaming data reader that can be configured for any entity type
    /// </summary>
    public class ConfigurableDataReader<T> : IDataReader
    {
        private readonly IEnumerator<T> _enumerator;
        private readonly ColumnConfiguration[] _columns;
        private bool _disposed = false;
        private int _recordsProcessed = 0;

        public ConfigurableDataReader(IEnumerable<T> data, params ColumnConfiguration[] columns)
        {
            _enumerator = data.GetEnumerator();
            _columns = columns;
        }

        public int RecordsProcessed => _recordsProcessed;

        public bool Read()
        {
            if (_enumerator.MoveNext())
            {
                _recordsProcessed++;
                return true;
            }
            return false;
        }

        public object GetValue(int ordinal)
        {
            if (ordinal < 0 || ordinal >= _columns.Length)
                throw new ArgumentOutOfRangeException(nameof(ordinal));

            var column = _columns[ordinal];
            var value = column.ValueGetter(_enumerator.Current);
            return value ?? DBNull.Value;
        }

        public string GetName(int ordinal) => _columns[ordinal].Name;
        public int GetOrdinal(string name) => Array.FindIndex(_columns, c => c.Name == name);
        public Type GetFieldType(int ordinal) => _columns[ordinal].Type;
        public int FieldCount => _columns.Length;
        public object this[int i] => GetValue(i);
        public object this[string name] => GetValue(GetOrdinal(name));

        // Required IDataReader members
        public bool IsDBNull(int i) => GetValue(i) == DBNull.Value;
        public string GetString(int i) => GetValue(i) as string;
        public bool GetBoolean(int i) => (bool)GetValue(i);
        public int GetInt32(int i) => (int)GetValue(i);
        public void Close() => Dispose();
        public DataTable GetSchemaTable() => null;
        public bool NextResult() => false;
        public int Depth => 0;
        public bool IsClosed => _disposed;
        public int RecordsAffected => -1;

        // Not implemented
        public byte GetByte(int i) => throw new NotImplementedException();
        public long GetBytes(int i, long fieldOffset, byte[] buffer, int bufferoffset, int length) => throw new NotImplementedException();
        public char GetChar(int i) => throw new NotImplementedException();
        public long GetChars(int i, long fieldoffset, char[] buffer, int bufferoffset, int length) => throw new NotImplementedException();
        public IDataReader GetData(int i) => throw new NotImplementedException();
        public string GetDataTypeName(int i) => throw new NotImplementedException();
        public DateTime GetDateTime(int i) => throw new NotImplementedException();
        public decimal GetDecimal(int i) => throw new NotImplementedException();
        public double GetDouble(int i) => throw new NotImplementedException();
        public float GetFloat(int i) => throw new NotImplementedException();
        public Guid GetGuid(int i) => throw new NotImplementedException();
        public short GetInt16(int i) => throw new NotImplementedException();
        public long GetInt64(int i) => throw new NotImplementedException();
        public int GetValues(object[] values) => throw new NotImplementedException();

        public void Dispose()
        {
            if (!_disposed)
            {
                _enumerator?.Dispose();
                _disposed = true;
            }
        }
    }

    /// <summary>
    /// Configuration for a column in the configurable data reader
    /// </summary>
    public class ColumnConfiguration
    {
        public string Name { get; set; }
        public Type Type { get; set; }
        public Func<object, object> ValueGetter { get; set; }

        public static ColumnConfiguration Create<T, TValue>(string name, Func<T, TValue> getter)
        {
            return new ColumnConfiguration
            {
                Name = name,
                Type = typeof(TValue),
                ValueGetter = obj => getter((T)obj)
            };
        }
    }

    #region Data Models

    /// <summary>
    /// Model for driver import row data
    /// </summary>
    public class DriverImportRow
    {
        public string ExternalId { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string PhoneNumber { get; set; }
        public string EmployeeNumber { get; set; }
        public string DepartmentName { get; set; }
        public string SiteName { get; set; }
        public string CustomerName { get; set; }
        public bool Active { get; set; } = true;
    }

    /// <summary>
    /// Model for vehicle import row data
    /// </summary>
    public class VehicleImportRow
    {
        public string VIN { get; set; }
        public string HireNo { get; set; }
        public string ModelName { get; set; }
        public string SerialNumber { get; set; }
        public string RegistrationNumber { get; set; }
        public string DepartmentName { get; set; }
        public string SiteName { get; set; }
        public string CustomerName { get; set; }
        public bool Active { get; set; } = true;
        public int? YearManufactured { get; set; }
    }

    #endregion
}

namespace FleetXQ.Tools.BulkImporter.Extensions
{
    /// <summary>
    /// Extension methods for working with streaming data
    /// </summary>
    public static class EnumerableExtensions
    {
        /// <summary>
        /// Batches an enumerable into chunks of specified size for processing
        /// </summary>
        public static IEnumerable<IEnumerable<T>> Batch<T>(this IEnumerable<T> source, int batchSize)
        {
            var batch = new List<T>(batchSize);
            foreach (var item in source)
            {
                batch.Add(item);
                if (batch.Count == batchSize)
                {
                    yield return batch;
                    batch = new List<T>(batchSize);
                }
            }
            if (batch.Count > 0)
                yield return batch;
        }

        /// <summary>
        /// Creates a streaming data reader from any enumerable with column configuration
        /// </summary>
        public static ConfigurableDataReader<T> ToDataReader<T>(
            this IEnumerable<T> source,
            params ColumnConfiguration[] columns)
        {
            return new ConfigurableDataReader<T>(source, columns);
        }
    }
}
