import { describe, it, expect, beforeEach, vi } from 'vitest'
import ko from 'knockout'
import fs from 'fs'
import path from 'path'

describe('VehicleToPreOpChecklistGridViewModelCustom', () => {
    let viewModel;
    let customViewModel;
    let mockApplicationController;
    let mockDepartmentChecklist;

    beforeEach(() => {
        // Setup FleetXQ namespace
        global.FleetXQ = {
            Web: {
                ViewModels: {}
            }
        };

        // Mock ApplicationController
        mockApplicationController = {
            viewModel: {
                security: {
                    currentUserClaims: vi.fn().mockReturnValue({
                        HasVehiclesAccess: 'True',
                        CanCreateChecklist: 'True',
                        CanEditVehicleChecklist: 'True',
                        CanDeleteChecklist: 'True'
                    })
                }
            },
            getProxyForComponent: vi.fn().mockReturnValue({
                ActivateQuestion: vi.fn((config) => {
                    // Call the success handler
                    if (config.successHandler) {
                        config.successHandler();
                    }
                })
            }),
            showEditPopup: vi.fn()
        };
        global.ApplicationController = mockApplicationController;

        // Mock console.error and console.warn to avoid test output noise
        global.console.error = vi.fn();
        global.console.warn = vi.fn();

        // Read and evaluate the actual custom file
        const customFilePath = path.resolve(__dirname, '../../../WebApplicationLayer/wwwroot/ViewModels/VehicleToPreOpChecklistView/VehicleToPreOpChecklistGridViewModel.custom.js');
        const customFileContent = fs.readFileSync(customFilePath, 'utf8');
        eval(customFileContent);

        // Create mock department checklist with language enablement methods
        mockDepartmentChecklist = {
            Data: {
                IsThaiEnabled: ko.observable(false),
                IsFrenchEnabled: ko.observable(false),
                IsFilipinoEnabled: ko.observable(false),
                IsSpanishEnabled: ko.observable(false),
                IsTraditionalChineseEnabled: ko.observable(false),
                IsVietnameseEnabled: ko.observable(false)
            }
        };

        // Create base view model with required properties
        viewModel = {
            DataStore: {
                CheckAuthorizationForEntityAndMethod: vi.fn().mockReturnValue(true)
            },
            selectedObjectId: ko.observable(null),
            selectedObject: ko.observable(null),
            setIsBusy: vi.fn(),
            ShowError: vi.fn(),
            contextId: 'test-context',
            VehicleToPreOpChecklistViewFilterViewModel: {
                commands: {
                    searchCommand: vi.fn()
                }
            },
            parentViewModel: {
                DepartmentChecklistFormViewModel: {
                    GetDepartmentChecklistObject: vi.fn(() => mockDepartmentChecklist)
                }
            },
            isMemoryOnlyCollection: false,
            StatusData: {
                DisplayMode: ko.observable('view')
            },
            commands: {} // Initialize commands object
        };

        // Create the custom view model
        customViewModel = new FleetXQ.Web.ViewModels.VehicleToPreOpChecklistGridViewModelCustom(viewModel);
    });

    describe('Command Visibility', () => {
        it('should show ADDQUESTION popup when user has proper permissions', () => {
            // Setup
            viewModel.DataStore.CheckAuthorizationForEntityAndMethod.mockReturnValue(true);
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                HasVehiclesAccess: 'True',
                CanCreateChecklist: 'True'
            });

            // Execute
            const result = customViewModel.IsShowADDQUESTIONPopupCommandVisible();

            // Assert
            expect(result).toBe(true);
        });

        it('should not show ADDQUESTION popup when user lacks permissions', () => {
            // Setup
            viewModel.DataStore.CheckAuthorizationForEntityAndMethod.mockReturnValue(true);
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                HasVehiclesAccess: 'True',
                CanCreateChecklist: 'False'
            });

            // Execute
            const result = customViewModel.IsShowADDQUESTIONPopupCommandVisible();

            // Assert
            expect(result).toBe(false);
        });

        it('should show EditQuestion popup when item is selected and user has permissions', () => {
            // Setup
            viewModel.selectedObjectId(1);
            viewModel.selectedObject({
                Data: {
                    PreOperationalChecklistId: ko.observable('test-id')
                }
            });
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                HasVehiclesAccess: 'True',
                CanEditVehicleChecklist: 'True'
            });

            // Execute
            const result = customViewModel.IsShowEditQuestionPopupCommandVisible();

            // Assert
            expect(result).toBe(true);
        });

        it('should show Delete command when item is selected and user has permissions', () => {
            // Setup
            viewModel.selectedObjectId(1);
            viewModel.DataStore.CheckAuthorizationForEntityAndMethod.mockReturnValue(true);
            ApplicationController.viewModel.security.currentUserClaims.mockReturnValue({
                HasVehiclesAccess: 'True',
                CanDeleteChecklist: 'True'
            });

            // Execute
            const result = customViewModel.IsDeleteCommandVisible();

            // Assert
            expect(result).toBe(true);
        });
    });

    describe('Question Visibility', () => {
        describe('IsThaiQuestionVisible', () => {
            it('should return false when parentViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = null;

                // Execute
                const result = customViewModel.IsThaiQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when DepartmentChecklistFormViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = {
                    DepartmentChecklistFormViewModel: null
                };

                // Execute
                const result = customViewModel.IsThaiQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when department checklist is null', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel.GetDepartmentChecklistObject.mockReturnValue(null);

                // Execute
                const result = customViewModel.IsThaiQuestionVisible();

                // Assert - original implementation can return null in this case
                expect(result).toBeFalsy();
            });

            it('should return false when Thai is not enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsThaiEnabled(false);

                // Execute
                const result = customViewModel.IsThaiQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when Thai is enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsThaiEnabled(true);

                // Execute
                const result = customViewModel.IsThaiQuestionVisible();

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('IsFrenchQuestionVisible', () => {
            it('should return false when parentViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = null;

                // Execute
                const result = customViewModel.IsFrenchQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when DepartmentChecklistFormViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = {
                    DepartmentChecklistFormViewModel: null
                };

                // Execute
                const result = customViewModel.IsFrenchQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when department checklist is null', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel.GetDepartmentChecklistObject.mockReturnValue(null);

                // Execute
                const result = customViewModel.IsFrenchQuestionVisible();

                // Assert - original implementation can return null in this case
                expect(result).toBeFalsy();
            });

            it('should return false when French is not enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsFrenchEnabled(false);

                // Execute
                const result = customViewModel.IsFrenchQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when French is enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsFrenchEnabled(true);

                // Execute
                const result = customViewModel.IsFrenchQuestionVisible();

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('IsFilipinoQuestionVisible', () => {
            it('should return false when parentViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = null;

                // Execute
                const result = customViewModel.IsFilipinoQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when DepartmentChecklistFormViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = {
                    DepartmentChecklistFormViewModel: null
                };

                // Execute
                const result = customViewModel.IsFilipinoQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when department checklist is null', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel.GetDepartmentChecklistObject.mockReturnValue(null);

                // Execute
                const result = customViewModel.IsFilipinoQuestionVisible();

                // Assert - original implementation can return null in this case
                expect(result).toBeFalsy();
            });

            it('should return false when Filipino is not enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsFilipinoEnabled(false);

                // Execute
                const result = customViewModel.IsFilipinoQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when Filipino is enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsFilipinoEnabled(true);

                // Execute
                const result = customViewModel.IsFilipinoQuestionVisible();

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('IsSpanishQuestionVisible', () => {
            it('should return false when parentViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = null;

                // Execute
                const result = customViewModel.IsSpanishQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when DepartmentChecklistFormViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = {
                    DepartmentChecklistFormViewModel: null
                };

                // Execute
                const result = customViewModel.IsSpanishQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when department checklist is null', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel.GetDepartmentChecklistObject.mockReturnValue(null);

                // Execute
                const result = customViewModel.IsSpanishQuestionVisible();

                // Assert - original implementation can return null in this case
                expect(result).toBeFalsy();
            });

            it('should return false when Spanish is not enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsSpanishEnabled(false);

                // Execute
                const result = customViewModel.IsSpanishQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when Spanish is enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsSpanishEnabled(true);

                // Execute
                const result = customViewModel.IsSpanishQuestionVisible();

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('IsTraditionalChineseQuestionVisible', () => {
            it('should return false when parentViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = null;

                // Execute
                const result = customViewModel.IsTraditionalChineseQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when DepartmentChecklistFormViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = {
                    DepartmentChecklistFormViewModel: null
                };

                // Execute
                const result = customViewModel.IsTraditionalChineseQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when department checklist is null', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel.GetDepartmentChecklistObject.mockReturnValue(null);

                // Execute
                const result = customViewModel.IsTraditionalChineseQuestionVisible();

                // Assert - original implementation can return null in this case
                expect(result).toBeFalsy();
            });

            it('should return false when Traditional Chinese is not enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsTraditionalChineseEnabled(false);

                // Execute
                const result = customViewModel.IsTraditionalChineseQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when Traditional Chinese is enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsTraditionalChineseEnabled(true);

                // Execute
                const result = customViewModel.IsTraditionalChineseQuestionVisible();

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('IsVietnameseQuestionVisible', () => {
            it('should return false when parentViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = null;

                // Execute
                const result = customViewModel.IsVietnameseQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when DepartmentChecklistFormViewModel is not available', () => {
                // Setup
                viewModel.parentViewModel = {
                    DepartmentChecklistFormViewModel: null
                };

                // Execute
                const result = customViewModel.IsVietnameseQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return false when department checklist is null', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel.GetDepartmentChecklistObject.mockReturnValue(null);

                // Execute
                const result = customViewModel.IsVietnameseQuestionVisible();

                // Assert - original implementation can return null in this case
                expect(result).toBeFalsy();
            });

            it('should return false when Vietnamese is not enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsVietnameseEnabled(false);

                // Execute
                const result = customViewModel.IsVietnameseQuestionVisible();

                // Assert
                expect(result).toBe(false);
            });

            it('should return true when Vietnamese is enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsVietnameseEnabled(true);

                // Execute
                const result = customViewModel.IsVietnameseQuestionVisible();

                // Assert
                expect(result).toBe(true);
            });
        });

        describe('Multiple language scenarios', () => {
            it('should handle multiple languages enabled simultaneously', () => {
                // Setup
                mockDepartmentChecklist.Data.IsThaiEnabled(true);
                mockDepartmentChecklist.Data.IsFrenchEnabled(true);
                mockDepartmentChecklist.Data.IsFilipinoEnabled(false);
                mockDepartmentChecklist.Data.IsSpanishEnabled(true);
                mockDepartmentChecklist.Data.IsTraditionalChineseEnabled(false);
                mockDepartmentChecklist.Data.IsVietnameseEnabled(true);

                // Execute & Assert
                expect(customViewModel.IsThaiQuestionVisible()).toBe(true);
                expect(customViewModel.IsFrenchQuestionVisible()).toBe(true);
                expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
                expect(customViewModel.IsSpanishQuestionVisible()).toBe(true);
                expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
                expect(customViewModel.IsVietnameseQuestionVisible()).toBe(true);
            });

            it('should handle all languages disabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsThaiEnabled(false);
                mockDepartmentChecklist.Data.IsFrenchEnabled(false);
                mockDepartmentChecklist.Data.IsFilipinoEnabled(false);
                mockDepartmentChecklist.Data.IsSpanishEnabled(false);
                mockDepartmentChecklist.Data.IsTraditionalChineseEnabled(false);
                mockDepartmentChecklist.Data.IsVietnameseEnabled(false);

                // Execute & Assert
                expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
                expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
                expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
                expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
                expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
                expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
            });

            it('should handle all languages enabled', () => {
                // Setup
                mockDepartmentChecklist.Data.IsThaiEnabled(true);
                mockDepartmentChecklist.Data.IsFrenchEnabled(true);
                mockDepartmentChecklist.Data.IsFilipinoEnabled(true);
                mockDepartmentChecklist.Data.IsSpanishEnabled(true);
                mockDepartmentChecklist.Data.IsTraditionalChineseEnabled(true);
                mockDepartmentChecklist.Data.IsVietnameseEnabled(true);

                // Execute & Assert
                expect(customViewModel.IsThaiQuestionVisible()).toBe(true);
                expect(customViewModel.IsFrenchQuestionVisible()).toBe(true);
                expect(customViewModel.IsFilipinoQuestionVisible()).toBe(true);
                expect(customViewModel.IsSpanishQuestionVisible()).toBe(true);
                expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(true);
                expect(customViewModel.IsVietnameseQuestionVisible()).toBe(true);
            });
        });

        describe('Edge cases', () => {
            it('should handle undefined parentViewModel', () => {
                // Setup
                viewModel.parentViewModel = undefined;

                // Execute & Assert - original implementation returns false for these cases
                expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
                expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
                expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
                expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
                expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
                expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
            });

            it('should handle undefined DepartmentChecklistFormViewModel', () => {
                // Setup
                viewModel.parentViewModel.DepartmentChecklistFormViewModel = undefined;

                // Execute & Assert - original implementation returns false for these cases
                expect(customViewModel.IsThaiQuestionVisible()).toBe(false);
                expect(customViewModel.IsFrenchQuestionVisible()).toBe(false);
                expect(customViewModel.IsFilipinoQuestionVisible()).toBe(false);
                expect(customViewModel.IsSpanishQuestionVisible()).toBe(false);
                expect(customViewModel.IsTraditionalChineseQuestionVisible()).toBe(false);
                expect(customViewModel.IsVietnameseQuestionVisible()).toBe(false);
            });
        });
    });

    describe('Activation', () => {
        it('should activate question and refresh list on success', () => {
            // Setup
            const mockData = { id: 'test-id' };
            viewModel.selectedObject({
                Data: {
                    PreOperationalChecklistId: ko.observable('test-id')
                }
            });

            // Execute
            customViewModel.initialize();
            viewModel.Activate();

            // Assert
            expect(viewModel.setIsBusy).toHaveBeenCalledWith(true);
            expect(ApplicationController.getProxyForComponent).toHaveBeenCalledWith('PreopChecklistAPI');
            expect(viewModel.VehicleToPreOpChecklistViewFilterViewModel.commands.searchCommand).toHaveBeenCalled();
        });

        it('should show activate command only for inactive questions', () => {
            // Setup
            viewModel.selectedObjectId(1);
            viewModel.selectedObject({
                Data: {
                    Active: ko.observable(false)
                }
            });

            // Execute
            customViewModel.initialize();
            const result = viewModel.commands.IsActivateCommandVisible();

            // Assert
            expect(result).toBe(true);
        });
    });

    describe('Order Change', () => {
        it('should open order change popup in edit mode', () => {
            // Setup
            viewModel.Vehicle = ko.observable({ id: 'test-vehicle' });
            viewModel.isMemoryOnlyCollection = false;
            viewModel.StatusData.DisplayMode('view');

            // Execute
            customViewModel.initialize();
            viewModel.CHANGEORDER();

            // Assert
            expect(ApplicationController.showEditPopup).toHaveBeenCalledWith(
                'UpdateQuestionsOrderForm',
                viewModel,
                viewModel.Vehicle(),
                false,
                viewModel.contextId,
                '70%',
                false
            );
        });

        it('should open order change popup in memory only mode when appropriate', () => {
            // Setup
            viewModel.Vehicle = ko.observable({ id: 'test-vehicle' });
            viewModel.isMemoryOnlyCollection = true;

            // Execute
            customViewModel.initialize();
            viewModel.CHANGEORDER();

            // Assert
            expect(ApplicationController.showEditPopup).toHaveBeenCalledWith(
                'UpdateQuestionsOrderForm',
                viewModel,
                viewModel.Vehicle(),
                true,
                viewModel.contextId,
                '70%',
                false
            );
        });
    });
}); 