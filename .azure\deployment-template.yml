parameters:
  - name: environment
    type: string
  - name: appServiceName
    type: string
  - name: azureSubscription
    type: string
  - name: resourceGroupName
    type: string

steps:
  # Debug variables
  - powershell: |
      Write-Host "Debug Variables for ${{ parameters.environment }}:"
      Write-Host "----------- Environment Info -----------"
      Write-Host "Environment: ${{ parameters.environment }}"
      Write-Host "------------ Service Info -------------"
      Write-Host "appServiceName: ${{ parameters.appServiceName }}"
      Write-Host "azureSubscription: ${{ parameters.azureSubscription }}"
      Write-Host "resourceGroupName: ${{ parameters.resourceGroupName }}"
      Write-Host "------------ Storage Info -------------"
      Write-Host "AzureStorageAccountName: $(AzureStorageAccountName)"
      Write-Host "AzureStorageConfigContainer: $(AzureStorageConfigContainer)"
    displayName: "Debug Variables for ${{ parameters.environment }}"

  # Verify Azure connection
  - task: AzureCLI@2
    displayName: "Verify Azure Connection for ${{ parameters.environment }}"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      scriptType: "ps"
      scriptLocation: "inlineScript"
      inlineScript: |
        Write-Host "Verifying Azure connection for ${{ parameters.environment }}..."
        az account show
        Write-Host "Checking permissions on resource group..."
        az resource list --resource-group ${{ parameters.resourceGroupName }}
        Write-Host "Checking App Service..."
        az webapp show --name ${{ parameters.appServiceName }} --resource-group ${{ parameters.resourceGroupName }}

  # Download environment-specific configuration
  - powershell: |
      $env = "${{ parameters.environment }}"
      Write-Host "Preparing appsettings for environment: $env"

      # Download environment-specific settings file from Azure Storage
      Write-Host "Downloading appsettings from Azure Storage..."

      # Verify storage account details from variable group
      Write-Host "Storage account details:"
      Write-Host "AzureStorageAccountName: $(AzureStorageAccountName)"
      Write-Host "AzureStorageConfigContainer: $(AzureStorageConfigContainer)"

      # Check if variables are empty
      if ([string]::IsNullOrEmpty("$(AzureStorageAccountName)")) {
          Write-Error "AzureStorageAccountName is not set in the variable group!"
          exit 1
      }

      if ([string]::IsNullOrEmpty("$(AzureStorageConfigContainer)")) {
          Write-Error "AzureStorageConfigContainer is not set in the variable group!"
          exit 1
      }

      # Get storage account details from variable group
      $storageAccount = "$(AzureStorageAccountName)"
      $storageContainer = "$(AzureStorageConfigContainer)"
      $storageKey = "$(AzureStorageAccountKey)"
      $blobName = "appsettings.$env.json"
      $settingsFile = "$(Pipeline.Workspace)\drop\appsettings.$env.json"

      # Use AzCopy or Azure CLI to download the file with storage key
      Write-Host "Attempting to download from Azure Storage..."
      try {
          az storage blob download --account-name $storageAccount --container-name $storageContainer --name $blobName --file $settingsFile --account-key $storageKey
          $downloadSuccess = $?
      } catch {
          Write-Warning "Error downloading from Azure Storage: $_"
          $downloadSuccess = $false
      }

      if ($downloadSuccess -and (Test-Path $settingsFile)) {
          Write-Host "Environment-specific settings file downloaded: $settingsFile"
      } else {
          Write-Warning "Failed to download settings file from Azure Storage for $env environment."
          Write-Host "This may be acceptable if the deployment package already contains the correct settings."
      }
    displayName: "Download configuration from Azure Storage for ${{ parameters.environment }}"

  # Deploy to App Service
  - task: AzureWebApp@1
    displayName: "Deploy to App Service (${{ parameters.environment }})"
    inputs:
      azureSubscription: "${{ parameters.azureSubscription }}"
      appType: "webApp"
      appName: "${{ parameters.appServiceName }}"
      resourceGroupName: "${{ parameters.resourceGroupName }}"
      package: "$(Pipeline.Workspace)/drop/*.zip"
      deploymentMethod: "auto"
      appSettings: >-
        -ASPNETCORE_ENVIRONMENT "${{ parameters.environment }}" 
        -ASPNETCORE_DETAILEDERRORS "true" 
        -ASPNETCORE_SHUTDOWNTIMEOUTSECONDS "90" 
        -ASPNETCORE_FORWARDEDHEADERS_ENABLED "true"
        -WEBSITE_LOAD_USER_PROFILE "1"
        -WEBSITE_ENABLE_CORS "true"
        -ASPNETCORE_LOGGING__CONSOLE__LOGLEVEL__DEFAULT "Debug"

  # Post-deployment verification
  - powershell: |
      Write-Host "Deployment to ${{ parameters.environment }} completed successfully!"
      Write-Host "App Service: ${{ parameters.appServiceName }}"
      Write-Host "Resource Group: ${{ parameters.resourceGroupName }}"
      Write-Host "Subscription: ${{ parameters.azureSubscription }}"
    displayName: "Post-deployment verification for ${{ parameters.environment }}" 